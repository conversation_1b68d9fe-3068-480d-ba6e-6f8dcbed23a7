name: deploy favori-user

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  EC2_SECURITY_GROUP_ID: sg-0c6019dc538b156b0
  EC2_HOST: *************

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: 18
      
      - name: Install dependencies
        run: yarn install
      
      - name: Build Nuxt.js
        run: yarn build:production
      
      - name: Generate ssh key
        run: echo "$SSH_SECRET_KEY" > ${{ runner.temp }}/key && chmod 600 ${{ runner.temp }}/key
        env:
          SSH_SECRET_KEY: ${{ secrets.SSH_SECRET_KEY }}
          
      # IPアドレスを取得
      - name: Check Public IP
        id: ip
        uses: haythem/public-ip@v1.3

      # AWS CLIをインストールする
      - name: Install AWS CLI
        run: |
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install --update
          aws --version

      # AWS CLIにキーを設定をする
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1

      # SSHのセキュリティグループを開放する
      - name: Open security group
        run: aws ec2 authorize-security-group-ingress --group-id ${{ env.EC2_SECURITY_GROUP_ID }} --protocol tcp --port 22 --cidr ${{ steps.ip.outputs.ipv4 }}/32

      # frontend のデプロイ
      - name: Deploy frontend-user
        run: rsync -avz -e "ssh -i ${{ runner.temp }}/key -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" .output/ ec2-user@${{ env.EC2_HOST }}:/var/www/frontend/user/

      # backend のデプロイ
      - name: Deploy backend
        run: ssh -i ${{ runner.temp }}/key -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no ec2-user@${{ env.EC2_HOST }} "/bin/bash /var/www/backend/deploy.sh"

      # pm2 再起動
      - name: Restart pm2
        run:  ssh -i ${{ runner.temp }}/key -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no ec2-user@${{ env.EC2_HOST }} "/var/www/nginx/pm2restart"

      # SSHのセキュリティグループを閉じる
      - name: Close security group
        run: aws ec2 revoke-security-group-ingress --group-id ${{ env.EC2_SECURITY_GROUP_ID }} --protocol tcp --port 22 --cidr ${{ steps.ip.outputs.ipv4 }}/32