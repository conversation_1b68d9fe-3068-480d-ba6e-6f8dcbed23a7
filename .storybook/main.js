const path = require("path");

module.exports = {
  stories: ["../stories/**/*.stories.mdx", "../stories/**/*.stories.@(js|jsx|ts|tsx)"],
  addons: [
    "@storybook/addon-essentials",
    "storybook-addon-nuxt"
  ],
  framework: {
    name: "@storybook/vue3-vite",
    options: {}
  },
  docs: {
    autodocs: "tag"
  },
  staticDirs: ['../assets'],
  async viteFinal(config, { configType }) {
    config.resolve.alias = {
			...config.resolve.alias,
			'@': path.resolve(__dirname, '../')
		}

    config.css = {
      preprocessorOptions: {
        scss: { additionalData: `@import "@/assets/styles/_mixin.scss";\
                                 @import "@/assets/styles/_theme.scss";\
                                 @import "@/assets/styles/common.scss";\
                                 @import "@/assets/styles/animation.scss";` },
      },
    };
    return config;
  },
};