<template>
  <div>
    <div v-html="iframeHtml" />
    <NuxtPage />
  </div>
</template>
<script lang="ts" setup>
import { computed } from '#imports';
import { useHead, useRouter } from '#app';

const runtimeConfig = useRuntimeConfig()
const router = useRouter();
const currentPath = computed(
  () => `https://favori.wedding${router.currentRoute.value.path}`
);

const iframeHtml = ref(`<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=${runtimeConfig.public.gtmId}" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>`)

// LPページ以外でのみog:url設定を適用
const isLpPage = computed(() => router.currentRoute.value.path.startsWith('/lp/'));

useHead(() => {
  if (isLpPage.value) {
    return {};
  }
  return {
    meta: [
      { property: 'og:url', content: currentPath.value }
    ],
  };
});

// メモリ上に保持しているトークンとCookieに保存されているトークンを比較
const { setTmpAccessToken } = useAccessTokenState()
router.beforeEach((to, from, next) => {
  setTmpAccessToken('default');
  next()
})
router.afterEach((to, from) => {
  setTmpAccessToken('default');
})

onMounted(() => {
  // ダブルクリック禁止 : クリック後にリンクを無効化
  document.addEventListener("click", function (event) {
    const target = event.target as HTMLElement | null;
    const link = target?.closest("a"); // <a>タグを探索
    // <a>タグか確認
    if (link?.tagName === "A") {
      const href = link.getAttribute("href");
      if (href && (href.startsWith("/") || href.startsWith(window.location.origin))) {
        // data-disabled="true"を付与
        link.setAttribute("data-disabled", "true");
        // 1秒後に削除
        setTimeout(() => {
          link.removeAttribute("data-disabled");
        }, 1000);
      }
    }
});

  // Material Icons
  const materialIcons = new FontFace(
    'Material Icons',
    'url(/fonts/icons/materialIcons.woff2) format("woff2")',
    {
      style: 'normal',
      weight: '400',
    }
  );

  // Material Icons Outlined (weight 400)
  const materialIconsOutlined400 = new FontFace(
    'Material Icons Outlined',
    'url(/fonts/icons/materialIconsOutlined400.woff2) format("woff2")',
    {
      style: 'normal',
      weight: '400',
    }
  );

  // Material Symbols Outlined (weight 200)
  const materialSymbolsOutlined200 = new FontFace(
    'Material Symbols Outlined',
    'url(/fonts/icons/materialSymbolsOutlined200.woff2) format("woff2")',
    {
      style: 'normal',
      weight: '200',
    }
  );

  // Material Symbols Outlined (weight 400)
  const materialSymbolsOutlined400 = new FontFace(
    'Material Symbols Outlined',
    'url(/fonts/icons/materialSymbolsOutlined400.woff2) format("woff2")',
    {
      style: 'normal',
      weight: '400',
    }
  );

  // フォントをロードしてドキュメントに追加
  Promise.all([
    materialIcons.load(),
    materialIconsOutlined400.load(),
    materialSymbolsOutlined200.load(),
    materialSymbolsOutlined400.load()
  ]).then(() => {
    document.fonts.add(materialIcons);
    document.fonts.add(materialIconsOutlined400);
    document.fonts.add(materialSymbolsOutlined200);
    document.fonts.add(materialSymbolsOutlined400);
    document.body.classList.add('material-icons-loaded');
  });
});
</script>
<style lang="scss">
.material-icons ,
.material-icons-outlined ,
.material-symbols , 
.material-symbols-outlined {
  font-size: 0;
  display: none;
  visibility: hidden;
  opacity: 0;
  width: 1em;
  overflow: hidden;
  body.material-icons-loaded & {
    visibility: visible;
    overflow: visible;
    opacity: 1;
  }
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-symbols-outlined {
  font-family: 'Material Symbols Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.material-symbols-outlined {
  font-family: 'Material Symbols Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

// ダブルクリック禁止 : クリック後にリンクを無効化
a[data-disabled="true"] {
  pointer-events: none;
}
</style>
