@import url("https://fonts.googleapis.com/css2?family=Noto+Serif+JP:wght@200;300;400;500;600;700;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Waterfall&display=swap");
@import url("https://fonts.googleapis.com/css2?family=WindSong:wght@400;500&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=WindSong:wght@400;500&display=swap");
@-webkit-keyframes slideInToTop {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}
@keyframes slideInToTop {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}
@-webkit-keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@-webkit-keyframes slideInToLeft {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
@keyframes slideInToLeft {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
@-webkit-keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@-webkit-keyframes slideInToRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
@keyframes slideInToRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
@-webkit-keyframes profileBackground {
  0% {
    opacity: 0;
    rotate: -10deg;
    transform: translateX(-40px);
  }
  100% {
    opacity: 1;
    rotate: 0deg;
    transform: translateX(0);
  }
}
@keyframes profileBackground {
  0% {
    opacity: 0;
    rotate: -10deg;
    transform: translateX(-40px);
  }
  100% {
    opacity: 1;
    rotate: 0deg;
    transform: translateX(0);
  }
}
@-webkit-keyframes mainVisualBackground {
  0% {
    background-size: 400px;
  }
  100% {
    background-size: 376px;
  }
}
@keyframes mainVisualBackground {
  0% {
    background-size: 400px;
  }
  100% {
    background-size: 376px;
  }
}
@-webkit-keyframes mainVisualBackgroundSp {
  0% {
    background-size: 120%;
  }
  100% {
    background-size: 100%;
  }
}
@keyframes mainVisualBackgroundSp {
  0% {
    background-size: 120%;
  }
  100% {
    background-size: 100%;
  }
}
@keyframes slideInToLeft {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }
  80% {
    opacity: 1;
    transform: translateX(0px);
  }
  100% {
    opacity: 0;
    transform: translateX(150%);
  }
}
@keyframes slideInToRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  80% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-150%);
  }
}
@-webkit-keyframes mainVisualFirst {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes mainVisualFirst {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes profileBackground {
  0% {
    opacity: 0;
    rotate: -10deg;
    transform: translateX(-40px);
  }
  100% {
    opacity: 1;
    rotate: 0deg;
    transform: translateX(0);
  }
}
span.lang-ja {
  display: none !important;
}
span.lang-en {
  display: inline-block !important;
}

.webInvitationView {
  font-family: "Noto Serif JP", serif;
  color: #333;
  background: #fff;
}
.webInvitationView * {
  box-sizing: border-box;
}
.webInvitationView ::before,
.webInvitationView ::after {
  box-sizing: inherit;
}
.webInvitationView p,
.webInvitationView table,
.webInvitationView blockquote,
.webInvitationView address,
.webInvitationView pre,
.webInvitationView iframe,
.webInvitationView form,
.webInvitationView figure,
.webInvitationView dl {
  margin: 0;
}
.webInvitationView h1,
.webInvitationView h2,
.webInvitationView h3,
.webInvitationView h4,
.webInvitationView h5,
.webInvitationView h6 {
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
  margin: 0;
}
.webInvitationView ul,
.webInvitationView ol {
  margin: 0;
  padding: 0;
  list-style: none;
}
.webInvitationView dt {
  font-weight: bold;
}
.webInvitationView dd {
  margin-left: 0;
}
.webInvitationView hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
  border: 0;
  border-top: 1px solid;
  margin: 0;
  clear: both;
  color: inherit;
}
.webInvitationView pre {
  font-family: monospace, monospace;
  font-size: inherit;
}
.webInvitationView address {
  font-style: inherit;
}
.webInvitationView a {
  background-color: transparent;
  text-decoration: none;
  color: #8E7EAC;
}
.webInvitationView abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
.webInvitationView b,
.webInvitationView strong {
  font-weight: bolder;
}
.webInvitationView code,
.webInvitationView kbd,
.webInvitationView samp {
  font-family: monospace, monospace;
  font-size: inherit;
}
.webInvitationView small {
  font-size: 80%;
}
.webInvitationView sub,
.webInvitationView sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
.webInvitationView sub {
  bottom: -0.25em;
}
.webInvitationView sup {
  top: -0.5em;
}
.webInvitationView img {
  border-style: none;
  vertical-align: bottom;
}
.webInvitationView embed,
.webInvitationView object,
.webInvitationView iframe {
  border: 0;
  vertical-align: bottom;
}
.webInvitationView button,
.webInvitationView input,
.webInvitationView optgroup,
.webInvitationView select,
.webInvitationView textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  vertical-align: middle;
  color: inherit;
  font: inherit;
  border: 0;
  background: transparent;
  padding: 0;
  margin: 0;
  outline: 0;
  border-radius: 0;
  text-align: inherit;
}
.webInvitationView [type=checkbox] {
  -webkit-appearance: checkbox;
  -moz-appearance: checkbox;
       appearance: checkbox;
}
.webInvitationView [type=radio] {
  -webkit-appearance: radio;
  -moz-appearance: radio;
       appearance: radio;
}
.webInvitationView button,
.webInvitationView input {
  overflow: visible;
}
.webInvitationView button,
.webInvitationView select {
  text-transform: none;
}
.webInvitationView button,
.webInvitationView [type=button],
.webInvitationView [type=reset],
.webInvitationView [type=submit] {
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
}
.webInvitationView button[disabled],
.webInvitationView [type=button][disabled],
.webInvitationView [type=reset][disabled],
.webInvitationView [type=submit][disabled] {
  cursor: default;
}
.webInvitationView button::-moz-focus-inner,
.webInvitationView [type=button]::-moz-focus-inner,
.webInvitationView [type=reset]::-moz-focus-inner,
.webInvitationView [type=submit]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
.webInvitationView button:-moz-focusring,
.webInvitationView [type=button]:-moz-focusring,
.webInvitationView [type=reset]:-moz-focusring,
.webInvitationView [type=submit]:-moz-focusring {
  outline: 1px dotted ButtonText;
}
.webInvitationView select::-ms-expand {
  display: none;
}
.webInvitationView option {
  padding: 0;
}
.webInvitationView fieldset {
  margin: 0;
  padding: 0;
  border: 0;
  min-width: 0;
}
.webInvitationView legend {
  color: inherit;
  display: table;
  max-width: 100%;
  padding: 0;
  white-space: normal;
}
.webInvitationView progress {
  vertical-align: baseline;
}
.webInvitationView textarea {
  overflow: auto;
}
.webInvitationView [type=number]::-webkit-inner-spin-button,
.webInvitationView [type=number]::-webkit-outer-spin-button {
  height: auto;
}
.webInvitationView [type=search] {
  outline-offset: -2px;
}
.webInvitationView [type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}
.webInvitationView ::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}
.webInvitationView label[for] {
  cursor: pointer;
}
.webInvitationView details {
  display: block;
}
.webInvitationView summary {
  display: list-item;
}
.webInvitationView [contenteditable] {
  outline: none;
}
.webInvitationView table {
  border-collapse: collapse;
  border-spacing: 0;
}
.webInvitationView caption {
  text-align: left;
}
.webInvitationView td,
.webInvitationView th {
  vertical-align: top;
  padding: 0;
}
.webInvitationView th {
  text-align: left;
  font-weight: bold;
}
.webInvitationView [hidden] {
  display: none;
}
.webInvitationView .viewBlock_title,
.webInvitationView .guestAnswer_header_title {
  height: 119px;
  color: #493E04;
  text-align: center;
  font-family: "WindSong", cursive;
  font-size: 32px;
  font-weight: 500;
  letter-spacing: 3.2px;
  position: relative;
}
.webInvitationView .viewBlock_title span,
.webInvitationView .guestAnswer_header_title span {
  padding-top: 40px;
  display: block;
  color: #493E04;
  text-align: center;
  font-family: "WindSong", cursive;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 3.2px;
}
.webInvitationView .viewBlock_title_sub {
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  margin: 0 0 16px;
  background-size: auto 8px;
  background-position: center;
  background-repeat: repeat-x;
  position: relative;
}
.webInvitationView .viewBlock_title_sub::before {
  content: "";
  display: block;
  background: #9C9C9C;
  width: 100%;
  height: 1px;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
}
.webInvitationView .viewBlock_title_sub span {
  display: inline-block;
  background: #FFF;
  padding: 0 12px;
  position: relative;
  z-index: 1;
}
.webInvitationView .link {
  display: inline-block;
}
.webInvitationView .link.is-search {
  color: #767676;
  font-size: 14px;
  position: relative;
  text-decoration: none;
}
.webInvitationView .link.is-search::before {
  content: "";
  display: block;
  width: 24px;
  height: 24px;
  background-image: url("../images/webinvitation/theme_marble/icon-search.svg");
  background-size: contain;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}
.webInvitationView .btn.is-delete {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: #333;
  background: rgba(0, 0, 0, 0.1);
  font-size: 14px;
  position: relative;
  padding: 8px 28px;
  border-radius: 48px;
  transition: 0.35s ease-in-out;
  text-decoration: none;
}
.webInvitationView .btn.is-delete:hover {
  background: rgba(0, 0, 0, 0.15);
}
.webInvitationView .btn.is-delete::before {
  content: "";
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  background: currentColor;
  -webkit-mask-image: url("../images/webinvitation/theme_wa/icon-remove.svg");
          mask-image: url("../images/webinvitation/theme_wa/icon-remove.svg");
  -webkit-mask-size: contain;
          mask-size: contain;
}
.webInvitationView .btn.is-add {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #FFF;
  background: var(--marble-gold, #C29533);
  text-decoration: none;
  font-size: 14px;
  position: relative;
  margin: 0 20px 92px;
  padding: 8px 15px;
  border-radius: 8px;
  transition: 0.35s ease-in-out;
}
.webInvitationView .btn.is-add:hover {
  filter: brightness(1.1);
}
.webInvitationView .btn.is-add::before {
  content: "";
  display: inline-block;
  width: 28px;
  height: 28px;
  margin-right: 8px;
  background: #FFF;
  -webkit-mask-image: url("../images/webinvitation/theme_wa/icon-plus.svg");
          mask-image: url("../images/webinvitation/theme_wa/icon-plus.svg");
  -webkit-mask-size: contain;
          mask-size: contain;
}
.webInvitationView .btn.is-confirm {
  display: block;
  color: #FFF;
  background: var(--marble-gold, #C29533);
  font-size: 18px;
  position: relative;
  margin: 34px auto 24px;
  padding: 19px 15px;
  border-radius: 40px;
  max-width: 276px;
  width: 100%;
  text-align: center;
  transition: 0.35s ease-in-out;
  font-weight: 700;
  line-height: 18px;
  letter-spacing: 1.8px;
  text-decoration: none;
}
.webInvitationView .btn.is-confirm:hover {
  filter: brightness(1.1);
}
.webInvitationView .mainVisual {
  background-image: url("../images/webinvitation/theme_marble/bg_mv.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
  min-height: 100vh;
}
.webInvitationView .mainVisual.is-animated .mainVisual_box::before {
  -webkit-animation: fadeIn 0.5s ease-in-out;
          animation: fadeIn 0.5s ease-in-out;
}
.webInvitationView .mainVisual.is-animated .mainVisual_box::after {
  -webkit-animation: fadeIn 0.5s ease-in-out;
          animation: fadeIn 0.5s ease-in-out;
}
.webInvitationView .mainVisual::before {
  content: none;
}
.webInvitationView .mainVisual::after {
  background: linear-gradient(180deg, transparent 75.78%, #fff 100%);
}
.webInvitationView .mainVisual_first {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  max-width: 376px;
  background: #F7F7F7;
  z-index: 100;
  overflow: hidden;
  -webkit-animation: 4.3s mainVisualFirst forwards ease-in-out;
          animation: 4.3s mainVisualFirst forwards ease-in-out;
  pointer-events: none;
}
@media (max-width: 680px) {
  .webInvitationView .mainVisual_first {
    max-width: 100%;
  }
}
.webInvitationView .mainVisual_first_symbol {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
}
.webInvitationView .mainVisual_first_symbol_1 {
  -webkit-animation: 4s slideInToRight forwards ease-in-out;
          animation: 4s slideInToRight forwards ease-in-out;
}
.webInvitationView .mainVisual_first_symbol_1::before, .webInvitationView .mainVisual_first_symbol_1::after {
  content: "";
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  -webkit-animation: 2s fuwafuwa linear infinite;
          animation: 2s fuwafuwa linear infinite;
}
.webInvitationView .mainVisual_wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
}
.webInvitationView .mainVisual_wrap::before {
  content: "";
  display: block;
  width: 146px;
  height: 236px;
  position: absolute;
  top: 0;
  left: 0;
  background-image: url("../images/webinvitation/theme_marble/mv_symbol_1.png");
  background-size: contain;
  background-repeat: no-repeat;
  z-index: 100;
  -webkit-animation: slideInFromLeft 2s ease-in-out;
          animation: slideInFromLeft 2s ease-in-out;
}
.webInvitationView .mainVisual_wrap::after {
  content: "";
  display: block;
  width: 171px;
  height: 297px;
  position: absolute;
  right: 0;
  bottom: -25px;
  background-image: url("../images/webinvitation/theme_marble/mv_symbol_2.png");
  background-size: contain;
  background-repeat: no-repeat;
  z-index: 100;
  -webkit-animation: slideInFromRight 2s ease-in-out;
          animation: slideInFromRight 2s ease-in-out;
}
.webInvitationView .mainVisual_title {
  position: relative;
  padding: 20px 0 28px;
}
.webInvitationView .mainVisual_title::before {
  content: "";
  margin: 0 20px;
  display: block;
  aspect-ratio: 328/416;
  background-image: url("../images/webinvitation/theme_marble/mv_main.png");
  background-size: cover;
  background-repeat: no-repeat;
  -webkit-clip-path: polygon(30% 0%, 70% 0%, 100% 15%, 100% 85%, 70% 100%, 30% 100%, 0 85%, 0 15%);
          clip-path: polygon(30% 0%, 70% 0%, 100% 15%, 100% 85%, 70% 100%, 30% 100%, 0 85%, 0 15%);
}
.webInvitationView .mainVisual_title::after {
  content: "";
  display: block;
  width: calc(100% - 20px);
  height: auto;
  aspect-ratio: 354/438;
  background-image: url("../images/webinvitation/theme_marble/mv_img_symbol_1.png");
  background-size: cover;
  background-repeat: no-repeat;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.webInvitationView .mainVisual_title_text {
  width: 187px;
  height: 88px;
  position: absolute;
  top: -29px;
  right: 32px;
  background-image: url("../images/webinvitation/theme_marble/mv_title_symbol.png");
  background-size: cover;
  background-repeat: no-repeat;
}
.webInvitationView .mainVisual_box {
  padding: 0;
  width: 100%;
}
.webInvitationView .mainVisual_box .names {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 7px;
  margin-top: 0;
}
.webInvitationView .mainVisual_box .name {
  width: auto;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--marble-gold-text, #493E04);
  text-align: center;
  font-family: Montserrat;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 5.6px;
}
.webInvitationView .mainVisual_box .mainVisual_name_bride::after {
  content: "&";
  white-space: nowrap;
  display: flex;
  color: var(--marble-gold-text, #493E04);
  text-align: center;
  font-family: Montserrat;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 5.6px;
}
.webInvitationView .mainVisual_date {
  margin-top: 7px;
}
.webInvitationView .mainVisual_date span {
  color: var(--marble-gold-text, #493E04);
  text-align: center;
  font-family: "Montserrat", sans-serif;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.webInvitationView .countDown {
  background: var(--marble-gray-bg, #F7F7F7);
  padding: 24px 0;
  color: #493E04;
  position: relative;
}
.webInvitationView .countDown.is-animated::before {
  -webkit-animation: slideInFromLeft 0.5s ease-in-out;
          animation: slideInFromLeft 0.5s ease-in-out;
  opacity: 1;
}
.webInvitationView .countDown.is-animated::after {
  -webkit-animation: slideInFromRight 0.5s ease-in-out;
          animation: slideInFromRight 0.5s ease-in-out;
  opacity: 1;
}
.webInvitationView .countDown.is-animated .countDown_box {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .countDown::before {
  content: "";
  width: 112px;
  height: 186px;
  background-image: url("../images/webinvitation/theme_marble/countDown_symbol_1.png");
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: -57px;
  left: 0;
  z-index: 1;
  opacity: 0;
}
.webInvitationView .countDown::after {
  content: "";
  display: block;
  width: 157px;
  height: 186px;
  background-image: url("../images/webinvitation/theme_marble/countDown_symbol_2.png");
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  bottom: 10px;
  right: 0;
  opacity: 0;
}
.webInvitationView .countDown_wrap {
  display: flex;
  align-items: center;
  height: calc(100vw + 32px);
  max-height: 448px;
  background-image: url("../images/webinvitation/theme_marble/bg_marble.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  padding: 0;
}
.webInvitationView .countDown_title {
  height: 23px;
  color: var(--marble-gold-text, #493E04);
  text-align: center;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 23px;
  font-family: "WindSong", cursive;
}
.webInvitationView .countDown_details {
  margin-top: 16px;
}
.webInvitationView .countDown_block_days {
  height: 31px;
  margin-top: 40px;
}
.webInvitationView .countDown_block_value_large {
  font-family: "Montserrat", sans-serif;
  color: var(--marble-gold-text, #493E04);
  font-size: 44px;
  font-style: normal;
  font-weight: 300;
  line-height: 31px;
  letter-spacing: 4.4px;
}
.webInvitationView .countDown_block_value_small {
  color: var(--marble-gold-text, #493E04);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1.4px;
}
.webInvitationView .countDown_block_label {
  color: var(--marble-gold-text, #493E04);
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 31px;
  letter-spacing: 3.2px;
}
.webInvitationView .countDown_block_label_small {
  color: var(--marble-gold-text, #493E04);
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 2.2px;
}
.webInvitationView .countDown_blocks {
  position: relative;
}
.webInvitationView .countDown_blocks_details {
  margin-bottom: 59px;
}
.webInvitationView .countDown_date {
  color: var(--marble-gold-text, #493E04);
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 7.2px;
}
.webInvitationView .countDown_date_detail {
  color: var(--marble-gold-text, #493E04);
  text-align: center;
  font-family: Montserrat;
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 3.3px;
}
.webInvitationView .countDown_box {
  padding: 0;
  font-family: "Montserrat", sans-serif;
  opacity: 0;
}
.webInvitationView .countDown_symbol_2 {
  content: "";
  display: block;
  width: 100%;
  height: 23px;
  text-align: center;
  position: absolute;
  top: calc(100% + 16px);
  left: 50%;
  transform: translateX(-50%);
  background-image: url("../images/webinvitation/theme_marble/countDown_symbol_wedding.png");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  color: var(--marble-gold-text, #493E04);
  font-family: WindSong;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 0.64px;
}
.webInvitationView .message {
  background-image: url("../images/webinvitation/theme_marble/bg_message.jpg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-bottom: 48px;
}
.webInvitationView .message.is-animated .message_title {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .message.is-animated .message_contents {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .message_title {
  opacity: 0;
}
.webInvitationView .message_contents {
  color: var(--marble-gold-text, #493E04);
  text-align: center;
  font-family: "Noto Serif JP", serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 180%;
  padding-bottom: 39px;
  opacity: 0;
}
.webInvitationView .message_slides {
  margin: 0 20px;
  opacity: 0;
}
.webInvitationView .message_slides img {
  width: 100%;
  height: 100%;
  aspect-ratio: 335/260;
  -o-object-fit: cover;
     object-fit: cover;
}
.webInvitationView .message_slides.is-animated {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .profile {
  background: var(--marble-gray-bg, #F7F7F7);
  padding: 24px 16px 12px;
}
.webInvitationView .profile.is-animated .profile_title {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .profile_title {
  opacity: 0;
}
.webInvitationView .profile_wrap {
  background: #fff;
  padding-bottom: 64px;
}
.webInvitationView .profile_items {
  display: flex;
  flex-direction: column;
  gap: 80px;
}
.webInvitationView .profile_item {
  margin-bottom: 0;
  position: relative;
  opacity: 0;
}
.webInvitationView .profile_item.is-animated {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .profile_item::before {
  content: "";
  display: block;
  width: 267px;
  height: 287px;
  position: absolute;
  top: 89px;
  background-size: contain;
  background-repeat: no-repeat;
}
.webInvitationView .profile_item:nth-of-type(odd)::before {
  left: -16px;
  background-image: url("../images/webinvitation/theme_marble/profile_symbol_2_left.png");
}
.webInvitationView .profile_item:nth-of-type(even)::before {
  right: -16px;
  background-image: url("../images/webinvitation/theme_marble/profile_symbol_2_right.png");
}
.webInvitationView .profile_item_image {
  margin-top: 0;
  margin-bottom: 32px;
  position: relative;
}
.webInvitationView .profile_item_image::before {
  content: "";
  display: block;
  width: 274px;
  height: 274px;
  background-image: url("../images/webinvitation/theme_marble/profile_symbol_1.png");
  background-size: contain;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}
.webInvitationView .profile_item_image img {
  width: 280px;
  height: 280px;
  -o-object-fit: cover;
     object-fit: cover;
  -webkit-clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
          clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}
.webInvitationView .profile_item_name {
  margin-bottom: 0;
}
.webInvitationView .profile_item_name_position {
  color: var(--marble-gold-text, #493E04);
  text-align: center;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 3.5px;
}
.webInvitationView .profile_item_name_main {
  color: var(--marble-gold-text, #493E04);
  text-align: center;
  font-family: "Noto Serif JP", serif;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 5px;
  margin-top: 8px;
}
.webInvitationView .profile_item_text {
  color: var(--marble-gold-text, #493E04);
  font-family: "Noto Serif JP", serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  letter-spacing: 0.7px;
  margin-top: 16px;
}
.webInvitationView .gallery {
  background: var(--marble-gray-bg, #F7F7F7);
  opacity: 0;
}
.webInvitationView .gallery.is-animated {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .gallery_title span {
  background: transparent;
}
.webInvitationView .swiper-button-prev,
.webInvitationView .swiper-rtl .swiper-button-next {
  left: 8px;
}
.webInvitationView .swiper-button-next,
.webInvitationView .swiper-rtl .swiper-button-prev {
  right: 8px;
}
.webInvitationView .swiper-slide-thumb-active img {
  box-sizing: border-box;
  border: 1px solid var(--marble-gold-text, #493E04);
}
.webInvitationView .swiper-button-next,
.webInvitationView .swiper-button-prev {
  width: 40px;
  height: 40px;
}
.webInvitationView .swiper-button-next::after,
.webInvitationView .swiper-button-prev::after {
  content: "";
  display: block;
  width: 40px;
  height: 40px;
}
.webInvitationView .swiper-button-prev::after,
.webInvitationView :host(.swiper-rtl) .swiper-button-next::after {
  content: "";
  background-image: url("../images/webinvitation/theme_wa/icon_slide_arrow_left.png");
  background-size: cover;
}
.webInvitationView .swiper-button-next::after {
  background-image: url("../images/webinvitation/theme_wa/icon_slide_arrow_right.png");
  background-size: cover;
}
.webInvitationView .swiper-horizontal > .swiper-pagination-bullets,
.webInvitationView .swiper-pagination-bullets.swiper-pagination-horizontal,
.webInvitationView .swiper-pagination-custom,
.webInvitationView .swiper-pagination-fraction {
  position: static;
}
.webInvitationView .swiper-pagination-bullet-active {
  background: var(--marble-gold-text, #493E04);
}
.webInvitationView .information {
  padding: 24px 16px 12px;
  background: var(--marble-gray-bg, #F7F7F7);
}
.webInvitationView .information_box.is-animated .information_title {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .information_wrap {
  background: #fff;
  padding-bottom: 12px;
}
.webInvitationView .information_title {
  margin-bottom: 0;
  opacity: 0;
}
.webInvitationView .information_title_sub::before {
  background: #C29533;
}
.webInvitationView .information_title_sub span {
  color: #493E04;
}
.webInvitationView .information_block {
  padding: 16px 20px 12px 20px;
  border-radius: 4px;
  border: 1px solid var(--marble-gray2, #E0E0E0);
  opacity: 0;
}
.webInvitationView .information_block.is-animated {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .information_block_detail {
  border: none;
  background: var(--marble-gray-bg, #F7F7F7);
  padding: 20px;
}
.webInvitationView .information_block_detail .information_title_sub span {
  background: var(--marble-gray-bg, #F7F7F7);
}
.webInvitationView .information_block_address_link_url {
  color: var(--marble-gold-text, #493E04);
  text-decoration: underline;
}
.webInvitationView .information_date {
  opacity: 0;
}
.webInvitationView .information_date.is-animated {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .freeField_contents:last-of-type {
  padding-bottom: 0;
}
.webInvitationView .freeField a {
  color: var(--marble-gold-text, #493E04);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 240%;
  /* 33.6px */
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.webInvitationView .guestAnswer {
  background: var(--marble-gray1, #F0F0F0);
  padding: 20px 16px;
}
.webInvitationView .guestAnswer_header {
  opacity: 0;
}
.webInvitationView .guestAnswer_header.is-animated {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .guestAnswer_header_title {
  height: 87px;
}
.webInvitationView .guestAnswer_header_sub_title {
  color: var(--black-222, #222);
  text-align: center;
  font-family: "Noto Serif JP", serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: 1.28px;
  margin-top: 12px;
}
.webInvitationView .guestAnswer_header_text {
  color: var(--marble-gold-text, #493E04);
  text-align: center;
  font-family: "Noto Serif JP", serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 30px;
  margin-top: 32px;
}
.webInvitationView .guestAnswer_header_text_limit {
  font-size: 20px;
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.webInvitationView .guestAnswer_header_caution {
  border: 1px solid var(--marble-gray2, #E0E0E0);
  background: var(--white-fff-20, rgba(255, 255, 255, 0.2));
  color: #222;
  text-align: center;
  font-family: "Noto Serif JP", serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  letter-spacing: 0.28px;
  padding: 8px 0;
  margin-top: 10px;
}
.webInvitationView .guestAnswer_title_sub {
  margin-bottom: 58px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}
.webInvitationView .guestAnswer_title_sub span {
  color: var(---font, #222);
  text-align: center;
  font-family: "Noto Sans JP";
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  /* 100% */
  letter-spacing: 1.8px;
}
.webInvitationView .guestAnswer_title_sub::before, .webInvitationView .guestAnswer_title_sub::after {
  content: "";
  display: block;
  width: 18px;
  height: 1px;
  background: #9C9C9C;
}
.webInvitationView .guestAnswer_answer {
  margin-top: 40px;
}
.webInvitationView .guestAnswer_answer_box {
  opacity: 0;
}
.webInvitationView .guestAnswer_answer_box.is-animated {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .guestAnswer_answer_box .information_title_sub span {
  background: var(--marble-gray1, #F0F0F0);
  color: var(--black-333, var(--text-black, #333));
}
.webInvitationView .guestAnswer_answer_box .information_title_sub::before {
  background: #9C9C9C;
}
.webInvitationView .guestAnswer_answer_box_flex {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}
.webInvitationView .guestAnswer_answer_box_flex:last-of-type {
  margin-bottom: 20px;
}
.webInvitationView .guestAnswer_answer_box_radio input:checked + .guestAnswer_answer_box_radio_wrap span {
  color: var(--black-000, #000);
}
.webInvitationView .guestAnswer_answer_box_radio input:checked + .guestAnswer_answer_box_radio_wrap::before {
  border: none;
  background-image: url("../images/webinvitation/theme_marble/img_attend_bg.png");
  background-color: transparent;
  background-size: contain;
  background-repeat: no-repeat;
  transform: rotate(0deg);
}
.webInvitationView .guestAnswer_answer_box_radio_wrap {
  background: transparent;
}
.webInvitationView .guestAnswer_answer_box_radio_wrap:hover span {
  color: var(--black-000, #000);
}
.webInvitationView .guestAnswer_answer_box_radio_wrap:hover::before {
  width: 100%;
  height: 100%;
  border: none;
  background-image: url("../images/webinvitation/theme_marble/img_attend_bg.png");
  background-color: transparent;
  background-size: contain;
  background-repeat: no-repeat;
  transform: rotate(0deg);
  margin: 0;
}
.webInvitationView .guestAnswer_answer_box_radio_wrap::before {
  width: 100%;
  height: 100%;
  transform: rotate(-10deg);
  background: none;
  border: none;
}
.webInvitationView .guestAnswer_answer_box_radio_wrap span {
  color: var(--black-000-40, rgba(0, 0, 0, 0.4));
}
.webInvitationView .guestAnswer_answer_box_radio_title {
  color: var(--black-000, #000);
  text-align: center;
  text-shadow: 0px 0px 10px rgba(255, 255, 255, 0.8);
  font-family: "Noto Serif JP", serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  position: relative;
  z-index: 1;
}
.webInvitationView .guestAnswer_answer_box_radio_sub {
  color: var(--black-000, #000);
  text-align: center;
  text-shadow: 0px 0px 10px rgba(255, 255, 255, 0.8);
  font-family: "Noto Serif JP", serif;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  z-index: 1;
}
.webInvitationView .guestAnswer_box {
  margin-top: 44px;
  padding: 0 0 30px;
  background: var(--white-fff-60, rgba(255, 255, 255, 0.6));
  opacity: 0;
}
.webInvitationView .guestAnswer_box.is-animated {
  -webkit-animation: slideIn 0.8s ease-in-out;
          animation: slideIn 0.8s ease-in-out;
  opacity: 1;
}
.webInvitationView .guestAnswer_box_wrap {
  padding: 34px 20px;
}
.webInvitationView .guestAnswer_box hr {
  border-top: 1px solid var(--Gray, #D9D9D9);
}
.webInvitationView .guestAnswer_box hr:first-of-type {
  margin-top: 34px;
}
.webInvitationView .guestAnswer_box_row:last-of-type {
  margin-bottom: 0;
}
.webInvitationView .guestAnswer_box_row_text {
  color: var(---font, #222);
  font-family: "Noto Sans JP";
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  /* 153.846% */
  letter-spacing: 0.78px;
}
.webInvitationView .guestAnswer_box_input input {
  background: #fff;
  border: 1px solid var(--Gray, #D9D9D9);
}
.webInvitationView .guestAnswer_box_radio input:checked + .guestAnswer_box_radio_wrap {
  border: 2px solid var(--marble-gold, #C29533);
}
.webInvitationView .guestAnswer_box_select select {
  background: #fff;
  border: 1px solid var(--Gray, #D9D9D9);
}
.webInvitationView .guestAnswer_box_textarea textarea {
  background: #fff;
  border: 1px solid var(--Gray, #D9D9D9);
  padding: 16px;
}
.webInvitationView .guestAnswer_box_checkbox input:checked + .guestAnswer_box_checkbox_wrap::before {
  background: var(--marble-gold, #C29533);
  border: var(--marble-gold, #C29533);
}
.webInvitationView .guestAnswer_box_button_zip {
  background: var(--marble-gold, #C29533);
}
.webInvitationView .guestAnswer_box_file {
  width: 100%;
}
.webInvitationView .guestAnswer_box_file input {
  display: none;
}
.webInvitationView .guestAnswer_box_file_button:hover {
  background: rgba(0, 0, 0, 0.1);
}
.webInvitationView .guestAnswer_box_file_button::before {
  background-image: url("../images/webinvitation/theme_wa/icon-plus.svg");
}

.header_button span {
  background: #222;
  height: 2px;
}

.footer {
  padding-top: 0;
}
.footer .logo {
  margin-top: 38px;
}
.footer_nav ul {
  margin: 0 auto;
  justify-content: center;
}
.footer_nav ul li a {
  color: #222;
  text-decoration: none;
}
/*# sourceMappingURL=style.css.map */