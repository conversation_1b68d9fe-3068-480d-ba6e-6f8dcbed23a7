
.webInvitationView{
  white-space: pre-wrap;
  &_navigation{
    position: sticky;
    top: 0;
    z-index: 1003;
    width: 100%;
    @include sp {
      position: fixed;
    }
    ~ .blocks {
      position: relative;
      z-index: 1002;
    }
    &_button{
      display: block;
      position: absolute;
      z-index: 11;
      top: 20px;
      right: 15px;
      width: 34px;
      height: 34px;
      cursor: pointer;
      .line{
        width: 24px;
        height: 2px;
        background: #FFF;
        position: absolute;
        margin: auto;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        transition: 0.35s;
        &:nth-child(1){
          top: -20px;
        }
        &:nth-child(3){
          bottom: -20px;
        }
      }
    }
    nav{
      position: absolute;
      z-index: 9;
      color: #FFF;
      background: rgba(0,0,0,0.84);
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      opacity: 0;
      pointer-events: none;
      transition: 0.35s;
      height: 100vh;
      height: 100dvh;
      min-height: 420px;
      .webInvitationView.is-sp &{
        height: 680px;
      }
      @include pc {
        height: 680px;
      }
      li + li{
        margin-top: 24px;
      }
    }
    &.is-show{
      .webInvitationView_navigation_button{
        .line{
          &:nth-child(1){
            top: 0;
            transform: rotate(45deg);
          }
          &:nth-child(2){
            transform: rotateY(95deg);
          }
          &:nth-child(3){
            bottom: 0;
            transform: rotate(-45deg);
          }
        }
      }
      nav{
        opacity: 1;
        pointer-events: all;
      }
    }
  }
  &_flex{
    width: 100%;
    height: 100vh;
    height: 100dvh;
    display: flex;
    justify-content: center;
    align-items: center;
    .webInvitationView.is-sp &{
      height: auto;
    }
    @include sp {
      align-items: flex-start;
      height: auto;
    }
    &_wrap{
      max-width: 848px;
      width: 100%;
      height: 100%;
      max-height: 680px;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      .webInvitationView.is-sp &{
        display: block;
      }
      @include sp {
        align-items: flex-start;
        height: auto;
        max-height: none;
      }
      &_scroll{
        color: #333;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        font-size: 10px;
        padding-right: 72px;
        position: absolute;
        right: -30px;
        bottom: -6px;
        z-index: 1;
        transform: rotate(90deg);
        transform-origin: right top;
        .webInvitationView.is-sp &{
          display: none;
        }
        @include sp {
          display: none;
        }
        &::before{
          content: '';
          display: block;
          width: 64px;
          height: 1px;
          background: currentColor;
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          margin: auto;
        }
      }
    }
    &_content{
      width: calc(100% - 375px);
      margin: 0 50px;
      .webInvitationView.is-sp &{
        display: none;
      }
      @include sp {
        display: none;
      }
    }
  }
  &_pc{
    &_wrap{
      width: 100%;
      padding-right: 118px;
      .webInvitationView.is-sp &{
        display: none;
      }
      @include sp {
        display: none;
      }
    }
    &_box{
      max-width: 356px;
      width: 100%;
      padding: 45px 20px;
      text-align: center;
      background: rgba(0,0,0,0.24);
      & + .webInvitationView_pc_box{
        margin-top: 10px;
      }
      &_name{
        font-size: 26px;
        margin-bottom: 12px;
      }
      &_date{
        font-size: 42px;
        margin-bottom: 12px;
        &_detail{
          font-size: 18px;
        }
      }
      &_countDown{
        margin-top: 24px;
        padding-top: 24px;
        border-top: 1px solid currentColor;
        &_blocks{
          &_details{
            display: flex;
            justify-content: space-around;
            align-items: center;
          }
        }
        &_block{
          margin: 0 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          &_value{
            line-height: 1;
            font-size: 36px;
          }
          &_label{
            line-height: 1;
            font-weight: 500;
            font-size: 12px;
            margin-top: 6px;
            text-transform: uppercase;
          }
        }
      }
      &_text{
        font-size: 14px;
        line-height: 1.5;
        &_limit{
          font-size: 20px;
          font-weight: bold;
          text-decoration: underline;
        }
      }
      &_button{
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 24px auto 0;
        border: 1px solid currentColor;
        max-width: 190px;
        width: 100%;
        min-height: 50px;
      }
    }
  }
  &_sp_wrap{
    max-width: 375px;
    max-height: 680px;
    width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    box-shadow: 0 0 10px rgba(0,0,0,0.4);
    border-radius: 14px;
    .webInvitationView.is-sp &{
      max-width: 100%;
      max-height: 680px;
      min-height: 680px;
      box-shadow: none;
      border-radius: 0;
      @include sp {
        max-height: 100vh;
        max-height: 100dvh;
      }
    }
    @include sp {
      max-width: 100%;
      max-height: none;
      box-shadow: none;
      border-radius: 0;
      overflow-y: unset;
    }
  }
  .footer{
    background: #FFF8ED;
    padding-top: 40px;
    &_wrap{
      background: #FFF;
      text-align: center;
      padding-bottom: 20px;
      .logo{
        margin-top: 36px;
        display: inline-block;
        margin-bottom: 16px;
        img{
          width: 80px;
        }
      }
      a{
        text-decoration: none;
      }
      ul{
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        max-width: 310px;
        width: 100%;
        margin: 0 auto;
        a{
          display: inline-block;
          font-size: 12px;
          padding: 4px 17px;
          margin: 2px 0;
          &:hover{
            text-decoration: underline;
          }
        }
      }
    }
  }
  &.is-sp{
    min-height: 680px;
    position: relative;
    z-index: 10;
  }
  @include sp {
    h1 {
      display: none;
    }
  }
}


.viewBlock.guestAnswer {
  position: relative;
  z-index: 1000;
  .inputAttendance .inputTitle {
    color: var(--white-100, var(--white-100, #FFF));
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    overflow: hidden;
    span{
      position: relative;
      &:before ,
      &:after {
        content: " ";
        height: 1px;
        width: 100vw;
        background: #fff;
        display: block;
        position: absolute;
        top: 50%;
        left: calc(100% + 10px);
        right: auto;
      }
      &:after {
        left: auto;
        right: calc(100% + 10px);
      }
    }
  }
  .inputAttendance .inputRadio {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-around;
    position: relative;
    font-weight: bold;
    .input-error {
      position: absolute;
      bottom: 0;
      left: 0;
      color: #fff;
    }
    label {
      display: block;
      width: 27.33%;
      position: relative;
      cursor: default;
      margin: 0;
      &:after {
        content: " ";
        padding-top: 100%;
        height: 0;
        display: none;
      }
      &:last-child {
        margin-right: 0;
      }
      > span {
        cursor: pointer;
        display: block;
        width: 100%;
        position: relative;
        text-align: center;
        margin: 0;
        padding: 0;
        font-size: 16px;
        background: none;
        color: #fff;
        border-radius: 50%;
        border: 6px solid rgba(#fff, 0);
        transition: 0.3s ease;
        &:before {
          display: none;
          width: 0;
          height: 0;
          height: 0;
        }
        &:after {
          content: " ";
          display: block;
          width: 100%;
          padding-top: 100%;
          height: 0;
        }
        .txt {
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          text-align: center;
          transform: translateY(-50%);
          .ja {
            font-size: 18px;
            display: block;
          }
          .en {
            display: block;
            font-size: 12px;
            margin-top: 5px;
          }
        }
      }
      // &:hover > span ,
      input:checked + span {
        position: static;
        border-color: #fff;
        background: rgba(#fff, .2);
        &:after {
          display: block;
          background: none;
          position: static;
        }
      }
    }
  }

  .blockGuests {
    margin-bottom: 34px;
    padding-bottom: 0;
    border-bottom: 1px solid var(--Gray, #D9D9D9);
    .blockTitle {
      color: var(---font, #222);
      text-align: center;
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      letter-spacing: 1.8px;
      margin-bottom: 40px;
      &:before ,
      &:after {
        content: " ";
        display: inline-block;
        width: 18px;
        height: 1px;
        vertical-align: middle;
        margin: 0 10px;
        background: var(--Gray_dark, #9C9C9C);
      }
    }
  }

  .inputRadioBtn .inputRadio {
    font-size: 0;
    label {
      display: inline-block;
      width: 30.33%;
      margin-right: 3%;
      cursor: default;
      &:last-child {
        margin-right: 0;
      }
      > span {
        display: block;
        width: 100%;
        text-align: center;
        margin: 0;
        padding: 0;
        background: #000;
        border-radius: 8px;
        padding: 12px 0 10px;
        font-size: 16px;
        background: #f4f4f4;
        border: 2px solid #f4f4f4;
        cursor: pointer;
        &:before {
          display: none;
          width: 0;
          height: 0;
        }
      }
      input:checked + span {
        border-color: #EE645E;
        background: #fff;
        &:after {
          display: none;
        }
      }
      input:disabled + span {
        opacity: 1;
      }
    }
  }
  .inputRadioBtn.inputGuestType .inputRadio {
    label {
      width: 47%;
    }
  }

  .guestAnswer_box_label {
    &[data-required="true"] {
      &:after {
        content: "*";
        margin-left: 5px;
        vertical-align: top;
        font-size: 12px;
        color: #E65C7A;
      }
    }
  }

  .guestAnswer_box_input input, 
  .guestAnswer_box_input textarea,
  .guestAnswer_box .selectWrap select {
    background: #fff;
    width: 100%;
    padding: 18px 16px;
    font-size: 16px;
    line-height: 1;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    height: 56px;
  }
  .guestAnswer_box .selectWrap select {
    padding-top: 0;
    padding-bottom: 0;
    line-height: 1.6;
  }
  .guestAnswer_box_input textarea {
    height: 5em;
    min-height: 0;
  }
  .guestAnswer_box .selectWrap:before {
    bottom: 50%;
    margin-bottom: -3px;
  }

  .inputImages {
    white-space: nowrap;
    overflow-x: auto;
    margin-bottom: 10px;
  }
  .inputImages .inputRadio {
    .img {
      display: block;
      background-position: center center;
      background-repeat: no-repeat;
      background-size: cover;
      width: 104px;
      // height: 104px;
      margin-bottom: 2px;
      border: 1px solid var(--Gray_light, #F4F4F4);
    }
    label {
      display: inline-block;
      cursor: default;
      > span {
        cursor: pointer;
        margin: 0;
        padding: 0;
        &:before {
          display: none;
        }
      }
      // &:hover > span ,
      input:checked + span {
        &:after {
          display: none;
        }
        .img {
          position: relative;
          &:after {
            content: " ";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 4px solid #EE645E;
          }
        }
      }
    }
  }

  .inputRadio label input:disabled + span {
    opacity: 1;
  }

  .inputDate {
    display: flex;
    align-items: flex-end;
    .unit:last-child {
      margin-right: 0;
    }
  }

  .inputAllergy {
    // border-color: var(--primary-color, #333);
    color: var(--text-color, #333);
    border: none;
    position: relative;
    &:after {
      content: " ";
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid var(--primary-color, #aaaaaa);
      border-radius: 5px;
      opacity: .6;
      z-index: 1;
    }
    > * {
      position: relative;
      z-index: 2;
    }
    .boxAllergyCategoryTitle {
      color: var(--primary-color, #333);
    }
    .boxAllergySubCategoryTitle {
      color: var(--text-color, #333);
      font-weight: 700;
    }
  }
  .inputCheckbox {
    label input:checked + span {
      &:before {
        background: var(--primary-color, #2f587c);
        border-color: var(--primary-color, #2f587c);
      }
    }
  }
}
.webInvitationView .btn.is-confirm:hover {
  &:disabled {
    filter: none !important;
  }
}


// MEMO: WEB招待状 [洋風] 用のカスタマイズ
// .viewBlock.guestAnswer .blockGuests .blockTitle ,
// .viewBlock.guestAnswer .guestAnswer_box_input .title ,
// .viewBlock.guestAnswer .guestAnswer_box_label ,
// .viewBlock.guestAnswer .inputRadio label > span {
//   color: #fff;
// }
// .viewBlock.guestAnswer .blockGuests .blockTitle:before, 
// .viewBlock.guestAnswer .blockGuests .blockTitle:after {
//   background: #fff;
// }
// .viewBlock.guestAnswer .inputRadioBtn .inputRadio label > span {
//   background: rgba(255, 255, 255, .2);
//   color: #fff;
// }
// .viewBlock.guestAnswer .inputRadioBtn .inputRadio label input:checked + span {
//   border: 2px solid rgba(255, 255, 255, .2);
//   background: #ac8149;
// }
// .viewBlock.guestAnswer .inputRadio label input:checked + span::after {
//   background: #ac8149;
// }
// .viewBlock.guestAnswer .inputRadio label input:checked + span::before {
//   border-color: #ac8149;
// }
// .viewBlock.guestAnswer .inputDate .unit {
//   color: #fff;
// }
// .viewBlock.guestAnswer .guestAnswer_box_input input, 
// .viewBlock.guestAnswer .guestAnswer_box_input textarea, 
// .viewBlock.guestAnswer .guestAnswer_box .selectWrap select {
//   border-color: #9c9c9c;
// }

// MEMO: WEB招待状 [ボタニカル] 用のカスタマイズ
// .viewBlock.guestAnswer .inputAttendance .inputTitle:before, 
// .viewBlock.guestAnswer .inputAttendance .inputTitle:after {
//   background: #333;
// }
// .viewBlock.guestAnswer .inputAttendance .inputTitle ,
// .viewBlock.guestAnswer .inputAttendance .inputRadio label > span {
//   color: #333;
// }
// .viewBlock.guestAnswer .inputRadio label input:checked + span::after {
//   background: #8E7EAC;
// }
// .viewBlock.guestAnswer .inputAttendance .inputRadio label input:checked + span ,
// .viewBlock.guestAnswer .inputRadioBtn .inputRadio label input:checked + span ,
// .viewBlock.guestAnswer .inputImages .inputRadio label input:checked + span .img:after ,
// .viewBlock.guestAnswer .inputRadio label input:checked + span::before {
//   border-color: #8E7EAC;
// }

// 内部要素の fixed(モーダル) が使えないので、修正しました。
.webInvitationView .guestAnswer_box.is-animated {
  animation: none !important;
  opacity: 1 !important;
  transform: none !important;
}
.webInvitationView .guestAnswer_box.is-animated .guestAnswer_box_wrap {
  animation: 1s 0s slideInToTop2 forwards ease-in-out !important;
  position: relative !important;
}
@keyframes slideInToTop2 {
  0% {
    opacity: 0;
    // margin-top: -20px;
    top: 20px;
    // transform: translateY(20px);
  }
  100% {
    top: 0;
    // transform: translateY(0);
    opacity: 1;
  }
}
