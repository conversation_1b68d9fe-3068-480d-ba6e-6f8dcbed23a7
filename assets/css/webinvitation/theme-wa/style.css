@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=Noto+Serif+JP:wght@200;300;400;500;600;700;900&display=swap");
@keyframes passBar {
  0% {
    left: 0%;
    width: 0%;
  }
  50% {
    left: 0%;
    width: 100%;
  }
  51% {
    left: 0%;
    width: 100%;
  }
  100% {
    left: 100%;
    width: 0%;
  }
}
@keyframes passText {
  0% {
    visibility: hidden;
  }
  50% {
    visibility: hidden;
  }
  100% {
    visibility: visible;
  }
}
[data-animation="passBarWhite"], [data-animation="passBarBlack"], [data-animation="passBarBrown"] {
  position: relative;
  visibility: hidden;
  transform: translate3d(0, 0, 0);
}
[data-animation="passBarWhite"]::before, [data-animation="passBarBlack"]::before, [data-animation="passBarBrown"]::before {
  content: '';
  display: inline-block;
  width: 0%;
  height: 100%;
  position: absolute;
  left: 0%;
  top: 0;
  bottom: 0;
  opacity: 1;
  visibility: visible;
  z-index: 1;
  transform: translate3d(0, 0, 0);
}
[data-animation="passBarWhite"].is-animated, [data-animation="passBarBlack"].is-animated, [data-animation="passBarBrown"].is-animated {
  animation: passText 0s ease .5s 1 normal forwards;
}
[data-animation="passBarWhite"].is-animated::before, [data-animation="passBarBlack"].is-animated::before, [data-animation="passBarBrown"].is-animated::before {
  animation: passBar 1s ease 0s 1 normal forwards;
}
[data-animation="passBarWhite"]::before {
  background: #FFF;
}
[data-animation="passBarBlack"]::before {
  background: #333;
}
[data-animation="passBarBrown"]::before {
  background: #A39C94;
}
[data-animation="sway"] {
  animation: sway 3s infinite;
}
@keyframes sway {
  0% {
    transform: rotate(0);
  }
  3% {
    transform: rotate(-5deg);
  }
  6% {
    transform: rotate(5deg);
  }
  9% {
    transform: rotate(-5deg);
  }
  12% {
    transform: rotate(5deg);
  }
  15% {
    transform: rotate(-5deg);
  }
  18% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(0);
  }
}
[data-animation="fadeIn"] {
  opacity: 0;
}
[data-animation="fadeIn"].is-animated {
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
[data-animation="slideIn"] {
  opacity: 0;
  transform: translateY(20px);
}
[data-animation="slideIn"].is-animated {
  animation: slideIn 0.5s ease-in-out;
  animation-fill-mode: both;
}
@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}
[data-animation="slideInFromLeft"] {
  opacity: 0;
  transform: translateX(-20px);
}
@media screen and (max-width: 767px) {
  [data-animation="slideInFromLeft"] {
    transform: translateX(20px);
  }
}
[data-animation="slideInFromLeft"].is-animated {
  animation: slideInFromLeft 0.5s ease-in-out;
  animation-fill-mode: both;
}
@media screen and (max-width: 767px) {
  [data-animation="slideInFromLeft"].is-animated {
    opacity: 1;
    transform: translateX(0);
    animation: slideInFromRight 0.5s ease-in-out;
  }
}
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInToLeft {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
[data-animation="slideInFromRight"] {
  opacity: 0;
  transform: translateX(20px);
}
[data-animation="slideInFromRight"].is-animated {
  animation: slideInFromRight 0.5s ease-in-out;
  animation-fill-mode: both;
}
@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInToRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
[data-animation="label"] {
  opacity: 0;
}
[data-animation="label"].is-animated {
  animation: label 0.35s ease-in-out;
  animation-fill-mode: both;
}
@keyframes label {
  0% {
    opacity: 0;
    transform: rotate(-1.5deg) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}
[data-animation="slideInGroup"] > * {
  opacity: 0;
  transform: translateY(20px);
}
[data-animation="slideInGroup"].is-animated > * {
  animation: slideIn 0.35s ease-in-out;
  animation-fill-mode: both;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(1) {
  animation-delay: 0.25s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(2) {
  animation-delay: 0.35s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(3) {
  animation-delay: 0.45s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(4) {
  animation-delay: 0.55s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(5) {
  animation-delay: 0.65s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(6) {
  animation-delay: 0.75s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(7) {
  animation-delay: 0.85s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(8) {
  animation-delay: 0.95s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(9) {
  animation-delay: 1.05s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(10) {
  animation-delay: 1.15s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(11) {
  animation-delay: 1.25s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(12) {
  animation-delay: 1.35s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(13) {
  animation-delay: 1.45s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(14) {
  animation-delay: 1.55s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(15) {
  animation-delay: 1.65s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(16) {
  animation-delay: 1.75s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(17) {
  animation-delay: 1.85s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(18) {
  animation-delay: 1.95s;
}
[data-animation="slideInGroup"].is-animated > *:nth-of-type(19) {
  animation-delay: 2.05s;
}
[data-animation="slideInGroupSmall"] > * {
  opacity: 0;
  transform: translateY(20px);
}
[data-animation="slideInGroupSmall"].is-animated > * {
  animation: slideIn 0.25s ease-in-out;
  animation-fill-mode: both;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(1) {
  animation-delay: 0.2s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(2) {
  animation-delay: 0.25s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(3) {
  animation-delay: 0.3s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(4) {
  animation-delay: 0.35s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(5) {
  animation-delay: 0.4s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(6) {
  animation-delay: 0.45s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(7) {
  animation-delay: 0.5s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(8) {
  animation-delay: 0.55s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(9) {
  animation-delay: 0.6s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(10) {
  animation-delay: 0.65s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(11) {
  animation-delay: 0.7s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(12) {
  animation-delay: 0.75s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(13) {
  animation-delay: 0.8s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(14) {
  animation-delay: 0.85s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(15) {
  animation-delay: 0.9s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(16) {
  animation-delay: 0.95s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(17) {
  animation-delay: 1s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(18) {
  animation-delay: 1.05s;
}
[data-animation="slideInGroupSmall"].is-animated > *:nth-of-type(19) {
  animation-delay: 1.1s;
}
@keyframes fuwafuwa {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
  100% {
    transform: translateY(0px);
  }
}
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInToLeft {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInToRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
@keyframes profileBackground {
  0% {
    opacity: 0;
    rotate: -10deg;
    transform: translateX(-40px);
  }
  100% {
    opacity: 1;
    rotate: 0deg;
    transform: translateX(0);
  }
}
@keyframes mainVisualBackground {
  0% {
    background-size: 400px;
  }
  100% {
    background-size: 376px;
  }
}
@keyframes mainVisualBackgroundSp {
  0% {
    background-size: 120%;
  }
  100% {
    background-size: 100%;
  }
}
@keyframes slideInToLeft {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }
  80% {
    opacity: 1;
    transform: translateX(0px);
  }
  100% {
    opacity: 0;
    transform: translateX(150%);
  }
}
@keyframes slideInToRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  80% {
    opacity: 1;
    transform: translateX(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-150%);
  }
}
@keyframes mainVisualFirst {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes profileBackground {
  0% {
    opacity: 0;
    rotate: -10deg;
    transform: translateX(-40px);
  }
  100% {
    opacity: 1;
    rotate: 0deg;
    transform: translateX(0);
  }
}
@keyframes fuwafuwa {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
  100% {
    transform: translateY(0px);
  }
}
span.lang-ja {
  display: inline-block !important;
}
span.lang-en {
  display: none !important;
}
.webInvitationView {
  font-family: 'Noto Serif JP', serif;
  color: #333;
}
.webInvitationView * {
  box-sizing: border-box;
}
.webInvitationView ::before, .webInvitationView ::after {
  box-sizing: inherit;
}
.webInvitationView p, .webInvitationView table, .webInvitationView blockquote, .webInvitationView address, .webInvitationView pre, .webInvitationView iframe, .webInvitationView form, .webInvitationView figure, .webInvitationView dl {
  margin: 0;
}
.webInvitationView h1, .webInvitationView h2, .webInvitationView h3, .webInvitationView h4, .webInvitationView h5, .webInvitationView h6 {
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
  margin: 0;
}
.webInvitationView ul, .webInvitationView ol {
  margin: 0;
  padding: 0;
  list-style: none;
}
.webInvitationView dt {
  font-weight: bold;
}
.webInvitationView dd {
  margin-left: 0;
}
.webInvitationView hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
  border: 0;
  border-top: 1px solid;
  margin: 0;
  clear: both;
  color: inherit;
}
.webInvitationView pre {
  font-family: monospace, monospace;
  font-size: inherit;
}
.webInvitationView address {
  font-style: inherit;
}
.webInvitationView a {
  background-color: transparent;
  text-decoration: none;
  color: inherit;
}
.webInvitationView abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  text-decoration: underline dotted;
}
.webInvitationView b, .webInvitationView strong {
  font-weight: bolder;
}
.webInvitationView code, .webInvitationView kbd, .webInvitationView samp {
  font-family: monospace, monospace;
  font-size: inherit;
}
.webInvitationView small {
  font-size: 80%;
}
.webInvitationView sub, .webInvitationView sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
.webInvitationView sub {
  bottom: -0.25em;
}
.webInvitationView sup {
  top: -0.5em;
}
.webInvitationView img {
  border-style: none;
  vertical-align: bottom;
}
.webInvitationView embed, .webInvitationView object, .webInvitationView iframe {
  border: 0;
  vertical-align: bottom;
}
.webInvitationView button, .webInvitationView input, .webInvitationView optgroup, .webInvitationView select, .webInvitationView textarea {
  -webkit-appearance: none;
  appearance: none;
  vertical-align: middle;
  color: inherit;
  font: inherit;
  border: 0;
  background: transparent;
  padding: 0;
  margin: 0;
  outline: 0;
  border-radius: 0;
  text-align: inherit;
}
.webInvitationView [type="checkbox"] {
  -webkit-appearance: checkbox;
  appearance: checkbox;
}
.webInvitationView [type="radio"] {
  -webkit-appearance: radio;
  appearance: radio;
}
.webInvitationView button, .webInvitationView input {
  overflow: visible;
}
.webInvitationView button, .webInvitationView select {
  text-transform: none;
}
.webInvitationView button, .webInvitationView [type="button"], .webInvitationView [type="reset"], .webInvitationView [type="submit"] {
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}
.webInvitationView button[disabled], .webInvitationView [type="button"][disabled], .webInvitationView [type="reset"][disabled], .webInvitationView [type="submit"][disabled] {
  cursor: default;
}
.webInvitationView button::-moz-focus-inner, .webInvitationView [type="button"]::-moz-focus-inner, .webInvitationView [type="reset"]::-moz-focus-inner, .webInvitationView [type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}
.webInvitationView button:-moz-focusring, .webInvitationView [type="button"]:-moz-focusring, .webInvitationView [type="reset"]:-moz-focusring, .webInvitationView [type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}
.webInvitationView select::-ms-expand {
  display: none;
}
.webInvitationView option {
  padding: 0;
}
.webInvitationView fieldset {
  margin: 0;
  padding: 0;
  border: 0;
  min-width: 0;
}
.webInvitationView legend {
  color: inherit;
  display: table;
  max-width: 100%;
  padding: 0;
  white-space: normal;
}
.webInvitationView progress {
  vertical-align: baseline;
}
.webInvitationView textarea {
  overflow: auto;
}
.webInvitationView [type="number"]::-webkit-inner-spin-button, .webInvitationView [type="number"]::-webkit-outer-spin-button {
  height: auto;
}
.webInvitationView [type="search"] {
  outline-offset: -2px;
}
.webInvitationView [type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
.webInvitationView ::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}
.webInvitationView label[for] {
  cursor: pointer;
}
.webInvitationView details {
  display: block;
}
.webInvitationView summary {
  display: list-item;
}
.webInvitationView [contenteditable] {
  outline: none;
}
.webInvitationView table {
  border-collapse: collapse;
  border-spacing: 0;
}
.webInvitationView caption {
  text-align: left;
}
.webInvitationView td, .webInvitationView th {
  vertical-align: top;
  padding: 0;
}
.webInvitationView th {
  text-align: left;
  font-weight: bold;
}
.webInvitationView [hidden] {
  display: none;
}
.webInvitationView .viewBlock_title {
  text-align: center;
  font-size: 20px;
  font-weight: 500;
  margin: 20px 0 40px;
  background-image: url("/images/webinvitation/theme_wa/title_bar.png");
  background-size: auto 8px;
  background-position: center;
  background-repeat: repeat-x;
}
.webInvitationView .viewBlock_title span {
  background: #FFF;
  padding: 0 20px;
}
.webInvitationView .viewBlock_title_sub {
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  margin: 0 0 16px;
  background-size: auto 8px;
  background-position: center;
  background-repeat: repeat-x;
  position: relative;
}
.webInvitationView .viewBlock_title_sub::before {
  content: '';
  display: block;
  background: #EE645E;
  width: 100%;
  height: 1px;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
}
.webInvitationView .viewBlock_title_sub span {
  display: inline-block;
  background: #FFF;
  padding: 0 12px;
  position: relative;
  z-index: 1;
}
.webInvitationView .link {
  display: inline-block;
}
.webInvitationView .link.is-search {
  color: #B18A3E;
  font-size: 14px;
  position: relative;
}
.webInvitationView .link.is-search::before {
  content: '';
  display: block;
  width: 24px;
  height: 24px;
  background-image: url("/images/webinvitation/theme_wa/icon-search.svg");
  background-size: contain;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}
.webInvitationView .btn.is-delete {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: #333;
  background: rgba(0, 0, 0, 0.1);
  font-size: 14px;
  position: relative;
  padding: 8px 28px;
  border-radius: 48px;
  transition: 0.35s ease-in-out;
}
.webInvitationView .btn.is-delete:hover {
  background: rgba(0, 0, 0, 0.15);
}
.webInvitationView .btn.is-delete::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  background: currentColor;
  mask-image: url("/images/webinvitation/theme_wa/icon-remove.svg");
  mask-size: contain;
}
.webInvitationView .btn.is-add {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #FFF;
  background: #B18A3E;
  font-size: 14px;
  position: relative;
  margin: 0 20px 92px;
  padding: 8px 15px;
  border-radius: 8px;
  transition: 0.35s ease-in-out;
}
.webInvitationView .btn.is-add:hover {
  filter: brightness(1.1);
}
.webInvitationView .btn.is-add::before {
  content: '';
  display: inline-block;
  width: 28px;
  height: 28px;
  margin-right: 8px;
  background: #FFF;
  mask-image: url("/images/webinvitation/theme_wa/icon-plus.svg");
  mask-size: contain;
}
.webInvitationView .btn.is-confirm {
  display: block;
  color: #FFF;
  background: #EE645E;
  font-size: 18px;
  position: relative;
  margin: 34px auto 24px;
  padding: 19px 15px;
  border-radius: 40px;
  max-width: 276px;
  width: 100%;
  text-align: center;
  transition: 0.35s ease-in-out;
}
.webInvitationView .btn.is-confirm:hover {
  filter: brightness(1.1);
}
.webInvitationView .mainVisual_background_image {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
}
.webInvitationView .mainVisual_background_noImage {
  background-image: url("/images/webinvitation/theme_wa/mainVisual_bg.jpg");
  background-size: cover;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
}
.webInvitationView .mainVisual_first {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  max-width: 376px;
  background: #FFF8ED;
  z-index: 100;
  overflow: hidden;
  animation: 4.3s mainVisualFirst forwards ease-in-out;
  pointer-events: none;
}
@media screen and (max-width: 767px) {
  .webInvitationView .mainVisual_first {
    max-width: 100%;
    position: fixed;
  }
}
.webInvitationView .mainVisual_first_symbol {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
}
.webInvitationView .mainVisual_first_symbol_1 {
  animation: 4s slideInToRight forwards ease-in-out;
}
.webInvitationView .mainVisual_first_symbol_1::before, .webInvitationView .mainVisual_first_symbol_1::after {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  animation: 2s fuwafuwa linear infinite;
}
.webInvitationView .mainVisual_first_symbol_1::before {
  width: 294px;
  height: 158px;
  background-image: url("/images/webinvitation/theme_wa/first_symbol_1.png");
  top: 6px;
  right: -60px;
}
.webInvitationView .mainVisual_first_symbol_1::after {
  width: 247px;
  height: 133px;
  background-image: url("/images/webinvitation/theme_wa/first_symbol_2.png");
  top: 74px;
  right: -80px;
  animation-delay: 0.2s;
}
.webInvitationView .mainVisual_first_symbol_2 {
  animation: 4s slideInToLeft forwards ease-in-out;
}
.webInvitationView .mainVisual_first_symbol_2::before, .webInvitationView .mainVisual_first_symbol_2::before {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  width: 294px;
  height: 158px;
  background-image: url("/images/webinvitation/theme_wa/first_symbol_3.png");
  top: 42%;
  left: -100px;
  animation: 2.2s fuwafuwa infinite;
  animation-delay: 0.1s;
}
.webInvitationView .mainVisual_first_symbol_3 {
  animation: 4s slideInToRight forwards ease-in-out;
}
.webInvitationView .mainVisual_first_symbol_3::before, .webInvitationView .mainVisual_first_symbol_3::after {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  animation: 1.8s fuwafuwa infinite;
}
.webInvitationView .mainVisual_first_symbol_3::before {
  width: 286px;
  height: 154px;
  background-image: url("/images/webinvitation/theme_wa/first_symbol_4.png");
  right: -130px;
  bottom: 50px;
  animation-delay: 0.1s;
}
.webInvitationView .mainVisual_first_symbol_3::after {
  width: 286px;
  height: 154px;
  background-image: url("/images/webinvitation/theme_wa/first_symbol_5.png");
  right: 10px;
  bottom: -10px;
}
.webInvitationView .mainVisual.is-animated .mainVisual_title {
  animation: slideIn 0.5s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .mainVisual.is-animated .mainVisual_name_bride {
  animation: slideInFromLeft 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.1s;
}
.webInvitationView .mainVisual.is-animated .mainVisual_name_groom {
  animation: slideInFromRight 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.2s;
}
.webInvitationView .mainVisual_title {
  opacity: 0;
  transform: translateY(20px);
  display: inline-block;
}
.webInvitationView .mainVisual_title_text {
  display: inline-block;
}
.webInvitationView .mainVisual_title_text::before {
  content: '招待状';
}
.webInvitationView .mainVisual_title_symbol {
  display: inline-block;
}
.webInvitationView .mainVisual_title_symbol_start::before {
  content: '｛';
}
.webInvitationView .mainVisual_title_symbol_end::before {
  content: '｝';
}
.webInvitationView .mainVisual_name_bride {
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .mainVisual_name_groom {
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .mainVisual_names {
  position: relative;
}
.webInvitationView .mainVisual_names::before {
  content: '';
  display: block;
  width: 50px;
  height: 50px;
  background-image: url("/images/webinvitation/theme_wa/mainVisual_name_icon.svg");
  background-size: contain;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}
.webInvitationView .mainVisual_name + .name {
  margin-left: 70px;
}
.webInvitationView .countDown {
  background: #FFF8ED;
}
.webInvitationView .countDown.is-animated .countDown_box {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .countDown.is-animated .countDown_title span {
  animation: passText 0s ease .4s 1 normal forwards;
  animation-delay: 1s;
}
.webInvitationView .countDown.is-animated .countDown_title span::before {
  animation: passBar 0.8s ease 0s 1 normal forwards;
  animation-delay: 0.6s;
  background: #EC4D47;
}
.webInvitationView .countDown.is-animated .countDown_blocks {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.8s;
}
.webInvitationView .countDown.is-animated .countDown_dates {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.2s;
}
.webInvitationView .countDown_box {
  background-image: url("/images/webinvitation/theme_wa/countDown_bg.png");
  background-size: 150%;
  border-image-source: url("/images/webinvitation/theme_wa/countDown_bar.png");
  border-image-slice: 20;
  border-image-width: 10px;
  border-image-outset: 0px;
  border-image-repeat: repeat;
  opacity: 0;
  transform: translateX(20px);
}
.webInvitationView .countDown_box::before {
  content: '';
  display: block;
  width: 100%;
  height: 58px;
  background-image: url("/images/webinvitation/theme_wa/countDown_bar_top.png");
  background-size: auto 58px;
  background-position: center;
  background-repeat: repeat-x;
  position: absolute;
  left: 0;
  right: 0;
  bottom: -9px;
  margin: auto;
}
.webInvitationView .countDown .countDown_date, .webInvitationView .countDown .countDown_block_value {
  color: #EC4D47;
}
.webInvitationView .countDown_blocks {
  opacity: 0;
  transform: translateX(20px);
}
.webInvitationView .countDown_dates {
  opacity: 0;
  transform: translateX(20px);
}
.webInvitationView .countDown_title {
  color: #EC4D47;
  font-weight: bold;
}
.webInvitationView .countDown_title span {
  position: relative;
  visibility: hidden;
  transform: translate3d(0, 0, 0);
}
.webInvitationView .countDown_title span::before {
  content: '';
  display: inline-block;
  width: 0%;
  height: 100%;
  position: absolute;
  left: 0%;
  top: 0;
  bottom: 0;
  opacity: 1;
  visibility: visible;
  z-index: 1;
  transform: translate3d(0, 0, 0);
}
.webInvitationView .countDown_wrap {
  position: relative;
}
.webInvitationView .countDown_wrap::before, .webInvitationView .countDown_wrap::after {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
}
.webInvitationView .countDown_symbol {
  position: relative;
}
.webInvitationView .countDown_symbol_1::before, .webInvitationView .countDown_symbol_1::after {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
}
.webInvitationView .countDown_symbol_1::before {
  width: 137px;
  height: 74px;
  background-image: url("/images/webinvitation/theme_wa/countDown_symbol_1.png");
  top: -44px;
  right: -70px;
  opacity: 0;
  transform: translateX(20px);
}
.webInvitationView .countDown_symbol_1::after {
  width: 137px;
  height: 74px;
  background-image: url("/images/webinvitation/theme_wa/countDown_symbol_2.png");
  top: 4px;
  right: -100px;
  opacity: 0;
  transform: translateX(20px);
}
.webInvitationView .countDown_symbol_1.is-animated::before {
  animation: slideInFromRight 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.5s;
}
.webInvitationView .countDown_symbol_1.is-animated::after {
  animation: slideInFromRight 0.5s ease-in-out;
  animation-delay: 1.8s;
  animation-fill-mode: both;
}
.webInvitationView .countDown_symbol_6::before, .webInvitationView .countDown_symbol_6::after {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
}
.webInvitationView .countDown_symbol_6::before {
  width: 137px;
  height: 74px;
  background-image: url("/images/webinvitation/theme_wa/countDown_symbol_3.png");
  bottom: 42px;
  left: -82px;
  opacity: 0;
  transform: translateX(-20px);
}
.webInvitationView .countDown_symbol_6::after {
  width: 115px;
  height: 62px;
  background-image: url("/images/webinvitation/theme_wa/countDown_symbol_4.png");
  bottom: 10px;
  left: -40px;
  opacity: 0;
  transform: translateX(-20px);
}
.webInvitationView .countDown_symbol_6.is-animated::before {
  animation: slideInFromLeft 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.6s;
}
.webInvitationView .countDown_symbol_6.is-animated::after {
  animation: slideInFromLeft 0.5s ease-in-out;
  animation-delay: 1.9s;
  animation-fill-mode: both;
}
.webInvitationView .message {
  background: #FFF8ED;
  padding: 20px;
}
.webInvitationView .message.is-animated .message_wrap {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .message.is-animated .message_title {
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.6s;
}
.webInvitationView .message.is-animated .message_contents {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.8s;
}
.webInvitationView .message_wrap {
  background: #FFF;
  padding: 10px 0 32px;
  opacity: 0;
  transform: translateX(20px);
}
.webInvitationView .message_title {
  opacity: 0;
}
.webInvitationView .message_contents {
  min-height: 120px;
  font-size: 14px;
  position: relative;
  line-height: 1.8;
  position: relative;
  padding-bottom: 32px;
}
.webInvitationView .message_symbol {
  position: relative;
}
.webInvitationView .message_symbol_3::before {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  width: 149px;
  height: 80px;
  background-image: url("/images/webinvitation/theme_wa/message_symbol_1.png");
  top: -20px;
  right: -94px;
  opacity: 0;
  transform: translateX(20px);
}
.webInvitationView .message_symbol_3.is-animated::before {
  animation: slideInFromRight 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1s;
}
.webInvitationView .message_symbol_4::before, .webInvitationView .message_symbol_4::after {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
}
.webInvitationView .message_symbol_4::before {
  width: 137px;
  height: 74px;
  background-image: url("/images/webinvitation/theme_wa/message_symbol_2.png");
  bottom: -38px;
  left: -56px;
  opacity: 0;
  transform: translateX(-20px);
}
.webInvitationView .message_symbol_4::after {
  width: 137px;
  height: 74px;
  background-image: url("/images/webinvitation/theme_wa/countDown_symbol_3.png");
  bottom: -62px;
  left: -110px;
  opacity: 0;
  transform: translateX(-20px);
}
.webInvitationView .message_symbol_4.is-animated::before {
  animation: slideInFromLeft 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.1s;
}
.webInvitationView .message_symbol_4.is-animated::after {
  animation: slideInFromLeft 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.4s;
}
.webInvitationView .profile {
  background: #FFF8ED;
  padding: 20px;
}
.webInvitationView .profile.is-animated .profile_wrap {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .profile.is-animated .profile_title {
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.6s;
}
.webInvitationView .profile_wrap {
  background: #FFF;
  padding: 10px 0 48px;
  opacity: 0;
  transform: translateX(20px);
}
.webInvitationView .profile_title {
  opacity: 0;
}
.webInvitationView .profile_item {
  padding: 0 27px;
  margin-bottom: 62px;
}
.webInvitationView .profile_item:nth-child(1) .profile_item_image::before {
  background-image: url("/images/webinvitation/theme_wa/profile_item_bg_1.png");
}
.webInvitationView .profile_item:nth-child(2) .profile_item_image::before {
  background-image: url("/images/webinvitation/theme_wa/profile_item_bg_2.png");
}
.webInvitationView .profile_item:nth-child(odd) {
  position: relative;
}
.webInvitationView .profile_item:nth-child(odd).is-animated::before {
  animation: slideInFromLeft 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.5s;
}
.webInvitationView .profile_item:nth-child(odd).is-animated::after {
  animation: slideInFromLeft 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.8s;
}
.webInvitationView .profile_item:nth-child(odd)::before, .webInvitationView .profile_item:nth-child(odd)::after {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
}
.webInvitationView .profile_item:nth-child(odd)::before {
  width: 137px;
  height: 74px;
  background-image: url("/images/webinvitation/theme_wa/profile_symbol_1.png");
  top: 182px;
  left: -56px;
}
.webInvitationView .profile_item:nth-child(odd)::after {
  width: 115px;
  height: 62px;
  background-image: url("/images/webinvitation/theme_wa/profile_symbol_2.png");
  top: 220px;
  left: -90px;
}
.webInvitationView .profile_item:nth-child(even) {
  position: relative;
}
.webInvitationView .profile_item:nth-child(even).is-animated::before {
  animation: slideInFromRight 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.5s;
}
.webInvitationView .profile_item:nth-child(even).is-animated::after {
  animation: slideInFromRight 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.8s;
}
.webInvitationView .profile_item:nth-child(even)::before, .webInvitationView .profile_item:nth-child(even)::after {
  content: '';
  display: block;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
}
.webInvitationView .profile_item:nth-child(even)::before {
  width: 137px;
  height: 74px;
  background-image: url("/images/webinvitation/theme_wa/profile_symbol_3.png");
  top: 182px;
  right: -56px;
}
.webInvitationView .profile_item:nth-child(even)::after {
  width: 115px;
  height: 62px;
  background-image: url("/images/webinvitation/theme_wa/profile_symbol_4.png");
  top: 220px;
  right: -90px;
}
.webInvitationView .profile_item_image {
  margin-bottom: 20px;
  position: relative;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .profile_item_image.is-animated {
  animation: slideIn 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.5s;
}
.webInvitationView .profile_item_image.is-animated::before {
  animation: 1s profileBackground forwards ease-in-out;
  animation-delay: 0.8s;
}
.webInvitationView .profile_item_image::before {
  content: '';
  display: block;
  width: 220px;
  height: 220px;
  background-image: url("/images/webinvitation/theme_wa/profile_item_bg_other.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  left: -8px;
  bottom: 8px;
  z-index: -1;
  opacity: 0;
  rotate: -10deg;
  transform: translateX(-40px);
}
.webInvitationView .profile_item_name.is-animated .profile_item_name_position {
  animation: slideIn 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.8s;
}
.webInvitationView .profile_item_name.is-animated .profile_item_name_main {
  animation: slideIn 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.9s;
}
.webInvitationView .profile_item_name_position {
  font-size: 14px;
  text-align: center;
  margin-bottom: 4px;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .profile_item_name_main {
  font-size: 20px;
  font-weight: bold;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .profile_item_text {
  color: #222;
  font-size: 14px;
  line-height: 1.8;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .profile_item_text.is-animated {
  animation: slideIn 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.0s;
}
.webInvitationView .gallery {
  background: #FFF8ED;
  padding: 20px 0;
}
.webInvitationView .gallery.is-animated .gallery_wrap {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .gallery.is-animated .gallery_title {
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1s;
}
.webInvitationView .gallery.is-animated .gallery_slides {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.4s;
}
.webInvitationView .gallery.is-animated .gallery_thumbnail {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.8s;
}
.webInvitationView .gallery .gallery_title span {
  background: #FFF8ED;
}
.webInvitationView .gallery_wrap {
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .gallery_title {
  opacity: 0;
}
.webInvitationView .gallery_slides {
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .swiper-button-prev, .webInvitationView .swiper-rtl .swiper-button-next {
  left: 8px;
}
.webInvitationView .swiper-button-next, .webInvitationView .swiper-rtl .swiper-button-prev {
  right: 8px;
}
.webInvitationView .swiper-slide-thumb-active img {
  box-sizing: border-box;
  border: 1px solid #EE645E;
}
.webInvitationView .swiper-button-next, .webInvitationView .swiper-button-prev {
  width: 40px;
  height: 40px;
}
.webInvitationView .swiper-button-next::after, .webInvitationView .swiper-button-prev::after {
  content: '';
  display: block;
  width: 40px;
  height: 40px;
}
.webInvitationView .swiper-button-prev::after, .webInvitationView :host(.swiper-rtl) .swiper-button-next::after {
  content: '';
  background-image: url("/images/webinvitation/theme_wa/icon_slide_arrow_left.png");
  background-size: cover;
}
.webInvitationView .swiper-button-next::after {
  background-image: url("/images/webinvitation/theme_wa/icon_slide_arrow_right.png");
  background-size: cover;
}
.webInvitationView .swiper-horizontal > .swiper-pagination-bullets, .webInvitationView .swiper-pagination-bullets.swiper-pagination-horizontal, .webInvitationView .swiper-pagination-custom, .webInvitationView .swiper-pagination-fraction {
  position: static;
}
.webInvitationView .swiper-pagination-bullet-active {
  background: #EE645E;
}
.webInvitationView .information {
  background: #FFF8ED;
  padding: 20px;
}
.webInvitationView .information .information_title {
  opacity: 0;
  margin-bottom: 30px;
}
.webInvitationView .information .information_title_sub {
  opacity: 0;
}
.webInvitationView .information hr {
  border-top: none;
  border-bottom: 1px solid #EE645E;
}
.webInvitationView .information_box {
  background: #FFF;
  padding: 10px 0 40px;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .information_box + .information_box {
  margin-top: 20px;
}
.webInvitationView .information_box.is-animated {
  animation: slideIn 0.6s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .information_box.is-animated .information_title {
  animation: fadeIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.4s;
}
.webInvitationView .information_box.is-animated .information_date_label {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.6s;
}
.webInvitationView .information_box.is-animated .information_date_values {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.7s;
}
.webInvitationView .information_date {
  color: #222;
  margin-bottom: 40px;
}
.webInvitationView .information_date_label {
  color: #B18A3E;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .information_date_values {
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .information_block {
  border: 1px solid rgba(238, 100, 94, 0.3);
  border-radius: 4px;
  padding: 16px 20px;
  margin: 0 auto 12px;
  width: calc(100% - 40px);
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .information_block.is-animated {
  animation: slideIn 0.6s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.7s;
}
.webInvitationView .information_block.is-animated .information_title_sub {
  animation: fadeIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.9s;
}
.webInvitationView .information_block.is-animated .information_block_time {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.2s;
}
.webInvitationView .information_block.is-animated .information_block_address {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.2s;
}
.webInvitationView .information_block.is-animated .information_block_text {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 1.2s;
}
.webInvitationView .information_block.information_block_detail {
  border: none;
  background: #FBF0F0;
}
.webInvitationView .information_block.information_block_detail .information_title_sub span {
  background: #FBF0F0;
}
.webInvitationView .information_block_time {
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .information_block_address {
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .information_block_address_link_url {
  color: #EE645E;
}
.webInvitationView .information_block_text {
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .information_block_text a {
  color: #EE645E;
}
.webInvitationView .guestAnswer {
  color: #FFF;
  background-image: url("/images/webinvitation/theme_wa/guestAnswer_bg.png");
  background-size: 375px;
  padding: 20px;
}
.webInvitationView .guestAnswer_header {
  padding-top: 40px;
  margin-bottom: 40px;
  text-align: center;
}
.webInvitationView .guestAnswer_header.is-animated .guestAnswer_header_title {
  animation: slideIn 0.6s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .guestAnswer_header.is-animated .guestAnswer_header_sub_title {
  animation: slideIn 0.6s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.1s;
}
.webInvitationView .guestAnswer_header.is-animated .guestAnswer_header_text {
  animation: slideIn 0.6s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.2s;
}
.webInvitationView .guestAnswer_header.is-animated .guestAnswer_header_caution {
  animation: slideIn 0.6s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.3s;
}
.webInvitationView .guestAnswer_header_title {
  font-size: 30px;
  font-weight: bold;
  margin-bottom: 12px;
  padding-top: 75px;
  position: relative;
  letter-spacing: 6px;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .guestAnswer_header_title::before {
  content: '';
  display: block;
  width: 50px;
  height: 50px;
  background-image: url("/images/webinvitation/theme_wa/guestAnswer_icon.svg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
}
.webInvitationView .guestAnswer_header_sub_title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .guestAnswer_header_text {
  font-size: 16px;
  font-weight: normal;
  line-height: 1.4;
  margin-bottom: 10px;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .guestAnswer_header_text_limit {
  font-size: 20px;
  font-weight: bold;
  text-decoration: underline;
}
.webInvitationView .guestAnswer_header_caution {
  font-size: 14px;
  line-height: 1.5;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 0;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .guestAnswer .information_title_sub::before {
  background: #FFF;
}
.webInvitationView .guestAnswer .information_title_sub span {
  background-image: url("/images/webinvitation/theme_wa/guestAnswer_bg.png");
  background-size: 375px;
}
.webInvitationView .guestAnswer_answer {
  width: 100%;
  margin-bottom: 34px;
}
.webInvitationView .guestAnswer_answer_box {
  padding-bottom: 16px;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .guestAnswer_answer_box.is-animated {
  animation: slideIn 0.6s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .guestAnswer_answer_box_flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 335px;
  width: 100%;
  margin: 0 auto;
}
.webInvitationView .guestAnswer_answer_box input {
  display: none;
}
.webInvitationView .guestAnswer_answer_box input:checked + .guestAnswer_answer_box_radio_wrap::before {
  width: 100px;
  height: 100px;
  border: 5px solid #FFF;
}
.webInvitationView .guestAnswer_answer_box_radio {
  cursor: pointer;
}
.webInvitationView .guestAnswer_answer_box_radio_wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100px;
  height: 100px;
  position: relative;
}
.webInvitationView .guestAnswer_answer_box_radio_wrap::before {
  content: '';
  display: block;
  width: 0px;
  height: 0px;
  background: rgba(255, 255, 255, 0.2);
  border: 0 solid #FFF;
  border-radius: 50px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  transition: 0.35s ease-in-out;
  box-sizing: border-box;
}
.webInvitationView .guestAnswer_answer_box_radio_wrap:hover::before {
  width: 80px;
  height: 80px;
  border: 3px solid #FFF;
  background: rgba(255, 255, 255, 0.1);
}
.webInvitationView .guestAnswer_answer_box_radio_title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 2px;
}
.webInvitationView .guestAnswer_answer_box_radio_sub {
  font-size: 12px;
}
.webInvitationView .guestAnswer_box {
  color: #222;
  background: #FFF;
  padding: 10px 0 40px;
  font-size: 14px;
  font-family: Arial, Helvetica, sans-serif;
  box-sizing: border-box;
  opacity: 0;
  transform: translateY(20px);
}
.webInvitationView .guestAnswer_box.is-animated {
  animation: slideIn 0.6s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .guestAnswer_box hr {
  border-color: #D9D9D9;
  margin: 24px 0;
}
.webInvitationView .guestAnswer_box > hr {
  margin: 0;
}
.webInvitationView .guestAnswer_box_wrap {
  padding: 34px 20px 10px;
}
.webInvitationView .guestAnswer_box_label, .webInvitationView .guestAnswer_box .guestAnswer_box_input .title {
  color: #333;
  font-weight: bold;
  margin-bottom: 12px;
}
.webInvitationView .guestAnswer_box_label .guestAnswer_required, .webInvitationView .guestAnswer_box .guestAnswer_box_input .title .guestAnswer_required {
  color: #FF1B1B;
}
.webInvitationView .guestAnswer_box_label[data-required="true"]:after, .webInvitationView .guestAnswer_box .guestAnswer_box_input .title[data-required="true"]:after {
  content: "*";
  margin-left: 5px;
  vertical-align: top;
  font-size: 12px;
  color: #E65C7A;
}
.webInvitationView .guestAnswer_box_row {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--Gray, #D9D9D9);
}
.webInvitationView .guestAnswer_box_row_flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.webInvitationView .guestAnswer_box_row_text {
  font-size: 13px;
  line-height: 1.6;
  color: #222;
  margin-bottom: 12px;
}
.webInvitationView .guestAnswer_box_unit {
  font-size: 14px;
  margin-left: 4px;
}
.webInvitationView .guestAnswer_box_unit + * {
  margin-left: 12px;
}
.webInvitationView .guestAnswer_box.zip label {
  max-width: 160px;
}
.webInvitationView .guestAnswer_box_button, .webInvitationView .guestAnswer_box .search {
  display: inline-block;
  font-size: 14px;
  line-height: 1;
  color: #FFF;
  background: #B18A3E;
  padding: 20px 12px;
  border-radius: 4px;
  white-space: nowrap;
  margin-left: 12px;
  transition: 0.35s ease-in-out;
}
.webInvitationView .guestAnswer_box_button:hover, .webInvitationView .guestAnswer_box .search:hover {
  filter: brightness(1.1);
}
.webInvitationView .guestAnswer_box .search {
  margin-top: 20px;
}
.webInvitationView .guestAnswer_box_input {
  width: 100%;
}
.webInvitationView .guestAnswer_box_input input, .webInvitationView .guestAnswer_box_input textarea {
  width: 100%;
  padding: 18px 16px;
  font-size: 16px;
  line-height: 1;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
}
.webInvitationView .guestAnswer_box_input textarea {
  height: 5em;
  min-height: 0;
}
.webInvitationView .guestAnswer_box_input + .guestAnswer_box_input, .webInvitationView .guestAnswer_box_input .guestAnswer_box_date .unit label {
  margin-left: 8px;
}
.webInvitationView .guestAnswer_box_input.guestAnswer_box_date .inputDate {
  display: flex;
  align-items: flex-end;
}
.webInvitationView .guestAnswer_box_input.guestAnswer_box_date .inputDate .unit {
  margin-bottom: 10px;
}
.webInvitationView .guestAnswer_box_input_large {
  min-width: 90px;
}
.webInvitationView .guestAnswer_box_date .xs {
  max-width: 80px;
}
.webInvitationView .guestAnswer_box_date .unit {
  font-size: 14px;
  margin: 0 0 0 4px;
}
.webInvitationView .guestAnswer_box_date .unit + * {
  margin-left: 12px;
}
.webInvitationView .guestAnswer_box_date .unit + .xs {
  max-width: 62px;
}
.webInvitationView .guestAnswer_box_file {
  width: 100%;
}
.webInvitationView .guestAnswer_box_file input {
  display: none;
}
.webInvitationView .guestAnswer_box_file_button {
  display: block;
  position: relative;
  font-size: 14px;
  line-height: 1;
  background: rgba(0, 0, 0, 0.05);
  padding: 18px 25px 18px 50px;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: 0.35s ease-in-out;
}
.webInvitationView .guestAnswer_box_file_button:hover {
  background: rgba(0, 0, 0, 0.1);
}
.webInvitationView .guestAnswer_box_file_button::before {
  content: '';
  display: block;
  width: 24px;
  height: 24px;
  background-image: url("/images/webinvitation/theme_wa/icon-plus.svg");
  background-size: contain;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 16px;
  margin: auto;
}
.webInvitationView .guestAnswer_box_textarea {
  width: 100%;
}
.webInvitationView .guestAnswer_box_textarea textarea {
  width: 100%;
  min-height: 80px;
  padding: 18px 16px;
  font-size: 16px;
  line-height: 1;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  resize: none;
}
.webInvitationView .guestAnswer_box_select {
  width: 100%;
  position: relative;
}
.webInvitationView .guestAnswer_box_select::before {
  content: '';
  display: block;
  height: calc(9px / 2);
  width: 9px;
  clip-path: polygon(0 0, 100% 0, 50% 100%);
  background: #333;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 20px;
  margin: auto;
  pointer-events: none;
}
.webInvitationView .guestAnswer_box_select select {
  width: 100%;
  padding: 18px 16px;
  font-size: 16px;
  line-height: 1;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
}
.webInvitationView .guestAnswer_box_select + .guestAnswer_box_select {
  margin-left: 8px;
}
.webInvitationView .guestAnswer_box .selectWrap select {
  width: 100%;
  padding: 0 16px;
  font-size: 16px;
  line-height: 1;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  height: 56px;
}
.webInvitationView .guestAnswer_box_radio {
  width: 100%;
  cursor: pointer;
}
.webInvitationView .guestAnswer_box_radio + .guestAnswer_box_radio {
  margin-left: 8px;
}
.webInvitationView .guestAnswer_box_radio input {
  display: none;
}
.webInvitationView .guestAnswer_box_radio input:checked + .guestAnswer_box_radio_wrap {
  background: #FFF;
  border: 2px solid #EE645E;
  padding: 14px 10px;
}
.webInvitationView .guestAnswer_box_radio_wrap {
  display: block;
  box-sizing: content-box;
  padding: 14px 10px;
  text-align: center;
  font-size: 16px;
  line-height: 1;
  color: #333;
  background: rgba(0, 0, 0, 0.05);
  border: 2px solid transparent;
  border-radius: 8px;
  position: relative;
  transition: 0.35s ease-in-out;
}
.webInvitationView .guestAnswer_box_radio_wrap:hover {
  background: rgba(0, 0, 0, 0.1);
}
.webInvitationView .guestAnswer_box_checkbox {
  display: block;
  width: 100%;
  position: relative;
  cursor: pointer;
}
.webInvitationView .guestAnswer_box_checkbox + .guestAnswer_box_checkbox {
  margin-top: 8px;
}
.webInvitationView .guestAnswer_box_checkbox input {
  display: none;
}
.webInvitationView .guestAnswer_box_checkbox input:checked + .guestAnswer_box_checkbox_wrap::before {
  background: #EE645E;
  border: 2px solid #EE645E;
}
.webInvitationView .guestAnswer_box_checkbox_wrap {
  font-size: 16px;
  padding-left: 29px;
}
.webInvitationView .guestAnswer_box_checkbox_wrap::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 0, 0, 0.6);
  background-color: #fff;
  border-radius: 3px;
  box-sizing: border-box;
}
.webInvitationView .guestAnswer_box_checkbox_wrap::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 6px;
  width: 6px;
  height: 11px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.webInvitationView .guestAnswer_box_radiobox {
  display: block;
  width: 100%;
  margin-bottom: 8px;
  position: relative;
  cursor: pointer;
}
.webInvitationView .guestAnswer_box_radiobox + .guestAnswer_box_radiobox {
  margin-top: 8px;
}
.webInvitationView .guestAnswer_box_radiobox input {
  display: none;
}
.webInvitationView .guestAnswer_box_radiobox input:checked + .guestAnswer_box_radiobox_wrap::before {
  border: 2px solid #EE645E;
}
.webInvitationView .guestAnswer_box_radiobox input:checked + .guestAnswer_box_radiobox_wrap::after {
  background: #EE645E;
}
.webInvitationView .guestAnswer_box_radiobox_wrap {
  font-size: 16px;
  padding-left: 29px;
}
.webInvitationView .guestAnswer_box_radiobox_wrap::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 16px;
  height: 16px;
  border: 2px solid #D9D9D9;
  background-color: #fff;
  border-radius: 16px;
  box-sizing: border-box;
  transition: 0.35s ease-in-out;
}
.webInvitationView .guestAnswer_box_radiobox_wrap::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 4px;
  width: 8px;
  height: 8px;
  background-color: transparent;
  border-radius: 16px;
  box-sizing: border-box;
  transition: 0.35s ease-in-out;
  zoom: 0;
}
.webInvitationView .guestAnswer_box_remove {
  text-align: center;
  margin: 32px 0 24px;
}
.webInvitationView .guestAnswer .link {
  display: inline-block;
  color: #B18A3E;
  margin-top: 12px;
  padding-left: 28px;
}
.webInvitationView .guestAnswer .link:hover {
  text-decoration: underline;
}
.webInvitationView .guestAnswer_title_sub {
  font-size: 18px;
  text-align: center;
  margin-bottom: 48px;
}
.webInvitationView .guestAnswer_title_sub span {
  position: relative;
  display: inline-block;
  padding: 0 26px;
}
.webInvitationView .guestAnswer_title_sub span::before, .webInvitationView .guestAnswer_title_sub span::after {
  content: '';
  width: 18px;
  height: 1px;
  background: #9C9C9C;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
}
.webInvitationView .guestAnswer_title_sub span::before {
  left: 0;
}
.webInvitationView .guestAnswer_title_sub span::after {
  right: 0;
}
.webInvitationView .blockGuests {
  padding-bottom: 40px;
}
.webInvitationView .blockGuests .blockTitle {
  color: var(---font, #222);
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 1.8px;
  margin-bottom: 40px;
}
.webInvitationView .blockGuests .blockTitle:before, .webInvitationView .blockGuests .blockTitle:after {
  content: " ";
  display: inline-block;
  width: 18px;
  height: 1px;
  vertical-align: middle;
  margin: 0 10px;
  background: var(--Gray_dark, #9C9C9C);
}
.webInvitationView .inputGuestType .inputRadio, .webInvitationView .inputGender .inputRadio {
  font-size: 0;
}
.webInvitationView .inputGuestType .inputRadio label, .webInvitationView .inputGender .inputRadio label {
  display: inline-block;
  width: 30.33%;
  margin-right: 3%;
  cursor: default;
}
.webInvitationView .inputGuestType .inputRadio label:last-child, .webInvitationView .inputGender .inputRadio label:last-child {
  margin-right: 0;
}
.webInvitationView .inputGuestType .inputRadio label > span, .webInvitationView .inputGender .inputRadio label > span {
  display: block;
  width: 100%;
  text-align: center;
  margin: 0;
  padding: 0;
  background: #000;
  border-radius: 8px;
  padding: 12px 0;
  font-size: 16px;
  background: #f4f4f4;
  border: 2px solid #f4f4f4;
  cursor: pointer;
}
.webInvitationView .inputGuestType .inputRadio label > span:before, .webInvitationView .inputGender .inputRadio label > span:before {
  display: none;
  width: 0;
  height: 0;
}
.webInvitationView .inputGuestType .inputRadio label input:checked + span, .webInvitationView .inputGender .inputRadio label input:checked + span {
  border-color: #EE645E;
  background: #fff;
}
.webInvitationView .inputGuestType .inputRadio label input:checked + span:after, .webInvitationView .inputGender .inputRadio label input:checked + span:after {
  display: none;
}
.webInvitationView .inputGuestType .inputRadio label input:disabled + span, .webInvitationView .inputGender .inputRadio label input:disabled + span {
  opacity: 1;
}
.webInvitationView .inputGuestType .inputRadio label {
  width: 47%;
}
.webInvitationView .inputAttendance .inputTitle {
  color: var(--white-100, var(--white-100, #FFF));
  text-align: center;
  font-size: 18px;
  position: relative;
  margin-bottom: 20px;
}
.webInvitationView .inputAttendance .inputTitle:before, .webInvitationView .inputAttendance .inputTitle:after {
  content: " ";
  height: 1px;
  width: 30%;
  background: #fff;
  display: block;
  position: absolute;
  top: 50%;
  left: 0;
}
.webInvitationView .inputAttendance .inputTitle:after {
  left: auto;
  right: 0;
}
.webInvitationView .inputAttendance .inputRadio {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.webInvitationView .inputAttendance .inputRadio .input-error {
  position: absolute;
  bottom: 0;
  left: 0;
  color: #fff;
}
.webInvitationView .inputAttendance .inputRadio label {
  display: block;
  width: 27.33%;
  position: relative;
  cursor: default;
  margin: 0;
}
.webInvitationView .inputAttendance .inputRadio label:after {
  content: " ";
  padding-top: 100%;
  height: 0;
  display: none;
}
.webInvitationView .inputAttendance .inputRadio label:last-child {
  margin-right: 0;
}
.webInvitationView .inputAttendance .inputRadio label > span {
  cursor: pointer;
  display: block;
  width: 100%;
  position: relative;
  text-align: center;
  margin: 0;
  padding: 0;
  font-size: 16px;
  background: none;
  color: #fff;
  border-radius: 50%;
  border: 6px solid rgba(255, 255, 255, 0);
  transition: 0.3s ease;
}
.webInvitationView .inputAttendance .inputRadio label > span:before {
  display: none;
  width: 0;
  height: 0;
  height: 0;
}
.webInvitationView .inputAttendance .inputRadio label > span:after {
  content: " ";
  display: block;
  width: 100%;
  padding-top: 100%;
  height: 0;
}
.webInvitationView .inputAttendance .inputRadio label > span .txt {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  text-align: center;
  transform: translateY(-50%);
}
.webInvitationView .inputAttendance .inputRadio label > span .txt .ja {
  font-size: 18px;
  display: block;
}
.webInvitationView .inputAttendance .inputRadio label > span .txt .en {
  display: block;
  font-size: 12px;
  margin-top: 5px;
}
.webInvitationView .inputAttendance .inputRadio label input:checked + span {
  position: static;
  border-color: #fff;
  background: rgba(255, 255, 255, 0.2);
}
.webInvitationView .inputAttendance .inputRadio label input:checked + span:after {
  display: block;
  background: none;
  position: static;
}
.webInvitationView .inputImages {
  white-space: nowrap;
  overflow-x: auto;
  margin-bottom: 10px;
}
.webInvitationView .inputImages .inputRadio .img {
  display: block;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 104px;
  /* height: 104px; */
  margin-bottom: 2px;
  border: 1px solid var(--Gray_light, #F4F4F4);
}
.webInvitationView .inputImages .inputRadio .img-01 {
  background-image: url(@/assets/images/web-invitation/guest-answer-image-01.png);
}
.webInvitationView .inputImages .inputRadio .img-02 {
  background-image: url(@/assets/images/web-invitation/guest-answer-image-02.png);
}
.webInvitationView .inputImages .inputRadio .img-03 {
  background-image: url(@/assets/images/web-invitation/guest-answer-image-03.png);
}
.webInvitationView .inputImages .inputRadio label {
  display: inline-block;
  cursor: default;
}
.webInvitationView .inputImages .inputRadio label > span {
  cursor: pointer;
  margin: 0;
  padding: 0;
}
.webInvitationView .inputImages .inputRadio label > span:before {
  display: none;
}
.webInvitationView .inputImages .inputRadio label input:checked + span:after {
  display: none;
}
.webInvitationView .inputImages .inputRadio label input:checked + span .img {
  position: relative;
}
.webInvitationView .inputImages .inputRadio label input:checked + span .img:after {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 4px solid #EE645E;
}
.webInvitationView .imageList {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
}
.webInvitationView .imageList_item {
  width: 106px;
  min-width: 106px;
  cursor: pointer;
  transition: 0.35s ease-in-out;
  scroll-snap-align: start;
}
.webInvitationView .imageList_item:hover {
  opacity: 0.8;
}
.webInvitationView .imageList_item + .imageList_item {
  margin-left: 8px;
}
.webInvitationView .imageList_item_image {
  margin-bottom: 8px;
  position: relative;
}
.webInvitationView .imageList_item_image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}
.webInvitationView .imageList_item_image_search {
  display: block;
  width: 24px;
  height: 24px;
  background-image: url("/images/webinvitation/theme_wa/guestAnswer_icon_search.png");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  top: 5px;
  right: 5px;
}
.webInvitationView .imageList_item_title {
  font-size: 12px;
}
.webInvitationView .freeField {
  background: #FFF8ED;
  padding: 20px;
}
.webInvitationView .freeField_box {
  background: #FFF;
  padding: 10px 0 32px;
  opacity: 0;
  transform: translateX(20px);
}
.webInvitationView .freeField_box.is-animated {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
}
.webInvitationView .freeField_box.is-animated .freeField_title {
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.6s;
}
.webInvitationView .freeField_box.is-animated .freeField_contents {
  animation: slideIn 0.8s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.8s;
}
.webInvitationView .freeField_box.is-animated .freeField_contents a {
  color: #EE645E;
  text-decoration: underline;
}
.webInvitationView .freeField_box.is-animated .freeField_contents a:hover {
  text-decoration: none;
}
.webInvitationView .freeField_title {
  opacity: 0;
}
.webInvitationView .freeField_contents {
  opacity: 0;
}
