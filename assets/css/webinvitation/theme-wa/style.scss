
@import url('https://fonts.googleapis.com/css2?family=Noto+Serif+JP:wght@200;300;400;500;600;700;900&display=swap');

// アニメーション

@keyframes passBar{
	0% {
		left: 0%;
		width: 0%;
	}
	50% {
		left: 0%;
		width: 100%;
	}
	51% {
		left: 0%;
		width: 100%;
	}
	100% {
		left: 100%;
		width: 0%;
	}
}
@keyframes passText{
	0% { visibility: hidden; }
	50% { visibility: hidden; }
	100% { visibility: visible; }
}

[data-animation="passBarWhite"],
[data-animation="passBarBlack"],
[data-animation="passBarBrown"]{
  position: relative;
  visibility: hidden;
  transform: translate3d(0, 0, 0);
  &::before {
    content: '';
    display: inline-block;
    width: 0%;
    height: 100%;
    position: absolute;
    left: 0%;
    top: 0;
    bottom: 0;
    opacity: 1;
    visibility: visible;
    z-index: 1;
    transform: translate3d(0, 0, 0);
  }
  &.is-animated{
    animation: passText 0s ease .5s 1 normal forwards;
    &::before{
      animation: passBar 1s ease 0s 1 normal forwards;
    }
  }
}
[data-animation="passBarWhite"]{
  &::before{
    background: #FFF;
  }
}
[data-animation="passBarBlack"]{
  &::before{
    background: #333;
  }
}
[data-animation="passBarBrown"]{
  &::before{
    background: #A39C94;
  }
}

[data-animation="sway"]{
  animation: sway 3s infinite;
}
@keyframes sway {
	0% {	transform: rotate(0);}
	3% {	transform: rotate(-5deg);}
	6% {	transform: rotate(5deg);}
	9% {	transform: rotate(-5deg);}
	12% {	transform: rotate(5deg);}
	15% {	transform: rotate(-5deg);}
	18% {	transform: rotate(0);}
	100% {	transform: rotate(0);}
}


[data-animation="fadeIn"]{
  opacity: 0;
  &.is-animated{
    animation: fadeIn 0.5s ease-in-out;
    animation-fill-mode: both;
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

[data-animation="slideIn"]{
  opacity: 0;
  transform: translateY(20px);
  &.is-animated{
    animation: slideIn 0.5s ease-in-out;
    animation-fill-mode: both;
  }
}
@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

[data-animation="slideInFromLeft"]{
  opacity: 0;
  transform: translateX(-20px);
  @include sp {
    transform: translateX(20px);
  }
  &.is-animated{
    animation: slideInFromLeft 0.5s ease-in-out;
    animation-fill-mode: both;
    @include sp {
      opacity: 1;
      transform: translateX(0);
      animation: slideInFromRight 0.5s ease-in-out;
    }
  }
}
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInToLeft {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }

  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
[data-animation="slideInFromRight"]{
  opacity: 0;
  transform: translateX(20px);
  &.is-animated{
    animation: slideInFromRight 0.5s ease-in-out;
    animation-fill-mode: both;
  }
}
@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInToRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}

//ラベル
[data-animation="label"]{
  opacity: 0;
  &.is-animated{
    animation: label 0.35s ease-in-out;
    animation-fill-mode: both;
  }
}
@keyframes label {
  0% {
    opacity: 0;
    transform: rotate(-1.5deg) scale(1.02);
  }

  100% {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

// 複数要素の順番スライド
[data-animation="slideInGroup"]{
  & > *{
    opacity: 0;
    transform: translateY(20px);
  }
  &.is-animated{
    & > *{
      animation: slideIn 0.35s ease-in-out;
      animation-fill-mode: both;
      @for $i from 1 to 20 {
        &:nth-of-type(#{$i}) { animation-delay: 0.15s + 0.1s * $i; }
      }
    }
  }
}

[data-animation="slideInGroupSmall"]{
  & > *{
    opacity: 0;
    transform: translateY(20px);
  }
  &.is-animated{
    & > *{
      animation: slideIn 0.25s ease-in-out;
      animation-fill-mode: both;
      @for $i from 1 to 20 {
        &:nth-of-type(#{$i}) { animation-delay: 0.15s + 0.05s * $i; }
      }
    }
  }
}



/* 回転するアニメーション */
@keyframes fuwafuwa {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInToLeft {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }

  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}
@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}
@keyframes slideInToRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
}

@keyframes profileBackground {
  0% {
    opacity: 0;
    rotate: -10deg;
    transform: translateX(-40px);
  }
  100% {
    opacity: 1;
    rotate: 0deg;
    transform: translateX(0);
  }
}


@keyframes mainVisualBackground {
  0% {
    background-size: 400px;
  }
  100% {
    background-size: 376px;
  }
}

@keyframes mainVisualBackgroundSp {
  0% {
    background-size: 120%;
  }
  100% {
    background-size: 100%;
  }
}

@keyframes slideInToLeft {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }
  80% {
    opacity: 1;
    transform: translateX(0px);
  }

  100% {
    opacity: 0;
    transform: translateX(150%);
  }
}

@keyframes slideInToRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }
  80% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(-150%);
  }
}

@keyframes mainVisualFirst {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes profileBackground {
  0% {
    opacity: 0;
    rotate: -10deg;
    transform: translateX(-40px);
  }
  100% {
    opacity: 1;
    rotate: 0deg;
    transform: translateX(0);
  }
}

@keyframes fuwafuwa {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
  100% {
    transform: translateY(0px);
  }
}

// 言語の設定
span{
  &.lang-ja{
    display: inline-block !important;
  }
  &.lang-en{
    display: none !important;
  }
}

.webInvitationView{
  font-family: 'Noto Serif JP', serif;
  color: #333;

  // reset
  * {
    box-sizing: border-box;
  }
  ::before,
  ::after {
    box-sizing: inherit;
  }
  p,
  table,
  blockquote,
  address,
  pre,
  iframe,
  form,
  figure,
  dl {
    margin: 0;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: inherit;
    line-height: inherit;
    font-weight: inherit;
    margin: 0;
  }
  ul,
  ol {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  dt {
    font-weight: bold;
  }
  dd {
    margin-left: 0;
  }
  hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
    border: 0;
    border-top: 1px solid;
    margin: 0;
    clear: both;
    color: inherit;
  }
  pre {
    font-family: monospace, monospace;
    font-size: inherit;
  }
  address {
    font-style: inherit;
  }
  a {
    background-color: transparent;
    text-decoration: none;
    color: inherit;
  }
  abbr[title] {
    border-bottom: none;
    text-decoration: underline;
    text-decoration: underline dotted;
  }
  b,
  strong {
    font-weight: bolder;
  }
  code,
  kbd,
  samp {
    font-family: monospace, monospace;
    font-size: inherit;
  }
  small {
    font-size: 80%;
  }
  sub,
  sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  img {
    border-style: none;
    vertical-align: bottom;
  }
  embed,
  object,
  iframe {
    border: 0;
    vertical-align: bottom;
  }
  button,
  input,
  optgroup,
  select,
  textarea {
    -webkit-appearance: none;
    appearance: none;
    vertical-align: middle;
    color: inherit;
    font: inherit;
    border: 0;
    background: transparent;
    padding: 0;
    margin: 0;
    outline: 0;
    border-radius: 0;
    text-align: inherit;
  }
  [type="checkbox"] {
    -webkit-appearance: checkbox;
    appearance: checkbox;
  }
  [type="radio"] {
    -webkit-appearance: radio;
    appearance: radio;
  }
  button,
  input {
    overflow: visible;
  }
  button,
  select {
    text-transform: none;
  }
  button,
  [type="button"],
  [type="reset"],
  [type="submit"] {
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
  }
  button[disabled],
  [type="button"][disabled],
  [type="reset"][disabled],
  [type="submit"][disabled] {
    cursor: default;
  }
  button::-moz-focus-inner,
  [type="button"]::-moz-focus-inner,
  [type="reset"]::-moz-focus-inner,
  [type="submit"]::-moz-focus-inner {
    border-style: none;
    padding: 0;
  }
  button:-moz-focusring,
  [type="button"]:-moz-focusring,
  [type="reset"]:-moz-focusring,
  [type="submit"]:-moz-focusring {
    outline: 1px dotted ButtonText;
  }
  select::-ms-expand {
    display: none;
  }
  option {
    padding: 0;
  }
  fieldset {
    margin: 0;
    padding: 0;
    border: 0;
    min-width: 0;
  }
  legend {
    color: inherit;
    display: table;
    max-width: 100%;
    padding: 0;
    white-space: normal;
  }
  progress {
    vertical-align: baseline;
  }
  textarea {
    overflow: auto;
  }
  [type="number"]::-webkit-inner-spin-button,
  [type="number"]::-webkit-outer-spin-button {
    height: auto;
  }
  [type="search"] {
    outline-offset: -2px;
  }
  [type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit;
  }
  label[for] {
    cursor: pointer;
  }
  details {
    display: block;
  }
  summary {
    display: list-item;
  }
  [contenteditable] {
    outline: none;
  }
  table {
    border-collapse: collapse;
    border-spacing: 0;
  }
  caption {
    text-align: left;
  }
  td,
  th {
    vertical-align: top;
    padding: 0;
  }
  th {
    text-align: left;
    font-weight: bold;
  }
  [hidden] {
    display: none;
  }

  // 共通
  .viewBlock_title{
    text-align: center;
    font-size: 20px;
    font-weight: 500;
    margin: 20px 0 40px;
    background-image: url('/images/webinvitation/theme_wa/title_bar.png');
    background-size: auto 8px;
    background-position: center;
    background-repeat: repeat-x;
    span{
      background: #FFF;
      padding: 0 20px;
    }
  }

  .viewBlock_title_sub{
    text-align: center;
    font-size: 18px;
    font-weight: 400;
    margin: 0 0 16px;
    background-size: auto 8px;
    background-position: center;
    background-repeat: repeat-x;
    position: relative;
    &::before{
      content: '';
      display: block;
      background: #EE645E;
      width: 100%;
      height: 1px;
      position: absolute;
      top: 0;
      bottom: 0;
      margin: auto;
    }
    span{
      display: inline-block;
      background: #FFF;
      padding: 0 12px;
      position: relative;
      z-index: 1;
    }
  }


  .link{
    display: inline-block;
    &.is-search{
      color: #B18A3E;
      font-size: 14px;
      position: relative;
      &::before{
        content: '';
        display: block;
        width: 24px;
        height: 24px;
        background-image: url('/images/webinvitation/theme_wa/icon-search.svg');
        background-size: contain;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        margin: auto;
      }
    }
  }

  .btn{
    &.is-delete{
      display: inline-flex;
      justify-content: center;
      align-items: center;
      color: #333;
      background: rgba(0, 0, 0, 0.1);
      font-size: 14px;
      position: relative;
      padding: 8px 28px;
      border-radius: 48px;
      transition: 0.35s ease-in-out;
      &:hover{
        background: rgba(0, 0, 0, 0.15);
      }
      &::before{
        content: '';
        display: inline-block;
        width: 24px;
        height: 24px;
        margin-right: 8px;
        background: currentColor;
        mask-image: url('/images/webinvitation/theme_wa/icon-remove.svg');
        mask-size: contain;
      }
    }
    &.is-add{
      display: flex;
      justify-content: center;
      align-items: center;
      color: #FFF;
      background: #B18A3E;
      font-size: 14px;
      position: relative;
      margin: 0 20px 92px;
      padding: 8px 15px;
      border-radius: 8px;
      transition: 0.35s ease-in-out;
      &:hover{
        filter: brightness(1.1);
      }
      &::before{
        content: '';
        display: inline-block;
        width: 28px;
        height: 28px;
        margin-right: 8px;
        background: #FFF;
        mask-image: url('/images/webinvitation/theme_wa/icon-plus.svg');
        mask-size: contain;
      }
    }
    &.is-confirm{
      display: block;
      color: #FFF;
      background: #EE645E;
      font-size: 18px;
      position: relative;
      margin: 34px auto 24px;
      padding: 19px 15px;
      border-radius: 40px;
      max-width: 276px;
      width: 100%;
      text-align: center;
      transition: 0.35s ease-in-out;
      &:hover{
        filter: brightness(1.1);
      }
    }
  }

  // メインビジュアル
  .mainVisual{
    &_background{
      position: relative;
      z-index: 1;
      &_image{
        display: flex;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }
      &_noImage{
        background-image: url('/images/webinvitation/theme_wa/mainVisual_bg.jpg');
        background-size: cover;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
      }
    }
    &_first{
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;
      max-width: 376px;
      background: #FFF8ED;
      z-index: 100;
      overflow: hidden;
      animation: 4.3s mainVisualFirst forwards ease-in-out;
      pointer-events: none;
      @include sp {
        max-width: 100%;
        position: fixed;
      }
      &_symbol{
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        height: 100%;
      }
      &_symbol_1{
        animation: 4s slideInToRight forwards ease-in-out;
        &::before,
        &::after{
          content: '';
          display: block;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
          animation: 2s fuwafuwa linear infinite;
        }
        &::before{
          width: 294px;
          height: 158px;
          background-image: url('/images/webinvitation/theme_wa/first_symbol_1.png');
          top: 6px;
          right: -60px;
        }
        &::after{
          width:247px;
          height: 133px;
          background-image: url('/images/webinvitation/theme_wa/first_symbol_2.png');
          top: 74px;
          right: -80px;
          animation-delay: 0.2s;
        }
      }
      &_symbol_2{
        animation: 4s slideInToLeft forwards ease-in-out;
        &::before,
        &::before{
          content: '';
          display: block;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
          width: 294px;
          height: 158px;
          background-image: url('/images/webinvitation/theme_wa/first_symbol_3.png');
          top: 42%;
          left: -100px;
          animation: 2.2s fuwafuwa infinite;
          animation-delay: 0.1s;
        }
      }
      &_symbol_3{
        animation: 4s slideInToRight forwards ease-in-out;
        &::before,
        &::after{
          content: '';
          display: block;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
          animation: 1.8s fuwafuwa infinite;
        }
        &::before{
          width: 286px;
          height: 154px;
          background-image: url('/images/webinvitation/theme_wa/first_symbol_4.png');
          right: -130px;
          bottom: 50px;
          animation-delay: 0.1s;
        }
        &::after{
          width:286px;
          height: 154px;
          background-image: url('/images/webinvitation/theme_wa/first_symbol_5.png');
          right: 10px;
          bottom: -10px;
        }
      }
    }
    &.is-animated{
      .mainVisual{
        &_title{
          animation: slideIn 0.5s ease-in-out;
          animation-fill-mode: both;
        }
        &_name{
          &_bride{
            animation: slideInFromLeft 0.5s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.1s;
          }
          &_groom{
            animation: slideInFromRight 0.5s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.2s;
          }
        }
      }
    }
    &_title{
      opacity: 0;
      transform: translateY(20px);
      display: inline-block;
      &_text{
        display: inline-block;
        &::before{
          content: '招待状';
        }
      }
      &_symbol{
        display: inline-block;
        &_start{
          &::before{
            content: '｛';
          }
        }
        &_end{
          &::before{
            content: '｝';
          }
        }
      }
    }
    &_name{
      &_bride{
        opacity: 0;
        transform: translateY(20px);
      }
      &_groom{
        opacity: 0;
        transform: translateY(20px);
      }
    }
    &_names{
      position: relative;
      &::before{
        content: '';
        display: block;
        width: 50px;
        height: 50px;
        background-image: url('/images/webinvitation/theme_wa/mainVisual_name_icon.svg');
        background-size: contain;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
      }
    }
    &_name{
      & + .name{
        margin-left: 70px;
      }
    }
  }

  //カウントダウン
  .countDown{
    background: #FFF8ED;
    &.is-animated{
      .countDown{
        &_box{
          animation: slideIn 0.8s ease-in-out;
          animation-fill-mode: both;
        }
        &_title{
          span{
            animation: passText 0s ease .4s 1 normal forwards;
            animation-delay: 1s;
            &::before{
              animation: passBar 0.8s ease 0s 1 normal forwards;
              animation-delay: 0.6s;
              background: #EC4D47;
            }
          }
        }
        &_blocks{
          animation: slideIn 0.8s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 0.8s;
        }
        &_dates{
          animation: slideIn 0.8s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1.2s;
        }
      }
    }
    &_box{
      background-image: url('/images/webinvitation/theme_wa/countDown_bg.png');
      background-size: 150%;
      border-image-source: url('/images/webinvitation/theme_wa/countDown_bar.png');
      border-image-slice: 20;
      border-image-width: 10px;
      border-image-outset: 0px;
      border-image-repeat: repeat;
      opacity: 0;
      transform: translateX(20px);
      &::before{
        content: '';
        display: block;
        width: 100%;
        height: 58px;
        background-image: url('/images/webinvitation/theme_wa/countDown_bar_top.png');
        background-size: auto 58px;
        background-position: center;
        background-repeat: repeat-x;
        position: absolute;
        left: 0;
        right: 0;
        bottom: -9px;
        margin: auto;
      }
    }
    .countDown_date,
    .countDown_block_value{
      color: #EC4D47;
    }
    &_blocks{
      opacity: 0;
      transform: translateX(20px);
    }
    &_dates{
      opacity: 0;
      transform: translateX(20px);
    }
    &_title{
      color: #EC4D47;
      font-weight: bold;
      span{
        position: relative;
        visibility: hidden;
        transform: translate3d(0, 0, 0);
        &::before {
          content: '';
          display: inline-block;
          width: 0%;
          height: 100%;
          position: absolute;
          left: 0%;
          top: 0;
          bottom: 0;
          opacity: 1;
          visibility: visible;
          z-index: 1;
          transform: translate3d(0, 0, 0);
        }
      }
    }
    &_wrap{
      position: relative;
      &::before,
      &::after{
        content: '';
        display: block;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        position: absolute;
      }
    }
    &_symbol{
      position: relative;
    }
    &_symbol_1{
      &::before,
      &::after{
        content: '';
        display: block;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        position: absolute;
      }
      &::before{
        width: 137px;
        height: 74px;
        background-image: url('/images/webinvitation/theme_wa/countDown_symbol_1.png');
        top: -44px;
        right: -70px;
        opacity: 0;
        transform: translateX(20px);
      }
      &::after{
        width: 137px;
        height: 74px;
        background-image: url('/images/webinvitation/theme_wa/countDown_symbol_2.png');
        top: 4px;
        right: -100px;
        opacity: 0;
        transform: translateX(20px);
      }
      &.is-animated{
        &::before{
          animation: slideInFromRight 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1.5s;
        }
        &::after{
          animation: slideInFromRight 0.5s ease-in-out;
          animation-delay: 1.8s;
          animation-fill-mode: both;
        }
      }
    }
    &_symbol_6{
      &::before,
      &::after{
        content: '';
        display: block;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        position: absolute;
      }
      &::before{
        width: 137px;
        height: 74px;
        background-image: url('/images/webinvitation/theme_wa/countDown_symbol_3.png');
        bottom: 42px;
        left: -82px;
        opacity: 0;
        transform: translateX(-20px);
      }
      &::after{
        width: 115px;
        height: 62px;
        background-image: url('/images/webinvitation/theme_wa/countDown_symbol_4.png');
        bottom: 10px;
        left: -40px;
        opacity: 0;
        transform: translateX(-20px);
      }
      &.is-animated{
        &::before{
          animation: slideInFromLeft 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1.6s;
        }
        &::after{
          animation: slideInFromLeft 0.5s ease-in-out;
          animation-delay: 1.9s;
          animation-fill-mode: both;
        }
      }
    }
  }

  //メッセージ
  .message{
    background: #FFF8ED;
    padding: 20px;
    &.is-animated{
      .message{
        &_wrap{
          animation: slideIn 0.8s ease-in-out;
          animation-fill-mode: both;
        }
        &_title{
          animation: fadeIn 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 0.6s;
        }
        &_contents{
          animation: slideIn 0.8s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 0.8s;
        }
      }
    }
    &_wrap{
      background: #FFF;
      padding: 10px 0 32px;
      opacity: 0;
      transform: translateX(20px);
    }
    &_title{
      opacity: 0;
    }
    &_contents{
      min-height: 120px;
      font-size: 14px;
      position: relative;
      line-height: 1.8;
      position: relative;
      padding-bottom: 32px;
    }
    &_symbol{
      position: relative;
    }
    &_symbol_3{
      &::before{
        content: '';
        display: block;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        position: absolute;
        width: 149px;
        height: 80px;
        background-image: url('/images/webinvitation/theme_wa/message_symbol_1.png');
        top: -20px;
        right: -94px;
        opacity: 0;
        transform: translateX(20px);
      }
      &.is-animated{
        &::before{
          animation: slideInFromRight 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1s;
        }
      }
    }
    &_symbol_4{
      &::before,
      &::after{
        content: '';
        display: block;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        position: absolute;
      }
      &::before{
        width: 137px;
        height: 74px;
        background-image: url('/images/webinvitation/theme_wa/message_symbol_2.png');
        bottom: -38px;
        left: -56px;
        opacity: 0;
        transform: translateX(-20px);
      }
      &::after{
        width: 137px;
        height: 74px;
        background-image: url('/images/webinvitation/theme_wa/countDown_symbol_3.png');
        bottom: -62px;
        left: -110px;
        opacity: 0;
        transform: translateX(-20px);
      }
      &.is-animated{
        &::before{
          animation: slideInFromLeft 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1.1s;
        }
        &::after{
          animation: slideInFromLeft 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1.4s;
        }
      }
    }
  }

  // プロフィール
  .profile{
    background: #FFF8ED;
    padding: 20px;
    &.is-animated{
      .profile{
        &_wrap{
          animation: slideIn 0.8s ease-in-out;
          animation-fill-mode: both;
        }
        &_title{
          animation: fadeIn 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 0.6s;
        }
      }
    }
    &_wrap{
      background: #FFF;
      padding: 10px 0 48px;
      opacity: 0;
      transform: translateX(20px);
    }
    &_title{
      opacity: 0;
    }
    &_item{
      padding: 0 27px;
      margin-bottom: 62px;
      &:nth-child(1){
        .profile_item_image::before{
          background-image: url('/images/webinvitation/theme_wa/profile_item_bg_1.png');
        }
      }
      &:nth-child(2){
        .profile_item_image::before{
          background-image: url('/images/webinvitation/theme_wa/profile_item_bg_2.png');
        }
      }
      &:nth-child(odd){
        position: relative;
        &.is-animated{
          &::before{
            animation: slideInFromLeft 0.5s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 1.5s;
          }
          &::after{
            animation: slideInFromLeft 0.5s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 1.8s;
          }
        }
        &::before,
        &::after{
          content: '';
          display: block;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
        }
        &::before{
          width: 137px;
          height: 74px;
          background-image: url('/images/webinvitation/theme_wa/profile_symbol_1.png');
          top: 182px;
          left: -56px;
        }
        &::after{
          width: 115px;
          height: 62px;
          background-image: url('/images/webinvitation/theme_wa/profile_symbol_2.png');
          top: 220px;
          left: -90px;
        }
      }
      &:nth-child(even){
        position: relative;
        &.is-animated{
          &::before{
            animation: slideInFromRight 0.5s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 1.5s;
          }
          &::after{
            animation: slideInFromRight 0.5s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 1.8s;
          }
        }
        &::before,
        &::after{
          content: '';
          display: block;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
        }
        &::before{
          width: 137px;
          height: 74px;
          background-image: url('/images/webinvitation/theme_wa/profile_symbol_3.png');
          top: 182px;
          right: -56px;
        }
        &::after{
          width: 115px;
          height: 62px;
          background-image: url('/images/webinvitation/theme_wa/profile_symbol_4.png');
          top: 220px;
          right: -90px;
        }
      }
      &_image{
        margin-bottom: 20px;
        position: relative;
        opacity: 0;
        transform: translateY(20px);
        &.is-animated{
          animation: slideIn 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 0.5s;
          &::before{
            animation: 1s profileBackground forwards ease-in-out;
            animation-delay: 0.8s;
          }
        }
        &::before{
          content: '';
          display: block;
          width: 220px;
          height: 220px;
          background-image: url('/images/webinvitation/theme_wa/profile_item_bg_other.png');
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
          left: -8px;
          bottom: 8px;
          z-index: -1;
          opacity: 0;
          rotate: -10deg;
          transform: translateX(-40px);
        }
      }
      &_name{
        &.is-animated{
          .profile_item_name{
            &_position{
              animation: slideIn 0.5s ease-in-out;
              animation-fill-mode: both;
              animation-delay: 0.8s;
            }
            &_main{
              animation: slideIn 0.5s ease-in-out;
              animation-fill-mode: both;
              animation-delay: 0.9s;
            }
          }
        }
        &_position{
          font-size: 14px;
          text-align: center;
          margin-bottom: 4px;
          opacity: 0;
          transform: translateY(20px);
        }
        &_main{
          font-size: 20px;
          font-weight: bold;
          opacity: 0;
          transform: translateY(20px);
        }
      }
      &_text{
        color: #222;
        font-size: 14px;
        line-height: 1.8;
        opacity: 0;
        transform: translateY(20px);
        &.is-animated{
          animation: slideIn 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1.0s;
        }
      }
    }
  }

  // ギャラリー
  .gallery{
    background: #FFF8ED;
    padding: 20px 0;
    &.is-animated{
      .gallery{
        &_wrap{
          animation: slideIn 0.8s ease-in-out;
          animation-fill-mode: both;
        }
        &_title{
          animation: fadeIn 0.5s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1s;
        }
        &_slides{
          animation: slideIn 0.8s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1.4s;
        }
        &_thumbnail{
          animation: slideIn 0.8s ease-in-out;
          animation-fill-mode: both;
          animation-delay: 1.8s;
        }
      }
    }
    .gallery_title{
      span{
        background: #FFF8ED;
      }
    }
    &_wrap{
      opacity: 0;
      transform: translateY(20px);
    }
    &_title{
      opacity: 0;
    }
    &_slides{
      opacity: 0;
      transform: translateY(20px);
    }
  }

  // スライド
  .swiper-button-prev, .swiper-rtl .swiper-button-next{
    left: 8px;
  }
  .swiper-button-next, .swiper-rtl .swiper-button-prev{
    right: 8px;
  }
  .swiper-slide-thumb-active{
    img{
      box-sizing: border-box;
      border: 1px solid #EE645E;
    }
  }
  .swiper-button-next, .swiper-button-prev{
    width: 40px;
    height: 40px;
    &::after{
      content: '';
      display: block;
      width: 40px;
      height: 40px;
    }
  }
  .swiper-button-prev, :host(.swiper-rtl) .swiper-button-next{
    &::after{
      content: '';
      background-image: url('/images/webinvitation/theme_wa/icon_slide_arrow_left.png');
      background-size: cover;
    }
  }
  .swiper-button-next{
    &::after{
      background-image: url('/images/webinvitation/theme_wa/icon_slide_arrow_right.png');
      background-size: cover;
    }
  }
  .swiper-horizontal>.swiper-pagination-bullets, .swiper-pagination-bullets.swiper-pagination-horizontal, .swiper-pagination-custom, .swiper-pagination-fraction{
    position: static;
  }
  .swiper-pagination-bullet-active{
    background: #EE645E;
  }

  // インフォメーション
  .information{
    background: #FFF8ED;
    padding: 20px;
    .information_title{
      opacity: 0;
      margin-bottom: 30px;
    }
    .information_title_sub{
      opacity: 0;
    }
    hr{
      border-top: none;
      border-bottom: 1px solid #EE645E;
    }
    &_box{
      background: #FFF;
      padding: 10px 0 40px;
      opacity: 0;
      transform: translateY(20px);
      & + .information_box{
        margin-top: 20px;
      }
      &.is-animated{
        animation: slideIn 0.6s ease-in-out;
        animation-fill-mode: both;
        .information{
          &_title{
            animation: fadeIn 0.8s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.4s;
          }
          &_date_label{
            animation: slideIn 0.8s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.6s;
          }
          &_date_values{
            animation: slideIn 0.8s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.7s;
          }
        }
      }
    }
    &_date{
      color: #222;
      margin-bottom: 40px;
      &_label{
        color: #B18A3E;
        opacity: 0;
        transform: translateY(20px);
      }
      &_values{
        opacity: 0;
        transform: translateY(20px);
      }
    }
    &_block{
      border: 1px solid rgba(238, 100, 94, 0.3);
      border-radius: 4px;
      padding: 16px 20px;
      margin: 0 auto 12px;
      width: calc(100% - 40px);
      opacity: 0;
      transform: translateY(20px);
      &.is-animated{
        animation: slideIn 0.6s ease-in-out;
        animation-fill-mode: both;
        animation-delay: 0.7s;
        .information{
          &_title_sub{
            animation: fadeIn 0.8s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.9s;
          }
          &_block{
            &_time{
              animation: slideIn 0.8s ease-in-out;
              animation-fill-mode: both;
              animation-delay: 1.2s;
            }
            &_address{
              animation: slideIn 0.8s ease-in-out;
              animation-fill-mode: both;
              animation-delay: 1.2s;
            }
            &_text{
              animation: slideIn 0.8s ease-in-out;
              animation-fill-mode: both;
              animation-delay: 1.2s;
            }
          }
        }
      }
      &.information_block_detail{
        border: none;
        background: #FBF0F0;
        .information_title_sub{
          span{
            background: #FBF0F0;
          }
        }
      }
      &_time{
        opacity: 0;
        transform: translateY(20px);
      }
      &_address{
        opacity: 0;
        transform: translateY(20px);
        &_link{
          &_url{
            color: #EE645E;
          }
        }
      }
      &_text{
        opacity: 0;
        transform: translateY(20px);
        a{
          color: #EE645E;
        }
      }
    }
  }

  // 回答
  .guestAnswer{
    color: #FFF;
    background-image: url('/images/webinvitation/theme_wa/guestAnswer_bg.png');
    background-size: 375px;
    padding: 20px;
    &_header{
      padding-top: 40px;
      margin-bottom: 40px;
      text-align: center;
      &.is-animated{
        .guestAnswer_header{
          &_title{
            animation: slideIn 0.6s ease-in-out;
            animation-fill-mode: both;
          }
          &_sub_title{
            animation: slideIn 0.6s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.1s;
          }
          &_text{
            animation: slideIn 0.6s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.2s;
          }
          &_caution{
            animation: slideIn 0.6s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.3s;
          }
        }
      }
      &_title{
        font-size: 30px;
        font-weight: bold;
        margin-bottom: 12px;
        padding-top: 75px;
        position: relative;
        letter-spacing: 6px;
        opacity: 0;
        transform: translateY(20px);
        &::before{
          content: '';
          display: block;
          width: 50px;
          height: 50px;
          background-image: url('/images/webinvitation/theme_wa/guestAnswer_icon.svg');
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          margin: auto;
        }
      }
      &_sub_title{
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
        opacity: 0;
        transform: translateY(20px);
      }
      &_text{
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        margin-bottom: 10px;
        opacity: 0;
        transform: translateY(20px);
        &_limit{
          font-size: 20px;
          font-weight: bold;
          text-decoration: underline;
        }
      }
      &_caution{
        font-size: 14px;
        line-height: 1.5;
        background: rgba(255,255,255,0.2);
        padding: 8px 0;
        opacity: 0;
        transform: translateY(20px);
      }
    }
    .information_title_sub{
      &::before{
        background: #FFF;
      }
      span{
        background-image: url('/images/webinvitation/theme_wa/guestAnswer_bg.png');
        background-size: 375px;
      }
    }

    &_answer{
      width: 100%;
      margin-bottom: 34px;
      &_box{
        padding-bottom: 16px;
        opacity: 0;
        transform: translateY(20px);
        &.is-animated{
          animation: slideIn 0.6s ease-in-out;
          animation-fill-mode: both;
        }
        &_flex{
          display: flex;
          justify-content: space-between;
          align-items: center;
          max-width: 335px;
          width: 100%;
          margin: 0 auto;
        }
        input{
          display: none;
          &:checked{
            & + .guestAnswer_answer_box_radio_wrap{
              &::before{
                width: 100px;
                height: 100px;
                border: 5px solid #FFF;
              }
            }
          }
        }
        &_radio{
          cursor: pointer;
          &_wrap{
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 100px;
            height: 100px;
            position: relative;
            &::before{
              content: '';
              display: block;
              width: 0px;
              height: 0px;
              background: rgba(255,255,255,0.2);
              border: 0 solid #FFF;
              border-radius: 50px;
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              margin: auto;
              transition: 0.35s ease-in-out;
              box-sizing: border-box;
            }
            &:hover{
              &::before{
                width: 80px;
                height: 80px;
                border: 3px solid #FFF;
                background: rgba(255,255,255,0.1);
              }
            }
          }
          &_title{
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 2px;
          }
          &_sub{
            font-size: 12px;
          }
        }
      }
    }

    &_box{
      color: #222;
      background: #FFF;
      padding: 10px 0 40px;
      font-size: 14px;
      font-family: Arial, Helvetica, sans-serif;
      box-sizing: border-box;
      opacity: 0;
      transform: translateY(20px);
      &.is-animated{
        animation: slideIn 0.6s ease-in-out;
        animation-fill-mode: both;
      }
      hr{
        border-color: #D9D9D9;
        margin: 24px 0;
      }
      & > hr{
        margin: 0;
      }
      &_wrap{
        padding: 34px 20px 10px;
      }
      &_label, 
      .guestAnswer_box_input .title {
        color: #333;
        font-weight: bold;
        margin-bottom: 12px;
        .guestAnswer_required{
          color: #FF1B1B;
        }
        &[data-required="true"] {
          &:after {
            content: "*";
            margin-left: 5px;
            vertical-align: top;
            font-size: 12px;
            color: #E65C7A;
          }
        }
      }
      &_row{
        margin-bottom: 24px;
        padding-bottom: 24px;
        border-bottom: 1px solid var(--Gray, #D9D9D9);
        &.is-noBorder{
          border-bottom: none;
          padding: 0;
          & + .guestAnswer_box_row:not(.is-noBorder) {
            padding-top: 24px;
            border-top: 1px solid var(--Gray, #D9D9D9);
          }
        }
        &.pb-68 {
          & + .guestAnswer_box_row:not(.is-noBorder){
            margin-top: 24px;
          }
        }
        &_flex{
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        &_text{
          font-size: 13px;
          line-height: 1.6;
          color: #222;
          margin-bottom: 12px;
        }
      }
      &_unit{
        font-size: 14px;
        margin-left: 4px;
        & + *{
          margin-left: 12px;
        }
      }
      &.zip{
        label{
          max-width: 160px;
        }
      }
      &_button,
      .search{
        display: inline-block;
        font-size: 14px;
        line-height: 1;
        color: #FFF;
        background: #B18A3E;
        padding: 20px 12px;
        border-radius: 4px;
        white-space: nowrap;
        margin-left: 12px;
        transition: 0.35s ease-in-out;
        &:hover{
          filter: brightness(1.1);
        }
      }
      .search{
        margin-top: 20px;
      }
      &_input{
        width: 100%;
        input,
        textarea {
          width: 100%;
          padding: 18px 16px;
          font-size: 16px;
          line-height: 1;
          border: 1px solid #D9D9D9;
          border-radius: 4px;
        }
        textarea {
          height: 5em;
          min-height: 0;
        }
        & + .guestAnswer_box_input,
        .guestAnswer_box_date .unit label{
          margin-left: 8px;
        }
        &.guestAnswer_box_date .inputDate {
          display: flex;
          align-items: flex-end;
          .unit {
            margin-bottom: 10px;
          }
        }
        &_large{
          min-width: 90px;
        }
      }
      &_date {
        .xs{
          max-width: 80px;
        }
        .unit{
          font-size: 14px;
          margin: 0 0 0 4px;
          & + *{
            margin-left: 12px;
          }
          & + .xs{
            max-width: 62px;
          }
        }
      }
      &_file{
        width: 100%;
        input{
          display: none;
        }
        &_button{
          display: block;
          position: relative;
          font-size: 14px;
          line-height: 1;
          background: rgba(0, 0, 0, 0.05);
          padding: 18px 18px 18px 42px;
          border-radius: 8px;
          text-align: center;
          cursor: pointer;
          transition: 0.35s ease-in-out;
          &:hover{
            background: rgba(0, 0, 0, 0.1);
          }
          &::before{
            content: '';
            display: block;
            width: 24px;
            height: 24px;
            background-image: url('/images/webinvitation/theme_wa/icon-plus.svg');
            background-size: contain;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 16px;
            margin: auto;
          }
        }
      }
      &_textarea{
        width: 100%;
        textarea{
          width: 100%;
          min-height: 80px;
          padding: 18px 16px;
          font-size: 16px;
          line-height: 1;
          border: 1px solid #D9D9D9;
          border-radius: 4px;
          resize: none;
        }
      }
      &_select{
        width: 100%;
        position: relative;
        &::before{
          content: '';
          display: block;
          height: calc(9px / 2);
          width: 9px;
          clip-path: polygon(0 0, 100% 0, 50% 100%);
          background: #333;
          position: absolute;
          top: 0;
          bottom: 0;
          right: 20px;
          margin: auto;
          pointer-events: none;
        }
        select{
          width: 100%;
          padding: 18px 16px;
          font-size: 16px;
          line-height: 1;
          border: 1px solid #D9D9D9;
          border-radius: 4px;
        }
        & + .guestAnswer_box_select{
          margin-left: 8px;
        }
      }
      .selectWrap{
        select{
          width: 100%;
          padding: 0 16px;
          font-size: 16px;
          line-height: 1;
          border: 1px solid #D9D9D9;
          border-radius: 4px;
          height: 56px;
        }
      }
      &_radio{
        width: 100%;
        cursor: pointer;
        & + .guestAnswer_box_radio{
          margin-left: 8px;
        }
        input{
          display: none;
          &:checked{
            & + .guestAnswer_box_radio_wrap{
              background: #FFF;
              border: 2px solid #EE645E;
              padding: 14px 10px;
            }
          }
        }
        &_wrap{
          display: block;
          box-sizing: content-box;
          padding: 14px 10px;
          text-align: center;
          font-size: 16px;
          line-height: 1;
          color: #333;
          background: rgba(0, 0, 0, 0.05);
          border: 2px solid transparent;
          border-radius: 8px;
          position: relative;
          transition: 0.35s ease-in-out;
          &:hover{
            background: rgba(0, 0, 0, 0.1);
          }
        }
      }
      &_checkbox{
        display: block;
        width: 100%;
        position: relative;
        cursor: pointer;
        & + .guestAnswer_box_checkbox{
          margin-top: 8px;
        }
        input{
          display: none;
          &:checked{
            & + .guestAnswer_box_checkbox_wrap{
              &::before{
                background: #EE645E;
                border: 2px solid #EE645E;
              }
            }
          }
        }
        &_wrap{
          font-size: 16px;
          padding-left: 29px;
          &::before{
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(0, 0, 0, 0.6);
            background-color: #fff;
            border-radius: 3px;
            box-sizing: border-box;
          }
          &::after{
            content: '';
            position: absolute;
            top: 2px;
            left: 6px;
            width: 6px;
            height: 11px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
          }
        }
      }
      &_radiobox{
        display: block;
        width: 100%;
        margin-bottom: 8px;
        position: relative;
        cursor: pointer;
        & + .guestAnswer_box_radiobox{
          margin-top: 8px;
        }
        input{
          display: none;
          &:checked{
            & + .guestAnswer_box_radiobox_wrap{
              &::before{
                border: 2px solid #EE645E;
              }
              &::after{
                background: #EE645E;
              }
            }
          }
        }
        &_wrap{
          font-size: 16px;
          padding-left: 29px;
          &::before{
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 16px;
            height: 16px;
            border: 2px solid #D9D9D9;
            background-color: #fff;
            border-radius: 16px;
            box-sizing: border-box;
            transition: 0.35s ease-in-out;
          }
          &::after{
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            width: 8px;
            height: 8px;
            background-color: transparent;
            border-radius: 16px;
            box-sizing: border-box;
            transition: 0.35s ease-in-out;
            zoom: 0;
          }
        }
      }
      &_remove{
        text-align: center;
        margin: 32px 0 24px;
      }
    }
    .link{
      display: inline-block;
      color: #B18A3E;
      margin-top: 12px;
      padding-left: 28px;
      &:hover{
        text-decoration: underline;
      }
    }
    &_title{
      &_sub{
        font-size: 18px;
        text-align: center;
        margin-bottom: 48px;
        span{
          position: relative;
          display: inline-block;
          padding: 0 26px;
          &::before,
          &::after{
            content: '';
            width: 18px;
            height: 1px;
            background: #9C9C9C;
            position: absolute;
            top: 0;
            bottom: 0;
            margin: auto;
          }
          &::before{
            left: 0;
          }
          &::after{
            right: 0;
          }
        }
      }
    }
  }

  .blockGuests {
    padding-bottom: 40px;
    .blockTitle {
      color: var(---font, #222);
      text-align: center;
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      letter-spacing: 1.8px;
      margin-bottom: 40px;
      &:before ,
      &:after {
        content: " ";
        display: inline-block;
        width: 18px;
        height: 1px;
        vertical-align: middle;
        margin: 0 10px;
        background: var(--Gray_dark, #9C9C9C);
      }
    }
  }

  .inputGuestType .inputRadio ,
  .inputGender .inputRadio {
    font-size: 0;
    label {
      display: inline-block;
      width: 30.33%;
      margin-right: 3%;
      cursor: default;
      &:last-child {
        margin-right: 0;
      }
      > span {
        display: block;
        width: 100%;
        text-align: center;
        margin: 0;
        padding: 0;
        background: #000;
        border-radius: 8px;
        padding: 12px 0;
        font-size: 16px;
        background: #f4f4f4;
        border: 2px solid #f4f4f4;
        cursor: pointer;
        &:before {
          display: none;
          width: 0;
          height: 0;
        }
      }
      input:checked + span {
        border-color: #EE645E;
        background: #fff;
        &:after {
          display: none;
        }
      }
      input:disabled + span {
        opacity: 1;
      }
    }
  }
  .inputGuestType .inputRadio {
    label {
      width: 47%;
    }
  }

  .inputAttendance .inputTitle {
    color: var(--white-100, var(--white-100, #FFF));
    text-align: center;
    font-size: 18px;
    margin-bottom: 20px;
    overflow: hidden;
    span{
      position: relative;
      &:before ,
      &:after {
        content: " ";
        height: 1px;
        width: 100vw;
        background: #fff;
        display: block;
        position: absolute;
        top: 50%;
        left: calc(100% + 10px);
        right: auto;
      }
      &:after {
        left: auto;
        right: calc(100% + 10px);
      }
    }
  }
  .inputAttendance .inputRadio {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-around;
    position: relative;
    .input-error {
      position: absolute;
      bottom: 0;
      left: 0;
      color: #fff;
    }
    label {
      display: block;
      width: 27.33%;
      position: relative;
      cursor: default;
      margin: 0;
      &:after {
        content: " ";
        padding-top: 100%;
        height: 0;
        display: none;
      }
      &:last-child {
        margin-right: 0;
      }
      > span {
        cursor: pointer;
        display: block;
        width: 100%;
        position: relative;
        text-align: center;
        margin: 0;
        padding: 0;
        font-size: 16px;
        background: none;
        color: #fff;
        border-radius: 50%;
        border: 6px solid rgba(#fff, 0);
        transition: 0.3s ease;
        &:before {
          display: none;
          width: 0;
          height: 0;
          height: 0;
        }
        &:after {
          content: " ";
          display: block;
          width: 100%;
          padding-top: 100%;
          height: 0;
        }
        .txt {
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          text-align: center;
          transform: translateY(-50%);
          .ja {
            font-size: 18px;
            display: block;
          }
          .en {
            display: block;
            font-size: 12px;
            margin-top: 5px;
          }
        }
      }
      // &:hover > span ,
      input:checked + span {
        position: static;
        border-color: #fff;
        background: rgba(#fff, .2);
        &:after {
          display: block;
          background: none;
          position: static;
        }
      }
    }
  }

  .inputImages {
    white-space: nowrap;
    overflow-x: auto;
    margin-bottom: 10px;
  }
  .inputImages .inputRadio {
    .img {
      display: block;
      background-position: center center;
      background-repeat: no-repeat;
      background-size: cover;
      width: 104px;
      // height: 104px;
      margin-bottom: 2px;
      border: 1px solid var(--Gray_light, #F4F4F4);
    }
    .img-01 {
      background-image: url(@/assets/images/web-invitation/guest-answer-image-01.png);
    }
    .img-02 {
      background-image: url(@/assets/images/web-invitation/guest-answer-image-02.png);
    }
    .img-03 {
      background-image: url(@/assets/images/web-invitation/guest-answer-image-03.png);
    }
    label {
      display: inline-block;
      cursor: default;
      > span {
        cursor: pointer;
        margin: 0;
        padding: 0;
        &:before {
          display: none;
        }
      }
      // &:hover > span ,
      input:checked + span {
        &:after {
          display: none;
        }
        .img {
          position: relative;
          &:after {
            content: " ";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: 4px solid #EE645E;
          }
        }
      }
    }

  }

  .imageList{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    &_item{
      width: 106px;
      min-width: 106px;
      cursor: pointer;
      transition: 0.35s ease-in-out;
      scroll-snap-align: start;
      &:hover{
        opacity: 0.8;
      }
      & + .imageList_item{
        margin-left: 8px;
      }
      &_image{
        margin-bottom: 8px;
        position: relative;
        img{
          width: 100%;
          height: auto;
          object-fit: cover;
        }
        &_search{
          display: block;
          width: 24px;
          height: 24px;
          background-image: url('/images/webinvitation/theme_wa/guestAnswer_icon_search.png');
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          position: absolute;
          top: 5px;
          right: 5px;
        }
      }
      &_title{
        font-size: 12px;
      }
    }
  }

  // フリーフィールド
  .freeField{
    background: #FFF8ED;
    padding: 20px;
    &_box{
      background: #FFF;
      padding: 10px 0 32px;
      opacity: 0;
      transform: translateX(20px);
      &.is-animated{
        animation: slideIn 0.8s ease-in-out;
        animation-fill-mode: both;
        .freeField{
          &_title{
            animation: fadeIn 0.5s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.6s;
          }
          &_contents{
            animation: slideIn 0.8s ease-in-out;
            animation-fill-mode: both;
            animation-delay: 0.8s;
            a{
              color: #EE645E;
              text-decoration: underline;
              &:hover{
                text-decoration: none;
              }
            }
          }
        }
      }
    }
    &_title{
      opacity: 0;
    }
    &_contents{
      opacity: 0;
    }
  }

  
}