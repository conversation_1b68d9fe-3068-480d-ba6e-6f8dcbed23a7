export function useSpMethodsAccordion(e) {
  if (window.innerWidth <= 767) {
    useMethodsAccordion(e);
  }
};

export function useMethodsAccordion(e) {
  let acdindex = Array.prototype.indexOf.call(document.querySelectorAll('.acdtrg') , e.currentTarget);
  let acdsbj = document.querySelectorAll('.acdsbj').item(acdindex);
  if (acdsbj.classList.contains("is-close")) {
    acdsbj.classList.remove("is-close");
    acdsbj.classList.add("is-addheight");
    let height = acdsbj.clientHeight;
    acdsbj.style.height = height + "px";
    acdsbj.classList.add("is-open");
    acdsbj.classList.add("is-close");
    setTimeout(() => acdsbj.classList.remove("is-addheight"), 5);
    setTimeout(() => acdsbj.classList.remove("is-close"), 5);
    setTimeout(() => acdsbj.style.height = "auto", 350);
    e.target.classList.remove("is-close");
  }
  else {
    let height = acdsbj.clientHeight;
    acdsbj.style.height = height + "px";
    setTimeout(() => acdsbj.classList.remove("is-open"), 5);
    setTimeout(() => acdsbj.classList.add("is-close"), 5);
    setTimeout(() => e.target.classList.add("is-close"), 5);
    setTimeout(() => acdsbj.style.height = "auto", 350);
  }
};

export function useMethodsTabChange(e) {
  let tabtrg = e.target.closest(".tabtrg");
  let tabindex = Array.prototype.indexOf.call(document.querySelectorAll('.tabtrg') , tabtrg);
  let tabsbj = document.querySelectorAll('.tabsbj').item(tabindex);
  let btnindex = Array.prototype.indexOf.call(tabtrg.children , e.currentTarget);
  let controlWindow = document.querySelectorAll(".controlWindow");
  controlWindow.forEach(element => element.classList.add('close') );
  Array.from(tabtrg.children).forEach(function(element) { element.classList.remove('is-active'); });
  e.currentTarget.classList.add("is-active");

  Array.from(tabsbj.children).forEach(function(element) { element.classList.remove('is-active'); });
  tabsbj.children[btnindex].classList.add("is-active");
}

let setClosing , setClose;
export function engageToast(data) {
  const target = document.getElementById("toast");
  target.innerHTML = data;
  target.classList.remove('close');
  clearTimeout(setClosing);
  clearTimeout(setClose);
  setTimeout(() => {target.classList.remove('closing')}, 10);
  setClosing = setTimeout(() => {target.classList.add('closing')}, 5000);
  setClose = setTimeout(() => {target.classList.add('close')}, 5350);
};

export function engageModalWindow(e) {
  let target = e;
  document.getElementById(target).classList.remove('close');
  setTimeout(() => {document.getElementById(target).classList.remove('closing')}, 10);
};

export function engageControlWindow(e) {
  let _self = e.currentTarget;
  let target = document.getElementById(_self.dataset.modalwindow);
  let controlWindow = document.querySelectorAll(".controlWindow");
  target.classList.add('standby');
  // 【！】後で画面端にかかる場合の位置調整入れる
  if (target.classList.contains("left")) {
    target.style.left = (_self.getBoundingClientRect().left + _self.clientWidth) + 'px';
  }
  else if (target.classList.contains("center")) {
    target.style.left = (_self.getBoundingClientRect().left + (_self.clientWidth / 2) - (target.clientWidth / 2)) + 'px';
  }
  else {
    target.style.left = (_self.getBoundingClientRect().left - target.clientWidth + _self.clientWidth) + 'px';
  }
  if (target.classList.contains("bottom")) {
    target.style.top = (_self.getBoundingClientRect().top + window.pageYOffset - target.clientHeight) + 'px';
  }
  else {
    target.style.top = (_self.getBoundingClientRect().top + window.pageYOffset + _self.clientHeight) + 'px';
  }
  controlWindow.forEach(element => element.classList.add('close') );
  target.classList.remove('close','standby');
};


export function exclusiveSelect(e) {
  let lists = e.currentTarget.closest("ul").children;
  for (let i = 0; i < lists.length; i++) {
    lists[i].classList.remove("is-selected");
  }
  e.target.closest(".items").classList.add("is-selected");
};
