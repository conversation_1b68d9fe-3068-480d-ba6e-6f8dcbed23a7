// _mixin.scss
$pc: 768px;
$sp: 767px;

@mixin pc {
  @media screen and (min-width: ($pc)) {
    @content;
  }
}

@mixin sp {
  @media screen and (max-width: ($sp)) {
    @content;
  }
}


// Hover Opacity
@mixin H_opacity($opa:0.7) { // ホバー時の透過設定 引数省略で0.7
	-webkit-transition: opacity 0.3s ease;
	transition: opacity 0.3s ease;
	&:hover {
		opacity: $opa;
	}
}


// List
@mixin Plane_list {
	margin: 0;
	padding: 0;
	list-style: none;
}


// Basically Before & After Format
@mixin BA($pos:absolute) { // Before , After基本セット 引数省略ならposition: absolute;
	content: "";
	display: inline-block;
	position: $pos;
	top: 50%;
	background-repeat: no-repeat;
	background-position: center center;
	background-size: contain;
	transform: translateY(-50%);
}


// 文字を省略して末尾を三点リーダー
@mixin lineClamp($clamp: 3) {
  -webkit-line-clamp: $clamp;
          line-clamp: $clamp;
  -webkit-box-orient: vertical;
          box-orient: vertical;
  display: -webkit-box;
  // display: box;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
}
