// favori theme



// メインカラー（エメラルドグリーン）
$color-main: #2F587C;

// アクセントカラー
$color-accent: #B18A3E;

// メイン_dark
$color-maindark: #6F8B81;

// 49454F（black_Light）
$color-blackLight: #49454F;

// 警告色
$color-alert: #FF1B1B;

// 注意色2
$color-alert2: #E65C7A;


// メインテキスト
$color-maintext: #000000;

// 白テキスト
$color-whitetext: #FFFFFF;

// 黒テキスト2
$color-blacktext2: #333333;

// 黒テキスト3 (black_sub)
$color-blacktext3: #5A5A5A;

// 黒テキスト4
$color-blacktext4: #4A4A4A;

// 黒テキスト5
$color-blacktext5: #666666;

// 黒テキスト6
$color-blacktext6: #797979;

// グレーテキスト
$color-graytext: #999999;

// Gray_dark テキスト
$color-graytext2: #9C9C9C;

// グレー透過テキスト
$color-graytext3: rgba(0, 0, 0, 0.6);


// プレースホルダー内テキスト
$color-placeholder: rgba(51, 51, 51, 0.4);




// 標準バックグラウンドカラー
$color-mainbackground: #FFFFFF;

/* メイン_ライト 背景用カラー */
$color-lightbackground: #EFF8FF;


/* Gray_light */
$color-lightgray: #F4F4F4;

/* Gray border */
$color-grayborder: #D9D9D9;

/* Gray_light2 */
$color-lightgray2: #FAFAFA;

/* Blue_2 */
$color-blue2: #5EA2E1;

/* pink */
$color-pink: #FAA0B4;


$serif_font: "Hiragino Mincho ProN";
$en_font: 'Roboto', sans-serif;
