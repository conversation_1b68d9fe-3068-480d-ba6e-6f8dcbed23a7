@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}
[data-animation=fadeIn],
.animation_fadeIn {
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
}

[data-animation=slideIn],
.animation_slideIn {
  animation: slideIn 0.5s ease-in-out;
  animation-fill-mode: both;
}

[data-animation=slideInGroup] > *,
.animation_slideInGroup > * {
  animation: slideIn 0.35s ease-in-out;
  animation-fill-mode: both;
}
[data-animation=slideInGroup] > *:nth-of-type(1),
.animation_slideInGroup > *:nth-of-type(1) {
  animation-delay: 0.25s;
}
[data-animation=slideInGroup] > *:nth-of-type(2),
.animation_slideInGroup > *:nth-of-type(2) {
  animation-delay: 0.35s;
}
[data-animation=slideInGroup] > *:nth-of-type(3),
.animation_slideInGroup > *:nth-of-type(3) {
  animation-delay: 0.45s;
}
[data-animation=slideInGroup] > *:nth-of-type(4),
.animation_slideInGroup > *:nth-of-type(4) {
  animation-delay: 0.55s;
}
[data-animation=slideInGroup] > *:nth-of-type(5),
.animation_slideInGroup > *:nth-of-type(5) {
  animation-delay: 0.65s;
}
[data-animation=slideInGroup] > *:nth-of-type(6),
.animation_slideInGroup > *:nth-of-type(6) {
  animation-delay: 0.75s;
}
[data-animation=slideInGroup] > *:nth-of-type(7),
.animation_slideInGroup > *:nth-of-type(7) {
  animation-delay: 0.85s;
}
[data-animation=slideInGroup] > *:nth-of-type(8),
.animation_slideInGroup > *:nth-of-type(8) {
  animation-delay: 0.95s;
}
[data-animation=slideInGroup] > *:nth-of-type(9),
.animation_slideInGroup > *:nth-of-type(9) {
  animation-delay: 1.05s;
}
[data-animation=slideInGroup] > *:nth-of-type(10),
.animation_slideInGroup > *:nth-of-type(10) {
  animation-delay: 1.15s;
}
[data-animation=slideInGroup] > *:nth-of-type(11),
.animation_slideInGroup > *:nth-of-type(11) {
  animation-delay: 1.25s;
}
[data-animation=slideInGroup] > *:nth-of-type(12),
.animation_slideInGroup > *:nth-of-type(12) {
  animation-delay: 1.35s;
}
[data-animation=slideInGroup] > *:nth-of-type(13),
.animation_slideInGroup > *:nth-of-type(13) {
  animation-delay: 1.45s;
}
[data-animation=slideInGroup] > *:nth-of-type(14),
.animation_slideInGroup > *:nth-of-type(14) {
  animation-delay: 1.55s;
}
[data-animation=slideInGroup] > *:nth-of-type(15),
.animation_slideInGroup > *:nth-of-type(15) {
  animation-delay: 1.65s;
}
[data-animation=slideInGroup] > *:nth-of-type(16),
.animation_slideInGroup > *:nth-of-type(16) {
  animation-delay: 1.75s;
}
[data-animation=slideInGroup] > *:nth-of-type(17),
.animation_slideInGroup > *:nth-of-type(17) {
  animation-delay: 1.85s;
}
[data-animation=slideInGroup] > *:nth-of-type(18),
.animation_slideInGroup > *:nth-of-type(18) {
  animation-delay: 1.95s;
}
[data-animation=slideInGroup] > *:nth-of-type(19),
.animation_slideInGroup > *:nth-of-type(19) {
  animation-delay: 2.05s;
}

.modal-slide-enter-active, .modal-slide-leave-active {
  transition: 0.35s ease;
}
.modal-slide-enter-active .modalContainer,
.modal-slide-enter-active .modal_box, .modal-slide-leave-active .modalContainer,
.modal-slide-leave-active .modal_box {
  transition: 0.35s ease;
  transform: translateY(0px);
}
.modal-slide-enter-from, .modal-slide-leave-to {
  opacity: 0;
}
.modal-slide-enter-from .modalContainer,
.modal-slide-enter-from .modal_box, .modal-slide-leave-to .modalContainer,
.modal-slide-leave-to .modal_box {
  transform: translateY(20px);
}