@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

[data-animation="fadeIn"],
.animation_fadeIn {
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;
}

[data-animation="slideIn"],
.animation_slideIn {
  animation: slideIn 0.5s ease-in-out;
  animation-fill-mode: both;
}

// 複数要素の順番スライド
[data-animation="slideInGroup"],
.animation_slideInGroup {
  & > *{
    animation: slideIn 0.35s ease-in-out;
    animation-fill-mode: both;
    @for $i from 1 to 20 {
      &:nth-of-type(#{$i}) { animation-delay: 0.15s + 0.1s * $i; }
    }
  }
}


// 元からシャドウが付いているブロック
@mixin animation_shadow() {
  & {
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    transition: 0.35s ease-in-out;
    &:hover,
    &:focus {
      box-shadow: 0 4px 4px rgba(0,0,0,0.12);
    }
    &:active{
      box-shadow: none;
    }
  }
}

// ホバー時にシャドウが付くブロック
@mixin animation_shadow_hover() {
  & {
    transition: 0.35s ease-in-out;
    &:hover,
    &:focus {
      box-shadow: 0 4px 4px rgba(0,0,0,0.12);
    }
    &:active{
      box-shadow: none;
    }
  }
}

// モーダル
.modal-slide{
  &-enter-active,
  &-leave-active{
    transition: 0.35s ease;
    .modalContainer,
    .modal_box{
      transition: 0.35s ease;
      transform: translateY(0px);
    }
  }
  &-enter-from,
  &-leave-to {
    opacity: 0;
    .modalContainer,
    .modal_box{
      transform: translateY(20px);
    }
  }
}

