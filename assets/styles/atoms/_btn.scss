

.btn{
  display: inline-block;
  outline: none;
  cursor: pointer;
  appearance: none;
  font-size: 12px;
  line-height: 1.4;
  padding: 9px 20px;
  transition: 0.2s;
  text-decoration: none;
  text-align: center;
  border-radius: 4px;
  position: relative;
  &:hover{

  }
  &:active{

  }
  &-primary{
    background: $color-main;
    color: #fff;
    border: solid 1px $color-main;
    &:hover{
      opacity: .6;
      @include sp {
        opacity: 1;
      }
    }
    &:active{

    }
  }
  &-secondary{
    background: $color-accent;
    color: #fff;
    &:hover{
      opacity: .6;
      @include sp {
        opacity: 1;
      }
    }
    &:active{

    }
    &:disabled {
      background: #D9D9D9;
      &:hover{
        opacity: 1;
      }
    }
  }

  &-primary-outline {
    background: #fff;
    border: 1px solid $color-main;
    color: $color-main;
    &:hover {
      background: $color-main;
      color: #fff;
    }
    &.disabled ,
    &:disabled {
      border-color: #aaa;
      color: #aaa;
      cursor: default;
      background: #eee;
      &:hover {
        border-color: #aaa;
        color: #aaa;
        background: #eee;
      }
    }
  }

  &-secondary-outline {
    background: #fff;
    border: 1px solid $color-accent;
    color: $color-accent;
    &:hover {
      background: $color-accent;
      color: #fff;
    }
    &.disabled ,
    &:disabled {
      border-color: #aaa;
      color: #aaa;
      cursor: default;
      background: #eee;
      &:hover {
        border-color: #aaa;
        color: #aaa;
        background: #eee;
      }
    }
  }

  &-default {
    font-weight: 400;
    color: var(--text-black, #333);
    background: var(--black-5, rgba(0, 0, 0, 0.05));
    &:hover {
      opacity: .6;
      // background: $color-main;
      // color: #fff;
    }
    &.disabled ,
    &:disabled {
      border-color: #aaa;
      color: #aaa;
      cursor: default;
      background: #eee;
      &:hover {
        border-color: #aaa;
        color: #aaa;
        background: #eee;
      }
    }
  }

  &-default-outline {
    background: #fff;
    border: 1px solid var(--Gray, #D9D9D9);
    color: var(--49454Fblack_Light, #49454F);
    font-weight: 600;
    &:hover {
      opacity: .6;
      // background: $color-main;
      // color: #fff;
    }
    &.disabled ,
    &:disabled {
      border-color: #aaa;
      color: #aaa;
      cursor: default;
      background: #eee;
      &:hover {
        border-color: #aaa;
        color: #aaa;
        background: #eee;
      }
    }
  }

  &-attendance {
    border-radius: 4px;
    border: 1px solid #AD871E;
    background: #FBF5E6;
    color: #AD871E;
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.24px;
    padding: 2px 6px;
    min-width: 55px;
    &:before {
      content: " ";
      @extend .icn-img;
      @extend .icn-attendance-present;
    }
    &-absent {
      background: #F4F4F4;
      color: #9C9C9C;
      border-color: #9C9C9C;
      &:before {
        @extend .icn-attendance-absent;
      }
    }
    &-pending {
      background: #FFE9EE;
      color: #E65C7A;
      border-color: #E65C7A;
      &:before {
        @extend .icn-attendance-pending;
      }
    }
    &-null {
      background: none;
      border: none;
      &:before {
        display: none !important;
      }
    }
  }

  &.btn-sm{
    padding: 5px 13px;
    font-size: 14px;
  }
  &.btn-md{
    font-size: 14px;
    font-weight: 400;
    padding: 10px 20px;
  }
  &.btn-lg{
    font-size: 18px;
  }
  &.btn-rounded {
    border-radius: 100px;
  }
  &.btn-shadow {
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.12);
  }
  &.btn-block{
    display: block;
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }
  &.sp_btn-block{
    @include sp {
      display: block;
      width: 100%;
      padding-left: 0;
      padding-right: 0;
    }
  }

  .icn-left {
    display: inline-block;
    vertical-align: middle;
    line-height: 0;
    margin-top: -.1em;
    margin-right: 0.2em;
    margin-left: -0.2em;
    font-size: 1.6em;
  }
  .icn-right {
    display: inline-block;
    vertical-align: middle;
    line-height: 0;
    margin-top: -.1em;
    margin-left: 0.2em;
    margin-right: -0.2em;
    font-size: 1.6em;
  }
  .icn-pull-right {
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -.5em;
    line-height: 1;
    vertical-align: middle;
    font-size: 1.6em;
  }
}

span.btn-attendance {
  cursor: default;
}