.input-list {
  max-width: 460px;
  margin-left: auto;
  margin-right: auto;
  .input-item {
    display: flex;
    padding-top: 8px;
    padding-bottom: 8px;
    align-items: flex-start;
    .input-icn {
      width: 40px;
      display: flex;
      align-items: center;
      color: #9C9C9C;
      padding-top: 10px;
    }
    .intput-action {
      flex-shrink: 0;
      margin-left: 12px;
      width: 70px;
      display: flex;
      color: #5A5A5A;
      color: #9C9C9C;
      font-size: 14px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .btn-icn {
        padding: 0 5px;
        color: #5A5A5A;
        i {
          font-size: 26px;
        }
      }
      .btn {
        height: 100%;
      }
    }
  }
}

.profileImage{
  .img {
    overflow: hidden;
    border-radius: 50%;
    position: relative;
    position: relative;
    overflow: hidden;
    &:after {
      content: " ";
      display: block;
      padding-top: 100%;
    }
    img {
      height: 100%;
      width: auto;
      max-width: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      @supports ( object-fit: cover ) {
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        transform: none;
        object-fit: cover;
      }
    }
    .loading {
      padding: 0 !important;
      position: absolute !important;
      top: 50% !important;
      left: 50% !important;
      bottom: auto !important;
      right: auto !important;
      transform: translate(-50%, -50%) !important;
      white-space: nowrap !important;
      .icn {
        position: static !important;
        margin: 0 !important;
      }
    }
  }
  &.size--sm .img {
    .loading {
      i {
        font-size: 20px;
      }
      .txt {
        display: none;
      }
    }
  }
}