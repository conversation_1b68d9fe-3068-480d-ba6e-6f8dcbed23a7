
.label-default {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  background: var(--Gray_light, #F4F4F4);
}

.label-attendance {
  border-radius: 2px;
  background: #9C9C9C;
  color: #FFF;
  font-size: 14px;
  padding: 2px 4px;
  font-weight: 700;
  letter-spacing: 0.1em;
  display: inline-block;
  &[data-attendance="PRESENT"] ,
  &[data-attendance="出席"] {
    background: $color-main;
    > .txt { display: none;}
    &:before {
      content: " ";
      background: url(@/assets/images/icon-check-w.svg) no-repeat center center;
      background-color: $color-main;
      background-size: 12px;
      width: 14px;
      height: 14px;
      display: inline-block;
      vertical-align: middle;
      margin-right: 5px;
    }
    &:after {
      content: "ご出席";
    }
  }
  &[data-attendance="ABSENT"] ,
  &[data-attendance="欠席"] {
    > .txt { display: none;}
    &:after {
      content: "ご欠席";
    }
  }
  &[data-attendance="PENDING"] ,
  &[data-attendance="未定"],
  &[data-attendance="保留"] {
    > .txt { display: none;}
    // background: #9C9C9C;
    &:after {
      content: "保留";
    }
  }
}

.label-tag {
  border-radius: 16px;
  border: 1px solid var(--Gray, #D9D9D9);
  background: var(--Gray_light, #F4F4F4);
  color: var(--black, var(--text-black, #333));
  font-size: 11px;
  font-weight: 400;
  letter-spacing: 0.25px;
  display: inline-block;
  padding: 0 8px;
  margin-right: 5px;
}

.label-main {
  text-decoration: none;
  display: inline-block;
  vertical-align: middle;
  padding: 3px 10px;
  border-radius: 50px;
  border: 1px solid $color-main;
  background: #FFFFFF;
  color: $color-main;
  text-align: center;
  font-size: 10px;
  font-weight: 400;
  line-height: 1;
  letter-spacing: 0.2px;
}

.label-accent {
  text-decoration: none;
  display: inline-block;
  vertical-align: middle;
  padding: 3px 10px;
  border-radius: 50px;
  border: 1px solid $color-accent;
  background: #FFFFFF;
  color: $color-accent;
  text-align: center;
  font-size: 10px;
  font-weight: 400;
  line-height: 1;
  letter-spacing: 0.2px;
}


.label-danger {
  text-decoration: none;
  display: inline-block;
  vertical-align: middle;
  padding: 5px 6px;
  border-radius: 4px;
  border: 1px solid #E65C7A;
  background: #FFFFFF;
  color: #E65C7A;
  text-align: center;
  font-size: 12px;
  font-weight: 700;
  line-height: 1;
  letter-spacing: 0.2px;
  position: relative;
  .icn {
    font-size: 1.4em;
    vertical-align: middle;
    margin-top: -1px;
    line-height: 0;
  }
  @include sp {
    font-size: 10px;
    padding: 4px 6px;
  }
}