.link-accent ,
.link-main ,
.link-text ,
.link-disabled {
  cursor: pointer;
  text-decoration: none;
  padding: 0;
  &:hover{
    text-decoration: underline;
  }
  .icn-left {
    vertical-align: middle;
    font-size: 1.2em;
    &.icn-lg {
      font-size: 1.8em;
    }
  }
  &.size--lg {
    font-size: 16px;
  }
  &.size--md {
    font-size: 14px;
  }
  &.size--sm {
    font-size: 12px;
  }

  .icn-right {
    display: inline-block;
    vertical-align: middle;
    line-height: 0;
    margin-top: -.1em;
    margin-right: -0.2em;
    margin-left: 0.2em;
    font-size: 1.6em;
  }
}
.link-accent { 
  color: $color-accent; 
  display: inline-block;
}
.link-underline { 
  text-decoration: underline;
  &:hover{
    text-decoration: none;
  }
}
.link-main { 
  color: $color-main; 
}
.link-text {
  color: var(--49454Fblack_Light, #49454F);
  font-size: 14px;
}
.link-disabled {
  color: #D9D9D9;
  cursor: default;
  &:hover {
    text-decoration: none;
  }
}
.link-text {
  cursor: pointer;
  color: #49454F;
  text-decoration: none;
  &:hover{
    text-decoration: underline;
  }
  &.link-text-disabled {
    cursor: default;
    > * {
      opacity: .6;
    }
    &:hover{
      text-decoration: none;
    }
  }
}
.material-icons.icn-sm {
  vertical-align: middle;
  font-size: 12px;
}
.fz-sm {
  font-size: 14px;
}
.fz-xs {
  font-size: 12px;
}

.link-sort {
  position: relative;
  padding: 0 0 0 19px;
  color: $color-blacktext2;
  font-size: 11px;
  line-height: 100%;
  vertical-align: top;
  &::before {
    @include BA;
    left: 0;
    width: 11px;
    height: 18px;
    background: url(@/assets/images/icon-swap_vert.svg) no-repeat center center/contain;
  }
}

.link-edit {
  display: inline-block;
  font-size: 14px;
  line-height: 1.2;
  color: #B18A3E;
  padding: 10px 0;
  text-decoration: none;
  cursor: pointer;
  img{
    width: 18px;
    height: 18px;
    vertical-align: text-bottom;
  }
  &:hover{
    text-decoration: underline;
  }
}


.link-text-delete {
  position: relative;
  padding: 0 0 0 22px;
  color: $color-alert;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  text-decoration: none;
  cursor: pointer;
  &::before {
    @include BA;
    left: 0;
    width: 18px;
    height: 18px;
    background-image: url(@/assets/images/icon-delete.svg);
  }
}

.link-selectImage{
  display: block;
  font-size: 12px;
  margin: 0 0 16px;
  padding: 18px 2px 18px 62px;
  position: relative;
  transition: 0.35s;
  &:hover{
    opacity: 0.5;
  }
  &::before {
    @include BA;
    left: 0;
    width: 50px;
    height: 50px;
    background-image: url(@/assets/images/icon-add_image.svg);
    transition: transform 0.35s ease;
  }
}

.link-img {
  display: inline-block;
  transition: opacity 0.35s ease;
  &:hover {
    opacity: .6;
  }
}