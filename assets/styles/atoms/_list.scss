.list-info {
  li {
    padding-top: 10px;
    padding-bottom: 11px;
    border-bottom: 1px solid $color-grayborder;
  }
  dt {
    color: $color-graytext2;
    font-size: 10px;
    line-height: 16px;
    letter-spacing: 1.5px;
  }
  dd {
    color: var(--black, #333);
    font-size: 16px;
    letter-spacing: 0.15px;
    margin-top: 2px;
    min-height: 1.5em;
    line-height: 1.5;
    word-wrap: break-word;
  }
}

.list-price {
  li {
    padding-top: 18px;
    padding-bottom: 10px;
    border-bottom: 1px solid $color-grayborder;
  }
  dl {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  dt {
    color: $color-graytext2;
    font-size: 10px;
    letter-spacing: 1.5px;
  }
  dd {
    color: var(--black, #333);
    font-size: 16px;
    letter-spacing: 0.15px;
    margin-top: 2px;
    min-height: 1.5em;
    line-height: 1.5;
    .num {
      font-size: 20px;
      font-weight: bold;
      &.num-sm {
        font-size: 16px;
        font-weight: normal;
      }
    }
    small {
      font-size: 12px;
      font-weight: normal;
      margin-left: 2px;
    }
  }
}

.indent1em {
  text-indent: -1em;
  margin-left: 1em;
}