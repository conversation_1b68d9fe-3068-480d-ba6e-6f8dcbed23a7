.nav-tab {
  border-bottom: 1px solid var(--<PERSON>, #D9D9D9);
  ul {
    display: flex;
    overflow-y: hidden;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; 
    overflow-scrolling: touch;
  }
  a {
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
    display: block;
    padding: 10px;
    color: #333;
    line-height: 1.6;
    padding: 15px 20px;
    margin-bottom: -1px;
    white-space: nowrap;
    &.current {
      color: #2F587C;
      font-weight: bold;
      border-bottom: 2px solid #2F587C;
    }
  }
}

.box-info {
  border-radius: 4px;
  background: #EFF8FF;
  padding: 8px 12px;
  p {
    font-size: 12px;
    color: #333;
    line-height: 1.5;
    padding: 0;
    margin: 0;
  }
  display: flex;
  .icn {
    font-size: 18px;
    margin-right: 5px;
    margin-left: -2px;
  }
}