@import "_normalize";

@font-face {
  font-family: 'Encorpada Classic';
  src: url('@/assets/fonts/EncorpadaClassic-Regular.eot');
  src: local('Encorpada Classic Regular'), local('EncorpadaClassic-Regular'),
      url('@/assets/fonts/EncorpadaClassic-Regular.eot?#iefix') format('embedded-opentype'),
      url('@/assets/fonts/EncorpadaClassic-Regular.woff2') format('woff2'),
      url('@/assets/fonts/EncorpadaClassic-Regular.woff') format('woff'),
      url('@/assets/fonts/EncorpadaClassic-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

*, *::before, *::after {
  box-sizing: border-box;
}


html {
  scroll-behavior: smooth;
}


body {
  background: $color-mainbackground;
  overflow-x: hidden;
  overscroll-behavior-y: none;
  font-family: 'Noto Sans JP', sans-serif;
  color: $color-maintext;
  font-smoothing: antialiased;
  -moz-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: none;
}

a:not([href]){
  cursor: pointer;
}


.section-inner {
  max-width: 1050px;
  width: 100%;
  margin: 0 auto;
}


ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}


li {
  list-style-type: none;
}


dl , 
dd {
  margin: 0;
}


p {
  margin: 0;
}

 
button {
  appearance: none;
  -webkit-appearance: none;
  border: none;
  background-color: transparent;
  cursor: pointer;
}


img {
  width: auto;
  max-width: 100%;
  vertical-align: bottom;
}

select,
input,
textarea {
  outline: none;
}

.acdtrg {
  display: block;
  position: relative;
  padding: 16px 40px 14px 20px;
  font-weight: 700;
  font-size: 14px;
  line-height: 120%;
  color: $color-blacktext2;
  cursor: pointer;
  &::after {
    @include BA;
    top: 45.7%;
    right: 14px;
    width: 12px;
    height: 8px;
    background-image: url(@/assets/images/icon-expand_more-b.svg);
    transform: rotate(-180deg);
    transition: transform 0.35s ease;
  }
  &.is-close::after {
    transform: rotate(0deg);
  }
}
.acdsbj {
  padding: 20px;
  font-size: 14px;
  overflow: hidden;
  opacity: 1;
  &.wide {
    padding-right: 0;
    padding-left: 0;
  }
  &.is-close {
    height: 0 !important;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
    transition: height 0.35s ease, padding-top 0.35s ease, padding-bottom 0.35s ease, opacity 0.35s ease;
  }
  &.is-open {
    transition: height 0.35s ease, padding-top 0.35s ease, padding-bottom 0.35s ease, opacity 0.35s ease;
  }
  &.is-addheight {
    position: absolute;
    width: 100%;
    visibility: hidden;
    opacity: 0;
    transition: height 0s, padding-top 0s, padding-bottom 0s;
  }
}
.tabsbj {
  position: relative;
  & > li {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    overflow: hidden;
    opacity: 0;
    &.is-active {
      position: relative;
      height: auto;
      opacity: 1;
    }
  }
}

.required::after {
  content: '*';
  margin-left: 5px;
  vertical-align: top;
  font-size: 12px;
  color: $color-alert2;
}


@include sp {
.section-inner {
  width: 100%;
  padding: 0;
}
.acdtrg {
  padding: 16px 35px 14px 16px;
  &::after {
    right: 19px;
  }
}
.acdsbj {
  padding: 20px 16px;
}
.sp-acdtrg {
  display: block;
  position: relative;
  padding: 16px 40px 14px 20px;
  font-weight: 700;
  font-size: 14px;
  line-height: 120%;
  color: $color-blacktext2;
  cursor: pointer;
  &::after {
    @include BA;
    top: 45.7%;
    right: 14px;
    width: 12px;
    height: 8px;
    background-image: url(@/assets/images/icon-expand_more-b.svg);
    transform: rotate(-180deg);
    transition: transform 0.35s ease;
  }
  &.is-close::after {
    transform: rotate(0deg);
  }
}
.sp-acdsbj {
  padding: 20px;
  font-size: 14px;
  overflow: hidden;
  opacity: 1;
  
  &.is-close {
    height: 0 !important;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
    transition: height 0.35s ease, padding-top 0.35s ease, padding-bottom 0.35s ease, opacity 0.35s ease;
  }
  &.is-open {
    transition: height 0.35s ease, padding-top 0.35s ease, padding-bottom 0.35s ease, opacity 0.35s ease;
  }
  &.is-addheight {
    position: absolute;
    width: 100%;
    visibility: hidden;
    opacity: 0;
    transition: height 0s, padding-top 0s, padding-bottom 0s;
  }
}
}

.input-error {
  font-size: 12px;
  margin-top: 5px;
  display: block;
  color: $color-alert2 !important;
  &.size--md {
    font-size: 14px;
  }
}



///
/// 共通要素
/// 


/// 見出し

.cmn-title {
  color: $color-accent;
  font-size: 18px;
  font-weight: 400;
  line-height: 130%;
  letter-spacing: 0.48px;
  &.size--lg {
    font-size: 24px;
  }  
  &.size--sm {
    font-size: 16px;
  }


  &.font--lato {
    font-family: Lato;
    font-weight: 300;
  }
  .cmn-titlesub {
    margin-left: 0.5em;
    font-size: 12px;
    line-height: 120%;
    letter-spacing: 0.24px;
    &::before {
      content: '-';
      margin-right: 0.5em;
    }
  }

  &.color--blue {
    color: var(--_dark, #0F2C4E);
  }

  &.cmn-title-en {
    font-size: 12px;
    .en {
      font-family: Lato;
      font-size: 24px;
      font-weight: 300;
    }
    .ja {
      &::before {
        content: '-';
        margin-left: 10px;
        margin-right: 5px;
      }
    }
  }
}

.cmn-titleborder {
  position: relative;
  padding: 3px 20px;
  color: $color-blacktext2;
  font-size: 20px;
  line-height: 120%;
  letter-spacing: 1px;
  &::before {
    content: '';
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background-color: $color-main;
  }
}


/// リンク

.cmn-link {
  color: $color-accent;
  text-decoration: none;
  cursor: pointer;
  &:hover {

    // TODO 動作を確認

  }
}

.cmn-linkback {
  position: relative;
  padding-left: 20px;
  color: $color-blackLight;
  font-size: 14px;
  line-height: 120%;
  text-decoration: none;
  &:before {
    @include BA;
    left: 0;
    width: 15px;
    height: 15px;
    background-image: url(@/assets/images/icon-arrow_backward-b.svg);
  }
  &:hover {

    // TODO 動作を確認

  }
}


// マーク付きテキスト

.cmn-textannotation {
  color: $color-blackLight;
  padding-left: 1em;
  text-indent: -1em;
  &::before {
    content: '※';
  }
  &.cmn-aligncenter ,
  &.cmn-alignright {
    padding-left: 0;
    text-indent: 0;
  }
}

.cmn-textnotice {
  position: relative;
  padding-left: 23px;
  color: $color-blacktext3;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0.28px;
  &:before {
    @include BA;
    left: 0;
    width: 20px;
    height: 20px;
    background-image: url(@/assets/images/icon-information-g.svg);
  }
}

.cmn-textquestion {
  position: relative;
  padding-left: 21px;
  color: $color-accent;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: 0.28px;
  &:before {
    @include BA;
    left: 0;
    width: 16px;
    height: 16px;
    background-image: url(@/assets/images/icon-help-g.svg);
  }
}


// カラー付きテキスト

.cmn-colormain {
  color: $color-main;
}

.cmn-coloraccent {
  color: $color-accent;
}


// テキスト配置

.cmn-alignright {
  text-align: right;
}

.cmn-aligncenter {
  text-align: center;
}
.sp_cmn-aligncenter {
  @include sp {
    text-align: center !important;
  }
}

.cmn-alignleft {
  text-align: left;
}


// 基本テキスト

p {
  margin-bottom: 1em;
  font-size: 14px;
  line-height: 1.5;
  &.size--sm {
    font-size: 12px;
  }
  &.size--lg {
    font-size: 16px;
  }
  &.size--md {
    font-size: 14px;
  }
  &.size--xl {
    font-size: 22px;
  }
}

// @include sp {
// p {
//   line-height: 1.2;
// }
// }

// 各所マージン

@for $i from 0 through 160 {
  .m-#{$i} { margin: 1px * $i; }
  .mt-#{$i} { margin-top: 1px * $i; }
  .mb-#{$i} { margin-bottom: 1px * $i; }
  .mr-#{$i} { margin-right: 1px * $i; }
  .ml-#{$i} { margin-left: 1px * $i; }
  .mx-#{$i} { margin-right: 1px * $i; margin-left: 1px * $i; }
  .my-#{$i} { margin-top: 1px * $i; margin-bottom: 1px * $i; }
}
@for $i from 0 through 160 {
  .p-#{$i} { padding: 1px * $i; }
  .pt-#{$i} { padding-top: 1px * $i; }
  .pb-#{$i} { padding-bottom: 1px * $i; }
  .pr-#{$i} { padding-right: 1px * $i; }
  .pl-#{$i} { padding-left: 1px * $i; }
  .px-#{$i} { padding-right: 1px * $i; padding-left: 1px * $i; }
  .py-#{$i} { padding-top: 1px * $i; padding-bottom: 1px * $i; }
}
.m-auto { margin: auto; }
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }
.mr-auto { margin-right: auto; }
.ml-auto { margin-left: auto; }
.mx-auto { margin-right: auto; margin-left: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }

@include sp {
@for $i from 0 through 160 {
  .spm-#{$i} { margin: 1px * $i; }
  .spmt-#{$i} { margin-top: 1px * $i; }
  .spmb-#{$i} { margin-bottom: 1px * $i; }
  .spmr-#{$i} { margin-right: 1px * $i; }
  .spml-#{$i} { margin-left: 1px * $i; }
  .spmx-#{$i} { margin-right: 1px * $i; margin-left: 1px * $i; }
  .spmy-#{$i} { margin-top: 1px * $i; margin-bottom: 1px * $i; }
}
.spm-auto { margin: auto; }
.spmt-auto { margin-top: auto; }
.spmb-auto { margin-bottom: auto; }
.spmr-auto { margin-right: auto; }
.spml-auto { margin-left: auto; }
.spmx-auto { margin-right: auto; margin-left: auto; }
.spmy-auto { margin-top: auto; margin-bottom: auto; }
}


.indent-1em {
  text-indent: -1em;
  margin-left: 1em;
}

.color-main {
  color: $color-main !important;
}
.color-maindark {
  color: $color-maindark !important;
}
.color-blacklight {
  color: $color-blackLight !important;
}
.color-alert {
  color: $color-alert !important;
}

.text-danger {
  color: var(--2, #E65C7A);
  .icn {
    vertical-align: middle;
    margin-top: -2px;
  }
}

// アラート表示
.alert_prepaymentPause {
  display: flex;
  align-items: flex-start;
  color: $color-alert;
  font-size: 14px;
  line-height: 1.5;
  text-align: left;
  margin: 0 auto 24px;
  img {
    width: 24px;
    height: 24px;
    margin-top: -1px;
    margin-right: 4px;
  }
  a {
    color: inherit;
    text-decoration: underline;
    &:hover {
      text-decoration: none;
    }
  }
}

.color-accent {
  color: $color-accent !important;
}
.color-danger {
  color: #E65C7A !important;
}
.color-gray {
  color: $color-graytext2 !important;
}
.text-right{
  text-align: right;
}

.box-gradient {
  padding: 28px 24px;
  background: #2F587C;
  position: relative;
  margin-left: -16px;
  margin-right: -16px;
  &:after {
    content: " ";
    background: url(@/assets/images/bg-gradient.jpg) top center no-repeat;
    background-size: cover;
    opacity: 0.8;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  .title {
    text-align: center;
    color: $color-main;
    font-size: 18px;
    font-weight: 700;
    letter-spacing: .1em;
    line-height: 1.6;
  }
  > .inner {
    border-radius: 10px;
    background: rgba(#FFF, .8);
    padding: 20px;
    position: relative;
    z-index: 1;
  }
  .text {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(#000, .1);
    color: var(--black, var(--text-black, #333));
    font-size: 12px;
    font-weight: 400;
    line-height: 1.8;
  }
}
[data-textalign="center"] { text-align: center;}
[data-textalign="left"] { text-align: left;}
[data-textalign="right"] { text-align: right;}


@import "atoms/_input";
@import "atoms/_modal";
@import "atoms/_link";
@import "atoms/_btn";
@import "atoms/_list";
@import "organisms/_faq";
@import "atoms/_label";
@import "atoms/_table";
@import "atoms/_icn";
@import "atoms/_nav";


.contener-sm {
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 20px;
  padding-right: 20px;
}
.contener-xs {
  max-width: 420px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 20px;
  padding-right: 20px;
}
.ph0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
} 
.sp_ph0 {
  @include sp {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
} 

hr {
  border: none;
  border-bottom: 4px solid #F4F4F4;
  margin: 10px 0;
  padding: 0;
}

.alert {
  border-radius: 4px;
  padding: 16px;
  line-height: 1.6;
  border: 1px solid var(--1, #FF1B1B);
  background: var(--white-100, #FFF);
  p {
    margin: 0;
    padding: 0;
  }
  .alert-heading {
    color: var(--1, #FF1B1B);
    font-size: 16px;
    font-weight: 400;
    margin: 0;
    padding: 0;
    margin-bottom: 16px;
    .icn-left {
      font-size: 1.5em;
      vertical-align: middle;
      margin-top: -3px;
    }
  }
}


.pc_only { display: block!important;}
.sp_only { display: none!important;}
@include sp {
  .sp_only { display: block!important;}
  .pc_only { display: none!important;}
  [data-sp-hidden="true"] { display: none!important;}
}

.d-ib { display: inline-block; }

.article-main {
  // margin-top: -60px;
}
.material-symbols-outlined {
  font-weight: 200;
}

.bold{
  font-weight: bold;
}

.preWrap {
  white-space: pre-wrap;
}

// 画像トリミングのリンクが切れた時にテキストを表示
.cropper-canvas{
  img{
    position: relative;
    &::before{
      content: "画像リンクがタイムアウトしましたので\Aリロードしてください";
      display: flex;
      justify-content: center;
      align-items: center;
      white-space: pre;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      margin: auto;
      text-align: center;
      height: 50px;
      width: 100%;
      color: #333;
      font-size: 10px;
      line-height: 1.2;
      background-color: rgb(255, 255, 255);
      border: 2px dotted rgb(200, 200, 200);
      border-radius: 5px;
    }
  }
}

// モーダルのスクロールが背景の要素に影響しないようにする
.modal .contents{
  overscroll-behavior: contain;
}

html:has(.modalWrap.is-open),
html:has(.modalWrap.is-open) body {
  overflow: hidden;
}
