.hr-full {
  border: none;
  padding: 0;
  margin: 0;
  border-bottom: 8px solid #F4F4F4;
  // margin-right: calc(50% - 50vw);
  // margin-left: calc(50% - 50vw);
  // padding-left: calc(50vw - 50%);
  // padding-right: calc(50vw - 50%);
  @include sp {
    width: 100vw;
    margin: 0;
    padding: 0;
    border-bottom-width: 4px;
  }
  
}
.article-questions {
  padding-top: 40px;
  @include sp {
    padding-top: 0;
  }
  .section-search-form {
    margin-top: 0;
    padding-top: 0;
    margin-bottom: 33px;
    max-width: 650px;
    margin-right: auto;
    margin-left: auto;
    @include sp {
      padding: 16px 16px 0;
      margin: 0 0 24px;
    }
    form {
      display: flex;
      margin-bottom: 12px;
      .btn {
        white-space: nowrap;
        width: 138px;
        margin-left: 12px;
        flex-shrink: 0;
        @include sp {
          width: 80px;
        }
      }
    }
    .search-keywords {
      border-radius: 4px;
      background: var(--Gray_light, #F4F4F4);
      padding: 19px 24px;
      .links {
        li {
          display: inline-block;
          margin-right: 5px;
          margin-bottom: 6px;
        }
        a {
          border-radius: 100px;
          border: 1px solid var(--Gray, #D9D9D9);
          background: #FFF;
          display: flex;
          padding: 4px 12px;
          font-size: 12px;
          font-weight: 400;
          cursor: pointer;
          transition: 0.35s ease;
          &:hover {
            opacity: .4;
          }
        }
      }
    }
  }
  .section-search-results {
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 20px;
    padding-right: 20px;
    margin-top: 24px;
    @include sp {
      padding-left: 0;
      padding-right: 0;
    }
    .cmn-title {
      @include sp {
        padding-left: 16px;
      }

    }
    .box-support-questions {
      border-radius: 4px;
      background: var(--_, #EFF8FF);
      font-size: 14px;
      padding: 15px 30px 20px;
      margin-bottom: 30px;
      @include sp {
        padding: 5px 20px 10px;
        margin: 16px 16px 32px;
        .cmn-title {
          padding-left: 0;
        }
      }
    }
  }

  .section-question-detail {
    max-width: 680px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 15px;
    padding-right: 15px;
    @include sp {
      margin-top: -36px;
    }
    figure {
      padding: 0;
      margin: 0;
    }
    img {
      height: auto;
    }
    a {
      color:  #AD871E;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
  }
  .contact-box {
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
    background: var(--Gray_light, #F4F4F4);
    padding: 16px;
    text-align: center;
    margin-top: 40px;
    margin-bottom: 40px;
    @include sp {
      margin-left: 15px;
      margin-right: 15px;
    }
    p {
      font-size: 12px;
      line-height: 1.6;
    }
    .btn-wrap {
      max-width: 320px;
      margin-left: auto;
      margin-right: auto;
      .btn {
        display: block;
        width: 100%;
      }
    }
  }


  .section-support {
    // margin-top: -60px;
    background: var(--Gray_light, #F4F4F4);
    padding: 16px 0;
    // margin-right: calc(50% - 50vw);
    // margin-left: calc(50% - 50vw);
    // padding-left: calc(50vw - 50%);
    // padding-right: calc(50vw - 50%);
    @include sp {
      padding: 16px;
      margin: 0;
      // margin-top: -60px;
    }
    .box-news {
      background: #fff;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
      padding: 16px;
      border: 1px solid var(--Gray, #D9D9D9);
    }
  }

  .btn-wrap {
    max-width: 320px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 16px;
    .btn {
      display: block;
      width: 100%;
    }
  }
}

.list-news {
  display: block;
  li {
    border-bottom: 1px solid var(--Gray, #D9D9D9);
  }
  a {
    color: var(--49454Fblack_Light, #49454F);
    font-size: 16px;
    font-weight: 400;
    padding: 12px 16px;
    display: block;
    text-decoration: none;
    .title {
      display: block;
    }
    .date {
      font-size: 12px;
      display: inline-block;
      color: var(--Gray_dark, #9C9C9C);
      vertical-align: baseline;
      @include sp {
        margin-bottom: 10px;
      }
    }
    .cats {
      display: inline-block;
      vertical-align: baseline;
    }
    .cat {
      display: inline-block;
      padding: 4px 11px;
      border-radius: 10px;
      color: #E65C7A;
      border: 1px solid var(--2, #E65C7A);
      background: var(--white-100, #FFF);
      font-size: 10px;
      margin-left: 10px;
    }
    position: relative;
    .icn-right {
      position: absolute;
      right: 20px;
      top: 50%;
      margin-top: -6px;
      font-size: 12px;
    }
    transition: 0.35s ease;
    &:hover {
      text-decoration: none;
      background: rgba(#000, .04);
    }
  }
}


.page-question-detail {
  .contents-title {
    h1 {
      padding-top: 14px;
      padding-bottom: 14px;
    }
    .section-inner {
      font-size: 16px;
      font-weight: 700;
      line-height: 1.4;
      padding-left: 25px;
      position: relative;
      &:before {
        content: "Q";
        color: #AD871E;
        font-weight: 700;
        font-size: 18px;
        margin-right: 8px;
        position: absolute;
        left: 0;
        top: -2px;
      }
    }
  }
}