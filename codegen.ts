import type { CodegenConfig } from '@graphql-codegen/cli'

const config: CodegenConfig = {
  overwrite: true,
  generates: {
    'types/api-schema.d.ts': {
      schema: process.env.API_ENDPOINT || 'http://localhost/graphql',
      plugins: ['typescript'],
    },
    'composables/generated.ts': {
      schema: process.env.API_ENDPOINT || 'http://localhost/graphql',
      documents: 'graphql/**/*.graphql',
      config: {
        documentMode: 'documentNode',
        vueApolloComposableImportFrom: '@vue/apollo-composable',
        vueCompositionApiImportFrom: 'vue',
      },
      plugins: ['typescript', 'typescript-operations', 'typescript-vue-apollo'],
    },
    './graphql.schema.json': {
      schema: process.env.API_ENDPOINT || 'http://localhost/graphql',
      plugins: ['introspection'],
    },
  },
}

export default config