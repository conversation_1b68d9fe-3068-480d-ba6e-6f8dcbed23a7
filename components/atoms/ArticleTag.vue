<template>
  <li
    class="tag"
    v-for="(tag , index) in paramsArticleTag"
    :key="tag"
    :class="classes(index)"
    @click="eventTag($event)"
  >
    {{ publishedTag(tag) }}
  </li>
</template>

<script lang="ts" setup>
import { exclusiveSelect } from '@/assets/js/customFunction.js'

export interface Props {
  propsArticleTag: Array,
  hash: boolean,
  exclusiveSelect: boolean,
  addClasses?: string;
  size?: 'sm' | 'md' | 'ml' | 'lg';
  spsize?: 'spsm' | 'spmd' | 'splg';
}

const params = withDefaults(defineProps<Props>(), {
  propsArticleTag: {},
  hash: true,
  exclusiveSelect: false,
  addClasses: '',
  size: 'md',
});

const classes = computed(() => (i) => {
  let margeClasses = params.addClasses;
  if (params.exclusiveSelect === true) {
    margeClasses += ` is-button`;
    if (i === 0) {
      margeClasses += ` is-selected`;
    }
  }
  margeClasses += ` tag--${params.size}`;
  margeClasses += ` tag--${params.size}`;
  margeClasses += (params.spsize ? ` tag--${params.spsize}` : '');
  return margeClasses;
});

const paramsArticleTag = params.propsArticleTag;

const publishedTag = computed(() => (data) => {
  let content = (params.hash?"#" + data:data);
  return content;
});

const eventTag = (target) => {
  exclusiveSelect(target);
};

</script>

<style lang="scss" scoped>
.tag {
  list-style: none;
  margin-right: 3px;
  padding: 3px 12px;
  background: $color-mainbackground;
  border: 1px solid $color-grayborder;
  border-radius: 100px;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.02em;
  color: $color-blacktext2;
  &.tag--sm {
    padding: 2px 7px;
    font-size: 10px;
  }
  &.tag--md {
    padding: 3px 12px;
    font-size: 12px;
  }
  &.tag--ml {
    margin-right: 6px;
    padding: 5px 11px;
    font-size: 12px;
    letter-spacing: -0.07em;
  }
  &.tag--lg {
    padding: 5px 10px;
    font-size: 14px;
  }
  &.is-button {

    cursor: pointer;
  }
  &.is-selected ,
  &:hover {
    border-color: $color-main;
    color: $color-main;
  }
  &.tag--ml {
    padding: 5px 11px;
    font-size: 12px;
    &.is-selected ,
    &:hover {
      background-color: $color-main;
      color: $color-whitetext;
    }
  }
}
</style>
