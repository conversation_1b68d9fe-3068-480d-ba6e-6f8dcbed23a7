<template>
  <NuxtLink
    :style="sizeClass"
    :class="classes"
    v-bind="$attrs"
    @click="emits('click', null);"
  >
    <span>
      <slot />
    </span>
  </NuxtLink>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  addClasses?: string;
  baseColor?: 'main' | 'reversal' | 'glay' | 'clear' | 'accent' | 'accent_reversal';
  align?: 'left' | 'center' | 'right';
  buttonsize?: 'auto' | 'full' | number;
  size?: 'sm' | 'md' | 'lg';
  spsize?: 'spsm' | 'spmd' | 'splg';
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  addClasses: '',
  baseColor: 'main',
  align: 'center',
  buttonsize: 'full',
  size: 'md',
  disabled: false
});

const classes = computed(() => {
  let margeClasses = props.addClasses;
  margeClasses += ` button--${props.baseColor}`;
  margeClasses += ` align--${props.align}`;
  margeClasses += ` buttonsize--${props.buttonsize}`;
  margeClasses += ` button--${props.size}`;
  margeClasses += (props.spsize ? ` button--${props.spsize}` : '');
  margeClasses += (props.disabled ? ' button--disabled' : '');
  return margeClasses;
});

const emits = defineEmits<{
  (e: 'click', v: null): void;
}>()

let sizeWidth:Ref<string> | string = ref('none');
const sizeClass = computed<string | number>(() => {
  const size = props.buttonsize;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})
</script>

<style lang="scss" scoped>
a {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  padding: 12px 10px 11px;
  border-radius: 4px;
  text-decoration: none;
  line-height: 120%;
  font-size: 12px;
  cursor: pointer;
  &.button--main {
    background-color: $color-main;
    color: $color-whitetext;
  }
  &.button--reversal {
    padding: 11px 10px 10px;
    border: 1px solid $color-main;
    background-color: $color-mainbackground;
    color: $color-main;
  }
  &.button--glay {
    padding: 11px 10px 10px;
    border: 1px solid $color-grayborder;
    background-color: $color-mainbackground;
    font-weight: 700;
    color: $color-blackLight;
  }
  &.button--clear {
    padding: 11px 10px 10px;
    border: 1px solid transparent;
    background-color: $color-mainbackground;
    font-weight: 400;
    color: $color-blackLight;
  }
  &.button--accent {
    padding: 11px 10px 10px;
    border: 1px solid $color-accent;
    background-color: $color-accent;
    font-weight: 700;
    color: $color-whitetext;
  }
  &.button--accent_reversal {
    padding: 11px 10px 10px;
    border: 1px solid $color-accent;
    background-color: $color-mainbackground;
    color: $color-accent;
  }
  &.align--left {
    text-align-last: left;
  }
  &.align--center {
    text-align: center;
  }
  &.align--right {
    text-align: right;
  }
  &.button--sm {
    padding: 8px 7px;
    font-size: 10px;
    &.button--reversal {
      padding: 7px;
    }
  }
  &.button--md {
    padding: 12px 10px 11px;
    font-size: 12px;
    &.button--reversal {
      padding: 11px 10px 10px;
    }
  }
  &.button--lg {
    padding: 13px 13px 12px;
    font-size: 16px;
    &.button--reversal {
      padding: 14px 13px;
    }
  }
  &.buttonsize--auto {
    width: max-content;
    padding-right: 30px;
    padding-left: 30px;
  }
  &.buttonsize--full {
    width: 100%;
  }
  &.button--disabled {
    border-color: $color-graytext2;
    background-color: $color-graytext2;
    color: $color-whitetext;
    pointer-events: none;
    cursor: default;
    &.button--glay {
      border-color: $color-grayborder;
      background-color: $color-grayborder;
    }
  }
  
  &.btn-login {
    span {
      display: inline-block;
      position: relative;
      padding-left: 21px;
      &::before {
        @include BA;
        left: 0;
        width: 16px;
        height: 16px;
        background-image: url(@/assets/images/icon-login-w.svg);
      }
    }
  }

  &.btn-registration {
    span {
      display: inline-block;
      position: relative;
      padding-left: 21px;
      &::before {
        @include BA;
        left: 0;
        width: 16px;
        height: 16px;
        background-image: url(@/assets/images/icon-account_circle.svg);
      }
    }
  }

  &.plus span ,
  &.plus-w span {
    position: relative;
    padding-left: 20px;
    &::before {
      @include BA;
      left: 0;
      width: 13px;
      height: 13px;
      background-image: url(@/assets/images/icon-plus.svg);
    }
  }
  &.plus-w span {
    &::before {
      width: 11px;
      height: 11px;
      background-image: url(@/assets/images/icon-plus-w.svg);
    }
  }
  &.gift span {
    position: relative;
    padding-left: 20px;
    &::before {
      @include BA;
      left: 0;
      width: 13px;
      height: 13px;
      background-image: url(@/assets/images/icon-gift-g.svg);
    }
  }
  &.mail span ,
  &.mail-w span {
    position: relative;
    padding-left: 21px;
    &::before {
      @include BA;
      left: 0;
      width: 14px;
      height: 14px;
      background-image: url(@/assets/images/icon-mail-w.svg);
    }
  }
  &.mail-w span {
    &::before {
      width: 14px;
      height: 14px;
      background-image: url(@/assets/images/icon-mail-w.svg);
    }
  }
}

@include sp {
a {
  &.button--spsm {
    padding: 8px 7px;
    font-size: 10px;
  }
  &.button--spmd {
    padding: 12px 10px 11px;
    font-size: 12px;
  }
  &.button--splg {
    padding: 13px 13px 12px;
    font-size: 16px;
  }
}
}
</style>