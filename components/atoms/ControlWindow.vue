<template>
<ul class="controlWindow">
  <li v-for="(datail, index) in windowData.datail" :key="datail" @click.prevent="eventControlWindow($event)">{{ datail.title }}</li>
</ul>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const windowData = params.data;

const emit = defineEmits(['eventControlWindow']);

const eventControlWindow = (e) => {
  let index = Array.prototype.indexOf.call(e.target.closest(".controlWindow").children , e.currentTarget);
  e.target.closest(".controlWindow").classList.add('close');
  emit('emitControlWindow' , index);
};
</script>

<style lang="scss" scoped>
.controlWindow {
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  width: max-content;
  padding: 7px 0;
  border-radius: 4px;
  background: $color-mainbackground;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.30), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  z-index: 100;
  li {
    padding: 15px 15px 14px;
    font-size: 16px;
    line-height: 120%;
    letter-spacing: 0.32px;
    cursor: pointer;
    &:hover {
      background-color: $color-lightbackground;
    }
  }
  &.fixed {
    position: fixed;
  }
  &.bottom {
    top: auto;
  }
  &.left {
    right: auto;
    left: 105%;
  }
  &.close {
    display: none;
  }
  &.standby {
    display: block;
    visibility: hidden;
  }
}

@include sp {
}
</style>