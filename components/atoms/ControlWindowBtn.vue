<template>
<div class="controlWindowWrap" :data-position="position" :data-mode="mode">
  <div class="controlWindowBtn">
    <button ref="btnRef" @click.prevent="isShowMenu = (! isShowMenu)">
      <span><slot name="button" /></span>
    </button>
  </div>
  <div class="controlWindow" v-if="isShowMenu" :style="{top: controlWindowTop, left: controlWindowLeft}">
    <slot />
  </div>
</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { onClickOutside } from '@vueuse/core';

const props = withDefaults(defineProps<{
  position: string,
  mode: string,
}>(), {
  position: 'top',
  mode: 'menu',
});

const isShowMenu = ref(false)

const btnRef = ref(null);

onClickOutside(btnRef, () => {
  isShowMenu.value = false;
});
// const eventControlWindow = (e) => {
//   let index = Array.prototype.indexOf.call(e.target.closest(".controlWindow").children , e.currentTarget);
//   e.target.closest(".controlWindow").classList.add('close');
//   emit('emitControlWindow' , index);
// };

const controlWindowTop = ref(false as any);
const controlWindowLeft = ref(false as any);
onMounted(() => {
  if (props.position === 'fixed') {
    setControlWindow();
    window.addEventListener('scroll', function() {
      isShowMenu.value = false;
      setControlWindow();
    });
    if (document.querySelector("#windowScroll")) {
      (document.querySelector("#windowScroll") as any).addEventListener('scroll', function() {
        isShowMenu.value = false;
        setControlWindow();
      });
    }
  }
});

const setControlWindow = () => {
  if (btnRef.value) {
    controlWindowTop.value = btnRef.value.getBoundingClientRect().top + 'px';
    controlWindowLeft.value = btnRef.value.getBoundingClientRect().left + 'px';
  }
}
</script>

<style lang="scss">
.controlWindowWrap {
  position: relative;
  .controlWindow {
    display: inline-block;
    position: absolute;
    top: 20px;
    right: 0;
    margin-top: 30px;
    margin-left: 0;
    width: max-content;
    padding: 7px 0;
    border-radius: 4px;
    background: $color-mainbackground;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.30), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
    z-index: 100;
    text-align: left;
    li {
      padding: 15px 15px 14px !important;
      font-size: 16px;
      line-height: 120%;
      letter-spacing: 0.32px;
      color: #000 !important;
      border: none !important;
      cursor: pointer;
      min-width: 160px;
      &.is--current ,
      &:hover {
        background-color: $color-lightbackground;
      }
    }
    &.fixed {
      position: fixed;
    }
    &.bottom {
      top: auto;
    }
    &.left {
      right: auto;
      left: 105%;
    }
    &.close {
      display: none;
    }
    &.standby {
      display: block;
      visibility: hidden;
    }
  }
  &[data-position="fixed"] {
    .controlWindow {
      position: fixed;
    }
  }
  &[data-position="bottom"] {
    .controlWindow {
      position: absolute !important;
      top: auto !important;
      bottom: 0 !important;
      left: 0 !important;
      transition: 0.35s ease;
      [data-scroll="top"] & {
        bottom: 50px !important;
      }
    }
  }
  &[data-position="bottom-right"] {
    .controlWindow {
      position: absolute !important;
      top: auto !important;
      bottom: 100% !important;
      left: auto !important;
      right: 0 !important;
      margin-top: 0;
      margin-bottom: 10px;
    }
  }
  &[data-mode="menu"] {
    .controlWindowBtn {
      position: absolute;
      top: 50%;
      right: 0;
      width: 50px;
      height: 50px;
      background: url(@/assets/images/icon-more_vert.svg) no-repeat center center;
      transform: translateY(-50%);
      font-size: 0;
      button {
        width: 100%;
        height: 100%;
        cursor: pointer;
      }
    }
  }
  &[data-mode="btn"] {
    .controlWindowBtn button {
      padding: 10px 40px;
      border-radius: 100px;
      background: $color-accent;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0.1px;
      color: $color-whitetext;
    }
  }
  &[data-mode="addbtn"] {
    .controlWindowBtn {
      position: relative;
      width: auto;
      height: auto;
      transform: none;
      button {
        display: inline-block;
        padding: 11px 21px 10px;
        border: 1px solid $color-main;
        border-radius: 4px;
        background-color: $color-mainbackground;
        text-decoration: none;
        line-height: 120%;
        font-size: 12px;
        color: $color-main;
        cursor: pointer;
        span {
          position: relative;
          padding-left: 20px;
          &::before {
            @include BA;
            left: 0;
            width: 13px;
            height: 13px;
            background-image: url(@/assets/images/icon-plus.svg);
          }
        }
      }
    }
  }
}

@include sp {
}
</style>