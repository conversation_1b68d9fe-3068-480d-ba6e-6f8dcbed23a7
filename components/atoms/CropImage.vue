<template>
  <div class="crop_image" ref="imageWrapElement" :style="containerStyle">
    <img
      ref="imageElement"
      :src="src"
      :style="imageStyle"
    >
  </div>
</template>

<script lang="ts" setup>
interface Props {
  src?: string,
  crop?: {
    x: number,
    y: number,
    width: number,
    height: number,
    rotate: number,
    scaleFactor: number,
    scaleX: number,
    scaleY: number,
    cropBoxLeft: number,
    cropBoxTop: number,
    cropBoxWidth: number,
    cropBoxHeight: number,
    originalWidth: number,
    originalHeight: number
  },
  loadKey?: string|number
}
const props = withDefaults(defineProps<Props>(), {
  src: '',
  crop: null,
  loadKey: 0
});

const src = ref(props.src);
const crop = ref(props.crop);
watch(() => props.src, (newVal) => {
  src.value = newVal;
});

watch(() => props.crop, (newVal) => {
  if (newVal) {
    crop.value = { ...newVal };
  }
}, { deep: true, immediate: true });

const imageWrapElement = ref(null);
const imageElement = ref(null);
const imageWrapWidth = ref(0);
const imageWrapHeight = ref(0);
const imageWidth = ref(0);
const imageHeight = ref(0);
const imageNaturalWidth = ref(0);
const imageNaturalHeight = ref(0);

const containerStyle = computed(() => {
  if (!crop.value) {
    return {
      width: 'auto',
      height: 'auto',
      overflow: 'hidden',
      position: 'relative'
    };
  }
  const aspectRatio = crop.value.cropBoxWidth / crop.value.cropBoxHeight;
  return {
    aspectRatio: aspectRatio,
    width: 'auto',
    height: 'auto',
    overflow: 'hidden',
    position: 'relative'
  };
});
const checkImageLoaded = () => {
  if (imageElement.value) {
    if(imageElement?.value.complete){
      handleImageLoaded();
    } else {
      imageElement.value.onload = handleImageLoaded;
    }
  }
};
const handleImageLoaded = () => {
  nextTick(() => {
    imageWrapWidth.value = imageWrapElement.value.offsetWidth;
    imageWrapHeight.value = imageWrapElement.value.offsetHeight;
    imageWidth.value = imageElement.value.offsetWidth;
    imageHeight.value = imageElement.value.offsetHeight;
    imageNaturalWidth.value = imageElement.value.naturalWidth;
    imageNaturalHeight.value = imageElement.value.naturalHeight;
    onSetImageStyle();
  });
};

onMounted(() => {
  checkImageLoaded();
});

watch(() => src.value, () => {
  nextTick(() => {
    checkImageLoaded();
  });
});
watch(() => crop.value, () => {
  nextTick(() => {
    checkImageLoaded();
  });
});

watch(() => props.loadKey, (newVal, oldVal) => {
  if(newVal != oldVal){
    checkImageLoaded();
  }
});

const calculateObjectPosition = () => {
let objectPositionX = 0;
  let objectPositionY = 0;
  if (crop.value.originalWidth !== crop.value.width) {
    objectPositionX = (crop.value.x / (crop.value.originalWidth - crop.value.width)) * 100;
  }
  if (crop.value.originalHeight !== crop.value.height) {
    objectPositionY = (crop.value.y / (crop.value.originalHeight - crop.value.height)) * 100;
  }

  if (crop.value.x === 0) {
    objectPositionX = 0;
  }
  if (crop.value.y === 0) {
    objectPositionY = 0;
  }

  if (crop.value.rotate == 90) {
    objectPositionX = (crop.value.y / (crop.value.originalWidth - crop.value.height )) * 100;
    objectPositionY = 100 - (crop.value.x / (crop.value.originalHeight - crop.value.width )) * 100;
  } else if (crop.value.rotate == 180) {
    objectPositionX = 100 - (crop.value.x / (crop.value.originalWidth - crop.value.width)) * 100;
    objectPositionY = 100 - (crop.value.y / (crop.value.originalHeight - crop.value.height)) * 100;
  } else if (crop.value.rotate == 270) {
    objectPositionX = (crop.value.originalWidth - crop.value.height - crop.value.y) / (crop.value.originalWidth - crop.value.height) * 100;
    objectPositionY = (crop.value.x / (crop.value.originalHeight - crop.value.width)) * 100;
  }

  return { objectPositionX, objectPositionY };
};

const imageStyle = ref({
  objectFit: 'contain' as const,
  width: '100%',
  height: '100%',
});
const onSetImageStyle = () => {
  if (!crop.value) {
    return {};
  }

  // トリミングボックスの座標を左上基準に計算
  let { objectPositionX, objectPositionY } = calculateObjectPosition();

  // 回転角度に応じてトランスフォームを適用
  let transform = `rotate(${crop.value.rotate}deg)`;
  let imgWidth = '100%';
  let imgHeight = '100%';

  if (crop.value.rotate == 90) {
    transform = `rotate(${crop.value.rotate}deg) translate(0, -100%)`;
    imgWidth = crop.value.height + 'px';
    imgHeight = crop.value.width + 'px';
  } else if (crop.value.rotate == 180) {
    transform = `rotate(${crop.value.rotate}deg) translate(-100%, -100%)`;
  } else if (crop.value.rotate == 270) {
    transform = `rotate(${crop.value.rotate}deg) translate(-100%, 0)`;
    imgWidth = crop.value.height + 'px';
    imgHeight = crop.value.width + 'px';
  }

  let imageZoom = '1';
  let heightImageZoom = (crop.value.originalHeight / crop.value.height) * (imageWrapHeight.value / imageNaturalHeight.value);
  let widthImageZoom = (crop.value.originalWidth / crop.value.width) * (imageWrapWidth.value / imageNaturalWidth.value);
  if(heightImageZoom > widthImageZoom){
    imageZoom = heightImageZoom.toString();
  }else{
    imageZoom = widthImageZoom.toString();
  }

  imageStyle.value = {
    objectFit: 'none' as const,
    width: imgWidth,
    height: imgHeight,
    objectPosition: `${Number.isNaN(objectPositionX) ? 0 : objectPositionX}% ${Number.isNaN(objectPositionY) ? 0 : objectPositionY}%`,
    transform,
    zoom: imageZoom
  };
};
</script>

<style lang="scss" scoped>
.crop_image {
  display: inline-block;
  max-width: 100%;
  max-height: 100%;
  img {
    display: block;
    transform-origin: top left;
    max-width: none;
    max-height: none;
  }
}
</style>