<template>
  <div
    class="crop_image crop_video video"
    ref="imageWrapElement"
    :style="containerStyle"
    :class="{'is-cover': props.cover}"
  >
    <video
      v-if="srcVideo"
      ref="videoElement"
      oncontextmenu="return false;"
      disablepictureinpicture
      controlslist="nodownload noplaybackrate"
      :src="isBase64 ? srcVideo : undefined"
      :loop="props.loop"
      :controls="props.controls"
      :poster="props.poster ? props.poster : srcImage"
      :muted="props.autoplay"
      :playsinline="props.autoplay ? true : false"
      :style="imageStyle"
      @canplay="handleImageLoaded"
    >
    </video>
    <img
      v-else-if="props.src"
      :src="props.src"
      alt=""
    />
    <Loading v-else />
  </div>
</template>

<script lang="ts" setup>
import Hls from 'hls.js';
interface Props {
  uuid?: string;
  src?: string,
  srcVideo?: string,
  size?: 'l' | 's' | 'm';
  cover?: boolean,
  controls?: boolean,
  loop?: boolean,
  autoplay?: boolean;
  poster?: string;
  crop?: {
    x: number,
    y: number,
    width: number,
    height: number,
    rotate: number,
    scaleFactor: number,
    scaleX: number,
    scaleY: number,
    cropBoxLeft: number,
    cropBoxTop: number,
    cropBoxWidth: number,
    cropBoxHeight: number,
    originalWidth: number,
    originalHeight: number
  },
  loadKey?: string|number
}
const props = withDefaults(defineProps<Props>(), {
  src: '',
  crop: null,
  size: 'l',
  cover: false,
  controls: false,
  loop: true,
  autoplay: true,
  poster: '',
  loadKey: 0
});

const src = ref(props.src);
const srcVideo = ref(props.srcVideo);
const uuid = ref(props.uuid);
const videoElement = ref(null);
const hls: Hls | null = ref(null);
const crop = ref(props.crop);


const isBase64 = computed(() => {
  return srcVideo.value && srcVideo.value.startsWith('data:video') || srcVideo.value && !srcVideo.value.includes('.m3u8');
});

const setupVideoLoop = (videoEl) => {
  if (props.loop && props.autoplay) {
    let loopTimeout;
    let lastCurrentTime = 0;
    
    const handleTimeUpdate = () => {
      const currentTime = videoEl.currentTime;
      const duration = videoEl.duration;
      
      // 動画の終端近く（250ms以内）に到達した場合
      if (duration > 0 && duration - currentTime < 0.25) {
        clearTimeout(loopTimeout);
        loopTimeout = setTimeout(() => {
          // タイムアウト後に再生位置が進んでいない場合（終端でスタック）
          if (videoEl.currentTime === lastCurrentTime && videoEl.currentTime > 0) {
            videoEl.currentTime = 0;
            videoEl.play().catch(e => console.log('Loop play failed:', e));
          }
        }, 500);
        lastCurrentTime = currentTime;
      } else {
        // 終端から離れた場合はタイムアウトをクリア
        clearTimeout(loopTimeout);
      }
    };

    const handleEnded = () => {
      // Safari 18.5でendedイベントが発火した場合のフォールバック
      if (videoEl.currentTime > 0) {
        videoEl.currentTime = 0;
        videoEl.play().catch(e => console.log('Loop play failed:', e));
      }
    };

    // 既存のイベントリスナーを削除してから追加
    videoEl.removeEventListener('timeupdate', handleTimeUpdate);
    videoEl.removeEventListener('ended', handleEnded);
    videoEl.addEventListener('timeupdate', handleTimeUpdate);
    videoEl.addEventListener('ended', handleEnded);
  }
};

const setupHlsLoop = (hlsInstance) => {
  if (props.loop && props.autoplay && hlsInstance) {
    hlsInstance.on(Hls.Events.MEDIA_ENDED, () => {
      hlsInstance.startLoad();
      videoElement.value.play().catch(e => console.log('HLS loop play failed:', e));
    });
  }
};

const onSetVideo = () => {
  if(srcVideo.value && videoElement.value) {
    if (hls.value) {
      hls.value.destroy();
      hls.value = null;
    }
    if (isBase64.value) {
      setupVideoLoop(videoElement.value);
      if(props.autoplay){
        videoElement.value.play();
      }
    } else if (Hls.isSupported()) {
      hls.value = new Hls({
        debug: false,
        xhrSetup: function (xhr, url) {
          xhr.withCredentials = true;
        },
        fetchSetup: function (context, initParams) {
          initParams.credentials = 'include';
          return new Request(context.url, initParams);
        },
      });
      hls.value.loadSource(generateVideoSrc(srcVideo.value));
      hls.value.attachMedia(videoElement.value);
      setupHlsLoop(hls.value);
      if(props.autoplay){
        hls.value.on(Hls.Events.MANIFEST_PARSED, () => {
          videoElement.value.play();
        });
      }
    } else if (videoElement.value.canPlayType('application/vnd.apple.mpegurl')) {
      // HLS.jsをサポートしていないデバイス（主にSafari）は、ネイティブHLSをサポートしている可能性あり
      videoElement.value.src = generateVideoSrc(srcVideo.value);
      setupVideoLoop(videoElement.value);
      if(props.autoplay){
        videoElement.value.addEventListener('loadedmetadata', () => {
          videoElement.value.play();
        });
      }
    }
  }
};

// `_hls`を削除するための関数
const generateVideoSrc = (srcVideo: string | undefined) => {
  if (!srcVideo) return srcVideo ? srcVideo : '';
  return srcVideo.replace('_hls', '');
};

const srcImage = ref<string|undefined>();
const fetchImages = async () => {
  if(uuid.value){
    const { getImages } = await useGetManyImages2([uuid.value + '_' + props.size]);
    watch(() => getImages.value, async () => {
      srcImage.value = getImages?.value?.[0]?.presigned_url;
      if(getImages?.value?.[0]?.presigned_url_main){
        srcVideo.value = getImages?.value?.[0]?.presigned_url_main;
      }
    }, { immediate: true, deep: true });
  }
};

onMounted(async() => {
  await fetchImages();
  onSetVideo();
  checkImageLoaded();
});

watch(() => props.uuid, async (newVal, oldVal) => {
  if(newVal != oldVal) {
    uuid.value = newVal;
    await fetchImages();
    onSetVideo();
  }
});
watch(() => videoElement.value, async (newVal, oldVal) => {
  if(newVal != oldVal) {
    onSetVideo();
  }
});
watch(() => props.srcVideo, async (newVal, oldVal) => {
  if(newVal != oldVal) {
    srcVideo.value = newVal;
  }
}, {
  deep: true,
  immediate: true
});
watch(() => srcVideo.value, async (newVal, oldVal) => {
  if(newVal != oldVal) {
    fetchImages();
    onSetVideo();
  }
});
watch(() => props.crop, (newVal) => {
  if (newVal) {
    crop.value = { ...newVal };
    handleImageLoaded();
  }
}, { deep: true });

// コンポーネントがアンマウントされる際にHLSインスタンスを破棄
onBeforeUnmount(() => {
  if (hls && hls.value) {
    hls.value.destroy();
    hls.value = null;
  }
});

const imageWrapElement = ref(null);
const imageWrapWidth = ref(0);
const imageWrapHeight = ref(0);
const imageWidth = ref(0);
const imageHeight = ref(0);
const imageNaturalWidth = ref(0);
const imageNaturalHeight = ref(0);

const containerStyle = computed(() => {
  if (!crop.value) {
    return {
      width: 'auto',
      height: 'auto',
      overflow: 'hidden',
      position: 'relative'
    };
  }
  const aspectRatio = crop.value.cropBoxWidth / crop.value.cropBoxHeight;
  return {
    aspectRatio: aspectRatio,
    width: 'auto',
    height: 'auto',
    overflow: 'hidden',
    position: 'relative'
  };
});
const checkImageLoaded = () => {
  if (videoElement.value) {
    // handleImageLoaded();
  }
};

const handleImageLoaded = () => {
  nextTick(() => {
    if(imageWrapElement.value && videoElement.value) {
      imageWrapWidth.value = imageWrapElement.value.offsetWidth;
      imageWrapHeight.value = imageWrapElement.value.offsetHeight;
      imageWidth.value = videoElement.value.offsetWidth;
      imageHeight.value = videoElement.value.offsetHeight;
      imageNaturalWidth.value = videoElement.value.videoWidth;
      imageNaturalHeight.value = videoElement.value.videoHeight;
      onSetImageStyle();
    }
  });
};

const calculateObjectPosition = () => {
let objectPositionX = 0;
  let objectPositionY = 0;
  if (crop.value.originalWidth !== crop.value.width) {
    objectPositionX = (crop.value.x / (crop.value.originalWidth - crop.value.width)) * 100;
  }
  if (crop.value.originalHeight !== crop.value.height) {
    objectPositionY = (crop.value.y / (crop.value.originalHeight - crop.value.height)) * 100;
  }

  if (crop.value.x === 0) {
    objectPositionX = 0;
  }
  if (crop.value.y === 0) {
    objectPositionY = 0;
  }

  if (crop.value.rotate == 90) {
    objectPositionX = (crop.value.y / (crop.value.originalWidth - crop.value.height )) * 100;
    objectPositionY = 100 - (crop.value.x / (crop.value.originalHeight - crop.value.width )) * 100;
  } else if (crop.value.rotate == 180) {
    objectPositionX = 100 - (crop.value.x / (crop.value.originalWidth - crop.value.width)) * 100;
    objectPositionY = 100 - (crop.value.y / (crop.value.originalHeight - crop.value.height)) * 100;
  } else if (crop.value.rotate == 270) {
    objectPositionX = (crop.value.originalWidth - crop.value.height - crop.value.y) / (crop.value.originalWidth - crop.value.height) * 100;
    objectPositionY = (crop.value.x / (crop.value.originalHeight - crop.value.width)) * 100;
  }

  return { objectPositionX, objectPositionY };
};

const imageStyle = ref({
  objectFit: 'cover' as const,
  width: '100%',
  height: '100%',
});
const onSetImageStyle = () => {
  if (!crop.value) {
    return {};
  }

  // トリミングボックスの座標を左上基準に計算
  let { objectPositionX, objectPositionY } = calculateObjectPosition();

  // 回転角度に応じてトランスフォームを適用
  let transform = `rotate(${crop.value.rotate}deg)`;
  let imgWidth = '100%';
  let imgHeight = '100%';

  if (crop.value.rotate == 90) {
    transform = `rotate(${crop.value.rotate}deg) translate(0, -100%)`;
    imgWidth = crop.value.height + 'px';
    imgHeight = crop.value.width + 'px';
  } else if (crop.value.rotate == 180) {
    transform = `rotate(${crop.value.rotate}deg) translate(-100%, -100%)`;
  } else if (crop.value.rotate == 270) {
    transform = `rotate(${crop.value.rotate}deg) translate(-100%, 0)`;
    imgWidth = crop.value.height + 'px';
    imgHeight = crop.value.width + 'px';
  }

  let imageZoom = '1';
  let heightImageZoom = (crop.value.originalHeight / crop.value.height) * (imageWrapHeight.value / imageNaturalHeight.value);
  let widthImageZoom = (crop.value.originalWidth / crop.value.width) * (imageWrapWidth.value / imageNaturalWidth.value);
  if(heightImageZoom > widthImageZoom){
    imageZoom = heightImageZoom.toString();
  }else{
    imageZoom = widthImageZoom.toString();
  }

  imageStyle.value = {
    objectFit: 'none' as const,
    width: imgWidth,
    height: imgHeight,
    objectPosition: `${Number.isNaN(objectPositionX) ? 0 : objectPositionX}% ${Number.isNaN(objectPositionY) ? 0 : objectPositionY}%`,
    transform,
    zoom: imageZoom
  };
};
</script>

<style lang="scss" scoped>
.crop_image {
  display: inline-block;
  max-width: 100%;
  max-height: 100%;
  video {
    display: block;
    transform-origin: top left;
    max-width: none;
    max-height: none;
  }
}
</style>