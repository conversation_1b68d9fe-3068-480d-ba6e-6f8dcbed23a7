<template>
  <iframe
    :src="googleMapSrc"
    width="100%"
    height="210"
    style="border:0"
    allowfullscreen=""
    loading="lazy"
  ></iframe>
</template>

<script lang="ts" setup>
interface Props {
  address?: string
};
const props = withDefaults(defineProps<Props>(), {
  address: ''
});

const address = ref(JSON.parse(JSON.stringify(props.address)));
watch(() => props.address, (newVal) => {
  address.value = JSON.parse(JSON.stringify(newVal));
});

const googleMapSrc = computed(() => {
  const encodedAddress = encodeURIComponent(address.value);
  return `https://maps.google.co.jp/maps?output=embed&q=${encodedAddress}`;
});
</script>