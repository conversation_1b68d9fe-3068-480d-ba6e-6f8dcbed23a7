<template>
  <video v-if="images.value?.[0]?.presigned_url && props.type == 'video'" :src="images.value[0].presigned_url"></video>
  <img v-else-if="images.value?.[0]?.presigned_url" :src="images.value[0].presigned_url" />
  <Loading v-else />
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  uuid: string;
  size?: 'l' | 's' | 'm';
  type?: 'image' | 'video';
}>(), {
  size: 'l',
  type: 'image'
});

const images = ref(null);
const fetchImages = async () => {
  const { getImages } = await useGetManyImages2([props.uuid + '_' + props.size]);
  images.value = getImages;
};

await fetchImages();

watch(() => props.uuid, async () => {
  await fetchImages();
});
</script>