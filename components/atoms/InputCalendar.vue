<template>
  <div
    class="inputCalendar"
    :class="onCheckSizeClass"
  >
    <div v-if="props.title" class="title"><span :class="{'required': props.required}">{{ props.title }}</span></div>
    <div :class="{'inputWrap': true, 'hasError': props.error.length > 0}">
      <!-- <VueDatePicker 
        :placeholder="placeholder"
        :disabled="disabled"
        v-model="date"
        :enable-time-picker="false"
        cancel-text="キャンセル"
        select-text="決定"
        :auto-apply="false"
        format="yyyy/MM/dd"
        locale="jp">
        <template #input-icon>
          <i class="material-symbols-outlined">calendar_month</i>
        </template>
      </VueDatePicker> -->
      <input
        type="date"
        max="9999-12-31"
        :class="{'hasError': props.error.length > 0}"
        :placeholder="placeholder"
        :disabled="disabled"
        v-model="formattedDate"
        >
    </div>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
const { $dayjs } = useNuxtApp() as any;
import dayjs from 'dayjs';

const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  value?: string | number,
  disabled?: boolean
  placeholder?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number
  error?: string
}>(), {
  title: '',
  required: false,
  value: '',
  disabled: false,
  placeholder: '',
  size: 'md',
  error: ''
});
const value = computed(() => {
  return props.value
});

const date = ref('' as any);

onMounted(() => {
  if (props.value) date.value = new Date(new Date(props.value).toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));
});

let sizeWidth:Ref<string> | string = ref('none');
const onCheckSizeClass = computed<string | number>(() => {
  const size = props.size;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})

const emit = defineEmits(['change']);

// isReload
watch(props, () => {
  if (props.value) date.value = new Date(new Date(props.value).toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));
})

// デフォルトのinput date消す場合ここの処理も消す
const formattedDate = computed({
  get() {
    return date.value ? dayjs(date.value).format('YYYY-MM-DD') : '';
  },
  set(value: string) {
    if (value) {
      const [year, month, day] = value.split('-');
      if (year.length === 4 && !year.startsWith('0')) {
        const dateString = `${year}-${month}-${day}`;
        if (dayjs(dateString).isValid()) {
          date.value = dayjs(dateString).toDate();
        }
      }
    }
    emit('change', $dayjs(date.value).format('YYYY-MM-DD'))
  }
});
</script>
<style lang="scss">
.inputCalendar {
  .dp__theme_light {
    --dp-primary-color: #2F587C;
  }
  .dp__input {
    color: #333;
    background: #FFF;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1;
    padding: 13px 12px 12px;
    width: 100%;
    &.hasError {
      border-color: $color-alert2;
    }
  }
  .dp__input_icon {
    color: #2F587C;
    line-height: 1;
    left: auto;
    right: 10px;
    i {
      font-size: 22px;
    }
  }
  .dp__clear_icon {
    display: none;
  }
  .dp__menu {
    font-size: 14px;
  }
  .dp__calendar_header_item ,
  .dp__cell_inner {
    width: 25px;
    height: 25px;
    padding: 0;
    margin: 0 auto;
  }
  .dp__disabled {
    background: var(--Gray_light, #F4F4F4);
  }
  .dp__disabled ~ * > .dp__input_icon {
    color: #D9D9D9;
  }
  .hasError .dp__input {
    border-color: $color-alert2;
  }
}
</style>
<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 7px;
  color: #49454F;
  white-space: nowrap;
}
.inputCalendar {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  position: relative;
  vertical-align: top;
  & + label{
    margin-left: 14px;
  }
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
  &.half{
    max-width: calc(50% - 7px);
    & + .half{
      margin-left: 14px;
    }
  }
  &.full{
    & + label{
      margin-left: 0;
    }
  }
}

.inputWrap {
  position: relative;
}
::placeholder {
  color: $color-placeholder;
}

// デフォルトのinput date消す場合ここのスタイルも消す
input{
  color: #333;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1;
  -webkit-appearance: none;
  appearance: none;
  text-align: left;
  padding: 13px 12px 12px;
  width: 100%;
  line-height: 1.4;
  &::-webkit-date-and-time-value {
    display: inline-block;
    min-height: 19px;
    text-align: left;
  }
  &.hasError,
  .hasError > * > &{
    border-color: $color-alert2;
  }
  &[disabled] {
    opacity: 0.4;
  }
}
label {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  position: relative;
  vertical-align: top;
  & + label{
    margin-left: 14px;
  }
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
  &.half{
    max-width: calc(50% - 7px);
    & + .half{
      margin-left: 14px;
    }
  }
  &.full{
    & + label{
      margin-left: 0;
    }
  }
}
</style>