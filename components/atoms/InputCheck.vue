<template>
  <span class="inputCheckbox">
    <div v-if="props.title" class="title">{{ props.title }}</div>
    <label
      v-for="(item, index) in items"
      :class="[{'is-block': props.block}]"
      :key="index"
    >
      <input
        type="checkbox"
        :name="props.name"
        :value="item.value"
        :checked="(checked.indexOf(item?.value) != -1)"
        :disabled="disabled"
        @click="eventChange"
      >
      <span>{{item.label}}</span>
    </label>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </span>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  name?: string,
  items?: {
    value: string | number,
    label: string
  }[],
  value: string[],
  disabled?: boolean,
  block?: boolean,
  error?: string
}>(), {
  name: '',
  value: () => { return []},
  disabled: false,
  block: false,
  error: ''
});

const checked = ref(props.value as string[]);

const emit = defineEmits(['change']);

const eventChange = ($event:any) => {
  const val = $event.target.value as string;
  if ($event.target.checked) {
    checked.value.push(val);
  } else {
    checked.value = checked.value.filter(item => item !== val);
  }
  emit('change' , checked.value);
};

// isReload
watch(props.value, (newVal, oldVal) => {
  if(newVal != oldVal) {
    checked.value = newVal;
  }
})
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 4px;
  color: #49454F;
}
label {
  display: inline-block;
  cursor: pointer;
  position: relative;
  margin-bottom: 12px;
  margin-right: 12px;
  &:last-child {
    margin-right: 0;
  }
  &.is-block {
    display: block;
  }
  input {
    display: none;
    &:checked {
      & + span {
        &::before{
          background-color: $color-main;
          border-color: $color-main;
        }
        &::after {
          content: "";
          display: inline-block;
          width: 11px;
          height: 6px;
          position: absolute;
          left: 3.5px;
          top: 5px;
          border-bottom: 2.5px solid #FFF;
          border-left: 2.5px solid #FFF;
          transform: rotate(-45deg);
          background: none !important;
        }
      }
    }
    &:disabled {
      & + span {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
  & > span {
    display: inline-block;
    font-size: 14px;
    color: #333;
    line-height: 18px;
    position: relative;
    padding-left: 24px;
    height: 16px;
    &::before {
      content: "";
      display: inline-block;
      width: 18px;
      height: 18px;
      border-radius: 2px;
      border: solid 1px #9C9C9C;
      background: #FFF;
      position: absolute;
      left: 0;
    }
  }
}
</style>