<template>
  <span>
    <div v-if="props.title" class="title">{{ props.title }}</div>
    <label
      class="input-btn"
      v-for="option in options"
      :key="option.value"
    >
      <input
        type="checkbox"
        :value="option.value"
        :checked="(checked.indexOf(option?.value) !== -1)"
        :disabled="disabled"
        @change="eventChange"
      >
      <span>{{option.label}}</span>
    </label>
  </span>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  options?: {
    value: string,
    label: string
  }[],
  value: string[],
  disabled: boolean,
}>(), {
  value: () => { return []},
  disabled: false,
});

const checked = ref(props.value as string[]);

const emit = defineEmits(['change']);

const eventChange = ($event:any) => {
  const val = $event.target.value as string;
  if ($event.target.checked) {
    checked.value.push(val);
  } else {
    checked.value = checked.value.filter(item => item !== val);
  }
  emit('change' , checked.value);
};

// isReload
watch(props, () => {
  checked.value = props.value;
})
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1.6;
  margin-bottom: 4px;
  color: #49454F;
}
.input-btn {
  input {
    display: none;
  }
  input ~ span {
    list-style: none;
    margin-right: 3px;
    padding: 3px 12px;
    background: $color-mainbackground;
    border: 1px solid $color-grayborder;
    border-radius: 100px;
    font-size: 12px;
    line-height: 120%;
    letter-spacing: 0.02em;
    color: $color-blacktext2;
    cursor: pointer;

    &:hover {
      border-color: $color-main;
      color: $color-main;
      // background-color: $color-main;
      // color: $color-whitetext;
    }
  }
  input:checked ~ span {
    border-color: $color-main;
    color: $color-main;
    // background-color: $color-main;
    // color: $color-whitetext;
  }
}
</style>