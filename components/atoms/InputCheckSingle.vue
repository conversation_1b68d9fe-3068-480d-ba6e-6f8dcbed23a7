<template>
  <span>
    <div v-if="props.title" class="title">{{ props.title }}</div>
    <label>
      <input
        type="checkbox"
        :name="props.name"
        :value="props.value"
        :checked="props.checked"
        :disabled="props.disabled"
        @click="eventChange"
      >
      <span>{{props.label}}</span>
    </label>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </span>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  name?: string,
  value?: string | number | boolean,
  label?: string,
  checked?: boolean,
  disabled?: boolean,
  error?: string
}>(), {
  name: '',
  disabled: false,
  block: false,
  error: ''
});

const emit = defineEmits(['change']);

const eventChange = ($event:any) => {
  emit('change' , $event.target.checked);
};

</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 4px;
  color: #49454F;
}
label {
  display: inline-block;
  cursor: pointer;
  position: relative;
  margin-bottom: 12px;
  margin-right: 12px;
  &:last-child {
    margin-right: 0;
  }
  input {
    display: none;
    &:checked {
      & + span {
        &::before{
          background-color: $color-main;
          border-color: $color-main;
        }
        &::after {
          content: "";
          display: inline-block;
          width: 14px;
          height: 8px;
          position: absolute;
          left: 2px;
          top: 4px;
          border-bottom: 2.5px solid #FFF;
          border-left: 2.5px solid #FFF;
          transform: rotate(-45deg);
        }
      }
      &:disabled {
        & + span {
          opacity: 0.5;
          pointer-events: none;
          &::before {
            background-color: $color-main;
            border-color: $color-main;
          }
        }
      }
    }
    &:disabled {
      & + span {
        opacity: 0.5;
        pointer-events: none;
        &::before {
          background: #F4F4F4;
        }
      }
    }
  }
  & > span {
    display: inline-block;
    font-size: 14px;
    color: #333;
    line-height: 18px;
    position: relative;
    padding-left: 24px;
    height: 16px;
    &::before {
      content: "";
      display: inline-block;
      width: 18px;
      height: 18px;
      border-radius: 2px;
      border: solid 1px #9C9C9C;
      background: #FFF;
      position: absolute;
      left: 0;
    }
  }
}
</style>