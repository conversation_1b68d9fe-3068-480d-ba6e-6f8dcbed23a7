<template>
  <span>
    <div v-if="props.title" class="title">{{ props.title }}</div>
    <label>
      <input
        type="checkbox"
        :name="props.name"
        :value="props.value"
        :checked="isChecked"
        :disabled="props.disabled"
        @change="eventChange"
      >
      <span>{{ props.label }}<slot /><span class="label-required">*</span></span>
    </label>
    <span class="input-error">{{ props.error }}</span>
  </span>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = withDefaults(defineProps<{
  title?: string,
  name?: string,
  value?: string | number | boolean,
  label?: string,
  checked?: boolean | null,
  disabled?: boolean,
  error?: string
}>(), {
  name: '',
  disabled: false,
  error: ''
});

const emit = defineEmits(['change']);
const isChecked = ref(props.checked ?? null);

watch(() => props.checked, (newChecked) => {
  isChecked.value = newChecked ?? null;
});

const eventChange = ($event: Event) => {
  const target = $event.target as HTMLInputElement;
  isChecked.value = target.checked ? target.checked : null;
  emit('change', isChecked.value);
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 12px;
  line-height: 1;
  margin-bottom: 4px;
  color: #49454F;
}
label {
  display: inline-block;
  cursor: pointer;
  position: relative;
  margin-bottom: 12px;
  margin-right: 12px;
  .label-required {
    margin-left: 5px;
    vertical-align: top;
    font-size: 12px;
    color: $color-alert2;
  }
  &:last-child {
    margin-right: 0;
  }
  input {
    display: none;
    &:checked {
      & + span {
        &::before {
          background-color: $color-main;
          border-color: $color-main;
        }
        &::after {
          content: "";
          display: inline-block;
          width: 14px;
          height: 8px;
          position: absolute;
          left: 2px;
          top: 4px;
          border-bottom: 2.5px solid #FFF;
          border-left: 2.5px solid #FFF;
          transform: rotate(-45deg);
        }
      }
    }
    &:disabled {
      & + span {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
  & > span {
    display: inline-block;
    font-size: 14px;
    color: #333;
    line-height: 18px;
    position: relative;
    padding-left: 24px;
    height: 16px;
    &::before {
      content: "";
      display: inline-block;
      width: 18px;
      height: 18px;
      border-radius: 2px;
      border: solid 1px #9C9C9C;
      background: #FFF;
      position: absolute;
      left: 0;
    }
  }
}
</style>
