<template>
  <label
    ref="inputDatalist"
    class="inputDatalist"
    :class="onCheckSizeClass"
  >
    <div v-if="props.title" class="title"><span :class="{'required': props.required}">{{ props.title }}</span></div>
    <div class="inputWrap">
      <input
        ref="inputRef"
        type="text"
        v-model="textValue"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        :class="{'hasError': props.error.length > 0, 'isShowDatalist': (isShowDatalist && showOptions.length)}"
        @focus="onInputFocus"
      >
      <ul class="datalist" v-if="isShowDatalist">
        <li v-for="(option, index) in showOptions" :key="index">
          <a @click.prevent="onClickDatalistItem(option)">{{ option.label }}</a>
        </li>
        <!-- <li v-if="! showOptions.length">
          <span v-if="textValue">見つかりませんでした</span>
          <span v-else>候補がありません</span>
        </li> -->
      </ul>
    </div>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </label>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { onClickOutside } from '@vueuse/core';
import '@vueform/multiselect/themes/default.css';

const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  value?: string | number,
  options: {
    value: string,
    label: string
  }[],
  disabled?: boolean
  placeholder?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number
  error?: string
}>(), {
  title: '',
  required: false,
  value: '',
  disabled: false,
  placeholder: '',
  size: 'md',
  error: ''
});

let sizeWidth:Ref<string> | string = ref('none');
const onCheckSizeClass = computed<string | number>(() => {
  const size = props.size;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})

const textValue = ref(props.value as string);
const showOptions = ref(props.options as any[]);

const emit = defineEmits(['change']);

// isReload
watch(textValue, () => {
  setOptions()
  emit('change', textValue.value);
})
watch(props.options, () => {
  setOptions()
})

const isShowDatalist = ref(false);
const inputRef = ref(null);
const onInputFocus = () => {
  setOptions()
  isShowDatalist.value = true;
};

const onClickDatalistItem = (option:any) => {
  textValue.value = option?.value;
}

const inputDatalist = ref(null);
onClickOutside(inputDatalist, () => {
  isShowDatalist.value = false;
});

const setOptions = () => {
  if (textValue.value) {
    showOptions.value = props.options.filter(option => (option.value.indexOf(textValue.value) !== -1))
  } else {
    showOptions.value = props.options;
  }
}
</script>

<style lang="scss" scoped>
.inputWrap {
  position: relative;
  // &::before{
  //   content: '';
  //   border-style: solid;
  //   border-width: 5px 5px 0 5px;
  //   border-color: #333 transparent transparent transparent;
  //   position: absolute;
  //   bottom: 18px;
  //   right: 10px;
  //   pointer-events: none;
  // }
  input{
    color: #333;
    background: #FFF;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1;
    padding: 13px 12px 12px;
    width: 100%;
    height: 44px;
    outline: unset;
    &.hasError {
      border-color: $color-alert2;
    }
    &[disabled] {
      opacity: 0.4;
    }
    &.isShowDatalist {
      border-radius: 4px 4px 0 0;
    }
  }
  .datalist {
    // display: none;
    position: absolute;
    top: 44px;
    left: 0;
    right: 0;
    z-index: 100;
    border-left: 1px solid #D9D9D9;
    border-right: 1px solid #D9D9D9;
    li {
      border-bottom: 1px solid #D9D9D9;
    }
    a {
      cursor: pointer;
      &:hover {
        background: #f5f5f5;
      }
    }
    li > a ,
    li > span {
      display: block;
      padding: 8px 10px;
      font-size: 12px;
      background: #fff;
      color: #333;
    }
  }
}
label.inputDatalist {
  .title{
    font-size: 12px;
    line-height: 1;
    margin-bottom: 4px;
    color: #49454F;
  }
  & {
    display: inline-block;
    width: 100%;
    position: relative;
    max-width: v-bind(sizeWidth);
    &.xs{
      max-width: 87px;
    }
    &.sm{
      max-width: 164px;
    }
    &.md{
      max-width: 180px;
    }
    &.ml{
      max-width: 220px;
    }
    &.lg{
      max-width: 293px;
    }
    &.xl{
      max-width: 343px;
    }
  }
}
</style>