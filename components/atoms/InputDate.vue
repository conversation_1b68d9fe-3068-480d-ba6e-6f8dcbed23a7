<template>
<div>
  <div class="row inputDate">
    <InputNumber
      :title="title"
      :disabled="disabled"
      :required="required"
      :placeholder="placeholder.replace(/^(.+)\-(.+)\-(.+)$/, '$1')"
      maxlength="4"
      size="xs"
      :value="input_y"
      :error="props.error"
      @update="update('y', $event)"
    />
    <span class="unit">年</span>
    <InputNumber
      size="xs"
      :placeholder="placeholder.replace(/^(.+)\-(.+)\-(.+)$/, '$2')"
      maxlength="2"
      :disabled="disabled"
      :value="input_m"
      :error="props.error"
      @update="update('m', $event)"
    />
    <span class="unit">月</span>
    <InputNumber
      size="xs"
      :placeholder="placeholder.replace(/^(.+)\-(.+)\-(.+)$/, '$3')"
      maxlength="2"
      :disabled="disabled"
      :value="input_d"
      :error="props.error"
      @update="update('d', $event)"
    />
    <span class="unit">日</span>
  </div>
  <span v-if="props.error" class="input-error">{{ props.error }}</span>
</div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const { $dayjs } = useNuxtApp() as any;

const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  value?: string,
  disabled?: boolean,
  placeholder?: string,
  options?: {
    value?: string,
    label?: string
  }[],
  error?: string
}>(), {
  value: '',
  required: false,
  disabled: false,
  error: '',
  placeholder: '1995-01-01'
});

const input = ref('')
const input_y = ref('')
const input_m = ref('')
const input_d = ref('')

const emit = defineEmits(['change']);

onMounted(() => {
  input.value = String(props.value)
  if (input.value.match(/^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})$/)) {
    input_y.value = props.value.replace(/^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})$/, '$1');
    input_m.value = props.value.replace(/^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})$/, '$2').replace(/^0/, '');
    input_d.value = props.value.replace(/^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})$/, '$3').replace(/^0/, '');
  }
})

// isReload
watch(() => props.value, (newVal) => {
  input.value = String(newVal)
  if (newVal.match(/^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})$/)) {
    input_y.value = newVal.replace(/^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})$/, '$1');
    input_m.value = newVal.replace(/^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})$/, '$2').replace(/^0/, '');
    input_d.value = newVal.replace(/^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})$/, '$3').replace(/^0/, '');
  }
})

const update = (key:string, value:string) => {
  if (key == 'y') {
    input_y.value = value;
  } else if (key == 'm') {
    input_m.value = value;
  } else if (key == 'd') {
    input_d.value = value;
  }

  let m = input_m.value;
  let d = input_d.value;
  if (m.length == 1) m = '0' + m;
  if (d.length == 1) d = '0' + d;
  if(!input_y.value && !m && !d){
    emit('change', null)
  }else{
    emit('change', input_y.value+'-'+m+'-'+d)
  }
};
</script>

<style lang="scss">
.inputDate {
  @include sp {
    display: flex;
    align-items: flex-end;
  }
  > * {
    vertical-align: bottom !important;
  }
  .unit{
    color: #333;
    margin: 0 20px 0 4px;
    font-size: 12px;
    display: inline-block;
    margin-bottom: 16px;
  }
  label .input-error { display: none; }
}

</style>