<template>
  <div class="upload">
    <div class="preview">
      <div ref="dropzone" class="dropzoneWrap">
        <div class="dropArea" :class="dropzoneClassList" v-if="files.length < maxCnt">
          <img src="@/assets/images/icon-upload-cloud.svg" alt="">
          <p>
            <strong>ここに写真をドラッグ＆ドロップ</strong>
            または
          </p>
          <label class="fileInput">
            <input
              type="file"
              :multiple="(maxCnt > 1) ? true : false"
              @change="onClickUpload"
            />
            <span>ファイルを選択</span>
          </label>
          <small>※1ファイルにつき 最大{{ props.maxFileSize }}MBまで</small>
        </div>
      </div>
      <div class="upload-result" v-for="(file, i) in files" :key="i">
        <button type="button" class="btn btn-delete" @click="onClickDelete(i)"></button>
        <img :src="file.fileData">
      </div>
    </div>
    <span v-if="error" class="input-error">{{ error }}</span>
    <span v-else-if="props.error" class="input-error">{{ props.error }}</span>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  // 画像ファイルサイズ (MB)
  maxFileSize?: number;
  // 選択できる画像数
  maxCnt?: number;
  error?: string;
  value?: string[] | string;
  imageUploadType: 'user' | 'admin';
}>(), {
  maxFileSize: 10,
  maxCnt: 1,
  imageUploadType: 'user'
});

// 更新API
let adminSettings = {};
if(props.imageUploadType == 'admin'){
  adminSettings = {clientId: 'admin'}
}
const { mutate } = useBinaryUploadImage2Mutation(adminSettings);

const emits = defineEmits<{
  (e: 'change', files: string[] | string): void;
}>();

const error = ref('' as string);
const files = ref([] as { fileName: string, fileData: string}[]);
const uploadFiles = ref('');
const dropzone = ref<HTMLElement>();
const dropzoneClassList = ref([] as string[]);

onMounted(() => {
  // 初期値がある場合
  files.value = [];
  if (props.value) {
    if (props.maxCnt > 1) {
      for (let i = 0; i < props.value.length; i++) {
        const value = props.value[i];
        files.value.push({fileName: getFileName(value), fileData: value});
      }
    } else {
      files.value.push({fileName: getFileName(String(props.value)), fileData: String(props.value)});
    }
  }

  // ドラッグオーバー時の処理
  dropzone.value?.addEventListener('dragover', function(e:any){
      e.preventDefault();
      if (dropzoneClassList.value.indexOf('is-dragover') === -1) {
        dropzoneClassList.value.push('is-dragover');
      }
  });

  // ドラッグアウト時の処理
  dropzone.value?.addEventListener('dragleave', function(e:any){
      e.preventDefault();
      dropzoneClassList.value = [];
  });

  // ドロップ時の処理
  dropzone.value?.addEventListener('drop', function(e:any){
      e.preventDefault();
      dropzoneClassList.value = [];
      upload(e.dataTransfer.files);
  });
});

// クリック時の処理
const onClickUpload = (e: any) => {
  upload(e.target.files);
};

// アップロード処理
const upload = async (inputFiles: any[]) => {
  for (let i = 0; i < inputFiles.length; i++) {
    const file = inputFiles[i];
    if (files.value.length >= props.maxCnt) break;

    // 選ばれなかったとき
    if (! file) {
      continue;
    }

    error.value = '';
    // サイズは1MB以下
    if (file.size / 1024 / 1024 >= props.maxFileSize) {
      error.value = '画像ファイルは'+String(props.maxFileSize)+'MB以下でアップロードしてください';
      continue;
    }

    // 変換が終わったら実行される
    if (files.value.length < props.maxCnt) {

      // アップロードした画像情報をプッシュ
      // console.log('file')
      // console.log(file)
      const previewData = await convertBase64(file);
      files.value.push({
        fileName: file.name,
        fileData: previewData
      });

      let formData = new FormData();
      formData.append('file_data', file);

      let imageFileType = FileType.FileTypeMemberMaterial;
      if(props.imageUploadType == 'admin'){
        imageFileType = FileType.FileTypeProductMaster;
      }
      try {
        const response = await mutate({
          fileData: file,
          fileType: imageFileType
        });
        uploadFiles.value = response?.data?.binaryUploadImage2;
        // console.log('upload response');
        // console.log(response);
      } catch (error) {
        // console.error('Upload error:', error);
      }
    }
    change();
  }

  // if (files.value.length > 0) {
  //   const formData = appendFilesToFormData(files.value);
  //   console.log('formData2')
  //   console.log(formData)
  //   try {
  //     const response = await mutate({
  //       fileType: FileType.FileTypeMemberMaterial,
  //       fileData: formData
  //     });
  //     console.log(response);
  //   } catch (error) {
  //     console.error('Upload error:', error);
  //   }
  // }
}

// ファイルをFormDataに追加する関数
const appendFilesToFormData = (uploadFiles: any) => {
  // console.log('uploadFiles')
  // console.log(uploadFiles)
  const formData = new FormData();
  uploadFiles.forEach((file: any) => {
    // console.log('file')
    // console.log(file)
    formData.append('file', file.fileData);
  });
  // console.log('formData1')
  // console.log(formData)
  return formData;
};

const onClickDelete = (index:number) => {
  let newFiles = [];
  for (let i = 0; i < files.value.length; i++) {
    if (index == i) continue;
    newFiles.push(files.value[i]);
  }
  files.value = newFiles;
  change();
};

const change = () => {
  // 1件のみなら 画像1個だけ返す
  if (props.maxCnt == 1) {
    if (files.value.length) {
      emits('change', uploadFiles.value);
    } else {
      emits('change', '');
    }
  } else {
    emits('change', uploadFiles.value);
  }
};
</script>

<style lang="scss" scoped>
.upload .dropArea {
  margin: 33px 0;
  padding: 30px 30px 32px;
  border-radius: 10px;
  border: 2px dashed $color-graytext2;
  background: $color-mainbackground;
  text-align: center;
  &.is-dragover {
    border-style: solid;
    background: rgba($color-graytext2, .1);
  }
  p {
    margin: 14px 0 17px;
    color: $color-blacktext2;
    font-size: 16px;
    letter-spacing: 0.96px;
    strong {
      display: block;
      margin-bottom: 16px;
      font-size: 20px;
      font-weight: 700;
      letter-spacing: 1.2px;
    }
  }
  .fileInput {
    display: block;
    max-width: 293px;
    margin: 0 auto 23px;
    padding: 17px 20px;
    border-radius: 2px;
    background: $color-main;
    cursor: pointer;
    span {
      display: inline-block;
      position: relative;
      padding: 0 7px 0 36px;
      color: $color-whitetext;
      font-size: 16px;
      font-weight: 700;
      &::before {
        @include BA;
        left: 0;
        width: 27px;
        height: 26px;
        background-image: url(@/assets/images/icon-fileimage.svg);
      }
    }
    input {
      display: none;
    }
  }
  small {
    color: $color-blacktext2;
    font-size: 14px;
    letter-spacing: 0.84px;
    padding-left: 1em;
    text-indent: -1em;
  }
}

.upload-result {
  margin-left: 5px;
  display: inline-block;
  width: 100px;
  height: 100px;
  vertical-align: top;
  position: relative;
  margin-right: 20px;

  img {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    object-fit: contain;
  }
  .btn.btn-delete {
    position: absolute;
    right: -10px;
    top: -10px;
    background: #888;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    z-index: 1;
    padding: 0;
    &:before ,
    &:after {
      content: " ";
      width: 16px;
      height: 2px;
      background: #fff;
      display: block;
      position: absolute;
      transform: rotate(45deg);
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -8px;
      margin-top: -1px;
    }
    &:after {
      transform: rotate(-45deg);
    }
    &:hover {
      background: #222;
    }
    ~ img {
      border: 1px solid #ccc;
    }
  }
}
</style>