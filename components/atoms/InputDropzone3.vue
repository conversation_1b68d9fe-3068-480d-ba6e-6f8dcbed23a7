<template>
  <div class="upload">
    <div class="preview">
      <template v-if="loading">
        <div class="preview_loading">画像をアップロード中...</div>
      </template>
      <template v-else>
        <div ref="dropzone" class="dropzoneWrap">
          <div class="dropArea" :class="dropzoneClassList">
            <img src="@/assets/images/icon-upload-cloud.svg" alt="">
            <p>
              <strong>ここに写真をドラッグ＆ドロップ</strong>
              または
            </p>
            <label class="fileInput">
              <input
                type="file"
                accept="image/*,video/*"
                :multiple="(maxCnt > 1) ? true : false"
                @change="onClickUpload"
              />
              <span>ファイルを選択</span>
            </label>
            <small>※1ファイルにつき 最大{{ props.maxFileSize }}MBまで</small>
          </div>
        </div>
      </template>
    </div>
    <span v-if="errorText" class="input-error">{{ errorText }}</span>
    <span v-else-if="props.error" class="input-error">{{ props.error }}</span>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  // 画像ファイルサイズ (MB)
  maxFileSize?: number;
  // 選択できる画像数
  maxCnt?: number;
  error?: string;
  value?: string[] | string;
  imageUploadType: 'user' | 'admin';
}>(), {
  maxFileSize: 50,
  maxCnt: 1,
  imageUploadType: 'user'
});

// 更新API
let adminSettings = {};
if(props.imageUploadType == 'admin'){
  adminSettings = {clientId: 'admin'}
}
const { mutate, loading } = useBinaryUploadImage2Mutation(adminSettings);

// const emits = defineEmits<{
//   (e: 'change', files: string[] | string): void;
// }>();
const emits = defineEmits<{
  (e: 'change', file: PreviewData | string): void;
  // (e: 'change', files: PreviewData[]): void;
}>();

const errorText = ref('' as string);
const files = ref([] as { fileName: string, fileData: string}[]);
const uploadFiles = ref('');
const dropzone = ref<HTMLElement>();
const dropzoneClassList = ref([] as string[]);

interface PreviewData {
  fileName: string;
  fileData: string | boolean;
  fileType: string;
}

const previewData: Ref<PreviewData> = ref({
  fileName: '',
  fileData: '',
  fileType: ''
});

onMounted(() => {
  // 初期値がある場合
  files.value = [];
  if (props.value) {
    if (props.maxCnt > 1) {
      for (let i = 0; i < props.value.length; i++) {
        const value = props.value[i];
        files.value.push({fileName: getFileName(value), fileData: value});
      }
    } else {
      files.value.push({fileName: getFileName(String(props.value)), fileData: String(props.value)});
    }
  }

  // ドラッグオーバー時の処理
  dropzone.value?.addEventListener('dragover', function(e:any){
      e.preventDefault();
      if (dropzoneClassList.value.indexOf('is-dragover') === -1) {
        dropzoneClassList.value.push('is-dragover');
      }
  });

  // ドラッグアウト時の処理
  dropzone.value?.addEventListener('dragleave', function(e:any){
      e.preventDefault();
      dropzoneClassList.value = [];
  });

  // ドロップ時の処理
  dropzone.value?.addEventListener('drop', function(e:any){
      e.preventDefault();
      dropzoneClassList.value = [];
      upload(e.dataTransfer.files);
  });
});

// クリック時の処理
const onClickUpload = (e: any) => {
  upload(e.target.files);
};

// アップロード処理
const upload = async (inputFiles: any[]) => {
  for (let i = 0; i < inputFiles.length; i++) {
    const file = inputFiles[i];

    if (files.value.length >= props.maxCnt) break;

    errorText.value = '';

    // サイズは64MB以下
    if (file.size / 1024 / 1024 >= props.maxFileSize) {
      errorText.value = 'ファイルは'+String(props.maxFileSize)+'MB以下でアップロードしてください';
      continue;
    }

    // 変換が終わったら実行される
    if (files.value.length < props.maxCnt) {
      let base64, fileType;
      if (file.type.startsWith('image/')) {
        // 画像ファイルの場合
        base64 = await convertBase64(file);
        fileType = 'IMAGE'
      } else {
        // 動画ファイルの場合
        try {
          base64 = await convertBase64Video(file);
          fileType = 'VIDEO'
        } catch (error) {
          errorText.value = error.message || '対応していない動画形式です';
          continue;
        }
      }

      // 変換処理に成功した場合のみデータを更新
      if (base64 != false) {
        previewData.value.fileName = file.name;
        previewData.value.fileType = fileType;
        previewData.value.fileData = base64;
      }
    }
    change();
  }
}

const change = () => {
  if (props.maxCnt == 1) {
    if (previewData.value.fileData) {
      emits('change', previewData.value);
    } else {
      emits('change', { fileName: '', fileData: '', fileType: '' });
    }
  } else {
    emits('change', previewData.value);
  }
};
</script>

<style lang="scss" scoped>
.preview_loading{
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #9C9C9C;
  font-weight: bold;
  font-size: 16px;
  min-height: 388px;
}
.upload .dropArea {
  margin: 33px 0;
  padding: 30px 30px 32px;
  border-radius: 10px;
  border: 2px dashed $color-graytext2;
  background: $color-mainbackground;
  text-align: center;
  &.is-dragover {
    border-style: solid;
    background: rgba($color-graytext2, .1);
  }
  p {
    margin: 14px 0 17px;
    color: $color-blacktext2;
    font-size: 16px;
    letter-spacing: 0.96px;
    strong {
      display: block;
      margin-bottom: 16px;
      font-size: 20px;
      font-weight: 700;
      letter-spacing: 1.2px;
    }
  }
  .fileInput {
    display: block;
    max-width: 293px;
    margin: 0 auto 23px;
    padding: 17px 20px;
    border-radius: 2px;
    background: $color-accent;
    cursor: pointer;
    span {
      display: inline-block;
      position: relative;
      padding: 0 7px 0 36px;
      color: $color-whitetext;
      font-size: 16px;
      font-weight: 700;
      &::before {
        @include BA;
        left: 0;
        width: 27px;
        height: 26px;
        background-image: url(@/assets/images/icon-fileimage.svg);
      }
    }
    input {
      display: none;
    }
  }
  small {
    color: $color-blacktext2;
    font-size: 14px;
    letter-spacing: 0.84px;
    padding-left: 1em;
    text-indent: -1em;
  }
}

.upload-result {
  margin-left: 5px;
  display: inline-block;
  width: 100px;
  height: 100px;
  vertical-align: top;
  position: relative;
  margin-right: 20px;

  img {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    object-fit: contain;
  }
  .btn.btn-delete {
    position: absolute;
    right: -10px;
    top: -10px;
    background: #888;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    z-index: 1;
    padding: 0;
    &:before ,
    &:after {
      content: " ";
      width: 16px;
      height: 2px;
      background: #fff;
      display: block;
      position: absolute;
      transform: rotate(45deg);
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -8px;
      margin-top: -1px;
    }
    &:after {
      transform: rotate(-45deg);
    }
    &:hover {
      background: #222;
    }
    ~ img {
      border: 1px solid #ccc;
    }
  }
}
</style>