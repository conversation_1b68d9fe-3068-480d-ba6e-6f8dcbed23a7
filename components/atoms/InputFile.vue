<template>
  <div class="inputFile">
    <div class="inputFileResults" v-if="files.length">
      <div class="inputFileResult" v-for="(file, i) in files" :key="i">
        <button type="button" class="btn-delete" @click="onClickDelete(i)"></button>
        <img :src="file.base64">
        <div class="fileName">{{ file.fileName }}</div>
      </div>
    </div>
    <label class="inputFileBtn" v-if="files.length < maxCnt">
      <span class="icn"></span>
      <input 
        type="file" 
        accept="image/*"
        :multiple="(maxCnt > 1) ? true : false"
        @change="onClickUpload"
      />
      <span v-if="props.placeholder">{{ props.placeholder }}</span>
    </label>
    <span v-if="error" class="input-error">{{ error }}</span>
    <span v-else-if="props.error" class="input-error">{{ props.error }}</span>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  placeholder?: string;
  // 画像ファイルサイズ (MB)
  maxFileSize?: number;
  // 画像最大幅 (px)
  maxImageSize?: number;
  // 選択できる画像数
  maxCnt?: number;
  error?: string;
  value?: string[] | string;
}>(), {
  maxFileSize: 10,
  maxCnt: 1
});

const emits = defineEmits<{
  (e: 'change', files: string[] | string): void;
}>();

const error = ref('' as string);
const files = ref([] as {fileName: string, base64: string}[]);

// 初期値がある場合
onMounted(() => {
  files.value = [];
  if (props.value) {
    if (props.maxCnt > 1) {
      for (let i = 0; i < props.value.length; i++) {
        const value = props.value[i];
        files.value.push({fileName: getFileName(value), base64: value});
      }
    } else {
      files.value.push({fileName: getFileName(String(props.value)), base64: String(props.value)});
    }
  }
})

// クリック時の処理
const onClickUpload = async(e: any) => {
  for (let i = 0; i < e.target.files.length; i++) {
    const file = e.target.files[i];
    if (files.value.length >= props.maxCnt) break;

    // 選ばれなかったとき
    if (! file) {
      continue;
    }

    error.value = '';
    if (file.type != 'image/png' && file.type != 'image/jpeg' && file.type != 'image/gif') {
      error.value = '画像ファイルはJPEG PNG GIF形式でアップロードしてください';
      continue;
    }

    // サイズは1MB以下
    if (file.size / 1024 / 1024 >= props.maxFileSize) {
      error.value = '画像ファイルは'+String(props.maxFileSize)+'MB以下でアップロードしてください';
      continue;
    }

    // 変換が終わったら実行される
    if (files.value.length < props.maxCnt) {
      const base64 = await convertBase64(file);
      files.value.push({fileName: file.name, base64:base64});
      change();
    }
  }
}
const onClickDelete = (index:number) => {
  let newFiles = [];
  for (let i = 0; i < files.value.length; i++) {
    if (index == i) continue;
    newFiles.push(files.value[i]);
  }
  files.value = newFiles;
  change();
};

const change = () => {
  // 1件のみなら 画像1個だけ返す
  if (props.maxCnt == 1) {
    if (files.value.length) {
      emits('change', files.value[0].base64);
    } else {
      emits('change', '');
    }
  } else {
    emits('change', files.value.map(file => file.base64));
  }
};
</script>

<style lang="scss" scoped>

.inputFileBtn {
  padding: 5px;
  display: flex;
  width: 100%;
  color: #B29358;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
  .icn {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 1px dashed #B29358;
    margin-right: 12px;
    transition: all 0.3s ease;
    position: relative;
    &:before ,
    &:after {
      content: " ";
      width: 20px;
      height: 2px;
      background: #B29358;
      display: block;
      position: absolute;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -10px;
      margin-top: -1px;
    }
    &:after {
      transform: rotate(90deg);
    }
  }
  input { display: none;}
  &:hover {
    .icn {
      border-style: solid;
      background: rgba(#B29358, .1);
    }
  }
}
.inputFileResult {
  display: flex;
  width: 100%;
  align-items: center;
  font-size: 12px;
  padding: 5px;
  position: relative;
  border-radius: 6px;
	transition: all 0.3s ease;
  &:hover {
    background: #EEEEEE;
  }
  img {
    object-fit: cover;
    width: 50px;
    height: 50px;
    flex-shrink: 0;
  }
  .fileName {
    margin-left: 20px;
    word-break: break-all;
    @include lineClamp(2);
    line-height: 1.4;
    max-height: 2.8em;
    margin-right: 30px;
  }
  .btn-delete {
    position: absolute;
    right: 10px;
    top: 50%;
    width: 26px;
    height: 26px;
    margin-top: -13px;
    border-radius: 50%;
    z-index: 1;
    transition: all 0.3s ease;
    &:before ,
    &:after {
      content: " ";
      width: 16px;
      height: 2px;
      background: #333;
      display: block;
      position: absolute;
      transform: rotate(45deg);
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -8px;
      margin-top: -1px;
    }
    &:after {
      transform: rotate(-45deg);
    }
    &:hover {
      opacity: .6;
    }
  }
}
</style>