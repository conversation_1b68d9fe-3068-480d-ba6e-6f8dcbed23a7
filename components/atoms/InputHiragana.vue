<template>
  <label
    :class="onCheckSizeClass"
  >
    <div v-if="props.title" class="title"><span :class="{'required': props.required}">{{ props.title }}</span></div>
    <div :class="{'withUnit' : props.unit, 'inputWrap': true}">
      <input
        ref="inputRef"
        type="text"
        :name="props.name"
        v-model="inputValue"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        :class="{'hasError': props.error.length > 0}"
        :maxlength="maxlength"
        :autocomplete="props.autocomplete"
      >
      <span v-if="props.unit" class="unit">{{ props.unit }}</span>
    </div>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </label>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  value?: string | number,
  name?: string,
  disabled?: boolean,
  placeholder?: string,
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string,
  // 形式 upperCase: 自動で大文字変換
  format?: 'capitalize' | 'upperCase' | 'lowerCase' | '',
  maxlength?: string,
  autocomplete?: string
}>(), {
  title: '',
  required: false,
  value: '',
  name: '',
  disabled: false,
  placeholder: '',
  size: 'md',
  error: '',
  format: '',
  maxlength: '255',
  autocomplete: ''
});
const emits = defineEmits<{
  (e: 'update', value: string): void;
}>()


let sizeWidth:Ref<string> | string = ref('none');
const onCheckSizeClass = computed<string | number>(() => {
  const size = props.size;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})

const inputValue = ref(props.value as string);
watch(() => props.value, (newValue) => {
  inputValue.value = newValue;
});
watch(inputValue, () => {
  let str = inputValue.value;
  // 半角カタカナを全角カタカナに変換
  str = toZenKata(str);
  // 全角カタカナをひらがなに変換
  str = str.replace(/[\u30a1-\u30f6]/g, function(match:any) {
      return String.fromCharCode(match.charCodeAt(0) - 0x60);
  });
  // スペースを全角に
  str = str.replace(/\s/g,"　");
  // ひらがな以外削除
  str = str.replace(/[^ぁ-ん　]/g,"");

  inputValue.value = str;
  emits('update', inputValue.value);
})

//muddy
const D_MUD = 'ガギグゲゴザジズゼゾダヂヅデドバビブベボパピプペポヴヷヺ';
const S_MUD = 'ｶﾞｷﾞｸﾞｹﾞｺﾞｻﾞｼﾞｽﾞｾﾞｿﾞﾀﾞﾁﾞﾂﾞﾃﾞﾄﾞﾊﾞﾋﾞﾌﾞﾍﾞﾎﾞﾊﾟﾋﾟﾌﾟﾍﾟﾎﾟｳﾞﾜﾞｦﾞ';
//kiyone
const D_KIY = 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホ'
            +'マミムメモヤユヨラリルレロワヲンァィゥェォッャュョ。、ー「」・';
const S_KIY = 'ｱｲｳｴｵｶｷｸｹｺｻｼｽｾｿﾀﾁﾂﾃﾄﾅﾆﾇﾈﾉﾊﾋﾌﾍﾎﾏﾐﾑﾒﾓﾔﾕﾖﾗﾘﾙﾚﾛﾜｦﾝｧｨｩｪｫｯｬｭｮ｡､ｰ｢｣･';

const toZenKata = (str:string) => {
    for (let i=0,len=D_MUD.length; i<len; i++) {
        str = str.split( S_MUD.slice(i*2, i*2+2) ).join( D_MUD.slice(i, i+1) );
    }
    for (let i=0,len=D_KIY.length; i<len; i++) {
        str = str.split( S_KIY.slice(i, i+1) ).join( D_KIY.slice(i, i+1) );
    }
    return str;
};
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 7px;
  color: #49454F;
  white-space: nowrap;
}
input{
  color: #333;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1;
  padding: 13px 12px 12px;
  width: 100%;
  &.hasError,
  .hasError > * > &{
    border-color: $color-alert2;
  }
  &[disabled] {
    opacity: 0.4;
  }
}
label {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  position: relative;
  vertical-align: top;
  & + label{
    margin-left: 14px;
  }
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
  &.half{
    max-width: calc(50% - 7px);
    & + .half{
      margin-left: 14px;
    }
  }
  &.full{
    & + label{
      margin-left: 0;
    }
  }
}

.inputWrap {
  position: relative;
}
::placeholder {
  color: $color-placeholder;
}

</style>