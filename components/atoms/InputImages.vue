
<template>
  <div class="UploadedImageBoxs" v-if="images.length">
    <UploadedImageBox
      v-for="(item, index) in images"
      :key="index"
      :uuid="item?.uuid"
      :src="item?.src"
      :crop="item"
      @cancel="onClickImageCancel(index)"
      @change="onClickImageChange('images', index, item)"
      @trim="onClickImageTrim('images', index, item)"
    ></UploadedImageBox>
  </div>
  <a v-if="images.length < props.max" class="cmn-link link-selectImage" @click="onAddImage()">画像を選択する</a>
  <ModalInputImageSelect
    v-if="isShowImageSelectModal"
    imageUploadType="user"
    @close="isShowImageSelectModal = false"
    @select="onSelectImage($event)"
    @upload="isShowImageSelectModal = false; isShowUploadModal = true"
  ></ModalInputImageSelect>
  <ModalInputImageUpload
    v-if="isShowUploadModal"
    imageUploadType="user"
    @close="isShowUploadModal = false"
    @upload="onUploadImage($event)"
  ></ModalInputImageUpload>
  <ModalInputImageTrimming
    v-if="isShowTrimmingModal"
    @close="isShowTrimmingModal = false"
    :src="selectImage"
    :uuid="selectImageUUID"
    :aspectWidth="1"
    :aspectHeight="1"
    @crop="onTrimmingImage($event)"
  ></ModalInputImageTrimming>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  imageUploadType?: 'user' | 'admin';
  max: number;
  images?: {
    src: string;
    width: string;
    height: string;
    x: string;
    y: string;
    uuid: string;
  }[],
}>(), {
  imageUploadType: 'user',
  images: [],
  max: 1
});

const images = ref([...props.images]);
watch(() => props.images, (newVal) => {
  images.value = newVal;
}, {
  deep: true
});


// データ定義
const isShowImageSelectModal = ref(false);
const isShowUploadModal = ref(false);
const isShowTrimmingModal = ref(false);
const selectImage = ref();
const selectImageUUID = ref('');
const selectIndex = ref(0);

const emits = defineEmits<{
  change: []
}>();

// アップロード済み画像の操作
const onClickImageCancel = async (index:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(props.images));
  dataUpdated.splice(index, 1);
  images.value = dataUpdated;
  emits('change', images.value);
}
const onClickImageChange = async (id:string, item:any) => {
  selectIndex.value = 0;
  isShowImageSelectModal.value = true;
}
const onClickImageTrim = async (id:string, index:number, item:any) => {
  selectIndex.value = index;
  selectImage.value = item.src;
  selectImageUUID.value = item.uuid;
  isShowTrimmingModal.value = true;
}

const onAddImage = async() => {
  selectIndex.value = images.value.length;
  isShowImageSelectModal.value = true;
};

// アップロード作業
const onSelectImage = async(image) => {
  selectImage.value = image.presigned_url;
  selectImageUUID.value = image.uuid;
  isShowImageSelectModal.value = false;
  isShowTrimmingModal.value = true;
};

const onUploadImage = async(image) => {
  selectImage.value = image.presigned_url;
  selectImageUUID.value = image.uuid;
  isShowUploadModal.value = false;
  isShowTrimmingModal.value = true;
};

const onTrimmingImage = async(image) => {
  let dataUpdated = JSON.parse(JSON.stringify(images.value));
  dataUpdated[selectIndex.value] = image;
  images.value = dataUpdated;
  isShowTrimmingModal.value = false;

  emits('change', images.value);
};
</script>

<style lang="scss">
.UploadedImageBoxs .imageBox {
  width: 100%;
  max-width: none;
  .imageBox_wrap {
    width: 100%;
  }
}
</style>