<template>
  <label class="inputMultiselect" :class="sizeClass">
    <div v-if="props.title" :class="{'required': props.required, 'title': true}">{{ props.title }}</div>
    <div :class="{'hasError': props.error.length > 0, 'selectWrap': true}">
      <Multiselect
        mode="tags"
        v-model="selected"
        :searchable="true"
        :canClear="false"
        :createTag="true"
        :addTagOn="['enter']"
        :options="options"
        :disabled="disabled"
        :placeholder="placeholder"
        noOptionsText="タグがありません"
        noResultsText="見つかりませんでした"
        :onCreate="handleTagCreate"
        @searchChange="handleSearch"
        @tag="emit('create', $event)"
      />
    </div>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </label>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Multiselect from '@vueform/multiselect';
import '@vueform/multiselect/themes/default.css';

const props = withDefaults(defineProps<{
  title?: string;
  required?: boolean;
  value: string[] | false;
  options?: {
    value?: string;
    label?: string;
  }[],
  disabled?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number;
  error?: string;
  placeholder?: string;
}>(), {
  value: false,
  required: false,
  disabled: false,
  size: 'md',
  error: ''
});

let sizeWidth:Ref<string> | string = ref('none');
const sizeClass = computed<string | number>(() => {
  const size = props.size;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})

const selected = ref(props.value as string[]);

const emit = defineEmits(['change', 'create']);

import { debounce } from 'perfect-debounce'
const handleSearch = debounce(async() => {
  // スペースのみの場合に非表示
  setTimeout(function(){
    document.querySelectorAll('.multiselect-option[aria-label]').forEach((span:any) => {
      if (! span.getAttribute('aria-label').trim()) {
        span.style.display = 'none';
      } else {
        span.style.display = '';
      }
      if (span.getAttribute('aria-label').match(/^\s/)) {
        span.textContent = span.getAttribute('aria-label').trim();
      }
    });
  }, 100);
}, 100);

// 頭のスペースをとる
const handleTagCreate = async (option, select$) => {
  if (! option.label.trim()) {
    return false;
  }
  option.label = option.label.trim();
  return option
}
// isReload
watch(selected, () => {
  let tags = [];
  for (let i = 0; i < selected.value.length; i++) {
    tags.push(selected.value[i].trim());
  }
  emit('change', tags);
})
</script>

<style lang="scss">
label.inputMultiselect {
  .title{
    font-size: 12px;
    line-height: 1;
    margin-bottom: 4px;
    color: #49454F;
  }
  & {
    display: inline-block;
    width: 100%;
    position: relative;
    max-width: v-bind(sizeWidth);
    &.xs{
      max-width: 87px;
    }
    &.sm{
      max-width: 164px;
    }
    &.md{
      max-width: 180px;
    }
    &.ml{
      max-width: 220px;
    }
    &.lg{
      max-width: 293px;
    }
    &.xl{
      max-width: 343px;
    }
  }
  .multiselect {
    font-size: 12px;
    &.is-active {
      border: 1px solid #D9D9D9;
      box-shadow: none;
      .multiselect-wrapper {
        // border-radius: 5px;
        // outline: -webkit-focus-ring-color auto 1px;
      }
    }
  }
  .multiselect-dropdown {
    .multiselect-option {
      font-size: 12px;
      &[aria-label=" "],
      &[aria-label="　"] {
        display: none;
      }
    }
  }
  .multiselect-placeholder {
    padding-right: 0;
    width: 100%;
  }
  .multiselect-caret {
    background: #333;
  }
}
</style>