<template>
<label
    :class="onCheckSizeClass"
  >
  <div v-if="props.title" class="title"><span :class="{'required': props.required}">{{ props.title }}</span></div>
  <div :class="{'inputWrap': true}">
    <input
      ref="inputRef"
      :type="inputType"
      v-model="inputValue"
      :disabled="props.disabled"
      :placeholder="props.placeholder"
      :class="{'hasError': props.error.length > 0}"
      :maxlength="maxlength"
      @focus="onSelectAllText"
      @blur="onBlurText"
    >
    <a
      v-if="isShowPasswordButton"
      class="password"
      @click="onClickPasswordIcon()"
      :class="{'is-show': isShowPassword}"
    ></a>
  </div>
  <span v-if="props.error" class="input-error">{{ props.error }}</span>
</label>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  value?: string | number,
  disabled?: boolean,
  placeholder?: string,
  isShowPasswordButton?: boolean,
  isFocusSelectText?: boolean,
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string,
  replacePattern?: string,
  maxlength?: string
}>(), {
  title: '',
  required: false,
  value: '',
  disabled: false,
  placeholder: '',
  isShowPasswordButton: true,
  isFocusSelectText: false,
  size: 'md',
  error: '',
  replacePattern: '',
  maxlength: '255'
});
const emits = defineEmits<{
  (e: 'update', value: string): void;
}>()

let isShowPassword:Ref<boolean> = ref(false);

const inputType = ref('password' as string);

const inputRef = ref();
const onSelectAllText = () => {
  if(props.isFocusSelectText){
    inputRef.value.select();
  }
};

const onClickPasswordIcon = (() => {
  if(isShowPassword.value === true){
    isShowPassword.value = false;
    inputType.value = 'password'
  }else{
    isShowPassword.value = true;
    inputType.value = 'text'
  }
})

const inputValue = ref(props.value as string);
watch(inputValue, () => {
  // データの書き換え
  if(props.replacePattern){
    inputValue.value = inputValue.value.replace(props.replacePattern, '')
  }
  emits('update', inputValue.value);
})
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 7px;
  color: #49454F;
  white-space: nowrap;
}
input{
  color: #333;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1;
  padding: 13px 12px 12px;
  width: 100%;
  &.hasError,
  .hasError > * > &{
    border-color: $color-alert2;
  }
  &[disabled] {
    opacity: 0.4;
  }
}
label {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  position: relative;
  vertical-align: top;
  & + label{
    margin-left: 14px;
  }
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
  &.half{
    max-width: calc(50% - 7px);
    & + .half{
      margin-left: 14px;
    }
  }
  &.full{
    & + label{
      margin-left: 0;
    }
  }
}

.inputWrap {
  position: relative;
}

::placeholder {
  color: $color-placeholder;
}

.password{
  cursor: pointer;
  display: block;
  position: absolute;
  width: 24px;
  height: 24px;
  background-image: url('@/assets/images/icon-visibility-off.svg');
  top: 12px;
  right: 8px;
  &.is-show{
    background-image: url('@/assets/images/icon-visibility-on.svg');
  }
}
</style>