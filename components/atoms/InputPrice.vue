<template>
  <label
    :class="onCheckSizeClass"
  >
    <div v-if="props.title" class="title"><span :class="{'required': props.required}">{{ props.title }}</span></div>
    <div :class="{'withUnit' : props.unit, 'inputWrap': true}">
      <input
        type="number"
        v-model="inputValue"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        :class="{'hasError': props.error.length > 0}"
      >
      <span v-if="props.unit" class="unit">{{ props.unit }}</span>
    </div>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </label>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  unit?: string,
  value?: string | number,
  disabled?: boolean,
  placeholder?: string,
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string,
  min?: number | null
}>(), {
  title: '',
  required: false,
  unit: '',
  value: '',
  disabled: false,
  placeholder: '',
  size: 'md',
  error: '',
  min: 0
});
const emits = defineEmits<{
  (e: 'update', value: string): void;
}>()

let sizeWidth:Ref<string> | string = ref('none');
const onCheckSizeClass = computed<string | number>(() => {
  const size = props.size;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})

const inputValue = ref(props.value as string);
watch(props, () => {
  inputValue.value = props.value;
});
watch(inputValue, () => {
  // データの書き換え
  inputValue.value = String(inputValue.value).substring(0, 8)
  if (props.min !== null) {
    if (inputValue.value < props.min) {
      inputValue.value = props.min;
    }
  }
  inputValue.value = inputValue.value.replace(/[^0-9]/, '')
  if (inputValue.value === '' || inputValue.value === null || inputValue.value === false || isNaN(Number(inputValue.value))) {
    inputValue.value = null
    emits('update', null);
  } else {
    inputValue.value = Number(inputValue.value);
  }
  emits('update', inputValue.value);
})
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 7px;
  color: #49454F;
  white-space: nowrap;
}
input{
  color: #333;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1;
  padding: 13px 12px 12px;
  width: 100%;
  &.hasError,
  .hasError > * > &{
    border-color: $color-alert2;
  }
  &[disabled] {
    opacity: 0.4;
  }
}
label {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  position: relative;
  vertical-align: top;
  & + label{
    margin-left: 14px;
  }
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
  &.half{
    max-width: calc(50% - 7px);
    & + .half{
      margin-left: 14px;
    }
  }
  &.full{
    & + label{
      margin-left: 0;
    }
  }
}

.inputWrap {
  position: relative;
}
.withUnit {
  display: flex;
  align-items: center;
  .unit {
    margin-left: 7px;
    color: $color-blacktext2;
    font-size: 12px;
    letter-spacing: 1.2px;
  }
}

::placeholder {
  color: $color-placeholder;
}

</style>