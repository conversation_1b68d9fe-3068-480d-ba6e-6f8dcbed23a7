<template>
  <div class="formQuantity">
    <a class="changeNum is-minus" @click="decrement()"></a>
    <input type="text" :value="count">
    <a class="changeNum is-plus" @click="increment()"></a>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const count = ref(0);
const decrement = () => {
  if(count.value >= 1){
    count.value--;
  }
}
const increment = () => {
  count.value++;
}
</script>

<style lang="scss" scoped>
.formQuantity {
  display: inline-flex;
  min-height: 34px;
}
input{
  font-size: 12px;
  color: #333;
  border: 1px solid #F4F4F4;
  width: 32px;
  text-align: center;
  &:disabled{
    opacity: 0.4;
  }
}
.changeNum{
  display: inline-block;
  width: 22px;
  color: #FFF;
  background: $color-main;
  position: relative;
  &::before,
  &::after{
    content: '';
    display: block;
    background: currentColor;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }
  &.is-minus{
    border-radius: 4px 0 0 4px;
    &::before{
      width: 12px;
      height: 1px;
    }
    &::after{
      display: none;
    }
  }
  &.is-plus{
    border-radius: 0 4px 4px 0;
    &::before{
      width: 12px;
      height: 1px;
    }
    &::after{
      width: 1px;
      height: 12px;
    }
  }
}
</style>
