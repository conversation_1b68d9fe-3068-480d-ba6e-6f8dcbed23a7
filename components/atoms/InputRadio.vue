<template>
  <span class="inputRadio">
    <div v-if="props.title" class="title" :class="{'required': props.required}">{{ props.title }}</div>
    <label
      v-for="(item, index) in items"
      :class="[{'is-block': props.block}]"
      :key="index"
    >
      <input
        type="radio"
        :name="name"
        :value="item.value"
        :checked="item.checked || String(props.value) == String(item.value)"
        :disabled="props.disabled"
        v-model="checkedValue"
        @change="onChange"
      >
      <span v-html="item.label"></span>
    </label>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </span>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  items?: {
    value?: string | number | boolean,
    checked?: boolean,
    label?: string
  }[],
  required?: boolean,
  disabled?: boolean,
  block?: boolean,
  error?: string,
  name?: string,
  value?: string | boolean,
}>(), {
  label: '',
  checked: false,
  required: false,
  disabled: false,
  block: false,
  error: '',
  value: '',
});

const emit = defineEmits(['change']);

const checkedValue = ref(String(props.value));
watch(() => props.value, (newValue) => {
  checkedValue.value = String(newValue);
});

const onChange = ($event:any) => {
  emit('change' , String($event?.target?.value));
};

const name:Ref<string> = ref((props.name) ? props.name : getRandomString(12));
</script>


<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 4px;
  color: #49454F;
}
label {
  display: inline-block;
  cursor: pointer;
  position: relative;
  margin-bottom: 16px;
  margin-right: 12px;
  &:last-child {
    margin-right: 0;
  }
  &.is-block {
    display: block;
  }
  input {
    display: none;
    &:checked {
      & + span {
        &::before{
          border-color: $color-main;
        }
        &::after {
          content: "";
          display: inline-block;
          background-color: $color-main;
          width: 8px;
          height: 8px;
          border-radius: 8px;
          position: absolute;
          left: 5px;
          top: 5px;
        }
      }
    }
    &:disabled {
      & + span {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
  & > span {
    display: inline-block;
    font-size: 14px;
    color: #333;
    line-height: 18px;
    position: relative;
    padding-left: 24px;
    &::before {
      content: "";
      display: inline-block;
      width: 18px;
      height: 18px;
      border-radius: 18px;
      border: solid 2px #9C9C9C;
      background: #FFF;
      position: absolute;
      left: 0;
    }
  }
}
</style>