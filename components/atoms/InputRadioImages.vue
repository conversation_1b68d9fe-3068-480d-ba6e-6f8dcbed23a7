<template>
  <span class="inputRadio">
    <div v-if="props.title" class="title" :class="{'required': props.required}">{{ props.title }}</div>
    <label
      v-for="(item, index) in items"
      :class="[{'is-block': props.block}]"
      :key="index"
    >
      <input
        type="radio"
        :name="name"
        :value="item.value"
        :checked="item.checked || props.value == item.value"
        :disabled="props.disabled"
        @change="onChange"
      >
      <span>
        <a href="#" class="icn-zoom" @click.prevent="showSlide.setUrl(item.image)"></a>
        <span class="img">
          <img :src="item.image">
        </span>
        <span class="txt">{{ item.label }}</span>
      </span>
    </label>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </span>
</template>

<script setup lang="ts">
import { useWebInvitationSlideModal } from '@/composables/useWebInvitationSlideModal'
const props = withDefaults(defineProps<{
  title?: string,
  items?: {
    value?: string | number | boolean,
    checked?: boolean,
    label?: string
  }[],
  required?: boolean,
  disabled?: boolean,
  block?: boolean,
  error?: string,
  name?: string,
  value?: string,
}>(), {
  label: '',
  checked: false,
  required: false,
  disabled: false,
  block: false,
  error: '',
  value: '',
});

const showSlide = useWebInvitationSlideModal();

const emit = defineEmits(['change']);

const onChange = ($event:any) => {
  emit('change' , String($event?.target?.value));
};

const name:Ref<string> = ref((props.name) ? props.name : getRandomString(12));

</script>


<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 4px;
  color: #49454F;
}
label {
  display: inline-block;
  cursor: pointer;
  position: relative;
  margin-bottom: 16px;
  margin-right: 12px;
  &:last-child {
    margin-right: 0;
  }
  &.is-block {
    display: block;
  }
  input {
    display: none;
    &:checked {
      & + span {
        &::before{
          border-color: $color-main;
        }
        &::after {
          content: "";
          display: inline-block;
          background-color: $color-main;
          width: 8px;
          height: 8px;
          border-radius: 8px;
          position: absolute;
          left: 5px;
          top: 5px;
        }
      }
    }
    &:disabled {
      & + span {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
  & > span {
    display: inline-block;
    font-size: 14px;
    color: #333;
    line-height: 18px;
    position: relative;
    padding-left: 24px;
    &::before {
      content: "";
      display: inline-block;
      width: 18px;
      height: 18px;
      border-radius: 18px;
      border: solid 2px #9C9C9C;
      background: #FFF;
      position: absolute;
      left: 0;
    }
  }
}

.icn-zoom {
  display: block;
  width: 24px;
  height: 24px;
  background-image: url("/images/webinvitation/theme_wa/guestAnswer_icon_search.png");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
}
.imgModal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1004;
  .imgModalBody {
    z-index: 9000 !important;
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch; 
    overflow-scrolling: touch;
    max-width: 400px;
    img {
      width: 100%;
    }
  }
  .close {
    position: absolute;
    right: 50%;
    margin-right: -205px;
    top: 20px;
    color: #fff;
    z-index: 10;
    cursor: pointer;
    @include sp {
      margin-right: 0;
      right: 4vw;
    }
  }
  .bg {
    cursor: pointer;
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    top: 0;
    background: #000;
    opacity: .4;
    z-index: 1;
  }
}
</style>