<template>
  <label
    :class="sizeClass"
    @change="onUpdateValue"
  >
    <div v-if="props.title" class="title" :class="{'required': props.required}">{{ props.title }}</div>
    <div class="selectWrap"><select
      :name="name"
      :disabled="disabled"
      :class="{'hasError': props.error.length > 0, 'is-placeholder': ! value}"
      @change="$emit('change', $event)"
    >
      <option
        v-for="(option, index) in options"
        :value="option.value"
        :key="index"
        :selected="value == option.value"
      >
        {{ option.label }}
      </option>
    </select></div>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </label>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  name?: string,
  value?: string | number,
  options?: {
    value?: string,
    label?: string
  }[],
  disabled?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string
}>(), {
  name: '',
  value: '',
  required: false,
  disabled: false,
  size: 'md',
  error: ''
});

let sizeWidth:Ref<string> | string = ref('none');
const sizeClass = computed<string | number>(() => {
  const size = props.size;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 4px;
  color: #49454F;
}
select{
  color: #333;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  padding: 5px 12px;
  width: 100%;
  height: 44px;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  &:disabled{
    opacity: 0.4;
  }
  &.hasError,
  .hasError > * > &{
    border-color: $color-alert2;
  }
  &.is-placeholder {
    color: $color-placeholder;
  }
}
.selectWrap {
  position: relative;
  &::before{
    content: '';
    border-style: solid;
    border-width: 5px 5px 0 5px;
    border-color: #333 transparent transparent transparent;
    position: absolute;
    bottom: 18px;
    right: 10px;
    pointer-events: none;
  }
}
label{
  display: inline-block;
  width: 100%;
  position: relative;
  max-width: v-bind(sizeWidth);
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.ml{
    max-width: 220px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
}
</style>