<template>
  <label class="switch" :class="[{'is-disabled': disabled}]">
    <input type="checkbox" :checked="checked" :disabled="disabled" @change="handleChange">
    <span class="slider"></span>
  </label>
</template>

<script>
export default {
  name: 'Switch',
  props: {
    checked: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:checked'],
  methods: {
    handleChange(event) {
      this.$emit('update:checked', event.target.checked);
    }
  }
}
</script>

<style lang="scss" scoped>
.switch {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 14px;
  margin: 4px 0;
  &.is-disabled{
    opacity: 0.5;
  }
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #C4C4C4;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: -3px;
  bottom: -3px;
  background-color: #FFF;
  border: 1px solid #C4C4C4;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: $color-main;
  &::before{
    border-color: $color-main;
  }
}

input:checked + .slider:before {
  transform: translateX(18px);
}
</style>