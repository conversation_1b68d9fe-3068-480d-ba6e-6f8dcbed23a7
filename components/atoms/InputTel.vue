<template>
  <label
    :class="onCheckSizeClass"
  >
    <div v-if="props.title" class="title"><span :class="{'required': props.required}">{{ props.title }}</span></div>
    <div :class="{'withUnit' : props.unit, 'inputWrap': true}">
      <input
        ref="inputRef"
        type="text"
        :name="props.name"
        :value="inputValue"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        :maxlength="props.maxlength"
        :autocomplete="props.autocomplete"
        :class="{'hasError': props.error.length > 0}"
        @input="inputValue = $event.target.value"
      >
      <span v-if="props.unit" class="unit">{{ props.unit }}</span>
    </div>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </label>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  value?: string | number,
  name?: string,
  disabled?: boolean,
  placeholder?: string,
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string,
  maxlength?: string,
  autocomplete?: string
}>(), {
  title: '',
  required: false,
  value: '',
  name: '',
  disabled: false,
  placeholder: '',
  size: 'md',
  error: '',
  maxlength: '255',
  autocomplete: ''
});
const emits = defineEmits<{
  (e: 'update', value: string): void;
}>()


let sizeWidth:Ref<string> | string = ref('none');
const onCheckSizeClass = computed<string | number>(() => {
  const size = props.size;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})

const inputValue = ref(props.value as string);
onMounted(() => {
  inputValue.value = props.value;
});
watch(() => props.value, (newValue) => {
  inputValue.value = newValue;
}, { deep: true });
// データの書き換え
watch(inputValue, () => {
  // 全角を半角に
  inputValue.value = inputValue.value.replace(/[Ａ-Ｚａ-ｚ０-９]/g, function(s:any) {
    return String.fromCharCode(s.charCodeAt(0) - 0xFEE0);
  });
  // 数字と-以外削除
  inputValue.value = inputValue.value.replace(/[^0-9\-]/g,"");
  if (props.maxlength) {
    if (inputValue.value.length > parseInt(props.maxlength)) {
      inputValue.value = inputValue.value.substring(0, parseInt(props.maxlength));
    }
  }
  emits('update', inputValue.value);
})
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 7px;
  color: #49454F;
  white-space: nowrap;
}
input{
  color: #333;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1;
  padding: 13px 12px 12px;
  width: 100%;
  &.hasError,
  .hasError > * > &{
    border-color: $color-alert2;
  }
  &[disabled] {
    opacity: 0.4;
  }
}
label {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  position: relative;
  vertical-align: top;
  & + label{
    margin-left: 14px;
  }
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
  &.half{
    max-width: calc(50% - 7px);
    & + .half{
      margin-left: 14px;
    }
  }
  &.full{
    & + label{
      margin-left: 0;
    }
  }
}

.inputWrap {
  position: relative;
}
::placeholder {
  color: $color-placeholder;
}

</style>