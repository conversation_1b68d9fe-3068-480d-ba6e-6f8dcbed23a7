<template>
  <label
    :class="onCheckSizeClass"
  >
    <div v-if="props.title" class="title"><span :class="{'required': props.required}">{{ props.title }}</span></div>
    <div :class="{'withUnit' : props.unit, 'inputWrap': true}">
      <input
        ref="inputRef"
        :type="props.type"
        :name="props.name"
        :value="props.value"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        :maxlength="props.maxlength"
        :class="{'hasError': props.error.length > 0}"
        @focus="emits('focus')"
        @input="onInputText"
        @blur="onBlurText"
      >
      <span v-if="props.unit" class="unit">{{ props.unit }}</span>
    </div>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </label>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  title?: string,
  type?: string,
  required?: boolean,
  unit?: string,
  name?: string,
  value?: string | number,
  disabled?: boolean,
  placeholder?: string,
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  maxlength?: string,
  error?: string,
  pattern?: 'number'|null,
  inputmode?: string|null
}>(), {
  title: '',
  type: 'text',
  required: false,
  unit: '',
  name: '',
  value: '',
  disabled: false,
  placeholder: '',
  size: 'md',
  maxlength: '255',
  error: '',
  pattern: null,
  inputmode: null
});
const emits = defineEmits<{
  (e: 'blur'): void;
  (e: 'focus'): void;
  (e: 'input', value: HTMLInputElement): void;
}>()

// 親からも実行可能に
const inputRef = ref();
defineExpose({
  inputRef
});

let sizeWidth:Ref<string> | string = ref('none');
const onCheckSizeClass = computed<string | number>(() => {
  const size = props.size;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})

const onBlurText = (event) => {
  emits('blur')
};

const onInputText = (event) => {
  const input = event.target as HTMLInputElement;
  if (props.pattern === 'number') {
    const pattern = /[^\d,]/g;
    const sanitizedValue = input.value.replace(pattern, '');
    const inputObj = event;
    inputObj.target.value = sanitizedValue;
    emits('input', inputObj);
  } else {
    emits('input', event);
  }
};
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 7px;
  color: #49454F;
  white-space: nowrap;
}
input{
  font-family: 'Noto SansJP', sans-serif;
  color: #333;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1;
  padding: 13px 12px 12px;
  width: 100%;
  &.hasError,
  .hasError > * > &{
    border-color: $color-alert2;
  }
  &[disabled] {
    opacity: 0.4;
  }
}
label {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  position: relative;
  vertical-align: top;
  & + label{
    margin-left: 14px;
  }
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
  &.half{
    max-width: calc(50% - 7px);
    & + .half{
      margin-left: 14px;
    }
  }
  &.full{
    & + label{
      margin-left: 0;
    }
  }
}

.inputWrap {
  position: relative;
}
.withUnit {
  display: flex;
  align-items: center;
  .unit {
    margin-left: 7px;
    color: $color-blacktext2;
    font-size: 12px;
    letter-spacing: 1.2px;
  }
}

::placeholder {
  color: $color-placeholder;
}

</style>