<template>
  <label
    :class="sizeClass"
  >
    <div v-if="props.title" class="title"><span :class="{'required': props.required}">{{ props.title }}</span></div>
    <textarea
      :type="props.type"
      :name="props.name"
      :value="props.value"
      :maxlength="props.maxlength"
      :disabled="props.disabled"
      :placeholder="props.placeholder"
      :class="{'hasError': props.error.length > 0}"
      @input="$emit('input', $event)"
    >{{ props.value }}</textarea>
    <span v-if="props.error" class="input-error">{{ props.error }}</span>
  </label>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  title?: string,
  type?: string,
  required?: boolean,
  name?: string,
  value?: string | number,
  maxlength?: string,
  disabled?: boolean,
  placeholder?: string,
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string
}>(), {
  type: 'text',
  required: false,
  name: '',
  value: '',
  disabled: false,
  maxlength: '1000',
  placeholder: '',
  size: 'md',
  error: ''
});

let sizeWidth:Ref<string> | string = ref('none');
const sizeClass = computed<string | number>(() => {
  const size = props.size;
  if (typeof size === 'number') {
    sizeWidth = size + 'px';
    return '';
  }
  return size;
})
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 7px;
  color: #49454F;
  white-space: nowrap;
}
textarea{
  font-family: 'Noto SansJP', sans-serif;
  min-height: 10.8em;
  color: #333;
  background: #FFF;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  padding: 13px 12px 12px;
  width: 100%;
  text-align: inherit;
  resize: vertical;
  &.hasError {
    border-color: $color-alert2;
  }
  &:disabled{
    opacity: 0.4;
  }
}
label {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  & + label{
    margin-left: 14px;
  }
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
  &.half{
    max-width: calc(50% - 7px);
    & + .half{
      margin-left: 14px;
    }
  }
}

::placeholder {
  color: $color-placeholder;
}
</style>