<template>
  <div class="upload">
    <div class="preview">
      <div class="btnArea" v-if="files.length < maxCnt || ! showPreview">
        <label class="fileInput" :class="props.btnClass" :data-disabled="props.disabled">
          <input
            type="file"
            :disabled="props.disabled"
            :multiple="(maxCnt > 1) ? true : false"
            :accept="accept"
            @change="onClickUpload"
          />
          <span>{{ props.btnText }}</span>
        </label>
      </div>
      <template v-if="showPreview">
        <div v-if="! isUploading">
          <div class="upload-result" v-for="(file, i) in files" :key="i">
            <button type="button" class="btn btn-delete" @click="onClickDelete(i)"></button>
            <span v-if="file.fileType == 'video'" class="icn-video"></span>
            <video v-if="file.fileType == 'video'" :src="value?.presigned_url_main" :poster="value?.presigned_url_s"></video>
            <img v-else :src="value?.presigned_url_s">
          </div>
        </div>
        <Loading v-else text="Uploading ..."></Loading>
      </template>
    </div>
    <span v-if="error" class="input-error">{{ error }}</span>
    <span v-else-if="props.error" class="input-error">{{ props.error }}</span>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  // 画像ファイルサイズ (MB)
  maxFileSize?: number;
  // 選択できる画像数
  maxCnt?: number;
  error?: string;
  value?: any[] | any;
  dirName?: string;
  disabled?: boolean;
  btnClass?: string;
  btnText?: string;
  isUpload?: boolean;
  showPreview?: boolean;
  accept?: string;
}>(), {
  maxFileSize: 50,
  maxCnt: 1,
  dirName: '',
  disabled: false,
  btnClass: 'btn btn-primary',
  btnText: 'アップロードするファイルを選択',
  isUpload: true,
  showPreview: true,
  accept: '*',
});

// 更新API
const { mutate } = useGuestBinaryUploadImage2Mutation();

const emits = defineEmits<{
  (e: 'change', files: string[] | string): void;
}>();

const error = ref('' as string);
const files = ref([] as { fileName: string, fileData: string, fileType: string}[]);
const uploadFiles = ref('');

const isUploading = ref(false);


onMounted(() => {
  // 初期値がある場合
  files.value = [];
  if (props.value) {
    if (props.maxCnt > 1) {
      for (let i = 0; i < props.value.length; i++) {
        const value = props.value[i];
        let fileData = '';
        if (value?.presigned_url_m) fileData = value?.presigned_url_m;
        if (value?.base64) fileData = value?.base64;
        if (fileData) files.value.push({fileName: getFileName(value), fileData: fileData, fileType: value?.type});
      }
    } else {
      let fileData = '';
      if (props.value?.presigned_url_m) fileData = props.value?.presigned_url_m;
      if (props.value?.base64) fileData = props.value?.base64;
      if (fileData) files.value.push({fileName: getFileName(String(props.value)), fileData: fileData, fileType: props.value?.type});
    }
  }
});

// クリック時の処理
const onClickUpload = (e: any) => {
  // 写真を変更する場合
  if (! props.showPreview && files.value.length > 0) {
    onClickDelete(files.value.length - 1);
  }
  if (! props.showPreview) {
    emits('change', '');
  }
  upload(e.target.files);
};

// アップロード処理
const upload = async (inputFiles: any[]) => {
  for (let i = 0; i < inputFiles.length; i++) {
    const file = inputFiles[i];
    if (files.value.length >= props.maxCnt) break;

    // 選ばれなかったとき
    if (! file) {
      continue;
    }

    error.value = '';
    // サイズは1MB以下
    if (file.size / 1024 / 1024 >= props.maxFileSize) {
      error.value = 'ファイルは'+String(props.maxFileSize)+'MB以下でアップロードしてください';
      continue;
    }

    // 変換が終わったら実行される
    if (files.value.length < props.maxCnt) {

      // アップロードした画像情報をプッシュ
      let fileType = 'image';
      if (file?.type.match(/^video/)) fileType = 'video';
      let previewData = '';
      try {
        if (fileType == 'video') {
          previewData = await convertBase64Video(file);
        } else {
          previewData = await convertBase64(file);
        }
        
        if (previewData === false) {
          error.value = '対応していないファイル形式です';
          continue;
        }
        
        files.value.push({
          fileName: file.name,
          fileData: previewData,
          fileType: fileType
        });
      } catch (err) {
        error.value = err.message || '対応していない動画形式です';
        continue;
      }

      let formData = new FormData();
      formData.append('file_data', file);

      if (props.isUpload) {
        isUploading.value = true;
        try {
          const response = await mutate({
            dirName: props.dirName,
            fileData: file
          });
          let value = JSON.parse(JSON.stringify(response?.data?.guestBinaryUploadImage2));
          value.type = fileType;
          uploadFiles.value = value;
        } catch (error) {
          console.error('Upload error:', error);
        }
        isUploading.value = false;
      } else {
        uploadFiles.value = {file: file, base64: previewData, type: fileType};
      }
    }
    change();
  }

  // if (files.value.length > 0) {
  //   const formData = appendFilesToFormData(files.value);
  //   try {
  //     const response = await mutate({
  //       fileType: FileType.FileTypeMemberMaterial,
  //       fileData: formData
  //     });
  //   } catch (error) {
  //     console.error('Upload error:', error);
  //   }
  // }
}

// ファイルをFormDataに追加する関数
const appendFilesToFormData = (uploadFiles: any) => {
  const formData = new FormData();
  uploadFiles.forEach((file: any) => {
    formData.append('file', file.fileData);
  });
  return formData;
};

const onClickDelete = (index:number) => {
  let newFiles = [];
  for (let i = 0; i < files.value.length; i++) {
    if (index == i) continue;
    newFiles.push(files.value[i]);
  }
  files.value = newFiles;
  change();
};

const change = () => {
  // 1件のみなら 画像1個だけ返す
  if (props.maxCnt == 1) {
    if (files.value.length) {
      emits('change', uploadFiles.value);
    } else {
      emits('change', '');
    }
  } else {
    emits('change', uploadFiles.value);
  }
};
</script>

<style lang="scss" scoped>
.upload .btnArea {
  .fileInput {
    display: block;
    cursor: pointer;
    input {
      display: none;
    }
    &[data-disabled="true"] {
      cursor: default !important;
    }
  }
}

.upload-result {
  margin-left: 5px;
  display: inline-block;
  width: 100px;
  height: 100px;
  vertical-align: top;
  position: relative;
  margin-right: 20px;

  img ,
  video {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    object-fit: contain;
  }
  .icn-video {
    display: block;
    width: 20px;
    height: 20px;
    background-image: url(@/assets/images/icon-video.svg);
    background-size: cover;
    position: absolute;
    top: 10px;
    right: 10px;
    pointer-events: none;
    z-index: 10;
  }
  .btn.btn-delete {
    position: absolute;
    right: -10px;
    top: -10px;
    background: #888;
    width: 26px;
    height: 26px;
    border-radius: 50%;
    z-index: 1;
    padding: 0;
    margin: 0;
    &:before ,
    &:after {
      content: " ";
      width: 16px;
      height: 2px;
      background: #fff;
      display: block;
      position: absolute;
      transform: rotate(45deg);
      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -8px;
      margin-top: -1px;
    }
    &:after {
      transform: rotate(-45deg);
    }
    &:hover {
      background: #222;
    }
    ~ img ,
    ~ video {
      border: 1px solid #ccc;
    }
  }
}
</style>