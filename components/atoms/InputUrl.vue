<template>
  <InputText
    type="url"
    :required="props.required"
    :title="props.title"
    :placeholder="props.placeholder"
    :size="props.size"
    :value="props.value"
    :error="props.error"
    :maxlength="props.maxlength"
    @input="emits('update', $event.target.value)"
  />
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  value?: string | number,
  disabled?: boolean,
  placeholder?: string,
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string,
  maxlength?: string,
}>(), {
  title: '',
  required: false,
  value: '',
  disabled: false,
  placeholder: '',
  size: 'md',
  error: '',
  maxlength: '255'
});
const emits = defineEmits<{
  (e: 'update', value: string): void;
}>()
</script>