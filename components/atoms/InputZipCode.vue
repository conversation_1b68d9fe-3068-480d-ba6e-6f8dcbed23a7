<template>
  <div class="zip">
    <div class="row">
      <InputNumber
        placeholder="1234567"
        :title="title"
        :disabled="disabled"
        :required="required"
        :size="164"
        :value="value"
        :error="error"
        name="zipcode"
        maxlength="7"
        @update="onChangeZipCode($event)"
      />
    </div>
    <a v-if="props.isShowZipLink" class="link-accent size--md" href="https://www.post.japanpost.jp/zipcode/" target="_blank">
      <i class="icn-left material-icons icn-lg">search</i>
      郵便番号を調べたい方はこちら
    </a>
  </div>
</template>

<script setup lang="ts">
import pkg from 'yubinbango-core2'; 
const { Core } = pkg;

const props = withDefaults(defineProps<{
  title?: string,
  value?: string | number,
  required?: boolean,
  disabled?: boolean,
  isShowZipLink?: boolean
  error?: string
}>(), {
  title:'郵便番号',
  value: '',
  required: false,
  disabled: false,
  isShowZipLink: true,
  error: ''
});

const emit = defineEmits(['change']);

const onChangeZipCode = (value:any) => {
  // 全角を半角に
  value = value.replace(/[Ａ-Ｚａ-ｚ０-９]/g, function(s:any) {
    return String.fromCharCode(s.charCodeAt(0) - 0xFEE0);
  });
  // 数字以外削除
  const zip = value.replace(/[^0-9]/, '');
  if (! zip.match(/^[0-9]{7}$/)) {
    emit('change', {
      postal_code: value
    });
    return false;
  }
  new Core(value.replace(/[^0-9]/, ''), function(addr:any) {
    // region=都道府県, locality=市区町村, street=町域
    const data = {
      postal_code: value,
      prefecture: addr?.region,
      city: addr?.locality + addr?.street
    }
    emit('change', data);
  })
};
</script>

<style lang="scss" scoped>
.link-accent {
  margin-top: 10px;
  display: inline-block;
}
</style>