<template>
  <div class="loading" :data-fullscreen="fullscreen">
    <span class="icn" v-if="isShowIcon">
      <div class="txt">{{ props.text }}</div>
      <div class="image">
        <img src="@/assets/images/loading_image.png" alt="">
      </div>
      <div class="progress">
        <img src="@/assets/images/loading.png" alt="">
      </div>
    </span>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  isShowIcon?: boolean;
  text?: string;
  fullscreen?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  isShowIcon: false,
  text: 'がんばって読み込んでいます',
  fullscreen: false,
});
</script>

<style lang="scss" scoped>
.loading {
  text-align: center;
  padding: 40px 0;
  color: #0F2C4E;
  .txt {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
  }
  .image{
    margin: 0 auto 12px;
    max-width: 120px;
    width: 100%;
    img{
      width: 100%;
      height: auto;
    }
  }
  .progress{
    max-width: 176px;
    width: 100%;
    margin: 0 auto;
    img{
      width: 100%;
      height: auto;
    }
  }
}

// モーダル内のLoadingの場合
.loading[data-fullscreen="true"] ,
.modalContainer .contentsInner .loading {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(#fff, .9);
  z-index: 100;
  padding: 0;
  .icn {
    position: absolute;
    top: 50%;
    display: block;
    text-align: center;
    left: 0;
    right: 0;
    margin-top: -1em;
  }
}

.loading[data-fullscreen="true"] {
  position: fixed;
}

.modalContainer .contentsInner .loading {
  transform: none;
  margin: 0;
  .icn {
    transform: translateY(-50%);
    margin: 0;
  }
}
</style>