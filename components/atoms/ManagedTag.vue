<template>
<div class="tag">
  <slot />

  <a @click.prevent="closeTag($event)">
    <img src="@/assets/images/icon-close-fill.svg" />
  </a>
</div>
</template>

<script lang="ts" setup>
const closeTag = (e) => {
  e.target.closest('.tag').remove();
};
</script>

<style lang="scss" scoped>
.tag{
  display: inline-block;
  color: $color-main;
  font-size: 11px;
  line-height: 1.6;
  border: 1px solid $color-main;
  border-radius: 26px;
  padding: 2px 8px 2px 12px;
  margin-right: 4px;
  margin-bottom: 4px;
  cursor: pointer;
}
</style>
