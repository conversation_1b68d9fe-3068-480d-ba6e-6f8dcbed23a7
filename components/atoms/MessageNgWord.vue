<template>
  <div v-if="isShow()">
    <p class="text-danger pt-10">※句読点、忌み語と思われる字句を検出しました。句読点や忌み語については<a href="https://www.favori-cloud.com/blog/paper_item_manners_001/" target="_blank">こちら</a>をご覧ください</p>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  value: string | null;
}
const props = withDefaults(defineProps<Props>(), {
  value: '',
});

const { ngWords } = await useNgWordsStore();

// const { data:ngWords, error, refresh } = await useFetch('/storage/data/ng_words.json', {
//   lazy: true,
//   server: false
// }) as any;

const isShow = () => {
  if (! props.value) return false;
  for (let i = 0; i < ngWords.value.length; i++) {
    const ngWord = ngWords.value[i];
    if (props.value.indexOf(ngWord) !== -1) return true;
  }
  return false;
};
</script>

<style lang="scss" scoped>
a {
  display: inline-block;
}
</style>