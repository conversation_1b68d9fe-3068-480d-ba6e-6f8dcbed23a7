<template>
  <NuxtLink @click.prevent="handleClick" :data-disabled="isDisabled"><slot /></NuxtLink>
</template>

<script lang="ts" setup>
// ダブルクリック無効化
// NuxtJSのSSRレンダリングのページは、ダブルクリックで遷移するとエラーになる様子
// 解決策が見えないので、とりあえず、ダブルクリック無効化の方向性で
const isDisabled = ref(false);

const handleClick = (event) => {
  if (isDisabled.value) {
    // クリック無効化
    event.preventDefault();
    return;
  }

  // クリック後にリンクを無効化
  isDisabled.value = true;

  // 1秒後にリンクを再度有効化
  setTimeout(() => {
    isDisabled.value = false;
  }, 1000); // ここで時間を調整
}
</script>

<style lang="scss" scoped>
[data-disabled="true"] {
  pointer-events: none;
}
</style>
