<template>
<div class="wrapGraph">
  <svg :style="progressExpression">
    <circle class="baseGraph" cx="25" cy="25" r="23"></circle>
    <circle class="lineGraph" cx="25" cy="25" r="23"></circle>
  </svg>
  <div class="graph">{{ props.params.current }}/{{ props.params.finish }}</div>
</div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

export interface Props {
  params: array,
};

const props = withDefaults(defineProps<Props>(), {
  params: {
    current: 1,
    finish: 3,
  }
});

const progressExpression = computed(() => {
  return {
    '--ProgressCurrent' : (144 * ((props.params.finish - (props.params.current - 1)) / props.params.finish)),
    '--ProgressFinish' : (144 - (144 * (100 / props.params.finish * props.params.current)) / 100),
  }
});
</script>

<style lang="scss" scoped>
:root {
  --ProgressCurrent: 0;
  --ProgressFinish: 90;
}
.wrapGraph {
  position: relative;
  width: 50px;
  height: 50px;
  margin-right: 20px;
  svg {
    position: relative;
    width: 50px;
    height: 50px;
    transform: rotate(-90deg);
    circle {
      position: relative;
      fill: none;
      stroke-width: 4;
      stroke: #f3f3f3;
      stroke-dasharray: 144;
      stroke-dashoffset: 0;
      stroke-linecap: round;
    }
  }
  .graph {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 2px;
    font-size: 13px;
    font-weight: bold;
  }
  .lineGraph {
    animation: circleAnim 1.5s forwards;
    stroke: $color-main;
  }
}
@keyframes circleAnim {
  0% {
    stroke-dashoffset: var(--ProgressCurrent);
  }
  99.9%, to {
    stroke-dashoffset: var(--ProgressFinish);
  }
}
</style>

<style lang="scss">
// scopedでないものはこちらへ
</style>