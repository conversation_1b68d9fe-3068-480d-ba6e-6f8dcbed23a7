<template>
<strong class="stars"><img src="@/assets/images/icon-reviewstar.svg" alt="" /><span class="starsScore" :style="showStarsScore(params.stars)"></span></strong>
<span class="review" v-if="review">{{ publishedReview(params.review) }}</span>
</template>

<script lang="ts" setup>
export interface Props {
  stars: Number,
  review: Number,
}

const params = withDefaults(defineProps<Props>(), {
  stars: 0,
  review: 0,
});

const showStarsScore = computed(() => (score) => {
  let adjustWhite = 0;
  if (Number.isInteger(score)) {
    adjustWhite = score - 1;
  }
  else {
    adjustWhite = Math.floor(score);
  }
  return {
    '--StarsWidth' : ((score * 15) + (adjustWhite * 5) + 2.5) + '%'
  }
});

const publishedReview = computed(() => (data) => {
  return "(" + data + ")";
});

</script>

<style lang="scss" scoped>
:root {
  --StarsWidth: 0;
}
.stars {
  display: inline-block;
  position: relative;
  background-color: $color-grayborder;
  overflow: hidden;
  line-height: 1;
  .starsScore {
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    width: var(--StarsWidth);
    height: 100%;
    background-color: #FFB341;
    transition: width 0.35s ease;
  }
  img {
    position: relative;
    z-index: 3;
  }
}
.review {
  margin-left: 5px;
  vertical-align: top;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.02em;
}

</style>
