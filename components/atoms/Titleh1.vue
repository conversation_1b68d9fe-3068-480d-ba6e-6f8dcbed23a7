<template>
<h1><div class="section-inner"><slot /></div></h1>
</template>

<style lang="scss" scoped>
h1 {
  margin: 0;
  padding: 24px 24px 25px;
  border-bottom: 10px solid $color-lightgray;
  background-color: $color-mainbackground;
  font-weight: 400;
  font-size: 24px;
  line-height: 130%;
  letter-spacing: 0.02em;
  color: $color-blacktext2;
  &.titleCategory {
    font-family: 'Cormorant', serif;
  }
  :deep(a.backward) {
    vertical-align: middle;
    cursor: pointer;
    img {
      width: 28px;
      margin-right: 12px;
      vertical-align: top;
      @include sp {
        width: 21px;
        margin-right: 8px;
        margin-top: -2px;
      }
    }
  }
}

.is-widelayout {
  .section-inner {
    max-width: 1250px;
    margin: 0 auto;
  }
}


@include sp {
h1 {
  padding: 16px 15px 18px;
  border-bottom-width: 4px;
  font-size: 16px;
  line-height: 120%;
}
}
</style>
