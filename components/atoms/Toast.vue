<template>
<div class="toast" :class="classList">
  {{ toastMessage.message }}
</div>
</template>

<script lang="ts" setup>
export interface Props {
  index: number;
}
const props = withDefaults(defineProps<Props>(), {
  index: 0
});

const { getToastMessage, shownToastMessage } = useToastMessageState();

// Class
const toastMessage = getToastMessage(props.index);
const classList = ref(['closing', 'close'] as string[])

// マウント時に開くアニメーションを追加
onMounted(() => {
  if (toastMessage.theme) classList.value.push('toast--'+toastMessage.theme);
  // 表示アニメーション
  if (toastMessage.shown) {
    classList.value = [];
  } else {
    classList.value = classList.value.filter(item => item !== 'close');
    setTimeout(() => {
      classList.value = classList.value.filter(item => item !== 'closing');
      shownToastMessage(props.index, 3350);
    }, 10);
  }

  // 非表示アニメーション
  setTimeout(() => {
    classList.value.push('closing');
  }, 3000);
  setTimeout(() => {
    classList.value.push('close');
  }, 3350);
})
</script>

<style lang="scss" scoped>
.toast {
  position: fixed;
  bottom: 29px;
  left: 50%;
  max-width: 97%;
  min-width: 25em;
  padding: 15px 16px 13px;
  border-radius: 4px;
  background-color: $color-blackLight;
  font-size: 14px;
  line-height: 150%;
  letter-spacing: 0.28px;
  color: $color-whitetext;
  opacity: 1;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.20), 0px 1px 10px 0px rgba(0, 0, 0, 0.12), 0px 4px 5px 0px rgba(0, 0, 0, 0.14);
  transform: translateX(-50%);
  transition: opacity 0.35s ease;
  z-index: 333;
  &.close {
    display: none;
  }
  &.closing {
    opacity: 0;
    pointer-events: none;
    * {
      pointer-events: none;
    }
  }
}

@include sp {
.toast {
  bottom: 66px;
  min-width: 24.4em;
  padding: 13px 16px;
}
}


.toast.toast--success {
  color: $color-main;
  background: #FFF;
  font-weight: 700;
  // border-radius: 10px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.18);
}
</style>