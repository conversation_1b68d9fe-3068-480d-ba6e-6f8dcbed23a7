<template>
  <div class="video" :class="{'is-cover': props.cover}">
    <video
      v-if="srcVideo"
      ref="video"
      oncontextmenu="return false;"
      disablepictureinpicture
      controlslist="nodownload noplaybackrate"
      :src="isBase64 ? srcVideo : undefined"
      :loop="props.loop"
      :controls="props.controls"
      :poster="props.poster ? props.poster : srcImage"
      :muted="props.autoplay || props.muted"
      :playsinline="props.autoplay || props.browserAutoplay ? true : false"
      @loadedmetadata="emits('loadedmetadata', null)"
      @canplay="emits('canplay', null)"
    >
    </video>
    <img
      v-else-if="props.src"
      :src="props.src"
      alt=""
    />
    <Loading v-else />
  </div>
</template>

<script lang="ts" setup>
import Hls from 'hls.js';
import { emit } from 'process';
const props = withDefaults(defineProps<{
  uuid?: string;
  src?: string;
  srcVideo?: string;
  size?: 'l' | 's' | 'm';
  cover?: boolean,
  controls?: boolean,
  loop?: boolean,
  autoplay?: boolean;
  poster?: string;
  muted?: boolean;
  browserAutoplay?: boolean;
}>(), {
  size: 'l',
  cover: false,
  controls: true,
  loop: false,
  autoplay: false,
  poster: '',
  muted: true,
  browserAutoplay: false
});

const emits = defineEmits<{
  (e: 'loadedmetadata', v: null): void;
  (e: 'canplay', v: null): void;
}>()

const srcVideo = ref(props.srcVideo);
const uuid = ref(props.uuid);
const muted = ref(props.muted);
const video = ref(null);
const hls: Hls | null = ref(null);

const isBase64 = computed(() => {
  return srcVideo.value && srcVideo.value.startsWith('data:video') || srcVideo.value && !srcVideo.value.includes('.m3u8');
});

const setupVideoLoop = (videoEl) => {
  if (props.loop && (props.autoplay || props.browserAutoplay)) {
    let loopTimeout;
    let lastCurrentTime = 0;
    
    const handleTimeUpdate = () => {
      const currentTime = videoEl.currentTime;
      const duration = videoEl.duration;
      
      // 動画の終端近く（250ms以内）に到達した場合
      if (duration > 0 && duration - currentTime < 0.25) {
        clearTimeout(loopTimeout);
        loopTimeout = setTimeout(() => {
          // タイムアウト後に再生位置が進んでいない場合（終端でスタック）
          if (videoEl.currentTime === lastCurrentTime && videoEl.currentTime > 0) {
            videoEl.currentTime = 0;
            videoEl.play().catch(e => console.log('Loop play failed:', e));
          }
        }, 500);
        lastCurrentTime = currentTime;
      } else {
        // 終端から離れた場合はタイムアウトをクリア
        clearTimeout(loopTimeout);
      }
    };

    const handleEnded = () => {
      // Safari 18.5でendedイベントが発火した場合のフォールバック
      if (videoEl.currentTime > 0) {
        videoEl.currentTime = 0;
        videoEl.play().catch(e => console.log('Loop play failed:', e));
      }
    };

    // 既存のイベントリスナーを削除してから追加
    videoEl.removeEventListener('timeupdate', handleTimeUpdate);
    videoEl.removeEventListener('ended', handleEnded);
    videoEl.addEventListener('timeupdate', handleTimeUpdate);
    videoEl.addEventListener('ended', handleEnded);
  }
};

const onSetVideo = () => {
  if(srcVideo.value && video.value){
    if (hls.value) {
      hls.value.destroy();
      hls.value = null;
    }
    if (isBase64.value) {
      setupVideoLoop(video.value);
      if(props.autoplay){
        video.value.play();
      }
    } else if (Hls.isSupported()) {
      hls.value = new Hls({
        debug: false,
        xhrSetup: function (xhr, url) {
          xhr.withCredentials = true;
        },
        fetchSetup: function (context, initParams) {
          initParams.credentials = 'include';
          return new Request(context.url, initParams);
        },
      });
      hls.value.loadSource(generateVideoSrc(srcVideo.value));
      hls.value.attachMedia(video.value);
      setupVideoLoop(video.value);
      if(props.autoplay){
        hls.value.on(Hls.Events.MANIFEST_PARSED, () => {
          video.value.play();
        });
      }
    } else if (video.value.canPlayType('application/vnd.apple.mpegurl')) {
      // HLS.jsをサポートしていないデバイス（主にSafari）は、ネイティブHLSをサポートしている可能性あり
      video.value.src = generateVideoSrc(srcVideo.value);
      setupVideoLoop(video.value);
      if(props.autoplay){
        video.value.addEventListener('loadedmetadata', () => {
          video.value.play();
        });
      }
    }
  }
};

// `_hls`を削除するための関数
const generateVideoSrc = (srcVideo: string | undefined) => {
  if (!srcVideo) return srcVideo ? srcVideo : '';
  return srcVideo.replace('_hls', '');
};

const srcImage = ref<string|undefined>();
const fetchImages = async () => {
  if(uuid.value){
    const { getImages } = await useGetManyImages2([uuid.value + '_' + props.size]);
    watch(() => getImages.value, async () => {
      srcImage.value = getImages?.value?.[0]?.presigned_url;
      if(getImages?.value?.[0]?.presigned_url_main){
        srcVideo.value = getImages?.value?.[0]?.presigned_url_main;
      }
    }, { immediate: true, deep: true });
  }
};

// スクロール時の自動再生用のIntersection Observer
let observer: IntersectionObserver | null = null;
const setupObserver = () => {
  if (!props.browserAutoplay || !video.value) return;

  observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // ビューポートに入ったときに再生
        video.value.play();
      } else {
        // ビューポートから出たときに停止
        video.value.pause();
      }
    });
  });

  if (video.value) {
    observer.observe(video.value);
  }
};

onMounted(async() => {
  fetchImages().then(() => onSetVideo());
  setupObserver();

  watch(() => props.uuid, async (newVal, oldVal) => {
    if(newVal != oldVal) {
      uuid.value = newVal;
      await fetchImages();
      onSetVideo();
    }
  });
  watch(() => props.muted, async (newVal, oldVal) => {
    if(newVal != oldVal) {
      muted.value = newVal;
    }
  });
  watch(() => video.value, async (newVal, oldVal) => {
    if(newVal != oldVal) {
      onSetVideo();
    }
  });
  watch(() => props.srcVideo, async (newVal, oldVal) => {
    if(newVal != oldVal) {
      srcVideo.value = newVal;
    }
  });
  watch(() => srcVideo.value, async (newVal, oldVal) => {
    if(newVal != oldVal) {
      await fetchImages();
      onSetVideo();
    }
  });
});

// コンポーネントがアンマウントされる際にHLSインスタンスを破棄
onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
  if (hls && hls.value) {
    hls.value.destroy();
    hls.value = null;
  }
});
</script>

<style scoped lang="scss">
.video {
  max-width: 100%;
  max-height: inherit;
  video {
    display: block;
    max-width: 100%;
    margin: auto;
    width: auto;
    height: auto;
    pointer-events: auto;
    background: #000;
  }
  &.is-cover {
    width: 100%;
    height: 100%;
    video {
      max-height: 100%;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style>
