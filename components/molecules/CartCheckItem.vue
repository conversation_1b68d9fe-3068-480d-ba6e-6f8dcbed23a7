<template>
  <div class="item">
    <div class="thumbnail" v-if="props.thumbnail != ''">
      <img :src="props.thumbnail" :alt="props.title">
    </div>
    <div class="title">{{ props.title }}</div>
  <div class="button_wrap">
    <ButtonMainColor baseColor="reversal" size="sm">印刷内容の確認</ButtonMainColor>
  </div>
</div>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  title?: string,
  thumbnail?: string
}>(), {
});
</script>

<style lang="scss" scoped>
.item {
  width: 100%;
  padding: 20px 14px;
  margin: 0 auto;
  border-bottom: 1px solid rgba(33,33,33,0.08);
}
.thumbnail{
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  background: #F1F1F1;
}
.title{
  font-size: 14px;
  color: #333;
  margin-bottom: 28px;
  line-height: 1.2;
}
</style>