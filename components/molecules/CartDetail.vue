<template>
<li class="items" v-for="(datail, index) in itemsData" :key="datail" :class="[(datail.option != '') ? 'withoption' : '', {'withoption': mode == 'small'}]">
  <div class="thumbnail" v-if="datail.thumbnail != ''"><img :src="publishedThumbnail(datail.thumbnail)" :alt="publishedTitle(datail.title)"></div>
  <div class="datail">
    <strong class="category" v-if="datail.category != ''">{{ publishedCategory(datail.category) }}</strong>
    <p class="title" v-if="datail.title != ''">{{ publishedTitle(datail.title) }}</p>
    <div class="wrapprice" v-if="datail.price != ''">
      <strong class="price">{{ publishedPrice(datail.price) }}</strong>
      <span class="tax">(税込)</span>
      <span class="unit" v-if="datail.priceUnit != ''">{{ publishedUnit(datail.priceUnit) }}</span>
    </div>
    <div class="orderd" v-if="datail.orderd != ''">
      数量：<b>{{ publishedOrderd(datail.orderd) }}</b>
      <span>{{ publishedOrderdUnit(datail.orderdUnit) }}</span>
    </div>
    <div class="wrapevent" v-if="datail.addevent != ''">
      <template v-for="(addevent, index) in datail.addevent" :key="addevent">
        <span class="btntodo" :class="addevent.class" :data-id="publishedId(datail.id)" :data-class="addevent.class" @click="eventtodo($event)">{{ publishedEventMenu(addevent.menu) }}</span>
      </template>
    </div>
  </div>
  <div class="button_wrap" v-if="mode == 'button'">
      <ButtonMainColor baseColor="reversal" size="md">変更する</ButtonMainColor>
  </div>
  <div class="input_wrap" v-if="mode == 'select'">
    <InputSelect/>
  </div>
  <div class="option" v-if="datail.option != ''">
    <p>オプション</p>
    <dl>
    <template v-for="(option, index) in datail.option" :key="option">
      <div class="detailoption">
        <dt>{{ publishedItem(option.item) }}</dt>
        <dd>{{ publishedSubstance(option.substance) }}</dd>
      </div>
    </template>
    </dl>
  </div>
</li>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
  mode: String
});

const itemsData = params.data;

const emit = defineEmits(['eventtodo']);

const eventtodo = (e) => {
  emit('emitEventtodoDetail', e.target.dataset.class,e.target.dataset.id);
}

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedId = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedCategory = computed(() => (data) => {
  return data;
});

const publishedPrice = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const publishedUnit = computed(() => (data) => {
  return data;
});

const publishedOrderd = computed(() => (data) => {
  return data;
});

const publishedOrderdUnit = computed(() => (data) => {
  return data;
});

const publishedEventMenu = computed(() => (data) => {
  return data;
});

const publishedItem = computed(() => (data) =>{
  return data + '：';
});

const publishedSubstance = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>
.items {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  &.withoption {
    width: 800px;
  }
  & ~ .items {
    margin-top: 15px;
  }
  .thumbnail {
    width: 140px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
  }
  .datail {
    margin-left: 13px;
    .category {
      display: block;
      margin-top: 1px;
      font-size: 12px;
      line-height: 18px;
      letter-spacing: 0.1em;
      color: $color-blacktext3;
    }
    .title {
      margin-top: 2px;
      font-size: 16px;
      line-height: 18px;
      letter-spacing: 0.1em;
      color: $color-blacktext2;
    }
    .wrapprice {
      display: flex;
      flex-wrap: wrap;
      align-items: baseline;
      margin-top: 5px;
      .price {
        letter-spacing: 0.1em;
        font-size: 21px;
        color: $color-blacktext2;
        &::first-letter {
          margin: 0 3px 0px 4px; 
          font-size: 12px;
        }
      }
      .tax {
        margin-left: 8px;
        font-size: 12px;
      }
      .unit {
        display: block;
        margin-left: 8px;
        letter-spacing: 0.1em;
        font-size: 11px;
        &::before {
          content: '/';
        }
      }
    }
    .orderd {
      margin-top: 6px;
      letter-spacing: 0.1em;
      font-size: 12px;
      b {
        font-size: 18px;
      }
    }
    .wrapevent {
      display: flex;
      align-items: center;
      margin-top: 9px;
      .btntodo {
        display: inline-flex;
        align-items: center;
        font-size: 12px;
        line-height: 145%;
        letter-spacing: 0.02em;
        color: $color-accent;
        cursor: pointer;
        & ~ .btntodo::before {
          @include BA(relative);
          top: 0;
          width: 1px;
          height: 1em;
          margin: 0 10px;
          background-color: $color-grayborder;
          transform: none;
        }
      }
      .toedititem, 
      .totrash {
        &::before {
          @include BA(relative);
          top: 0;
          width: 17px;
          height: 17px;
          margin-right: 6px;
          background-image: url(@/assets/images/icon-trash.svg);
          transform: none;
        }
      }
    }
  }
  .option {
    margin-left: 105px;
    font-size: 10px;
    line-height: 18px;
    letter-spacing: 0.1em;
    color: $color-maintext;
    dl {
      .detailoption {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
  &.withoption {
    .thumbnail {
      width: 95px;
    }
    .datail {
      margin-left: 11px;
      .category {
        margin-top: 0;
        font-size: 10px;
        line-height: 16px;
      }
      .title {
        margin-top: 0;
        font-size: 14px;
        line-height: 18px;
        letter-spacing: 0.15px;
      }
      .wrapprice {
        .price {
          font-size: 18px;
          line-height: 21px;
          letter-spacing: 0.1em;
          &::first-letter {
            margin-left: 3px; 
          }
        }
        .unit {
          &::before {
            margin-left: 1px;
          }
        }
      }
    }
  }
}

.button_wrap{
  max-width: 150px;
  width: 100%;
  margin-top: 28px;
  margin-left: 18px;
}
.input_wrap{
  max-width: 72px;
  width: 100%;
  margin-top: 28px;
  margin-left: auto;
  margin-right: 40px;
}

@include sp {
  .items {
    width: 100%;
    padding: 0 17px;
    .thumbnail {
      width: 83px;
    }
    .datail {
      .category {
        margin-top: 2px;
        font-size: 10px;
        line-height: 15px;
        letter-spacing: 0.1em;
      }
      .title {
        font-size: 14px;
        line-height: 18px;
      }
      .wrapprice {
        margin-top: 4px;
        .price {
          font-size: 18px;
          &::first-letter {
            margin: 0 2px;
          }
        }
        .tax {
          margin-left: 7px;
        }
        .unit {
          margin-left: 10px;
        }
      }
      .wrapevent {
        display: block;
        margin-top: 12px;
        .btntodo {
          display: flex;
          & ~ .btntodo {
            margin-top: 9px;
            &::before {
              display: none;
            }
          }
        }
      }
    }
    &.withoption {
      flex-wrap: wrap;
      width: 100%;
      .thumbnail {
        width: 95px;
      }
      .datail {
        .category {
          margin-top: 0;
          font-size: 10px;
          line-height: 15px;
          letter-spacing: 0.1em;
        }
        .title {
          font-size: 14px;
          line-height: 18px;
          letter-spacing: 0;
        }
        .wrapprice {
          .price {
            &::first-letter {
              margin: 0 2px;
            }
          }
          .tax {
            margin-left: 8px;
          }
          .unit {
            margin-left: 8px;
          }
        }
        .orderd {
          margin-top: 8px;
        }
      }
      .option {
        width: 100%;
        margin-top: 4px;
      }
    }
  }
  .button_wrap{
    margin-top: 10px;
    margin-left: 96px;
  }
  .input_wrap{
    margin-left: 96px;
    margin-right: auto;
  }
}
</style>