<template>
  <div class="crop_zoom_item">
    <CropImage
      @click="onClickImage(crop)"
      :src="src"
      :crop="crop"
    ></CropImage>
  </div>
</template>

<script lang="ts" setup>
import { useWebInvitationSlideModal } from '@/composables/useWebInvitationSlideModal';
interface Props {
  src?: string,
  crop?: {
    x: number,
    y: number,
    width: number,
    height: number,
    rotate: number,
    scaleFactor: number,
    scaleX: number,
    scaleY: number,
    cropBoxLeft: number,
    cropBoxTop: number,
    cropBoxWidth: number,
    cropBoxHeight: number,
    originalWidth: number,
    originalHeight: number
  },
  images?: {},
  index?: number
}
const props = withDefaults(defineProps<Props>(), {
  src: '',
  crop: null,
  images: null,
  index: null
});

const src = ref(props.src);
const crop = ref(props.crop);
const images = ref(props.images);
watch(() => props.src, (newVal) => {
  src.value = newVal;
});
watch(() => props.crop, (newVal) => {
  if (newVal) {
    crop.value = { ...newVal };
  }
}, { deep: true, immediate: true });

const showSlide = useWebInvitationSlideModal();
const onClickImage = (data:any) => {
  if(images.value){
    showSlide.setSlideData(props.images, props.index);
  }else{
    showSlide.set(data);
  }
};
</script>

<style lang="scss" scoped>
</style>