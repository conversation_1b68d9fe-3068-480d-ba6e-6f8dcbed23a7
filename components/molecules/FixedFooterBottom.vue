<template>
<div class="bottomWrap">
  <div class="bottom">

    <div class="bottom-inner">
      <div class="ui">
        <img src="@/assets/images/logo.svg" alt="favori" style="width: 80px" />
        <ListSnsNavi />
      </div>
      <div class="navi">
        <ListFooterNavi />
      </div>
      <small class="copyright"><FooterCopyright /></small>
    </div>

  </div>
</div>
</template>

<script lang="ts" setup>
</script>

<style lang="scss" scoped>
.bottom {
  padding: 32px 0 39px;
  .bottom-inner {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  .ui {
    display: flex;
    align-items: center;
  }
  .copyright {
    display: block;
    width: 100%;
    margin-top: 24px;
    line-height: 1.2;
    letter-spacing: 0.02em;
    text-align: right;
    font-size: 12px;
    color: #9D9D9D;
  }
}

.bottomWrap.alone {
  .bottom {
    padding: 21px 31px 24px;
    border-top: 8px solid $color-lightgray2;
    .ui {
      padding-bottom: 5px;
      img {
        width: 80px;
      }
      .snsnavi {
        margin: 0 0 1px 48px;
      }
    }
    .navi {
      ul {
        margin-top: 24px;
      }
    }
    :deep(.copyright) {
      margin-top: 37px;
    }
  }
}

@include sp {

.bottom {
  padding: 40px 0 39px;
  .ui {
    flex-direction: column;
    justify-content: center;
    width: 100%;
  }
  .navi {
    width: 100%;
  }
  .copyright {
    margin: 18px 23px 0 0;
  }
}
}
</style>