<template>
<div class="inputAllergyWrap">
  <InputRadio
    :required="props.required"
    :class="addClass"
    :title="title"
    :value="isUseInput"
    :items="options"
    :error="(isUseInput == '1') ? '' : error"
    @change="onChangeUseInput($event)"
    />
  <div class="inputAllergy" v-if="isUseInput == '1'">
    <div class="inputAllergies">
      <p class="pb-20">当てはまるアレルギーを選択してください</p>
      <div class="boxAllergyCategory" v-for="(allergyCategory, i) in allergyList" :key="i">
        <div class="boxAllergyCategoryTitle" v-if="allergyCategory?.category">{{ allergyCategory?.category }}</div>
        <div class="boxAllergySubCategory" v-for="(allergySubCategory, n) in allergyCategory.items" :key="n">
          <div class="boxAllergySubCategoryTitle" v-if="allergySubCategory?.subCategory">{{ allergySubCategory?.subCategory }}</div>
          <InputCheck
            :class="addClass"
            :value="inputAllergies[i][n]"
            :items="allergySubCategory.items.map(item => ({value: item?.name, label: item?.name}))"
            @change="inputAllergies[i][n] = $event"
          />
        </div>
      </div>
    </div>
    <p class="pb-12">該当のアレルギーがない場合は下記にご記入ください</p>
    <InputTextarea
      :class="addClass"
      placeholder="アレルギー情報をご入力ください"
      size="full"
      maxlength="255"
      :value="value?.allergy"
      :error="error"
      @input="onChange('allergy', $event.target.value)"
    />
  </div>
</div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const config = useRuntimeConfig()
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  addClass?: string,
  value?: {
    allergies: string[];
    allergy: string;
  },
  disabled?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string;
  isShowNull?: boolean;
  isOnlyPrimary?: boolean;
}>(), {
  value: () => ({
    allergies: [],
    allergy: ''
  }),
  required: false,
  addClass: '',
  disabled: false,
  size: 'md',
  error: '',
  isShowNull: false,
  isOnlyPrimary: false
});

const noValueText = 'アレルギーなし';

const isUseInput = ref('');
const inputAllergies = ref([] as any[]);

const options = computed(() => {
  let result = [];
  if (props.isShowNull) {
    result.push(
      {
        value: '',
        label: '未選択',
      }
    )
  }
  result.push({value: '9', label:'アレルギーなし'});
  result.push({value: '1', label:'アレルギーあり'});
  return result;
});

const allergyList = ref([]);
onMounted(async () => {
  try {
    const response = await fetch(config.public.s3PublicUrlAllergy as string)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    allergyList.value = await response.json()
    // 特定原材料（8品目）だけにする
    if (props.isOnlyPrimary) {
      let list = JSON.parse(JSON.stringify(allergyList.value));
      allergyList.value = [list[0]];
    }
  } catch (error) {
    console.error('Error fetching JSON data:', error)
    console.log(error)
  }


  let allergies = [] as any;
  if (props.value.allergies) allergies = props.value.allergies;
  initInputAllergies(allergies);

  if (allergies.length || props.value.allergy.replace(noValueText, '')) {
    isUseInput.value = '1';
  } else if (props.value.allergy == noValueText) {
    isUseInput.value = '9';
  }
})

const emit = defineEmits(['change']);

watch(inputAllergies, () => {
  let value = JSON.parse(JSON.stringify(props.value)) as any;
  value.allergies = [];
  for (let i = 0; i < allergyList.value.length; i++) {
    const allergyCategory = allergyList.value[i];
    for (let n = 0; n < allergyCategory.items.length; n++) {
      const allergySubCategory = allergyCategory.items[n];
      for (let m = 0; m < allergySubCategory.items.length; m++) {
        const item = allergySubCategory.items[m];
        if (inputAllergies.value[i][n].indexOf(item.name) !== -1) {
          value.allergies.push(item.name);
        }
      }
    }
  }
  emit('change', value);
}, {
  deep: true
});

const initInputAllergies = (allergies = [] as any) => {
  for (let i = 0; i < allergyList.value.length; i++) {
    const allergyCategory = allergyList.value[i];
    inputAllergies.value[i] = [];
    for (let n = 0; n < allergyCategory.items.length; n++) {
      const allergySubCategory = allergyCategory.items[n];
      inputAllergies.value[i][n] = [];
      for (let m = 0; m < allergySubCategory.items.length; m++) {
        const item = allergySubCategory.items[m];
        if (allergies.indexOf(item.name) !== -1) {
          inputAllergies.value[i][n].push(item.name);
        }
      }
    }
  }
}

const onChangeUseInput = (value : string) => {
  isUseInput.value = value;
  if (value == '1') {
    emit('change', {allergies: props.value.allergies, allergy: ''});
  } else if (value == '9') {
    emit('change', {allergies: [], allergy: noValueText});
    setTimeout(function() {
      initInputAllergies([]);
    }, 500);    
  } else {
    emit('change', {allergies: [], allergy: ''});
    setTimeout(function() {
      initInputAllergies([]);
    }, 500);    
  }
};

const onChange = (key: string, val : any) => {
  let value = JSON.parse(JSON.stringify(props.value)) as any;
  value[key] = val;
  emit('change', value);
};
</script>

<style lang="scss" scoped>
.inputAllergy {
  border: 1px solid var(--Gray, #D9D9D9);
  padding: 16px 20px;
  border-radius: 5px;
  font-size: 13px;
  p {
    font-size: 13px;
  }
}
.inputAllergies {
  .boxAllergyCategory {
    padding-bottom: 16px;
    margin-bottom: 16px;
    border-bottom: 1px solid var(--Gray, #D9D9D9);
  }
  .boxAllergySubCategory {
    padding-left: 8px;
    margin-bottom: 5px;
  }
}
.boxAllergyCategoryTitle {
  color: #333;
  // color: var(---main, var(--01-main, #EE645E));
  // color: #EE645E;
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 12px;
}
.boxAllergySubCategoryTitle {
  color: #333;
  font-size: 14px;
  font-weight: 400;
  // font-weight: 700;
  margin-bottom: 8px;
  &:after {
    content: "：";
  }
}
</style>

<style lang="scss">
.inputAllergy {
  textarea {
    min-height: 2em;
  }
}
.inputAllergyWrap {
  .inputRadio {
    label {
      display: block;
      margin-bottom: 10px;
    }
  }
}
</style>