<template>
  <InputRadio
    :title="title"
    :required="required"
    :items="options"
    @change="onChange"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  name?: string,
  value?: string|null,
  disabled?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string,
  isShowNull?: boolean,
  isShowNoUpdate?: boolean
}>(), {
  name: '',
  value: null,
  required: false,
  disabled: false,
  size: 'md',
  error: '',
  isShowNull: false,
  isShowNoUpdate: false
});

const emit = defineEmits(['change']);

const onChange = ($event : any) => {
  if ($event === 'null') {
    emit('change', null)
  } else {
    emit('change', $event)
  }
};

// リスト
const options = computed(() => {
  let result = [];
  if (props.isShowNull) {
    result.push(
      {
        value: 'null',
        checked: (String(props.value) === 'null'),
        label: '未回答',
      }
    )
  }
  result.push({
    value: GUEST_ATTENDANCE_MASTER.PRESENT,
    checked: (props.value === GUEST_ATTENDANCE_MASTER.PRESENT),
    label: GUEST_ATTENDANCE_MASTER.PRESENT,
  })
  result.push({
    value: GUEST_ATTENDANCE_MASTER.ABSENT,
    checked: (props.value === GUEST_ATTENDANCE_MASTER.ABSENT),
    label: GUEST_ATTENDANCE_MASTER.ABSENT,
  })
  result.push({
    value: GUEST_ATTENDANCE_MASTER.PENDING,
    checked: (props.value === GUEST_ATTENDANCE_MASTER.PENDING),
    label: GUEST_ATTENDANCE_MASTER.PENDING,
  });
  if (props.isShowNoUpdate) {
    result.push(
      {
        value: '',
        checked: (String(props.value) === ''),
        label: '変更しない',
      }
    )
  }
  return result;
});
</script>