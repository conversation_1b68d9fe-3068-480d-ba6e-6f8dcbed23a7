<template>
  <label class="inputBank">
    <div class="title"><span :class="{'required': props.required}">銀行名 / 支店</span></div>
    <div class="inputWrap" ref="inputWrapRef" :class="{'wrapped': isWrapped}">
      <div class="input" :class="{'is-placeholder': (! value?.bank_name || ! value?.branch_name)}">
        <span v-if="value?.bank_name">{{ value?.bank_name }}</span>
        <span v-else>銀行名</span>
        <span class="attr">/</span>
        <span v-if="value?.branch_name">{{ value?.branch_name }}</span>
        <span v-else>支店</span>
      </div>
      <a class="btn btn-primary-outline" @click.prevent="onClickShowModal()">銀行 / 支店を探す</a>
    </div>
    <ModalSearchBank
      v-if="isShowModal"
      :isSubModal="true"
      :value="value"
      @update="emit('update', $event)"
      @close="onClickCloseModal()"
    ></ModalSearchBank>
  </label>
</template>

<script setup lang="ts">
import { ref, onMounted, onUpdated } from 'vue';

const props = withDefaults(defineProps<{
  required?: boolean,
  addClass?: string,
  value?: {
    bank_name: string;
    bank_code: string;
    branch_code: string;
    branch_name: string;
  },
  disabled?: boolean
  error?: string
}>(), {
  value: {
    bank_name: "",
    bank_code: "",
    branch_code: "",
    branch_name: ""
  },
  required: false,
  addClass: '',
  disabled: false,
  error: ''
});

const emit = defineEmits(['update', 'showSubModal', 'closeSubModal']);

// モーダル表示
const isShowModal = ref(false);

// モーダル表示した場合
const onClickShowModal = () => {
  isShowModal.value = true;
  emit('showSubModal');
};
// モーダル閉じた場合
const onClickCloseModal = () => {
  isShowModal.value = false;
  emit('closeSubModal');
};

const inputWrapRef = ref<HTMLDivElement | null>(null);
const isWrapped = ref(false);

const checkWrapping = () => {
  if (inputWrapRef.value) {
    const inputWidth = inputWrapRef.value.querySelector('.input')?.offsetWidth || 0;
    const aTagWidth = inputWrapRef.value.querySelector('a')?.offsetWidth || 0;
    const parentWidth = inputWrapRef.value.clientWidth;

    isWrapped.value = inputWidth + aTagWidth > parentWidth;
  }
};

onMounted(() => {
  checkWrapping();
});

onUpdated(() => {
  checkWrapping();
});
</script>

<style lang="scss" scoped>
.title{
  font-size: 12px;
  line-height: 1;
  margin-bottom: 7px;
  color: #49454F;
  white-space: nowrap;
}
.input {
  color: var(--black, var(--text-black, #333));
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.28px;
  display: inline-block;
  margin-right: 20px;
  &.is-placeholder {
    color: var(--Gray_dark, #9C9C9C);
  }
  .attr {
    color: var(--black, var(--text-black, #333));
    display: inline-block;
    padding: 0 5px;
  }
}
label {
  display: inline-block;
  width: 100%;
  max-width: v-bind(sizeWidth);
  position: relative;
  vertical-align: top;
  & + label{
    margin-left: 14px;
  }
  &.xs{
    max-width: 87px;
  }
  &.sm{
    max-width: 164px;
  }
  &.md{
    max-width: 180px;
  }
  &.lg{
    max-width: 293px;
  }
  &.xl{
    max-width: 343px;
  }
  &.half{
    max-width: calc(50% - 7px);
    & + .half{
      margin-left: 14px;
    }
  }
  &.full{
    & + label{
      margin-left: 0;
    }
  }
}

.inputWrap {
  position: relative;

  &.wrapped {
    .btn {
      margin-top: 10px; /* 必要に応じて調整 */
    }
  }

  .btn {
    padding-left: 10px;
    padding-right: 10px;
    @include sp {
      display: inline-block;
    }
  }
}
</style>
  