<template>
  <InputRadio
    :title="title"
    :disabled="disabled"
    :required="required"
    :items="options"
    :error="error"
    @change="onChange"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  value?: string,
  disabled?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string
}>(), {
  value: '',
  required: false,
  disabled: false,
  size: 'md',
  error: ''
});

// リスト
const options = computed(() => {
  return [
    {
      value: 'MALE',
      checked: (props.value == 'MALE'),
      label: GENDER_MASTER.MALE,
    },
    {
      value: 'FEMALE',
      checked: (props.value == 'FEMALE'),
      label: GENDER_MASTER.FEMALE,
    },
    {
      value: 'ETC',
      checked: (props.value == 'ETC'),
      label: GENDER_MASTER.ETC,
    }
  ];
});

const emit = defineEmits(['change']);

const onChange = ($event : any) => {
  if ($event === 'MALE') {
    emit('change', 'MALE')
  } else if ($event === 'FEMALE') {
    emit('change', 'FEMALE')
  } else if ($event === 'ETC') {
    emit('change', 'ETC')
  } else {
    emit('change', null)
  }
};
</script>