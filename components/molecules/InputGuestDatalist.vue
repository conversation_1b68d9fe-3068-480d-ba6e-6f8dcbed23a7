<template>
  <div>
    <InputDatalist
      :title="title"
      :placeholder="placeholder"
      :required="required"
      :size="size"
      :options="options"
      :value="value"
      :error="error"
      @change="emit('change', $event)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  guestListId: string;
  title?: string;
  placeholder?: string;
  required?: boolean;
  value?: string;
  disabled?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number;
  error?: string;
  target: 'guest_title' | 'relationship' | 'guest_honor';
}>(), {
  guestListId: '',
  value: '',
  required: false,
  disabled: false,
  size: 'md',
  error: '',
  target: 'guest_title'
});

const emit = defineEmits(['change']);

// APIから読み込み
const {guestList} = useGetOneGuestList(String(props.guestListId));

const options = computed(() => {
  if (! guestList.value?.guests) return [];
  let items = [];
  for (let i = 0; i < guestList.value.guests.length; i++) {
    const guest = guestList.value.guests[i];
    if (! guest?.[props.target]) continue;
    const val = String(guest?.[props.target]);
    if (items.indexOf(val) !== -1) continue;
    items.push(val);
  }
  items.sort();
  return items.map(item => {
    return {value: item, label: item};
  });
});
</script>