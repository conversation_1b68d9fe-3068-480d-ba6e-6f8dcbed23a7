<template>
  <div class="row">
    <InputSelect
      :title="title"
      :required="required"
      :size="size"
      :options="options"
      :value="value"
      :error="error"
      @change="$emit('change', $event)"
    />
    <div class="text-right">
      <a class="link-edit" @click.prevent="onClickShowModal()">
        <img src="@/assets/images/icon-edit.svg" width="18" />
        グループを編集する
      </a>
    </div>
  </div>
  <ModalManageGroup
    v-if="isShowModal"
    :title="modalTitle"
    :isSubModal="true"
    :guestListId="guestListId"
    @close="onClickCloseModal()"
  ></ModalManageGroup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  guestListId: string;
  title?: string,
  modalTitle?: string,
  required?: boolean,
  name?: string,
  value?: string,
  disabled?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string
}>(), {
  guestListId: '',
  modalTitle: '個別に編集する',
  name: '',
  value: '',
  required: false,
  disabled: false,
  size: 'md',
  error: ''
});

const emit = defineEmits(['change', 'showSubModal', 'closeSubModal']);

// モーダル表示
const isShowModal = ref(false);

// モーダル表示した場合
const onClickShowModal = () => {
  isShowModal.value = true;
  emit('showSubModal');
};
// モーダル閉じた場合
const onClickCloseModal = () => {
  isShowModal.value = false;
  emit('closeSubModal');
};


// APIから guestGroup を読み込み
const {guestGroups, refetch} = useGetManyGuestGroup(String(props.guestListId))
// 敬称リスト
const options = computed(() => {
  const options = guestGroups.value.map(guestGroup => {
    return {value: guestGroup.id, label: guestGroup.name};
  });
  return [{value: '', label: 'グループを選択してください'}].concat(options);
});

onMounted(async() => {
  await refetch();
});
</script>