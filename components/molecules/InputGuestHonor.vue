<template>
  <div>
    <Loading v-if="loading" />
    <InputDatalist
      v-else
      :title="title"
      :placeholder="placeholder"
      :required="required"
      :size="size"
      :options="options"
      :value="value"
      :error="error"
      @change="emit('change', $event)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  guestListId: string;
  title?: string;
  placeholder?: string;
  required?: boolean;
  value?: string;
  disabled?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number;
  error?: string;
}>(), {
  guestListId: '',
  value: '',
  required: false,
  disabled: false,
  size: 'md',
  error: '',
});

const emit = defineEmits(['change']);

// APIから読み込み
const {guestList} = useGetOneGuestList(String(props.guestListId));
const {guestHonorMasters, loading} = useGetManyGuestHonorMaster();

const options = computed(() => {
  if (! guestList.value?.guests) return [];
  let items = [];
  // 様、くん、ちゃん の順に変える
  if (guestHonorMasters.value.find((item:any) => item.name == '様')) items.push('様');
  if (guestHonorMasters.value.find((item:any) => item.name == 'くん')) items.push('くん');
  if (guestHonorMasters.value.find((item:any) => item.name == 'ちゃん')) items.push('ちゃん');
  for (let i = 0; i < guestHonorMasters.value.length; i++) {
    const name = guestHonorMasters.value[i].name;
    if (items.indexOf(name) !== -1) continue;
    items.push(name);
  }
  if (guestList.value?.guests) {
    for (let i = 0; i < guestList.value.guests.length; i++) {
      const guest = guestList.value.guests[i];
      if (! guest?.guest_honor) continue;
      const val = String(guest?.guest_honor);
      if (items.indexOf(val) !== -1) continue;
      items.push(val);
    }
  }
  // items.sort();
  return items.map(item => {
    return {value: item, label: item};
  });
});
</script>