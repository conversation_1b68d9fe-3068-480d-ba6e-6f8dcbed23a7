<template>
  <div class="row profileImage profileImageEdit">
    <div class="img">
      <img
        v-if="value?.presigned_url"
        :src="value.presigned_url"
      />
      <ImageUUID v-else-if="value?.uuid" :uuid="value.uuid" />
      <img v-else src="@/assets/images/profile-image.svg" />
    </div>
    <InputUploadBtn
      btnClass="link-edit"
      btnText="写真を変更"
      :showPreview="false"
      :value="value"
      imageUploadType="user"
      @change="emit('change', $event)"
    ></InputUploadBtn>
  </div>
  <span v-if="props.error" class="input-error">{{ props.error }}</span>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  required?: boolean,
  value?: string,
  disabled?: boolean
  error?: string
}>(), {
  required: false,
  value: '',
  disabled: false,
  error: ''
});

const emit = defineEmits(['change', 'showSubModal', 'closeSubModal']);
</script>

<style lang="scss">
.profileImageEdit{
  display: flex;
  align-items: center;
  margin-bottom: 28px;
  .img {
    width: 88px;
    margin-right: 25px;
  }
  .upload {
    .fileInput {
      &:before {
        content: " ";
        display: inline-block;
        background: url(/assets/images/icon-edit.svg) center center no-repeat;
        width: 18px;
        height: 18px;
        background-size: contain;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }
}
</style>