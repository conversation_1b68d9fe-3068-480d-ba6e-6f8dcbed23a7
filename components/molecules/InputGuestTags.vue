<template>
  <div class="row inputGuestTags">
    <InputMultiselect
      :title="title"
      :required="required"
      :size="size"
      :options="options"
      :value="selected"
      :error="error"
      placeholder="御車代あり など　入力後 完了で追加"
      @change="onChange"
      @create="onCreateTag"
    />
    <div class="text-right">
      <a class="link-edit" @click.prevent="onClickShowModal()">
        <img src="@/assets/images/icon-edit.svg" width="18" />
        タグを編集する
      </a>
    </div>
  </div>
  <ModalManageTag
    v-if="isShowModal"
    :title="modalTitle"
    :isSubModal="true"
    :guestListId="guestListId"
    @close="onClickCloseModal()"
  ></ModalManageTag>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const props = withDefaults(defineProps<{
  guestListId: string;
  title?: string,
  modalTitle?: string,
  required?: boolean,
  name?: string,
  value: {id: string}[],
  disabled?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string
}>(), {
  guestListId: '',
  modalTitle: '個別に編集する',
  name: '',
  required: false,
  disabled: false,
  size: 'md',
  error: ''
});

const emit = defineEmits(['change', 'showSubModal', 'closeSubModal']);

// モーダル表示
const isShowModal = ref(false);

// モーダル表示した場合
const onClickShowModal = () => {
  isShowModal.value = true;
  emit('showSubModal');
};
// モーダル閉じた場合
const onClickCloseModal = () => {
  isShowModal.value = false;
  emit('closeSubModal');
};

// APIから guestTag を読み込み
const { guestTags, refetch } = useGetManyGuestTag(String(props.guestListId))
// 敬称リスト
const options = computed(() => {
  const options =  guestTags.value.map(guestTag => {
    return {value: guestTag.id, label: guestTag.tag};
  });
  return options;
});

const selected = computed(() => {
  if (! Array.isArray(props.value)) return [];
  return props.value.map(item => String(item.id));
});

const onChange = ($event:string[]) => {
  let result = [];
  if (! Array.isArray($event)) return ;
  result = $event.map(val => ({id: val}));
  emit('change' , result);
};

// const { create, errors: createErrors } = useCreateGuestTag();
const onCreateTag = async (tag:string) => {
  // 完了かスペースで追加をここでしようと思ったが、余計なデータも増えちゃうので、登録時にまとめて実行する
  // await create({guest_list_id: props.guestListId, tag: tag});
  // await refetch();
  // let results = JSON.parse(JSON.stringify(selected.value));
  // for (let i = 0; i < results.length; i++) {
  //   const result = results[i];
  //   const guestTag = guestTags.value.find(item => item.tag == result);
  //   if (guestTag) results[i] = guestTag.id;
  // }
  // emit('change' , results.map((val:any) => ({id: val})));
};

onMounted(async() => {
  await refetch();
});
</script>

<style lang="scss">
.inputGuestTags {
  .multiselect-tag {
    color: var(--black, #333);
    font-size: 11px;
    font-weight: 400;
    letter-spacing: 0.25px;
    border-radius: 16px;
    border: 1px solid var(--Gray, #D9D9D9);
    background: var(--Gray_light, #F4F4F4);
    padding: 0 0 0 10px; 
  }
}
</style>