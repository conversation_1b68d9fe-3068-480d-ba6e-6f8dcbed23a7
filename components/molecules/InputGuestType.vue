<template>
  <InputRadio
    :title="title"
    :disabled="disabled"
    :required="required"
    :items="options"
    :error="error"
    @change="onChange"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  title?: string,
  required?: boolean,
  value?: string,
  disabled?: boolean
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number,
  error?: string
}>(), {
  value: '',
  required: false,
  disabled: false,
  size: 'md',
  error: ''
});

// リスト
const options = computed(() => {
  return [
    {
      value: 'GROOM',
      checked: (props.value == 'GROOM'),
      label: GUEST_TYPE_MASTER.GROOM,
    },
    {
      value: 'BRIDE',
      checked: (props.value == 'BRIDE'),
      label: GUEST_TYPE_MASTER.BRIDE,
    },
  ];
});

const emit = defineEmits(['change']);

const onChange = ($event : any) => {
  if ($event === 'GROOM') {
    emit('change', 'GROOM')
  } else if ($event === 'BRIDE') {
    emit('change', 'BRIDE')
  } else {
    emit('change', null)
  }
};
</script>