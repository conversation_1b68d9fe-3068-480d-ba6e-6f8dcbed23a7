<template>
<div class="row">
  <InputText
    :title="props.title"
    :required="props.required"
    :placeholder="props.placeholder"
    :size="props.size"
    :value="value || ''"
    :error="error || ''"
    @input="emits('change', $event.target.value)"
    @focus="onFocusOldKanjiTarget()"
    @blur="onBlurOldKanjiTarget"
    ref="refInput"
  />
  <a :class="{'link-oldkanji edit': true, 'is-hide': oldKanjiBtnHide, 'is-disabled': oldKanjiBtnHidden }" @click="showOldKanjiModal = true;emits('showSubModal');">
    <img src="@/assets/images/icon-edit.svg" width="18" />
    旧字を入力する
  </a>
  <ModalOldkanji 
    v-if="showOldKanjiModal" 
    :isSubModal="props.isSubModal"
    :refTarget="refOldKanjiTarget?.inputRef"
    @close="showOldKanjiModal = false;emits('closeSubModal');"
    @change="onChangeOldKanji"
  ></ModalOldkanji>
</div>
</template>

<script setup lang="ts">
export interface Props {
  title?: string;
  placeholder?: string;
  required?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'half' | 'full' | number;
  value: string | null;
  error?: string;
  isSubModal?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  required: false,
  size: 'md',
  value: '',
  error: '',
  isSubModal: false,
});

const emits = defineEmits<{
  (e: 'change', value: string): void;
  (e: 'showSubModal'): void;
  (e: 'closeSubModal'): void;
}>();

// モーダル表示
const showOldKanjiModal = ref(false);
const refInput = ref(null);
const refOldKanjiTarget = ref(null);
const oldKanjiBtnHide = ref(true);
const oldKanjiBtnHidden = ref(true);
const onFocusOldKanjiTarget = () => {
  oldKanjiBtnHide.value = false;
  oldKanjiBtnHidden.value = false;
  refOldKanjiTarget.value = refInput.value;
};
const onBlurOldKanjiTarget = () => {
  oldKanjiBtnHide.value = true;
  setTimeout(function(){
    if (oldKanjiBtnHide.value) oldKanjiBtnHidden.value = true;
  }, 500)
};

const onChangeOldKanji = (value:string) => {
    emits('change', value)
}
</script>

<style lang="scss" scoped>
.edit{
  display: inline-block;
  font-size: 14px;
  line-height: 1.2;
  color: #B18A3E;
  padding: 10px 0;
  text-decoration: none;
  img{
    width: 18px;
    height: 18px;
    vertical-align: text-bottom;
  }
  &:hover{
    text-decoration: underline;
  }
}

.link-oldkanji {
  transition: 0.35s ease-in-out;
  &.is-hide {
    opacity: 0;
    cursor: default;
    &.is-disabled {
      pointer-events: none;
    }
  }
}

</style>
