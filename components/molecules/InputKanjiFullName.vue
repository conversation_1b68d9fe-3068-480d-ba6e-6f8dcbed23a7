<template>
<div class="inputs">
<div>
  <InputKanji
    title="姓"
    :required="props.required"
    placeholder="福永"
    size="full"
    :isSubModal="props.isSubModal"
    :value="values.last_name"
    :error="errors?.last_name?.$errors?.[0]?.$message"
    @showSubModal="emits('showSubModal')"
    @closeSubModal="emits('closeSubModal')"
    @change="emits('change', {last_name: $event, first_name: values.first_name})"
  />
</div>
<div>
  <InputKanji
    title="名"
    :required="props.required"
    placeholder="このみ"
    size="full"
    :isSubModal="props.isSubModal"
    :value="values.first_name"
    :error="errors?.first_name?.$errors?.[0]?.$message"
    @showSubModal="emits('showSubModal')"
    @closeSubModal="emits('closeSubModal')"
    @change="emits('change', {last_name: values.last_name, first_name: $event})"
  />
</div>
</div>
</template>

<script setup lang="ts">
export interface Props {
  required: boolean;
  values: {last_name: string, first_name: string};
  errors: any;
  isSubModal: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  required: false,
  values: {},
  errors: {},
  isSubModal: false,
});

const emits = defineEmits<{
  (e: 'change', values: any): void;
  (e: 'showSubModal'): void;
  (e: 'closeSubModal'): void;
}>();
</script>

<style lang="scss" scoped>
.inputs {
  display: flex;
  margin-bottom: 10px;
  > * {
    ~ * {
      margin-left: 14px;
    }
  }
}
</style>
