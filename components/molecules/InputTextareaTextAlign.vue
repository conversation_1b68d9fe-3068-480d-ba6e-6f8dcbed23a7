<template>
  <div>
    <div class="reset">
      <a href="#">初期値に戻す</a>
    </div>
    <InputTextarea
      :title="props.title"
      :name="props.name"
      :value="props.value"
      :disabled="props.disabled"
      :placeholder="props.placeholder"
      @change="$emit('change', $event)"
    />
    <div class="align_settings">
      <a class="align_setting align_setting-left" @click="onClickShowModal()">
        <img src="@/assets/images/icon-edit.svg" width="18" />
      </a>
      <a class="align_setting align_setting-center" @click="onClickShowModal()">
        <img src="@/assets/images/icon-edit.svg" width="18" />
      </a>
      <a class="align_setting align_setting-right" @click="onClickShowModal()">
        <img src="@/assets/images/icon-edit.svg" width="18" />
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
const props = withDefaults(defineProps<{
  title?: string,
  name?: string,
  value?: string,
  disabled?: boolean
}>(), {
  name: '',
  value: '',
  disabled: false,
});

const emit = defineEmits(['change', 'changeAlign', 'reset']);

</script>

<style lang="scss" scoped>
</style>