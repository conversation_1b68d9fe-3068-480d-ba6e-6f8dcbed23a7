<template>
<div class="thumbnail" v-if="itemsData.thumbnail != ''">
  <div class="first">
    <img :src="publishedThumbnail(itemsData.thumbnail[0])" :alt="publishedTitle(itemsData.title)">
  </div>
  <div class="thumbs">
    <ul>
      <li v-for="(thumb, index) in itemsData.thumbnail" :key="thumb"><img :src="publishedThumbnail(thumb)" alt=""></li>
    </ul>
  </div>
</div>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const itemsData = params.data;

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>
.thumbnail {
  display: flex;
  margin-top: 11px;
  .first {
    flex-shrink: 0;
    width: 336px;
  }
  .thumbs {
    ul {
      display: flex;
      flex-wrap: wrap;
      flex-direction: row-reverse;
      li {
        width: 60px;
        margin: 0 0 4px 4px;
        cursor: pointer;
      }
    }
  }
}

@include sp {
.thumbnail {
  display: block;
  margin-top: 16px;
  .first {
    width: 100%;
    max-width: 375px;
  }
  .thumbs {
    ul {
      flex-direction: row;
      li {
        margin: 0 0 2px 2px;
      }
    }
  }
}
}
</style>