<template>
<h3 class="name" :class="params.data.manage?'manage':''" v-if="params.data.name !== ''">{{ publishedName(params.data.name) }}<button v-if="params.data.manage" :data-modalwindow="params.data.manage" @click="controlWindow($event)"><img src="@/assets/images/icon-more_vert.svg" alt=""></button></h3>
<div class="items" v-for="(item, index) in params.data.datail" :key="item" :class="item.thumbnail === ''?item.control + ' empty':item.control">

  <div class="thumbnail">
    <template v-if="item.thumbnail !== ''">
    <img :src="publishedThumbnail(item.thumbnail)" :alt="publishedTitle(item.title)">
    </template>
    <template v-else>
    <a @click="addItem()"><img src="@/assets/images/icon-emptyimage.svg" :alt="publishedTitle(item.title)"></a>
    </template>
  </div>
  <div class="item" :class="item.mode === 'select'?'withSelect':''">
    <strong class="category" v-if="item.category !== ''">{{ publishedCategory(item.category) }}</strong>
    <p class="title" v-if="item.title !== ''">{{ publishedTitle(item.title) }}</p>
    <div class="wrapprice" v-if="item.price !== ''">
      <strong class="price">{{ publishedPrice(item.price) }}</strong>
      <span class="tax">（税込）</span>
      <span class="unit" v-if="item.priceUnit !== ''">{{ publishedUnit(item.priceUnit) }}</span>
    </div>
    <div class="orderd" v-if="item.orderd !== ''">
      <b>{{ publishedOrderd(item.orderd) }}</b>
      <span>{{ publishedOrderdUnit(item.orderdUnit) }}</span>
    </div>
    <div class="wrapevent" v-if="item.addevent !== ''">
      <template v-for="(addevent, index) in item.addevent" :key="addevent">
        <span class="btntodo" :class="addevent.class" :data-id="publishedId(item.id)" :data-class="addevent.class" @click="eventtodo($event)">{{ publishedEventMenu(addevent.menu) }}</span>
      </template>
    </div>
    <div class="button_wrap button01" v-if="item.mode === 'button01'">
      <ButtonMainColor baseColor="reversal" :buttonsize="200">編集する</ButtonMainColor>
      <ButtonMainColor :buttonsize="200">カートに入れる</ButtonMainColor>
    </div>
    <div class="button_wrap button02" v-if="item.mode === 'button02'">
      <ButtonMainColor baseColor="reversal" :buttonsize="200">セットを追加</ButtonMainColor>
    </div>
    <div class="button_wrap button03" v-if="item.mode === 'button03'">
    <template v-if="item.thumbnail !== ''">
      <ButtonMainColor baseColor="reversal" :buttonsize="200">編集する</ButtonMainColor>
      <ButtonMainColor :buttonsize="200">印刷手配に進む</ButtonMainColor>
    </template>
    <template v-else>
      <ButtonMainColor :buttonsize="200" @click="addItem()">アイテム選択して編集</ButtonMainColor>
    </template>
    </div>
    <template v-if="item.control === 'accordion'">
      <button class="accordionBtn"><span class="acdtrg is-close" @click="useMethodsAccordion($event)"></span></button>
    </template>
    <div class="option" v-if="item.option[0]">
      <dl>
        <div class="headeroption">
          <dt class="optiontitle">オプション</dt>
          <dd class="quantity">数量</dd>
          <dd class="price">価格</dd>
        </div>
        <template v-for="(option, index) in item.option" :key="option">
        <div class="detailoption">
          <dt class="optiontitle">{{ publishedItem(option.title) }}</dt>
          <dd class="quantity">{{ publishedSubstance(option.quantity) }}</dd>
          <dd class="price">{{ publishedPrice(option.price) }}</dd>
        </div>
        </template>
      </dl>
    </div>
  </div>
  <div class="input_wrap" v-if="item.mode === 'select'">
    <InputSelect size="xs"/>
  </div>
  <div class="created" v-if="item.created !== ''">
    {{ publishedCreated(item.created) }}
  </div>
</div>

</template>

<script lang="ts" setup>
import { useMethodsAccordion } from '@/assets/js/customFunction.js'

const params = defineProps({
  data: Array
});

const emit = defineEmits(['eventtodo', 'addItem', 'controlWindow']);

const eventtodo = (e) => {
  emit('emitEventtodoDetail', e.target.dataset.class,e.target.dataset.id);
}

const addItem = (e) => {
  emit('emitAddItem');
}

const controlWindow = (e) => {
  emit('emitEngageControlWindow', e);
}

const publishedName = computed(() => (data) => {
  return data;
});

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedId = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedCategory = computed(() => (data) => {
  return data;
});

const publishedPrice = computed(() => (data) => {
  if (isNaN(data)) {
    return data;
  }
  else {
    return '¥' + Number(data).toLocaleString();
  }
});

const publishedUnit = computed(() => (data) => {
  return data;
});

const publishedOrderd = computed(() => (data) => {
  return data;
});

const publishedOrderdUnit = computed(() => (data) => {
  return data + '購入';
});

const publishedEventMenu = computed(() => (data) => {
  return data;
});

const publishedItem = computed(() => (data) =>{
  return data;
});

const publishedSubstance = computed(() => (data) => {
  return data;
});

const publishedCreated = computed(() => (data) => {
  return '作成日：' + data;
});
</script>

<style lang="scss" scoped>
.items {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  &.withoption {
    width: 800px;
  }
  &.accordion {
    .item {
      position: relative;
      padding-right: 30px;
      .accordionBtn {
        position: absolute;
        right: 0;
        top: 60px;
        width: 30px;
        height: 30px;
        span {
          position: relative;
          width: 100%;
          height: 100%;
          padding: 0;
          &::after {
            right: 3px;
          }
        }
      }
    }
  }
  &.empty {
    align-items: center;
    .button03 {
      margin-top: 0;
    }
  }
  & ~ .items {
    margin-top: 15px;
  }

  .thumbnail {
    width: 140px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    a {
      cursor: pointer;
    }
  }
  .item {
    flex-grow: 1;
    margin-left: 13px;
    &.withSelect {
      max-width: 235px;
    }
    .category {
      display: block;
      margin-top: 0px;
      font-size: 10px;
      font-weight: 400;
      line-height: 12px;
      letter-spacing: 0.6px;
      color: $color-blacktext3;
    }
    .title {
      margin-top: 2px;
      font-size: 14px;
      line-height: 22px;
      letter-spacing: 0.90px;
      color: $color-blacktext2;
    }
    .wrapprice {
      display: flex;
      flex-wrap: wrap;
      align-items: baseline;
      .price {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        letter-spacing: 0.90px;
        color: $color-blacktext2;
        &::first-letter {
          margin: 0 3px 0px 4px; 
          font-size: 12px;
        }
      }
      .tax {
        margin-left: 0;
        font-size: 12px;
      }
      .unit {
        display: block;
        margin-left: 8px;
        letter-spacing: 0.1em;
        font-size: 11px;
        &::before {
          content: '/';
        }
      }
    }
    .orderd {
      margin-top: 1px;
      letter-spacing: 0.1em;
      font-size: 12px;
      b {
        font-size: 18px;
      }
    }
    .wrapevent {
      display: flex;
      align-items: center;
      .btntodo {
        display: inline-flex;
        align-items: center;
        font-size: 12px;
        line-height: 145%;
        letter-spacing: 0.02em;
        color: $color-accent;
        cursor: pointer;
        & ~ .btntodo::before {
          @include BA(relative);
          top: 0;
          width: 1px;
          height: 1em;
          margin: 0 10px;
          background-color: $color-grayborder;
          transform: none;
        }
      }
      .toedititem, 
      .totrash {
        &::before {
          @include BA(relative);
          top: 0;
          width: 17px;
          height: 17px;
          margin-right: 6px;
          background-image: url(@/assets/images/icon-trash.svg);
          transform: none;
        }
      }
    }
  }
  .option {
    width: 100%;
    margin-top: 24px;
    padding-left: 1px;
    font-size: 10px;
    line-height: 18px;
    letter-spacing: 0.1em;
    color: $color-maintext;
    dl {
      .headeroption ,
      .detailoption {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 0 14px 6px;
        border-bottom: 1px solid $color-grayborder;
        text-align: right;
        font-size: 10px;
        line-height: 120%;
        letter-spacing: 0.2px;
        .optiontitle {
          flex-grow: 1;
          text-align: left;
        }
        .quantity {
          width: 36px;
          text-align: center;
        }
        .price {
          width: 95px;
        }
      }
      .headeroption {
        color: $color-graytext2;
      }
      .detailoption {
        padding-top: 7px;
        padding-bottom: 8px;
        color: $color-blacktext2;
        font-size: 12px;
        line-height: 18px;
        letter-spacing: 1.2px;
      }
    }
  }
  .created {
    width: 100%;
    margin: 22px 0 2px;
    padding-right: 3px;
    text-align: right;
    color: $color-blacktext6;
    font-size: 11px;
  }
  &.withoption {
    .thumbnail {
      width: 95px;
    }
    .item {
      margin-left: 11px;
      .category {
        margin-top: 0;
        font-size: 10px;
        line-height: 16px;
      }
      .title {
        margin-top: 0;
        font-size: 14px;
        line-height: 18px;
        letter-spacing: 0.15px;
      }
      .wrapprice {
        .price {
          font-size: 18px;
          line-height: 21px;
          letter-spacing: 0.1em;
          &::first-letter {
            margin-left: 3px; 
          }
        }
        .unit {
          &::before {
            margin-left: 1px;
          }
        }
      }
    }
  }
}

.button_wrap{
  display: flex;
  gap: 14px;
  width: 100%;
  margin-top: 0;
  margin-left: 0;
  &.button01 {
    justify-content: flex-end;
  }
  &.button02 {
    margin-top: 23px;
  }

  &.button03 {
    justify-content: flex-end;
    margin-top: 20px;
  }
  :deep(a.button--md) {
    padding: 8px 10px 7px;
  }
}
.input_wrap{
  max-width: 115px;
  width: 100%;
  margin-top: 28px;
  margin-left: auto;
  padding-right: 43px;
}

@include sp {
  .items {
    width: 100%;
    padding: 20px 17px 0;
    &.accordion {
      padding-bottom: 20px;
    }
    .thumbnail {
      width: 110px;
    }
    .option {
      width: calc(100% + 123px);
      margin-left: -123px;
      padding-left: 0;
      dl {
        .headeroption ,
        .detailoption {
          padding: 0 0 6px;
        }
        .detailoption {
          padding-top: 7px;
          padding-bottom: 8px;
        }
      }
    }
    .item {
      &.withSelect {
        max-width: calc(100% - 127px);
      }
      .category {
        margin-top: 2px;
        font-size: 10px;
        line-height: 15px;
        letter-spacing: 0.1em;
      }
      .title {
        font-size: 14px;
        line-height: 18px;
      }
      .wrapprice {
        margin-top: 4px;
        .price {
          font-size: 18px;
          &::first-letter {
            margin: 0 2px;
          }
        }
        .tax {
          margin-left: 7px;
        }
        .unit {
          margin-left: 10px;
        }
      }
      .wrapevent {
        display: block;
        .btntodo {
          display: flex;
          & ~ .btntodo {
            margin-top: 9px;
            &::before {
              display: none;
            }
          }
        }
      }
    }
    &.withoption {
      flex-wrap: wrap;
      width: 100%;
      .thumbnail {
        width: 95px;
      }
      .item {
        .category {
          margin-top: 0;
          font-size: 10px;
          line-height: 15px;
          letter-spacing: 0.1em;
        }
        .title {
          font-size: 14px;
          line-height: 18px;
          letter-spacing: 0;
        }
        .wrapprice {
          .price {
            &::first-letter {
              margin: 0 2px;
            }
          }
          .tax {
            margin-left: 8px;
          }
          .unit {
            margin-left: 8px;
          }
        }
        .orderd {
          margin-top: 8px;
        }
      }
      .option {
        width: 100%;
        margin-top: 4px;
      }
    }
  }
  .button_wrap{
    margin-top: 10px;
    margin-left: 96px;
  }
  .input_wrap{
    max-width: 100%;
    margin-top: 15px;
    margin-left: 122px;
    margin-right: auto;
  }
}

</style>