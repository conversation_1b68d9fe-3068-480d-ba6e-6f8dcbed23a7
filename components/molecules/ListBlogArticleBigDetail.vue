<template>
<li class="articles" :class="layout" v-for="(datail, index) in articleData" :key="datail">
  <div class="thumbnail" v-if="datail.thumbnail">
    <NuxtLink :to="publishedLink(datail.link)"><img :src="publishedThumbnail(datail.thumbnail)" alt=""></NuxtLink>
  </div>
  <div class="datail">
    <div class="wrapdate">
      <span class="date">{{ publishedDate(datail.date) }}</span>
      <span class="category">{{ publishedCategory(datail.category) }}</span>
    </div>
    <p class="title" v-if="datail.title">{{ publishedTitle(datail.title) }}</p>
    <p class="description" v-if="(showMenu.description !== false && datail.description)">{{ publishedDescription(datail.description) }}</p>
    <div class="tags" v-if="(showMenu.tags !== false && datail.tags[0])">
      <ul>
        <ArticleTag :propsArticleTag="datail.tags" />
      </ul>
    </div>
  </div>
</li>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
  assignClass: String,
  assignMenu: Array,
});

const articleData = params.data;
const layout = params.assignClass;
const showMenu = params.assignMenu;

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedLink = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedDescription = computed(() => (data) => {
  return data;
});

const publishedDate = computed(() => (data) => {
  return data;
});

const publishedCategory = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>
.articles {
  width: 240px;
  padding: 0 17px 15px;
  & ~ .articles {
    border-left: 1px solid $color-grayborder;
  }
  &:nth-of-type(3n+1) {
    border-left: none;
  }
  &:nth-of-type(n+4) {
    padding-top: 16px;
    border-top: 1px solid $color-grayborder;
  }
  .thumbnail {
    position: relative;
    width: 100%;
    img {
      width: 100%;
    }
  }
  .datail {
    margin-top: 3px;
    .wrapdate {
      display: flex;
      justify-content: space-between;
      margin-top: 5px;
      .date {
        margin: 0 1em 0 2px;
        font-weight: 500;
        font-size: 10px;
        line-height: 120%;
        letter-spacing: 0.02em;
        color: $color-graytext2;
      }
      .category {
        font-weight: 500;
        font-size: 10px;
        line-height: 120%;
        letter-spacing: 0.02em;
        color: $color-main;
      }
    }
    .title {
      margin-top: 6px;
      font-weight: 700;
      font-size: 14px;
      line-height: 120%;
      letter-spacing: 0.02em;
      color: $color-blacktext2;
    }
    .description {
      margin-top: 8px;
      font-size: 12px;
      line-height: 145%;
      letter-spacing: 0.02em;
      color: $color-blacktext2;
    }
    .tags {
      margin-top: 7px;
      ul {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
}

@include sp {

.articles {
  display: flex;
  width: 100%;
  padding-top: 15px;
  border-top: 1px solid $color-grayborder;
  & ~ .articles {
    margin-left: 0;
    padding-top: 16px;
    border-left: none;
  }
  &:nth-of-type(n+2) {
    margin-top: 0;
  }
  &:nth-of-type(n+4) {
    padding-top: 15px;
  }
  .thumbnail {
    flex-shrink: 0;
    width: 127px;
  }
  .datail {
    margin: 0 0 0 10px;
    .wrapdate {
      margin-top: 1px;
      .date {
        margin-left: 0;
      }
    }
    .title {
      letter-spacing: 0;
    }
    .description {
      margin-top: 5px;
      font-size: 10px;
      line-height: 140%;
    }
    .tags {
      margin-top: 6px;
    }
  }
  &.fullparams {
    .thumbnail {
      width: 75px;
    }
    .datail {
      .title {
        margin-top: 9px;
      }
    }
  }
}

}
</style>