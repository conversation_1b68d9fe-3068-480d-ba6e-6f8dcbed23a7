<template>
<li class="articles" v-for="(datail, index) in articleData" :key="datail">
  <NuxtLink :to="publishedLink(datail.link)"><div class="thumbnail" v-if="datail.thumbnail">
    <img :src="publishedThumbnail(datail.thumbnail)" alt="">
  </div>
  <div class="datail">
    <div class="wrapdate">
      <span class="date">{{ publishedDate(datail.date) }}</span>
      <span class="category">{{ publishedCategory(datail.category) }}</span>
    </div>
    <p class="title" v-if="datail.title">{{ publishedTitle(datail.title) }}</p>
  </div></NuxtLink>
</li>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const articleData = params.data;

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedLink = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  const max = 27;
  let len = 0;
  let return_data = data;
  for (let i = 0; i < data.length; i++) {
    (data[i].match(/[ -~]/)) ? len += 1 : len += 2;
    if (len > (max * 2)) {
      return_data = data.substring(0, max) + '…';
      break;
    }
  }
  return return_data;
});

const publishedDate = computed(() => (data) => {
  return data;
});

const publishedCategory = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>
.articles {
  a {
    display: flex;
    width: 100%;
    padding: 12px;
    text-decoration: none;
  }
  & ~ .articles {
    margin-left: 0;
    border-top: 1px solid $color-grayborder;
  }
  .thumbnail {
    flex-shrink: 0;
    width: 86px;
  }
  .datail {
    margin-left: 6px;
    .wrapdate {
      display: flex;
      justify-content: space-between;
      margin-top: 0;
      .date {
        margin: 0 1em 0 2px;
        font-weight: 500;
        font-size: 10px;
        line-height: 120%;
        letter-spacing: 0.02em;
        color: $color-graytext2;
      }
      .category {
        font-weight: 500;
        font-size: 10px;
        line-height: 120%;
        letter-spacing: 0.02em;
        color: $color-main;
      }
    }
    .title {
      margin-top: 6px;
      font-weight: 400;
      font-size: 12px;
      line-height: 145%;
      letter-spacing: 0.02em;
      color: $color-blacktext2;
    }
  }
}

@include sp {
.articles {
  .datail {
    margin: 0 0 0 10px;
    .wrapdate {
      margin-top: 1px;
      .date {
        margin-left: 0;
      }
    }
    .title {
      letter-spacing: 0;
    }
  }
}
}
</style>