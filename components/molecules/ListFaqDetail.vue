<template>
  <div class="faqDetail">
    <div class="question"
      :class="{'is-open': isOpen}"
      @click="onClickOpenFaq"
    >
      <div class="labelText is-question">
        {{question}}
        <img class="chevron" src="@/assets/images/icon-chevron.svg">
      </div>
    </div>
    <div class="answer">
      <div class="labelText is-answer">
        {{answer}}
      </div>
      <div class="link" v-if="link">
        <NuxtLink :to="link">
          詳細を見る
          <img src="@/assets/images/icon-chevron-right-y.svg">
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
  
<script setup>
import { ref } from 'vue';
const params = defineProps({
  question: String,
  answer: String,
  link: String
});
const isOpen = ref(false);

const onClickOpenFaq = () => {
  isOpen.value = !isOpen.value;
}

</script>

<style lang="scss" scoped>
.labelText{
  position: relative;
  &::before{
    content: '';
    font-size: 16px;
    font-weight: bold;
    position: absolute;
    left: -1.2em;
    top: -0.1em;
  }
  &.is-question{
    &::before{
      content: 'Q';
      color: #B18A3E;
    }
  }
  &.is-answer{
    &::before{
      content: 'A';
      color: #6F8B81;
    }
  }
}
.faqDetail{
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}
.question,
.answer {
  padding: 18px 36px;
  border-bottom: 1px solid #F4F4F4;
}
.question{
  position: relative;
  cursor: pointer;
  .chevron{
    position: absolute;
    right: -24px;
    transition: 0.35s ease-in-out;
  }
  &.is-open{
    .chevron{
      transform: rotate(180deg);
    }
    & + .answer{
      line-height: 1.5;
      opacity: 1;
      visibility: visible;
      padding: 18px 36px;
      img { height: auto;}
    }
  }
}
.answer{
  background: $color-lightbackground;
  line-height: 0;
  opacity: 0;
  visibility: hidden;
  padding: 0 36px;
  transition: 0.35s ease-in-out;
  white-space: pre-line;
  img { height: 0;}
}
.link{
  text-align: right;
  a{
    color: #B18A3E;
    text-decoration: none;
    &:hover{
      text-decoration: underline;
    }
  }
}
</style>