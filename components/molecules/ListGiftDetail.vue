<template>
<li class="gifts" v-for="(datail, index) in itemsData.datail" :key="datail">
  <strong class="category" v-if="(datail.category !== '') && ((itemsData.type === 'listguestgift') || (itemsData.type === 'listsubgift'))">{{ publishedCategory(datail.category) }}</strong>

  <div class="thumbnail" v-if="datail.thumbnail !== ''"><img :src="publishedThumbnail(datail.thumbnail)" :alt="publishedTitle(datail.title)"><span class="close" v-if="(datail.close)"></span></div>

  <div class="datail">
    <strong class="category" v-if="(datail.category !== '') && ((itemsData.type !== 'listguestgift') && (itemsData.type !== 'listsubgift'))">{{ publishedCategory(datail.category) }}</strong>
    <small class="number" v-if="datail.number !== ''">{{ publishedNumber(datail.number, datail.numberUnit) }}</small>
    <p class="title" v-if="datail.title !== ''">{{ publishedTitle(datail.title) }}</p>
    <div class="labels" v-if="datail.labels">
      <span class="label" v-for="(label, index) in datail.labels" :key="label">{{ publishedLabel(label) }}</span>
    </div>
    <div class="wrapprice" v-if="datail.price !== ''">
      <strong class="price">{{ publishedPrice(datail.price) }}</strong>
      <s class="strike" v-if="datail.priceStrike !== ''">{{ publishedStrike(datail.priceStrike) }}</s>
      <span class="tax">(税込)</span>
      <span class="unit" v-if="datail.priceUnit !== ''">{{ publishedUnit(datail.priceUnit) }}</span>
    </div>
    <div class="orderd" v-if="datail.orderd !== ''">{{ publishedOrderd(datail.orderd) }}</div>
  </div>
</li>

</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const itemsData = params.data;

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedCategory = computed(() => (data) => {
  return data;
});

const publishedNumber = computed(() => (data, unit) => {
  if (unit === false) {
    return data;
  }
  else {
    return '商品番号 ' + data;
  }
});

const publishedLabel = computed(() => (data) => {
  return data;
});

const publishedPrice = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const publishedStrike = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const publishedUnit = computed(() => (data) => {
  return data;
});

const publishedOrderd = computed(() => (data) => {
  return data;
});
</script>
  
<style lang="scss" scoped>
.listgift {
  .gifts {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    & ~ .gifts {
      margin-top: 27px;
    }
    .thumbnail {
      flex-shrink: 0;
      position: relative;
      width: 90px;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
      .close {
        position: absolute;
        top: 3px;
        right: 3px;
        width: 20px;
        height: 20px;
        border: 1px solid $color-blackLight;
        border-radius: 100%;
        background: url(@/assets/images/icon-close-b.svg) no-repeat center center/8px;
        background-color: $color-mainbackground;
        cursor: pointer;
      }
    }
    .datail {
      width: calc(100% - 98px);
      margin-left: 8px;
    }
    .category {
      display: block;
      margin-top: 2px;
      font-size: 14px;
      line-height: 120%;
    }
    .number {
      display: block;
      margin-top: 4px;
      font-size: 10px;
      line-height: 120%;
      letter-spacing: 0.02em;
      color: $color-graytext;
    }
    .title {
      margin-top: 7px;
      font-size: 16px;
      line-height: 160%;
      color: $color-blacktext2;
    }
    .labels {
      margin-top: 7px;
      .label {
        display: inline-block;
        padding: 3px 7px 2px;
        background-color: $color-alert;
        font-size: 12px;
        line-height: 120%;
        letter-spacing: 0.02em;
        color: $color-whitetext;
      }
    }
    .wrapprice {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-end;
      margin-top: 0px;
      font-family: 'Roboto', sans-serif;
      .price {
        font-size: 18px;
        color: $color-alert;
      }
      .strike {
        margin-left: 7px;
        font-size: 15px;
      }
      .tax {
        margin-left: 3px;
        font-size: 10px;
      }
      .unit {
        display: block;
        letter-spacing: 0px;
        margin-left: 6px;
        font-size: 12px;
        &::before {
          content: '/';
        }
      }
    }
    .orderd {
      margin-top: 10px;
      letter-spacing: 0.1em;
      font-size: 12px;
    }
    & > .category {
      display: block;
      width: 100%;
      margin-bottom: 10px;
      color: $color-blacktext2;
      font-size: 16px;
      line-height: 120%;
      letter-spacing: 0.32px;
    }
  }
}
.listgift.listsubgift {
  .gifts {
    .thumbnail {
      width: 65px;
    }
    .title {
      margin-top: 4px;
      font-size: 12px;
      line-height: 145%;
      letter-spacing: 0.24px;
    }
  }
}
.listgift.listgiftdatail {
  .gifts {
    .thumbnail {
      width: 74px;
    }
    .number {
      margin-top: 3px;
    }
    .title {
      margin-top: 5px;
    }
  }
}
.listgift.listgiftrequest {
  .gifts {
    & ~ .gifts {
      margin-top: 7px;
    }
    .datail {
      margin-left: 12px;
    }
    .thumbnail {
      width: 57px;
    }
    .category {
      margin-top: 0;
      font-size: 10px;
      line-height: 1.6;
      letter-spacing: 1.5px;
      color: $color-graytext;
    }
    .title {
      margin-top: 3px;
    }
  }
}
.listgift.listgiftwrapping {
  .gifts {
    .thumbnail {
      width: 87px;
    }
    .category {
      margin-top: 0;
      font-size: 10px;
      line-height: 1.6;
      letter-spacing: 1.5px;
      color: $color-graytext;
    }
    .title {
      margin-top: 3px;
    }
  }
}
.listgift.listgiftrequesthistory {
  .gifts {
    .datail {
      margin-left: 6px;
    }
    .thumbnail {
      width: 65px;
    }
    .number {
      margin-top: 5px;
    }
    .title {
      margin-top: 5px;
    }
  }
}
.listgift.listgiftrequestconfirm {
  .gifts {
    .datail {
      margin-left: 10px;
    }
    .thumbnail {
      width: 65px;
    }
    .number {
      margin-top: 2px;
    }
    .title {
      margin-top: 5px;
    }
    .wrapprice {
        margin-top: 7px;
        line-height: 1.5;
        letter-spacing: 0.1em;
      .price {
        font-size: 12px;
        color: $color-blacktext2;
      }
      .tax {
        margin-left: 7px;
        font-size: 12px;
        color: $color-blacktext2;
      }
    }
  }
}


@include sp {
.listgift {
  .gifts .title {
    font-size: 16px;
  }
}
.listgift.listguestgift {
  .gifts .thumbnail {
    width: 90px;
  }
  .gifts .number {
    margin-top: 3px;
    font-size: 11px;
  }
  .gifts .category {
    font-size: 16px;
    font-weight: normal;
  }
  .gifts > .category {
    font-size: 16px;
  }
  .gifts .title {
    font-size: 16px;
  }
}
.listgift.listsubgift {
  .gifts .thumbnail {
    width: 65px;
  }
  .gifts .category {
    font-size: 16px;
    font-weight: normal;
  }
}
.listgift.listgiftwrapping {
  .datail {
    margin-left: 11px;
  }
  .gifts {
    .title {
      margin-top: 6px;
    }
  }
}
}
</style>