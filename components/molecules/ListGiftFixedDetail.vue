<template>
<li class="datailFixedGift" v-for="(datail, index) in itemsData.datail" :key="datail">
  <div class="status" :class="publishedStatus(datail.status)">
    <div v-if="datail.name" class="nameLabels">
      <p class="name">{{ publishedName(datail.name) }}<small>{{ publishedHonorific(datail.honorific) }}</small></p>
      <ul v-if="datail.labels[0]" class="labels">
        <li class="label" v-for="(label, index) in datail.labels" :key="label" :class="publishedLabelClass(label.class)">{{ publishedLabelTitle(label.title) }}</li>
      </ul>
    </div>
    <p v-if="datail.address" class="address">{{ publishedAddress(datail.address) }}</p>
    <p v-if="datail.telnumber" class="telnumber">{{ publishedTelnumber(datail.telnumber) }}</p>
    <div class="companions" v-if="datail.companions">
      <small>送り状記載内容</small>
      <ul>
        <li v-for="(companion, index) in datail.companions" :key="companion">{{ publishedCompanions(companion) }}</li>
        <li class="edit"><a href="#"><img src="@/assets/images/icon-edit.svg" width="18" /></a></li>
      </ul>
    </div>
  </div>
  <div class="datail">
    <p v-if="datail.arrival" class="arrival">{{ publishedArrival(datail.arrival) }}</p>
    <div v-if="datail.invoice" class="invoice">お問い合わせ送り状No.<strong>{{ publishedInvoice(datail.invoice) }}</strong><NuxtLink :to="publishedInvoicelink(datail.invoice)" target="_blank"><img src="@/assets/images/icon-target_blank.svg" alt="お問い合わせリンク" /></NuxtLink></div>
    <ul v-if="datail.thumbnails[0]" class="thumbnails">
      <li v-for="(thumbnail, index) in datail.thumbnails" :key="thumbnail">
        <div class="thumbnail" :class="publishedThumbnailClass(thumbnail.class)"><small v-if="index >= 1">{{ index + 1 }}点目</small><img :src="publishedThumbnail(thumbnail.thumbnail)" alt=""></div>
      </li>
    </ul>
  </div>
</li>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const itemsData = params.data;

const publishedStatus = computed(() => (data) => {
  return data;
});

const publishedName = computed(() => (data) => {
  return data;
});

const publishedHonorific = computed(() => (data) => {
  return data;
});

const publishedLabelClass = computed(() => (data) => {
  return data;
});

const publishedLabelTitle = computed(() => (data) => {
  return data;
});

const publishedAddress = computed(() => (data) => {
  return data;
});

const publishedTelnumber = computed(() => (data) => {
  return data;
});

const publishedArrival = computed(() => (data) => {
  return data;
});

const publishedInvoice = computed(() => (data) => {
  return data;
});

const publishedInvoicelink = computed(() => (data) => {
  // 仮 発送業者の判定振り分けが必要
  return 'http://jizen.kuronekoyamato.co.jp/jizen/servlet/crjz.b.NQ0010?id=' + data;
});

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedThumbnailClass = computed(() => (data) => {
  return data;
});

const publishedCompanions = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>
.datailFixedGift {
  display: flex;
  border: 1px solid $color-grayborder;
  border-radius: 4px;
  & ~ .datailFixedGift {
    margin-top: 8px;
  }
}

.status {
  width: 350px;
  padding: 11px 16px 11px 15px;
  background-color: $color-lightbackground;
  .nameLabels {
    display: flex;
    justify-content: space-between;
    .name {
      font-size: 16px;
      line-height: 120%;
      letter-spacing: 0.02em;
      color: $color-blacktext2;
      small {
        margin-left: 0.5em;
        font-size: 14px;
        color: $color-graytext2;
      }
    }
    .labels {
      display: flex;
      .label {
        padding: 2px 8px 2px 7px;
        border: 1px solid $color-main;
        border-radius: 16px;
        background-color: $color-mainbackground;
        text-align: center;
        font-size: 11px;
        line-height: 12px;
        letter-spacing: 0.25px;
        color: $color-main;
        & ~ .label {
          margin-left: 3px;
        }
      }
    }
  }
  .address ,
  .telnumber {
    margin-top: 7px;
    font-size: 12px;
    white-space: pre-wrap;
    line-height: 1.5;
    letter-spacing: 0.25px;
    color: $color-graytext3;
  }
  .telnumber {
    margin-top: 0;
  }
  &.afterDelivery {
    background: #F5F0E1;
    .nameLabels .labels .label {
      border-color: $color-accent;
      background-color: $color-accent;
      color: $color-whitetext;
      &.standbyDelivery {
        opacity: 0.5;
      }
    }
  }
}
.datail {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  width: 410px;
  padding: 11px 21px;
  .arrival {
    margin-top: 2px;
    font-weight: 700;
    font-size: 14px;
    line-height: 120%;
    color: $color-maindark;
  }
  .invoice {
    display: flex;
    align-items: baseline;
    margin-bottom: 9px;
    font-size: 10px;
    color: $color-blackLight;
    strong {
      margin: 0 10px 0 2px;
      font-weight: 700;
      font-size: 14px;
    }
    a {
      position: relative;
      top: 2px;
    }
  }
  .thumbnails {
    display: flex;
    align-items: center;
    width: 100%;
    li {
      & ~ li {
        margin-left: 16px;
      }
      .thumbnail {
        width: 88px;
        small {
          display: none;
          text-align: center;
        }
      }
      .subGift {
        width: 64px;
      }
    }
  }
}
.companions {
  margin-top: 7px;
  small {
    color: $color-graytext2;
    font-size: 10px;
    line-height: 140%;
    letter-spacing: 0.2px;
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;
    margin-top: 4px;
    color: $color-blacktext2;
    font-size: 12px;
    line-height: 145%;
    letter-spacing: 0.24px;
    li.edit {
      margin-left: 5px;
      img {
        width: 18px;
      }
    }
  }
}

@include sp {
.datailFixedGift {
  display: block;
}

.status {
  width: 100%;
  padding: 14px 12px 16px 11px;
  .address {
    margin-top: 9px;
  }
}
.datail {
  width: 100%;
  padding: 12px 10px;
  .arrival {
    color: $color-blacktext2;
  }
  .invoice {
    margin-top: 5px;
    margin-bottom: 15px;
    strong {
      margin: 0 13px 0 6px;
      letter-spacing: -0.3px;
    }
  }
  .thumbnails li .thumbnail small {
    display: block;
    padding-bottom: 2px;
  }
  .thumbnails li ~ li {
    margin-left: 16px;
  }
  .thumbnails li .thumbnail {
    width: 64px;
  }
  .thumbnails li .subGift {
    width: 47px;
  }
}
}
</style>