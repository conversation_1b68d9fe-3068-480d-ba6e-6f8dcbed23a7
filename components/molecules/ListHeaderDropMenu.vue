<template>
<div class="header-dropmenu" :class="{'close': ! show}">
  <div class="section-inner">

    <strong class="suttitle">{{ props.title }}</strong>
    <ul>
      <li v-for="(datail, index) in props.list" :key="datail" :class="'list' + (index + 1)">
        <NuxtLink :to="datail.link">{{ datail.menu }}</NuxtLink>
      </li>
    </ul>
  </div>
  <button class="button-close" @click="emits('close')"><img src="@/assets/images/icon-close.svg" alt="閉じる" /></button>
</div>
</template>

<script lang="ts" setup>
export interface Props {
  show: boolean;
  title?: string;
  list?: any;
}
const props = withDefaults(defineProps<Props>(), {
  show: false,
  title: 'ペーパーアイテム',
  list: [
    {
      menu: "結婚式招待状",
      link: "#01"
    }, 
    {
      menu: "席次表",
      link: "#02"
    }, 
    {
      menu: "席札",
      link: "#03"
    }, 
    {
      menu: "メニュー表",
      link: "#04"
    }, 
    {
      menu: "プログラム（式次第）",
      link: "#05"
    }, 
    {
      menu: "プロフィールブック",
      link: "#06"
    },
    {
      menu: "ゲストカード",
      link: "#07"
    }, 
    {
      menu: "ペーパーアイテム手作り・単品",
      link: "#08"
    }, 
  ],
});

const emits = defineEmits<{
  (e: 'close', v: null): void;
}>()
</script>

<style lang="scss" scoped>
.header-dropmenu {
  left: 0.55%;
  width: 98.9%;
  padding: 28px 25px;
  background: rgba(255, 255, 255, 0.95);
  z-index: 5;
  .section-inner {
    padding: 0 17px;
    .suttitle {
      display: inline-block;
      position: relative;
      margin-left: 3px;
      padding-left: 33px;
      line-height: 1.2;
      letter-spacing: 0.04em;
      font-size: 18px;
      &::before {
        @include BA;
        left: 0;
        width: 23px;
        height: 24px;
        background-image: url(@/assets/images/icon-category.svg);
        background-position: center -2px;
      }
    }
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    width: 700px;
    margin-top: 26px;
    li {
      width: 249px;
      margin-bottom: 8px;
      &:nth-of-type(even) {
        margin-left: 54px;
      }
      a {
        display: inline-flex;
        align-items: center;
        position: relative;
        width: 100%;
        min-height: 40px;
        padding: 3px 33px 0 38px;
        line-height: 1.2;
        text-decoration: none;
        font-size: 14px;
        color: $color-blacktext2;
        &::before ,
        &::after {
          @include BA;
        }
        &::before {
          left: 0;
          width: 28px;
          height: 28px;
        }
        &::after {
          right: 0;
          width: 8px;
          height: 12px;
          background-image: url(@/assets/images/icon-chevron-right-b.svg);
        }
      }
      @for $i from 1 through 8 {
        &.list#{$i} {
          a::before {
              background-image: url('@/assets/images/icon-category01-0' + $i + '.png');
          }
        }
      }
    }
  }
  .button-close {
    position: absolute;
    top: 33px;
    right: 66px;
  }
}
</style>