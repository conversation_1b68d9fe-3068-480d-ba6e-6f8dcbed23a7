<template>
<div class="thumbnail" v-if="params.data.thumbnail">
  <span class="ranking" :class="'ranking' + (params.index + 1)" v-if="(params.ranking === true)">{{ params.index + 1 }}</span>
  <NuxtLink :to="publishedLink(params.data.link)"><img :src="publishedThumbnail(params.data.thumbnail)" :alt="publishedTitle(params.data.title)"></NuxtLink>
</div>
<div class="datail">
  <button class="inFavorite" :data-id="dataFavorite(params.data.id)" @click.prevent="toggleInFavorite($event)"><img src="@/assets/images/icon-favorite-gl.svg" alt="お気に入り" /></button>
  <p class="title" v-if="params.data.title">{{ publishedTitle(params.data.title) }}</p>
  <div class="labels" v-if="params.data.labels && (params.subGift !== true)">
    <span class="label" v-for="(label, index) in params.data.labels" :key="label">{{ publishedLabel(label) }}</span>
  </div>
  <div class="wrapprice" v-if="params.data.price">
    <p class="price">
      <strong class="price" :class="params.data.priceStrike?'discount':''">{{ publishedPrice(params.data.price) }}<span class="tax">(税込)<template v-if="params.data.unit">{{ publishedUnit(params.data.unit) }}</template></span></strong>
      <s class="strike" v-if="params.data.priceStrike">{{ publishedStrike(params.data.priceStrike) }}<span class="tax">(税込)<template v-if="params.data.unit">{{ publishedUnit(params.data.unit) }}</template></span></s>
    </p>
  </div>
  <div class="wrapstars" v-if="(params.data.stars || params.data.stars===0)">
    <Stars :stars="params.data.stars" :review="params.data.review" />
  </div>
  <div class="tags" v-if="params.data.tags">
    <ul>
      <ArticleTag :propsArticleTag="params.data.tags" />
    </ul>
  </div>
</div>
</template>

<script lang="ts" setup>
const params = defineProps({
  ranking: Boolean,
  data: Array,
  index: Number,
});

const emit = defineEmits(['toggleInFavorite']);

const toggleInFavorite = (e) => {
  emit('emitToggleInFavorite' , e.target.closest(".inFavorite").dataset.id);
};

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedLink = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedLabel = computed(() => (data) => {
  return data;
});

const publishedPrice = computed(() => (data) => {
  return "¥" + data;
});

const publishedStrike = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const dataFavorite = computed(() => (data) => {
  return data;
});

const publishedUnit = computed(() => (data) => {
  return "／" + data;
});
</script>

<style lang="scss" scoped>
.thumbnail {
  position: relative;
  width: 100%;
  img {
    width: 100%;
  }
  .ranking {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 4px;
    left: 5px;
    width: 22px;
    height: 22px;
    padding: 0 0 1px 1px;
    border-radius: 50%;
    background: #FFFFFF;
    font-weight: 700;
    font-size: 12px;
    line-height: 110%;
    letter-spacing: 0.06em;
    color: $color-blacktext2;
    opacity: 0.8;
    &.ranking1 {
      background: $color-accent;
      color: $color-whitetext;
    }
    &.ranking2 {
      background: #858585;
      color: $color-whitetext;
    }
    &.ranking3 {
      background: #D05237;
      color: $color-whitetext;
    }
  }
}
.datail {
  position: relative;
  margin-top: 3px;
  .inFavorite {
    position: absolute;
    right: 14px;
    top: -23px;
    padding: 6px 5px 4px;
    width: 26px;
    height: 26px;
    background: $color-mainbackground;
    border: 1px solid $color-grayborder;
    border-radius: 100px;
    img {
      width: 16px;
      vertical-align: top;
    }
  }
  .title {
    display: flex;
    align-items: center;
    min-height: 2.7em;
    padding-top: 0.1em;
    font-size: 14px;
    line-height: 120%;
    letter-spacing: 0.02em;
    color: $color-blacktext2;
  }
  .labels {
    margin-top: 1px;
    .label {
      display: inline-block;
      width: auto;
      padding: 2px 3px 1px;
      background-color: $color-alert;
      font-size: 12px;
      line-height: 120%;
      letter-spacing: 0.02em;
      color: $color-whitetext;
    }
  }
  .wrapprice {
    position: relative;
    margin-top: 1px;
    padding-right: 17px;
    color: $color-blacktext4;
    .price {
      font-size: 18px;
      font-weight: 500;
      line-height: 105%;
      letter-spacing: 0.04em;
      .tax {
        font-size: 11px;
      }
      &.discount {
        color: $color-alert;
      }
    }
    .strike {
      display: block;
      margin-left: 0;
      font-size: 15px;
    }
  }
  .wrapstars {
    margin-top: 1px;
  }
  .tags {
    :deep(ul) {
      display: flex;
      flex-wrap: wrap;
      li {
        margin-right: 7px;
        padding: 3px 7px;
        font-size: 10px;
      }
    }
  }
}

@include sp {
.datail {
  margin-top: 5px;
  .title {
    font-size: 12px;
  }
  .wrapprice {
    margin-top: 3px;
    .price {
      font-size: 14px;
    }
  }
  .wrapstars {
    margin-top: 4px;
  }
  .tags {
    margin-top: 1px;
    ul li {
      font-size: 10px;
    }
  }
}
}
</style>