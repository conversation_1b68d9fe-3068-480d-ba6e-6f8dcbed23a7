<template>
  <div class="item">
    <div class="thumbnail" v-if="props.thumbnail">
      <img :src="publishedThumbnail(props.thumbnail)" :alt="publishedTitle(props.title)">
    </div>
    <div class="datail">
      <p class="title" v-if="props.title">{{ publishedTitle(props.title) }}</p>
      <div class="labels" v-if="props.labels[0]">
        <span class="label" v-for="(label, index) in props.labels" :key="label">{{ publishedLabel(label) }}</span>
      </div>
      <div class="wrapprice" v-if="props.price">
        <strong class="price">{{ publishedPrice(props.price) }}</strong>
        <s class="strike" v-if="props.priceStrike">{{ publishedStrike(props.priceStrike) }}</s>
        <span class="tax">(税込)</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  thumbnail?: string,
  title?: string,
  labels?: string[],
  price?: string,
  priceStrike?: string
}>(), {
  label: []
});

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedLabel = computed(() => (data) => {
  return data;
});

const publishedPrice = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const publishedStrike = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});
</script>

<style lang="scss" scoped>
.item {
  width: 138px;
  margin: 0 8px;
  .thumbnail {
    width: 138px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
  }
  .datail {
    margin-top: 2px;
  }
  .title {
    font-size: 14px;
    line-height: 120%;
    letter-spacing: 0.02em;
    color: $color-blacktext2;
  }
  .labels {
    margin-top: 1px;
    .label {
      display: inline-block;
      width: 99%;
      padding: 4px 7px;
      background-color: $color-alert;
      font-size: 10px;
      line-height: 120%;
      letter-spacing: 0.02em;
      color: $color-whitetext;
    }
  }
  .wrapprice {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    margin-top: 4px;
    font-family: 'Roboto', sans-serif;
    .price {
      font-size: 18px;
      color: $color-alert;
    }
    .strike {
      margin-left: 7px;
      font-size: 15px;
    }
    .tax {
      margin-left: 3px;
      font-size: 10px;
    }
  }
}

@include sp {
  .items .title {
    font-size: 12px;
  }
}

</style>

