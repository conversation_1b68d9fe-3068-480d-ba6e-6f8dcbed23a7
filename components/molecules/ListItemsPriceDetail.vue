<template>
<ul class="listitems">
  <li class="items" v-for="(datail, index) in params.data" :key="datail">

    <div class="thumbnail" v-if="datail.thumbnail"><img :src="publishedThumbnail(datail.thumbnail)" :alt="publishedTitle(datail.title)"></div>
    <div class="datail">
      <p class="title" v-if="datail.title">{{ publishedTitle(datail.title) }}</p>
      <div class="labels" v-if="datail.labels && (params.subGift !== true)">
        <span class="label" v-for="(label, index) in datail.labels" :key="label">{{ publishedLabel(label) }}</span>
      </div>
      <div class="wrapprice" v-if="datail.price && (params.subGift !== true)">
        <strong class="price" :class="datail.priceStrike?'discount':''">{{ publishedPrice(datail.price) }}</strong>
        <s class="strike" v-if="datail.priceStrike">{{ publishedStrike(datail.priceStrike) }}</s>
        <span class="tax">(税込)<b v-if="datail.unit">／{{ datail.unit }}</b></span>
      </div>
      <div class="assignment" v-if="datail.assignment && (params.subGift !== true)">
        <p :class="datail.assignment === datail.purchased?'enough':''">割り当て中：<b>{{ datail.assignment }}</b><b v-if="datail.purchased"> / {{ datail.purchased }}</b><small>セット</small></p>
      </div>
      <div class="control" v-if="datail.control">
        <ButtonMainColor baseColor="reversal">このギフトを選ぶ</ButtonMainColor>
        <a class="showDetail"><span>詳細を見る</span></a>
      </div>
    </div>

  </li>
</ul>

</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
  subGift: Boolean,
});

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedLabel = computed(() => (data) => {
  return data;
});

const publishedPrice = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const publishedStrike = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});
</script>

<style lang="scss" scoped>
.listitems {
  display: flex;
  flex-wrap: wrap;
  width: 671px;
  margin: 0 auto;
  .items {
    width: 155px;
    & ~ .items {
      margin-left: 17px;
    }
    &:nth-of-type(4n+1) {
      margin-left: 0;
    }
    &:nth-of-type(n+5) {
      margin-top: 22px;
    }
    .thumbnail {
      width: 155px;
      border: 1px solid #d9d9d9;
      border-radius: 2px;
    }
    .datail {
      margin-top: 2px;
    }
    .title {
      font-size: 14px;
      line-height: 120%;
      letter-spacing: 0.02em;
      color: $color-blacktext2;
    }
    .labels {
      margin-top: 1px;
      .label {
        display: inline-block;
        width: 99%;
        padding: 4px 7px;
        background-color: $color-alert;
        font-size: 10px;
        line-height: 120%;
        letter-spacing: 0.02em;
        color: $color-whitetext;
      }
    }
    .wrapprice {
      display: flex;
      flex-wrap: wrap;
      align-items: baseline;
      margin-top: 4px;
      font-family: 'Roboto', sans-serif;
      .price {
        letter-spacing: 1.1px;
        font-size: 18px;
        font-weight: 400;
        &.discount {
          color: $color-alert;
        }
      }
      .strike {
        margin-left: 7px;
        font-size: 15px;
      }
      .tax {
        margin-left: 5px;
        font-size: 10px;
        b {
          font-weight: 400;
        }
      }
    }
    .assignment {
      margin-top: 6px;
      p {
        color: $color-graytext2;
        font-size: 12px;
        line-height: 120%;
        letter-spacing: 0.24px;
        b {
          font-weight: 400;
          color: $color-blacktext2;
        }
        small {
          margin-left: 3px;
          font-size: 12px;
          line-height: 120%;
          letter-spacing: 0.24px;
          color: $color-blacktext2;
        }
        &.enough {
          b {
            color: $color-accent;
          }
        }
      }
    }
    .control {
      margin-top: 8px;
      text-align: center;
      :deep(a.button--md.button--reversal) {
        margin: 0;
        padding-top: 8px;
        padding-bottom: 8px;
      }
      .showDetail {
        display: block;
        margin-top: 3px;
        color: $color-accent;
        font-size: 12px;
        line-height: 210%;
        letter-spacing: 0.24px;
        span {
          display: inline-block;
          position: relative;
          padding-left: 22px;
          &:before {
            @include BA;
            left: 0;
            width: 17px;
            height: 17px;
            background-image: url(@/assets/images/icon-search-ac.svg)
          }
        }
      }
    }
  }
}

@include sp {
.listitems {
  min-width: 100%;
  .items {
    .title {
      font-size: 12px;
      margin-bottom: 4px;
    }
    .assignment {
      margin-top: 8px;
    }
    .control {
      margin-top: 7px;
    }
  }
}
}
</style>