<template>
<ul class="listitems">
  <li class="items" :class="'items' + (index + 1)" v-for="(datail, index) in itemsData" :key="datail">

    <div class="thumbnail" v-if="datail.thumbnail">
      <span class="ranking" :class="'ranking' + (index + 1)" v-if="(itemsRanking === true)">{{ index + 1 }}</span>
      <NuxtLink :to="publishedLink(datail.link)"><img :src="publishedThumbnail(datail.thumbnail)" :alt="publishedTitle(datail.title)"></NuxtLink>
    </div>
    <div class="datail">
      <p class="title" v-if="datail.title">{{ publishedTitle(datail.title) }}</p>
      <div class="wrapprice" v-if="datail.price">
        <p class="price">{{ publishedPrice(datail.price) }}
          <span class="tax">(税込)<template v-if="datail.unit">{{ publishedUnit(datail.unit) }}</template></span>
        </p>
        <button class="inFavorite" :data-id="dataFavorite(datail.id)" @click.prevent="toggleInFavorite($event)"><img src="@/assets/images/icon-favorite-gl.svg" alt="お気に入り" /></button>
      </div>
    </div>
  </li>
</ul>

</template>

<script lang="ts" setup>
const params = defineProps({
  ranking: Boolean,
  max: Number,
  data: Array,
});

const itemsRanking = params.ranking;
const itemsMax = params.max;
const itemsData = params.data;

const emit = defineEmits(['toggleInFavorite' , 'showStarsScore']);

const toggleInFavorite = (e) => {
  emit('emitToggleInFavorite' , e.target.closest(".inFavorite").dataset.id);
};

const showStarsScore = (stars) => {
  let adjustWhite = 0;
  if (Number.isInteger(stars)) {
    adjustWhite = stars - 1;
  }
  else {
    adjustWhite = Math.floor(stars);
  }
  return {
    '--StarsWidth' : ((stars * 15) + (adjustWhite * 5) + 2.5) + '%'
  }
};

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedLink = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedPrice = computed(() => (data) => {
  return "¥" + data;
});

const dataFavorite = computed(() => (data) => {
  return data;
});

const publishedReview = computed(() => (data) => {
  return "(" + data + ")";
});

const publishedUnit = computed(() => (data) => {
  return "／" + data;
});

const publishedTag = computed(() => (data) => {
  return "#" + data;
});
</script>


<style lang="scss" scoped>
:root {
  --StarsWidth: 0;
}
.listitems {
  display: flex;
  flex-wrap: wrap;
  width: 1025px;
  margin: 0 auto;
  .items {
    width: 157px;
    &.items1, 
    &.items2, 
    &.items3, 
    &.items4 {
      width: 245px;
      .datail {
        margin-top: 5px;
        .title {
          font-size: 14px;
        }
        .wrapprice {
          padding-right: 24px;
          .price {
            font-size: 18px;
          }
          .inFavorite {
            top: -3px;
            img {
              width: 23px;
            }
          }
        }
      }
    }
    & ~ .items {
      margin-left: 15px;
    }
    &:nth-of-type(5) {
      margin-left: 0;
    }
    &:nth-of-type(n+5) {
      margin-top: 30px;
    }
    .thumbnail {
      position: relative;
      width: 100%;
      img {
        width: 100%;
      }
      .ranking {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 4px;
        left: 5px;
        width: 22px;
        height: 22px;
        padding: 0 0 1px 1px;
        border-radius: 50%;
        background: #FFFFFF;
        font-weight: 700;
        font-size: 12px;
        line-height: 110%;
        letter-spacing: 0.06em;
        color: $color-blacktext2;
        opacity: 0.8;
        &.ranking1 {
          background: $color-accent;
          color: $color-whitetext;
        }
        &.ranking2 {
          background: #858585;
          color: $color-whitetext;
        }
        &.ranking3 {
          background: #D05237;
          color: $color-whitetext;
        }
      }
    }
    .datail {
      margin-top: 3px;
      .title {
        font-size: 12px;
        line-height: 120%;
        letter-spacing: 0.02em;
        color: $color-blacktext2;
      }
      .wrapprice {
        position: relative;
        margin-top: 2px;
        padding-right: 17px;
        color: $color-blacktext4;
        .price {
          font-size: 14px;
          line-height: 120%;
          letter-spacing: 0.04em;
          .tax {
            font-size: 11px;
          }
        }
        .inFavorite {
          position: absolute;
          right: 2px;
          top: 0;
          padding: 0;
          img {
            width: 16px;
          }
        }
      }
      .wrapstars {
        .stars {
          display: inline-block;
          position: relative;
          background-color: $color-grayborder;
          overflow: hidden;
          line-height: 1;
          .starsScore {
            display: inline-block;
            position: absolute;
            top: 0;
            left: 0;
            width: var(--StarsWidth);
            height: 100%;
            background-color: #FFB341;
            transition: width 0.35s ease;
          }
          img {
            position: relative;
            z-index: 3;
          }
        }
        .review {
          margin-left: 5px;
          vertical-align: top;
          font-size: 12px;
          line-height: 120%;
          letter-spacing: 0.02em;
        }
      }
      .tags {
        ul {
          display: flex;
          flex-wrap: wrap;
          li {
            margin-right: 7px;
            padding: 3px 12px;
            background: $color-mainbackground;
            border: 1px solid $color-grayborder;
            border-radius: 100px;
            font-size: 12px;
            line-height: 120%;
            letter-spacing: 0.02em;
            color: $color-blacktext2;
          }
        }
      }
    }
  }
}

@include sp {
.listitems {
  justify-content: space-between;
  width: 100%;
  max-width: 343px;
  .items {
    width: 104px;
    &.items1, 
    &.items2 {
      width: 163px;
      .datail {
        margin-top: 5px;
        .title {
          font-size: 12px;
        }
        .wrapprice {
          .price {
            font-size: 14px;
          }
          .inFavorite {
            top: -3px;
            img {
              width: 16px;
            }
          }
        }
      }
    } 
    &.items3, 
    &.items4 {
      width: 104px;
      .datail {
        margin-top: 5px;
        .title {
          font-size: 10px;
        }
        .wrapprice {
          .price {
            font-size: 12px;
          }
          .inFavorite {
            top: -3px;
            img {
              width: 16px;
            }
          }
        }
      }
    }
    & ~ .items {
      margin-left: 0;
    }
    &:nth-of-type(n+6) {
      display: none;
    }
    &:nth-of-type(n+3) {
      margin-top: 20px;
    }
    .datail {
      margin-top: 5px;
      .title {
        font-size: 10px;
      }
      .wrapprice {
        margin-top: 3px;
        .price {
          font-size: 12px;
        }
        .inFavorite {
          top: -3px;
        }
      }
      .wrapstars {
        margin-top: 4px;
      }
      .tags {
        margin-top: 1px;
        ul li {
          font-size: 10px;
        }
      }
    }
  }
}
}
</style>