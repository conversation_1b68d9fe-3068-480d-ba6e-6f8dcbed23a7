<template>
<ul class="listitems">
  <li class="items" v-for="(datail, index) in itemsData" :key="datail">

    <div class="thumbnail" v-if="datail.thumbnail">
      <img :src="publishedThumbnail(datail.thumbnail)" :alt="publishedTitle(datail.title)" @click="exclusiveSelect($event)">
      <button class="showDatailGift" :data-target="publishedTarget(datail.target)" @click.prevent="showDatailGift($event)"><img src="@/assets/images/icon-search-b.svg" alt="詳しく見る" /></button>
    </div>
    <div class="datail">
      <p class="title" v-if="datail.title">{{ publishedTitle(datail.title) }}</p>
    </div>
  </li>
</ul>

</template>

<script lang="ts" setup>
import { exclusiveSelect } from '@/assets/js/customFunction.js'

const params = defineProps({
  data: Array,
});

const itemsData = params.data;

const emit = defineEmits(['emitListItemsSmallDetail']);

const showDatailGift = (e) => {
  emit('emitListItemsSmallDetail' , e.target.closest(".showDatailGift").dataset.target);
};

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedTarget = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>
.listitems {
  display: flex;
  flex-wrap: wrap;
  width: 671px;
  margin: 0 auto;
  .items {
    width: 140px;
    cursor: pointer;
    & ~ .items {
      margin-left: 14px;
    }
    &:nth-of-type(4n+1) {
      margin-left: 0;
    }
    &:nth-of-type(n+5) {
      margin-top: 22px;
    }
    .thumbnail {
      position: relative;
      width: 100%;
      border: 1px solid $color-grayborder;
      border-radius: 2px;
      img {
        width: 100%;
      }
      .showDatailGift {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 3px;
        bottom: 4px;
        padding: 8px 7px 7px 8px;
        border: 1px solid $color-graytext2;
        border-radius: 50%;
        background-color: $color-mainbackground;
        z-index: 10;
        img {
          width: 14px;
        }
      }
    }
    .datail {
      margin-top: 9px;
    }
    .title {
      font-size: 10px;
      line-height: 120%;
      letter-spacing: 0.02em;
      color: $color-blacktext2;
    }
    &.is-selected {
      .thumbnail {
        border-color: $color-main;
        &::after {
          @include BA;
          left: 0;
          width: 100%;
          height: 100%;
          border: 3px solid $color-main;
        }
      }
    }
  }
}

.buttonTop {
  .listitems .items .thumbnail .showDatailGift {
    top: 5px;
    bottom: auto;
  }
}

@include sp {
.listitems {
  justify-content: space-between;
  width: 100%;
  max-width: 350px;
  .items {
    width: 163px;
    & ~ .items {
      margin-left: 0;
    }
    &:nth-of-type(n+3) {
      margin-top: 20px;
    }
    .thumbnail .showDatailGift {
      right: 6px;
      bottom: 5px;
      padding: 12px 12px 12px 12px;
      img {
        width: 20px;
      }
    }
    .datail {
      margin-top: 9px;
    }
    .title {
      font-size: 12px;
    }
  }
  &.threeColumn {
    justify-content: flex-start;
    max-width: 340px;
    .items {
      width: 106px;
      & ~ .items {
        margin-left: 11px;
      }
      &:nth-of-type(n+3) {
        margin-top: 0;
      }
      &:nth-of-type(n+4) {
        margin-top: 20px;
      }
      .thumbnail .showDatailGift {
        right: 5px;
      }
      .datail {
        margin-top: 4px;
      }
      .title {
        font-size: 10px;
      }
    }
  }
}
}
</style>