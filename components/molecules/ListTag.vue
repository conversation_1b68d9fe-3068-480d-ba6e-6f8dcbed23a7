<template>
<div class="tag">
  <div class="title">{{params.title}}</div>
  <div class="member">
    <span>{{params.quantity}}</span>名
  </div>
  <ul class="menu">
    <li>
      <a href="#" v-if="params.required !== true"><img src="@/assets/images/icon-edit.svg"></a>
    </li>
    <li>
      <a href="#" v-if="params.required !== true"><img src="@/assets/images/icon-trash.svg"></a>
    </li>
  </ul>
</div>
</template>

<script lang="ts" setup>
const params = defineProps({
  title: String,
  quantity: Number,
  required: Boolean
});
</script>

<style lang="scss" scoped>
.tag {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 14px 0;
  border-bottom: 1px solid #D9D9D9;
  font-size: 14px;
  .title {
    width: 100%;
    max-width: calc(100% - 134px);
  }
  .member {
    span{
      font-size: 16px;
      font-weight: bold;
    }
  }
  .menu {
    display: flex;
    list-style: none;
    li {
      width: 24px;
      margin-left: 14px;
      a {
        display: block;
        position: relative;
      }
    }
  }
}
</style>