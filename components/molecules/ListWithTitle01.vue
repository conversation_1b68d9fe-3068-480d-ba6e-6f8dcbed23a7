<template>

<h6 class="subtitle sp-acdtrg" :class="{ 'is-close' : ! isShowMenu }" @click="isShowMenu = ! isShowMenu">{{ footerTitle }}</h6>

<ul class="submenu sp-acdsbj" :class="{ 'is-close' : ! isShowMenu }">
  <li v-for="(datail, index) in footerList" :key="datail">
    <a v-if="datail.link.match(/^http/)" :href="datail.link" target="_blank">{{ datail.menu }}</a>
    <NuxtLinkToSSR v-else :to="datail.link">{{ datail.menu }}</NuxtLinkToSSR>
  </li>
</ul>

</template>

<script lang="ts" setup>
const isShowMenu = ref(false)

const params = defineProps({
  title: String,
  list: Array,
});

const footerTitle = params.title;
const footerList = params.list;
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
  line-height: 124%;
  letter-spacing: 0.02em;
  font-size: 12px;
  color: #9C9C9C;
  position: relative;
  &[href^="http"] {
    &::after {
      content: '';
      display: inline-block;
      width: 1.1em;
      height: 1.1em;
      margin-left: 0.2em;
      background: currentColor;
      mask-image: url(@/assets/images/icon-external.svg);
      mask-size: contain;
      mask-repeat: no-repeat;
      vertical-align: -0.15em;
    }
  }
}
.subtitle {
  margin: 0 0 8px;
  line-height: 1.2;
  font-size: 12px;
  font-weight: bold;
  color: $color-graytext2;
  &::after {
    top: 40%;
    right: 17px;
    background-image: url(@/assets/images/expand_more-gl.svg);
  }
}
ul {
  & > li {
    width: 170px;
    margin: 0;
    padding-right: 15px;
    ul {
      li {
        margin-top: 3px;
      }
    }
  }
  & ~ .subtitle {
    margin-top: 40px;
  }
}

@include pc {
  
}

@include sp {
.subtitle {
  padding: 16px 40px 16px 11px;
  letter-spacing: 0.02em;
}

ul {
  display: flex;
  flex-wrap: wrap;
  padding: 0 10px 15px;
  & > li {
    min-width: auto;
    margin: 0 0 12px;
    padding-right: 17px;
    ul {
      li {
        margin-top: 5px;
      }
    }
  }
  & ~ .subtitle {
    margin-top: 40px;
  }
  & + .subtitle {
    margin-top: 0px;
    padding-top: 15px;
    border-top: 1px solid $color-lightgray;
  }
}
}
</style>