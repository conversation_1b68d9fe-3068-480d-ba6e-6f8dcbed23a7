<template>

<h5 class="title">{{ footerTitle }}</h5>

<ul>
  <li v-for="(datail, index) in footerList" :key="datail" :class="{'space': !datail.menu}">
    <NuxtLinkToSSR :to=datail.link>{{ datail.menu }}</NuxtLinkToSSR>
  </li>
</ul>

</template>

<script lang="ts" setup>
const params = defineProps({
  title: String,
  list: Array,
});

const footerTitle = params.title;
const footerList = params.list;
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
  line-height: 120%;
  letter-spacing: 0.02em;
  font-size: 12px;
  color: #9C9C9C;
}
.space {
  display: block;
  min-height: 16px;
}
.title {
  margin: 0 0 8px;
  font-size: 14px;
}
ul {
  & > li + li {
    margin-top: 4px;
  }
}

@include sp {
.space {
  width: 100%;
  margin: 0;
}
.title {
  margin: 0 0 16px;
}
ul {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  & > li {
    min-width: 1em;
    padding-right: 0;
    border-top: none;
    ul {
      li {
        margin: 0 16px 16px 0;
      }
    }
  }
}
}
</style>