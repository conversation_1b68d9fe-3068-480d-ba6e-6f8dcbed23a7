<template>
<div class="searchArea">
  <h3>絞り込み検索</h3>

  <p>絞り込み条件</p>
  <div class="tags">
    <ManagedTag>席次表</ManagedTag>
    <ManagedTag>グリーン</ManagedTag>
    <ManagedTag>ナチュラル</ManagedTag>
    <ManagedTag>300円〜</ManagedTag>
  </div>

  <form action="/" class="searchform">
    <InputSearch class="searchMiddle" :formSettingParams="formSettingData" v-model="InputTextValue"/>
  </form>
  <div class="tagSearch">
    <ul>
      <ArticleTag :propsArticleTag="tagSearchformSettingData" />
    </ul>
  </div>
  <ul class="category">
    <li>
      <button class="acdtrg is-close" @click="useMethodsAccordion($event)">カテゴリ</button>
      <div class="acdsbj wide is-close">
        <InputSelect
          title="大カテゴリ"
          size="full"
        />
        <InputSelect
          title="中カテゴリ"
          size="full"
        />
      </div>
    </li>
    <li>
      <button class="acdtrg is-close" @click="useMethodsAccordion($event)">カラー</button>
      <div class="acdsbj wide is-close">
        カラー<br>
        カラー<br>
        カラー<br>
        カラー<br>
        カラー<br>
        カラー<br>
        カラー<br>
      </div>
    </li>
    <li>
      <button class="acdtrg is-close" @click="useMethodsAccordion($event)">テイスト</button>
      <div class="acdsbj wide is-close">
        テイスト<br>
        テイスト<br>
        テイスト<br>
        テイスト<br>
        テイスト<br>
        テイスト<br>
        テイスト<br>
      </div>
    </li>
    <li>
      <button class="acdtrg is-close" @click="useMethodsAccordion($event)">価格</button>
      <div class="acdsbj wide is-close">
        価格<br>
        価格<br>
        価格<br>
        価格<br>
        価格<br>
        価格<br>
        価格<br>
      </div>
    </li>
  </ul>

  <div class="postage">

    <p>送料</p>
    <InputCheck
      :items="[
        {
          value: 1,
          checked: false,
          label: '送料無料',
        }
      ]"
    />
    
  </div>
  <ButtonMainColor :buttonsize="241" to="#">絞り込む</ButtonMainColor>
  <ButtonMainColor baseColor="clear" :buttonsize="241" to="#">クリア</ButtonMainColor>
  
  
</div>
<div class="showArea">
  <div class="giftinfo">
    <p class="result"><strong>検索結果</strong><span>：</span><b>{{ publishedLength(props.data.param[0].datail) }}</b>件</p>
    <button class="sort">人気順</button>
  </div>
  <ListItems :data="props.data" @emitInFavorite="eventInFavorite" />
</div>
</template>

<script lang="ts" setup>
import { useMethodsAccordion } from '@/assets/js/customFunction.js'
interface Props {
  data: Array;
}

const props = withDefaults(defineProps<Props>(), {
  data: '',
});

const publishedLength = computed(() => (data) => {
  return data.length;
});

const formSettingData = {
  name: 'gift-search',
  id: 'gift-search',
}

const tagSearchformSettingData = [
  '全て',
  '新婦側ゲスト',
  '新郎側ゲスト',
  '大学友人',
  '親族',
]
</script>

<style lang="scss" scoped>
.searchArea {
  width: 228px;
  padding: 0 15px 0 20px;
  h3 {
    margin: 0 0 20px;
    color: $color-accent;
    font-size: 18px;
    font-weight: 400;
    line-height: 120%;
    letter-spacing: 0.72px;
    & + p {
      margin: 0 0 10px;
      font-size: 12px;
    }
  }
  .tags {
    margin-bottom: 20px;
  }

  .tagSearch {
    width: 100%;
    overflow: auto;
    ul {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin: 10px 0;
    }
  }
}

.category {
  .acdtrg {
    width: 100%;
    padding-left: 0;
    text-align: left;
    &::after {
      right: 7px;
      width: 9px;
      height: 6px;
    }
  }
  li {
    padding-bottom: 5px;
    label ~ label {
      margin-top: 10px;
    }
  }
}

.postage {
  display: flex;
  margin: 22px 0 18px;
  p {
    margin-right: 47px;
    font-weight: 700;
    font-size: 14px;
    line-height: 120%;
    color: $color-blacktext2;
  }
}

:deep(a.button--clear) {
  margin-top: 11px;
}

.giftinfo {
  display: flex;
  justify-content: space-between;
  margin-bottom: 27px;
  .result {
    color: $color-blacktext2;
    font-size: 10px;
    line-height: 120%;
    letter-spacing: 0.2px;
    strong {
      color: $color-accent;
      font-size: 16px;
      font-weight: 400;
      line-height: 120%;
      letter-spacing: 0.32px;
    }
    span {
      font-size: 12px;
      line-height: 120%;
      letter-spacing: 0.24px;
    }
    b {
      color: $color-accent;
      font-size: 16px;
      font-weight: 700;
      line-height: 120%;
      letter-spacing: 0.32px;
    }
  }
  .sort {
    position: relative;
    padding: 0 0 0 20px;
    color: $color-blacktext2;
    font-size: 14px;
    &:before {
      @include BA;
      top: 47%;
      left: 0;
      width: 13px;
      height: 16px;
      background-image: url(@/assets/images/icon-swap_vert.svg);
    }
  }
}

.showArea {
  width: 750px;
  padding: 0 3px 0 12px;
  :deep(.listitems) {
    gap: 33px 8px;
    margin-top: 25px;
    padding: 0 1px 0 3px;
    .items {
      width: 228px;
      .datail {
        margin-top: 9px;
        .title {
          margin-bottom: 0;
          line-height: 130%;
        }
        .labels .label {
          width: auto;
        }
      }
    }
  }
}

@include sp {
.searchArea {
  display: none;
}
}
</style>