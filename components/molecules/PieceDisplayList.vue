<template>
<div>
  <dt>{{ publishedTitle(guestInfo.menu) }}</dt>
  <dd v-if="guestInfo.content" v-html="publishedContents(guestInfo.content)"></dd>
  <dd v-else>　</dd>
</div>
</template>

<script lang="ts" setup>
const props = defineProps({
  data: {},
});

const guestInfo = props.data;

const publishedContents = computed(() => (data, e) => {
  if (guestInfo.menu === 'タグ') {
    let html = '';
    guestInfo.content.forEach (function(tag) {
      html = (html + '<li class="tag">' + tag + '</li>');
    });
    return '<ul class="tags">' + html + '</ul>';
  }
  else if (guestInfo.menu === '出欠') {
    return data;
  }
  else {
    if (! data) return '';
    let str = data.replace(/&/g, '&amp;');
    str = str.replace(/>/g, '&gt;');
    str = str.replace(/</g, '&lt;');
    str = str.replace(/"/g, '&quot;');
    str = str.replace(/'/g, '&#x27;');
    str = str.replace(/`/g, '&#x60;');
    return str;
  }
});

const publishedTitle = computed(() => (data:any) => {
  return data;
});

const publishedTag = computed(() => (data:any) => {
  return data;
});
</script>

<style lang="scss" scoped>
div {
  padding-top: 10px;
  padding-bottom: 11px;
  border-bottom: 1px solid $color-grayborder;
  dt {
    color: $color-graytext2;
    font-size: 10px;
    line-height: 16px;
    letter-spacing: 1.5px;
  }
  dd {
    color: var(--black, #333);
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0.15px;
    margin-top: 2px;
    :deep(span) {
      display: inline-block;
      position: relative;
      padding-left: 24px;
      &::before {
        @include BA;
        left: 0;
        width: 18px;
        height: 20px;
      }
      &.attendance::before {
        background-image: url(@/assets/images/icon-check.svg);
      }
      &.pending::before {
        background-image: url(@/assets/images/icon-question_mark.svg);
      }
      &.absence::before {
        background-image: url(@/assets/images/icon-absence.svg);
      }
    }
  }
  :deep(.tags) {
    display: flex;
    flex-wrap: wrap;
    margin: 5px 0 -4px;
    .tag {
      margin-right: 4px;
      padding: 3px 11px;
      background: $color-mainbackground;
      border: 1px solid $color-main;
      border-radius: 16px;
      font-size: 11px;
      font-family: Noto Sans JP;
      font-style: normal;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: 0.25px;
      color: $color-main;
    }
  }
}
@include sp {
}
</style>