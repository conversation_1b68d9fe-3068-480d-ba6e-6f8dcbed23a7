<template>
  <tr v-for="(guestData, index) in params.data.datail" :key="guestData" :class="guestData.principal === true?'principal':''">
    <td class="empty"></td>
    <td class="check">
      <label v-if="guestData.principal === true"><input type="checkbox" id="checkbox" v-model="checked" />
      <span class="checkbox"></span></label>
    </td>
    <td class="name">
      <p>{{ publishedTitle(guestData.name) }}<small>{{ publishedHonorific(guestData.honorific) }}</small></p>
    </td>
    <td class="gift01">
      <img v-if="guestData.principal === true" :src="publishedThumbnail(guestData.gifts[0])" alt="" />
    </td>
    <td class="gift02">
      <img v-if="guestData.principal === true" :src="publishedThumbnail(guestData.gifts[1])" alt="" />
    </td>
    <td class="gift03">
      <img v-if="guestData.principal === true" :src="publishedThumbnail(guestData.gifts[2])" alt="" />
    </td>
    <td class="empty"></td>
  </tr>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
}

const params = withDefaults(defineProps<Props>(), {
  data: {},
});

let image1 = {}
let image2 = {}
try {
  image1 = await import(`@/assets/images/icon-gift-g.svg`)
  image2 = await import(`@/assets/images/icon-gift-gl.svg`)
} catch (e) {
}

const publishedThumbnail = computed(() => (data) => {
  if (data !== null) {
    return image1.default
  }
  else {
    return image2.default
  }
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedHonorific = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>
.name {
  padding-left: 5px;
  font-size: 16px;
  letter-spacing: 0.4px;
  p {
    color: $color-blacktext2;
    small {
      margin-left: 4px;
      font-size: 14px;
    }
  }
}
@include sp {
.name {
  p {
    small {
      font-size: 10px;
    }
  }
}
}
</style>