<template>
  <tr v-for="(guest) in props.guests" :key="guest.id" :class="{'is-new': guest.member_confirm_type == 'New', 'is-unread': guest.member_confirm_type == 'Unread', 'is-normal': guest.member_confirm_type == 'Normal', 'is-child': ! (! guest.parent_guest_id)}">
    <td class="td-check" v-if="partsHidden.check !== 'hidden'">
      <label v-if="props.checkLimit !== null && checkedGuestIds.length >= props.checkLimit && checkedGuestIds.indexOf(guest.id) === -1"><input type="checkbox" :checked="checkedGuestIds.indexOf(guest.id) !== -1" disabled="disabled" /><span class="checkbox"></span></label>
      <label v-else><input type="checkbox" :checked="checkedGuestIds.indexOf(guest.id) !== -1" @change="emits('onChangeGuestCheck', guest.id);" /><span class="checkbox"></span></label>
    </td>
    <td class="td-profile" @click="onClickGuest(guest)">
      <span class="icn-message" v-if="guest.media_type == 'MEDIA_TYPE_MOVIE'"><img src="@/assets/images/icon-guest-movie.svg" /></span>
      <span class="icn-message" v-else-if="guest.message"><img src="@/assets/images/icon-guest-message.svg" /></span>
      <div class="profile-img profileImage size--sm">
        <div class="img">
          <ImageUUID v-if="guest?.image_url" :uuid="guest?.image_url" />
          <img v-else src="@/assets/images/profile-image.svg" />
        </div>
      </div>
    </td>
    <td class="td-name" @click="onClickGuest(guest)">
      <p>{{ guest.last_name }} {{ guest.first_name }}<small>{{ guest.guest_honor }}</small></p>
    </td>
    <template v-for="eventName, i in events" :key="i">
      <template v-if="! guest?.guest_event_attendances.filter(event => eventName.name == event.name).length">
        <td class="td-attendance" @click="onClickGuest(guest)"></td>
      </template>
      <template v-else-if="props.isModal">
        <template v-for="event, index in [guest?.guest_event_attendances.find(event => eventName.name == event.name)]" :key="index">
          <td class="td-attendance" @click="onClickGuest(guest)">
            <button v-if="event?.attendance == GUEST_ATTENDANCE_MASTER.PRESENT" class="btn btn-attendance btn-attendance-present">出席</button>
            <button v-else-if="event?.attendance == GUEST_ATTENDANCE_MASTER.ABSENT" class="btn btn-attendance btn-attendance-absent">欠席</button>
            <button v-else-if="event?.attendance == GUEST_ATTENDANCE_MASTER.PENDING" class="btn btn-attendance btn-attendance-pending">保留</button>
          </td>
        </template>
      </template>
      <template v-else>
        <template v-for="event, index in guest?.guest_event_attendances.filter(event => eventName.name == event.name)" :key="index">
          <td class="td-attendance">
            <ControlWindowBtn data-mode="sort" position="fixed">
              <template #button>
                <button v-if="event?.attendance == GUEST_ATTENDANCE_MASTER.PRESENT" class="btn btn-attendance btn-attendance-present">出席</button>
                <button v-else-if="event?.attendance == GUEST_ATTENDANCE_MASTER.ABSENT" class="btn btn-attendance btn-attendance-absent">欠席</button>
                <button v-else-if="event?.attendance == GUEST_ATTENDANCE_MASTER.PENDING" class="btn btn-attendance btn-attendance-pending">保留</button>
                <button v-else class="btn btn-attendance btn-attendance-null"></button>
                <i class="icn-left material-symbols-outlined mr-5">arrow_drop_down</i>
              </template>
              <ul>
                <li :class="{'is--current': event?.attendance == GUEST_ATTENDANCE_MASTER.PRESENT}" @click="onChangeGuestAttendance(guest, event, GUEST_ATTENDANCE_MASTER.PRESENT)"><i class="icn-img icn-attendance-present mr-5"></i>出席</li>
                <li :class="{'is--current': event?.attendance == GUEST_ATTENDANCE_MASTER.ABSENT}" @click="onChangeGuestAttendance(guest, event, GUEST_ATTENDANCE_MASTER.ABSENT)"><i class="icn-img icn-attendance-absent mr-5"></i>欠席</li>
                <li :class="{'is--current': (event?.attendance == GUEST_ATTENDANCE_MASTER.PENDING)}" @click="onChangeGuestAttendance(guest, event, GUEST_ATTENDANCE_MASTER.PENDING)"><i class="icn-img icn-attendance-pending mr-5"></i>保留</li>
                <li :class="{'is--current': (event?.attendance != GUEST_ATTENDANCE_MASTER.PRESENT && event?.attendance != GUEST_ATTENDANCE_MASTER.ABSENT && event?.attendance != GUEST_ATTENDANCE_MASTER.PENDING)}" @click="onChangeGuestAttendance(guest, event, null)"><i class="icn-img icn-attendance-null mr-5"></i>未回答</li>
              </ul>
            </ControlWindowBtn>
          </td>
        </template>
      </template>
    </template>
    <td @click="onClickGuest(guest)">
      {{ guest?.guest_group_name }}
      {{ guest?.guest_group?.name }}
    </td>
    <td @click="onClickGuest(guest)">
      <span v-for="(tag) in guest.guest_event_tags.sort((a, b) => String(a?.tag).localeCompare(b?.tag))" :key="tag.id" class="label-tag">{{ tag.tag }}</span>
      <span v-for="(tag) in guest.guest_tags" :key="tag.id" class="label-tag">{{ tag.tag }}</span>
    </td>
    <td @click="onClickGuest(guest)">
      <span v-if="guest?.allergies">
        {{ guest?.allergies.join('、') }}
        <span v-if="guest?.allergies.length && guest?.allergy">、</span>
      </span>
      {{ guest?.allergy }}
    </td>
    <td @click="onClickGuest(guest)">
      {{ PAYMENT_METHOD_MASTER?.[guest.payment_method] }}
    </td>
    <td class="registration" @click="onClickGuest(guest)">
      {{ $dayjs(guest.created_at).format('YYYY/MM/DD HH:mm') }}
    </td>
  </tr>
</template>

<script lang="ts" setup>
import type { GuestListFragmentFragment } from '@/composables/generated';

const router = useRouter();
const { $dayjs } : any = useNuxtApp();

export interface Props {
  guestListId: string;
  guests: GuestListFragmentFragment['guests'],
  // モーダル内からの利用(連名ゲストを追加する)
  isModal: boolean;
  partsHidden: {
    check?: string;
    name?: string;
    icon?: string;
    status?: string;
    group?: string;
    tags?: string;
    registration?: string;
  },
  checkedGuestIds: string[],
  events: any[],
  checkLimit?: number|null;
}

const props = withDefaults(defineProps<Props>(), {
  guestListId: '',
  isModal: false,
  guests: () => { return []},
  partsHidden: () => { return {}},
  checkedGuestIds: () => { return []},
  events: () => { return []},
  checkLimit: null
});

const emits = defineEmits<{
  (e: 'reload'): void;
  (e: 'onChangeGuestCheck', value?: any): void;
}>();

const { guestList, refetch } = useGetOneGuestList(String(props.guestListId));

const { update, errors } = useBulkUpdateGuest();
const onChangeGuestAttendance = async (guest:any, event:any, attendance:any) => {
  // console.log(guest.guest_event_attendances);
  const guest_event_attendance = guest?.guest_event_attendances.find((item:any) => item.name == event.name);
  if (! guest_event_attendance) return false;
  const isSuccess = await update([{
    id: guest.id,
    guest_event_answers: [{
      id: guest_event_attendance.id,
      name: guest_event_attendance.name,
      attendance: attendance
    }]
  }]);
  if (isSuccess) {
    emits('reload');
    return true;
  }
  return false;


  // let guest_event_answers = [] as any[];
  // const guestListGuest = guestList.value?.guests.find(item => item.id == guest.id);
  // console.log(guestListGuest);
  // if (guestListGuest?.guest_event_answers) {
  //   guest_event_answers = guestListGuest?.guest_event_answers.filter((item:any) => item.name == event.name);
  // }
  // if (! guest_event_answers.length) return false;
  // let input = guest_event_answers.map(item => ({
  //   id: item.id,
  //   attendance: attendance
  // }));
  // // console.log(input);
  // let item = {
  //   id: guest.id,
  //   guest_event_answers: input
  // } as any;
  // const isSuccess = await update([item]);
  // if (isSuccess) {
  //   emits('reload');
  //   return true;
  // }
  // return false;
};

const onClickGuest = (guest:any) => {
  if (props.isModal) {
    emits('onChangeGuestCheck', guest.id);
    return false;
  }
  router.push({ path: `/mypage/guest-list/${props.guestListId}/${guest.id}` })
};

</script>

<style lang="scss" scoped>
td, th {
  padding: 12px;
  font-size: 14px;
}
.td-check {
  padding-left: 22px;
}
.td-name {
  width: 10.5em;
  padding-left: 0;
  p {
    width: 10.5em;
    white-space: normal;
    @include lineClamp(2);
    margin: 0;
    font-size: 14px;
    line-height: 120%;
    color: $color-blacktext2;
    small {
      margin-left: 5px;
      font-size: 10px;
    }
  }
}
.td-profile {
  position: relative;
  // width: 200px;
  width: 32px;
  .icn-message {
    position: absolute;
    top: 4px;
    left: 30px;
    z-index: 1;;
  }
  .profile-img {
    width: 32px;
    display: inline-block;
    vertical-align: middle;
  }
}
.td-attendance {
  .icn-left {
    vertical-align: middle;
  }
}
tr.is-new {
  .td-check {
    position: relative;
    &:before {
      content: " ";
      display: block;
      width: 8px;
      height: 8px;
      background: #AD871E;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 5px;
      margin-top: -4px;
    }
  }
}
tr.is-unread ,
tr.is-new {
  .td-name p {
    font-weight: 700;
  }
}
tr.is-normal {
  background: #FAFAFA;
}
tr.is-child td {
  border-top: none !important;
}
</style>