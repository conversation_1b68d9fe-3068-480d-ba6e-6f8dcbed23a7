<template>
<strong class="category" v-if="(params.data.category !== '') && ((params.data.type === 'listguestgift') || (params.data.type === 'listsubgift'))">{{ publishedCategory(params.data.category) }}</strong>
<div class="thumbnail" v-if="params.data.thumbnail !== ''"><img :src="publishedThumbnail(params.data.thumbnail)" :alt="publishedTitle(params.data.title)"><span class="close" v-if="(params.data.close)"></span></div>

<div class="datail">
  <strong class="category" v-if="(params.data.category !== '') && ((params.data.type !== 'listguestgift') && (params.data.type !== 'listsubgift'))">{{ publishedCategory(params.data.category) }}</strong>
  <small class="number" v-if="params.data.number !== ''">{{ publishedNumber(params.data.number, params.data.numberUnit) }}</small>
  <p class="title" v-if="params.data.title !== ''">{{ publishedTitle(params.data.title) }}</p>
  <div class="labels" v-if="params.data.labels">
    <span class="label" v-for="(label, index) in params.data.labels" :key="label">{{ publishedLabel(label) }}</span>
  </div>
  <div class="wrapprice" v-if="params.data.price !== ''">
    <strong class="price">{{ publishedPrice(params.data.price) }}</strong>
    <s class="strike" v-if="params.data.priceStrike !== ''">{{ publishedStrike(params.data.priceStrike) }}</s>
    <span class="tax">(税込)</span>
    <span class="unit" v-if="params.data.priceUnit !== ''">{{ publishedUnit(params.data.priceUnit) }}</span>
  </div>
  <div class="orderd" v-if="params.data.orderd !== ''">
    <b>{{ publishedOrderd(params.data.orderd) }}</b>
    <a href="#">商品詳細を見る</a>
  </div>
</div>


<div class="payment">
  <ShowPaymentAmount :data="apiPaymentAmount01" />
</div>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const apiPaymentAmount01 = {
  display: [
  ],
  subtotal: [
    {
      article: '小計（税込）',
      amount: 110000,
    },
  ],
  calculation: [
  ],
  discount: [
  ],
  total: [
  ],
  point: [
  ],
}

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedCategory = computed(() => (data) => {
  return data;
});

const publishedNumber = computed(() => (data, unit) => {
  if (unit === false) {
    return data;
  }
  else {
    return '商品番号 ' + data;
  }
});

const publishedLabel = computed(() => (data) => {
  return data;
});

const publishedPrice = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const publishedStrike = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const publishedUnit = computed(() => (data) => {
  return data;
});

const publishedOrderd = computed(() => (data) => {
  return '数量：' + data;
});

const publishedOrderdUnit = computed(() => (data) => {
  return data;
});
</script>
  
<style lang="scss" scoped>
.thumbnail {
  flex-shrink: 0;
  position: relative;
  width: 64px;
}
.datail {
  width: calc(100% - 72px);
  margin-left: 8px;
}
.number {
  display: block;
  margin-top: 1px;
  font-size: 10px;
  line-height: 120%;
  letter-spacing: 0.02em;
  color: $color-graytext;
}
.title {
  margin-top: 3px;
  font-size: 12px;
  line-height: 150%;
  color: $color-blacktext2;
}
.labels {
  margin-top: 7px;
  .label {
    display: inline-block;
    padding: 3px 7px 2px;
    background-color: $color-alert;
    font-size: 12px;
    line-height: 120%;
    letter-spacing: 0.02em;
    color: $color-whitetext;
  }
}
.wrapprice {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  margin-top: 0;
  .price {
    font-size: 14px;
    font-weight: 400;
  }
  .strike {
    margin-left: 7px;
    font-size: 15px;
  }
  .tax {
    margin-left: 10px;
    font-size: 10px;
  }
  .unit {
    display: block;
    letter-spacing: 0px;
    margin-left: 6px;
    font-size: 12px;
    &::before {
      content: '/';
    }
  }
}
.orderd {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
  letter-spacing: 0.1em;
  font-size: 12px;
  b {
    font-weight: 400;
  }
  a {
    position: relative;
    padding-right: 19px;
    text-decoration: none;
    color: $color-accent;
    letter-spacing: 0.4px;
    &::after {
      @include BA;
      right: 0;
      width: 13px;
      height: 18px;
      background-image: url(@/assets/images/icon-chevron-right-y.svg);
    }
  }
}
.category {
  display: block;
  width: 100%;
  margin-bottom: 10px;
  color: $color-blacktext2;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0.32px;
}
.payment {
  width: 100%;
  :deep(.wrappayment) {
    width: 100%;
    margin: 15px 0 0;
    padding: 10px 0 0;
    border-top: 1px solid #D9D9D9;
    border-bottom: 0;
    dl > div dd {
      display: inline-flex;
      justify-content: flex-end;
    }
  }
}

@include sp {
.listgift {
  .gifts .title {
    font-size: 16px;
  }
}
}
</style>