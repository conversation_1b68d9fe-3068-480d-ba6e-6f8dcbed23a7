<template>
<div class="productWrap">
  <div class="product">

    <div class="thumbnail" v-if="params.data.thumbnail !== ''"><img :src="publishedThumbnail(params.data.thumbnail)" :alt="publishedTitle(params.data.title)"><span class="close" v-if="(params.data.close)"></span></div>
    <div class="datail">
      <div class="assignment">
        <p :class="params.data.assignment === params.data.purchased?'enough':''"><b>{{ params.data.assignment }}</b><small>セット</small><b> / {{ params.data.purchased }}</b><small>セット</small></p>
      </div>
      <p class="title" v-if="params.data.title !== ''">{{ publishedTitle(params.data.title) }}</p>
    </div>
    <div class="control">
      <ButtonMainColor>割り当て</ButtonMainColor>
    </div>

  </div>
</div>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
}

const params = withDefaults(defineProps<Props>(), {
  data: {},
});

const publishedThumbnail = computed(() => (data) => {
  return data;
});

const publishedTitle = computed(() => (data) => {
  const max = 31;
  let len = 0;
  let return_data = data;
  for (let i = 0; i < data.length; i++) {
    (data[i].match(/[ -~]/)) ? len += 1 : len += 2;
    if (len > (max * 2)) {
      return_data = data.substring(0, max) + '…';
      break;
    }
  }
  return return_data;
});
</script>

<style lang="scss" scoped>
.productWrap {
  position: relative;
  padding-left: 12px;

  &::before {
    @include BA;
    left: 0;
    width: 6px;
    height: 10px;
    background-image: url(@/assets/images/icon-chevron_right.svg);
    transform: translateY(-50%) scale(-1, 1);
  }
  .product {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    border-radius: 4px;
    border: 1px solid $color-grayborder;
    background: $color-mainbackground;
    .thumbnail {
      flex-shrink: 0;
      width: 74px;
    }
    .datail {
      padding: 0 10px 9px;
      text-align: left;
      color: $color-blacktext2;
      font-size: 12px;
      line-height: 120%;
      letter-spacing: 0.24px;
      .assignment {
        position: relative;
        margin-bottom: 7px;
        padding-left: 20px;
        color: $color-graytext2;
        font-size: 12px;
        line-height: 120%;
        letter-spacing: 0.24px;
        &::before {
          @include BA;
          left: 0;
          width: 15px;
          height: 10px;
          background-image: url(@/assets/images/icon-check.svg);
        }
        b {
          color: $color-accent;
          font-size: 16px;
          font-weight: 400;
          line-height: 120%;
          letter-spacing: 0.32px;
        }
      }
      .title {
        // word-break: break-all;
      }
    }
    .control {
      flex-shrink: 0;
      width: 80px;
      padding: 0 9px 9px 3px;
    }
  }
}
@include sp {
}
</style>