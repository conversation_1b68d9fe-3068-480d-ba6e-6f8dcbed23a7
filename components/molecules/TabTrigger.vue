<template>
<ul class="tabtrg">
  <li v-for="(tab, index) in props.data" :key="tab" :class="{'is-active': index === tabIndex}" @click="onClickTab(index)"><span v-html="publishedLabel(tab)"></span></li>
</ul>
</template>

<script lang="ts" setup>

export interface Props {
  data: string[],
  defaultTabIndex?: number
};

const props = withDefaults(defineProps<Props>(), {
  defaultTabIndex: 0
});

const tabIndex = ref(props.defaultTabIndex)

const onClickTab = (index:number) => {
  tabIndex.value = index;
  emits('onClickTab', index)
};

const emits = defineEmits<{
  (e: 'onClickTab', value: number): void;
}>();

const publishedLabel = computed(() => (data:any) => {
  return data;
});
</script>

<style lang="scss" scoped>
.tabtrg {
  display: flex;
  align-items: flex-end;
  & > li {
    cursor: pointer;
    span {
      display: inline-block;
      padding: 16px 28px 14px;
      border-bottom: 2px solid transparent;
      text-align: center;
      // font-weight: 700;
      font-size: 14px;
      line-height: 120%;
    }
    :deep(.addGuestList) {
      position: relative;
      padding-left: 22px;
      font-size: 14px;
      line-height: 100%;
      color: inherit;
      &::before {
        @include BA;
        left: 3px;
        width: 12px;
        height: 12px;
        background-image: url(@/assets/images/icon-plus-gl.svg);
      }
    }
    &:hover ,
    &.is-active {
      span {
        border-bottom-color: $color-main;
        color: $color-main;
      }
      :deep(.addGuestList::before) {
        background-image: url(@/assets/images/icon-plus.svg);
      }
    }
  }
}

@include sp {
.tabtrg {
  li {
    span {
      padding: 16px 15px 14px;
    }
  }
}
}


</style>