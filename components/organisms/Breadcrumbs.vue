<template>
<div class="breadcrumbs" v-if="breadcrumbs.length">
  <ul>
    <li v-for="(item, index) in breadcrumbs" :key="index">
      <NuxtLink v-if="item.link" :to="item.link">{{ item.title }}</NuxtLink>
      <span v-else>{{ item.title }}</span>
    </li>
  </ul>
</div>
</template>
<script lang="ts">
// 他コンポーネントで流用 breadcrumbs の型
export type Breadcrumbs = { title: string; link?: string; }[];

// 他コンポーネントで流用 breadcrumbs の初期値
export const BreadcrumbsDefault = () => {
  return [] as Breadcrumbs;
};
</script>

<script lang="ts" setup>
export interface Props {
  breadcrumbs?: Breadcrumbs;
}
const props = withDefaults(defineProps<Props>(), {
  breadcrumbs: BreadcrumbsDefault
});
</script>

<style lang="scss" scoped>
.breadcrumbs {
  position: static;
  width: 100%;
  padding: 13px 0 12px;
  background-color: $color-lightgray;
  z-index: 10;
  padding-left: 28px;
  ul {
    display: flex;
    li {
      margin: 0;
      line-height: 120%;
      letter-spacing: 0.02em;
      font-size: 12px;
      color: $color-blacktext5;

      & ~ li {
        position: relative;
        margin-left: 6px;
        padding-left: 11px;
        &::before {
          @include BA;
          background-image: url(@/assets/images/icon-chevron-right-breadcrumbs.svg);
          left: 0;
          width: 7px;
          height: 11px;
          color: $color-blacktext2;
        }
      }
      &:last-child {
        white-space: nowrap; // ここで改行を防止
        overflow: hidden; // ここでテキストがコンテナを超える場合に隠す
        text-overflow: ellipsis; // 省略記号を付ける
      }
      a {
        text-decoration: none;
        color: $color-accent;
        white-space: nowrap;
      }
    }
  }
}

.is-widelayout {
  .breadcrumbs {
    .section-inner {
      max-width: 1250px;
    }
  }
}

@include sp {
.breadcrumbs {
  padding: 12px 16px;
  ul {
    width: 100%;
    li {
      font-size: 10px;
      & ~ li {
        margin-left: 5px;
        &::before {
          left: 1px;
          width: 5px;
        }
      }
    }
  }
}
}
</style>