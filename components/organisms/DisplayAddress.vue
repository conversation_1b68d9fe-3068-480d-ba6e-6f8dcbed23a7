<template>
  <div class="wrap">
    <div class="flex">
      <h2>カード・バッグの送付先</h2>
      <a class="edit" href="#">
        <img src="@/assets/images/icon-edit.svg" width="18" />
        変更する
      </a>
    </div>
    <dl>
      <dt>お名前</dt>
      <dd>鈴木 このみ</dd>
    </dl>
    <dl>
      <dt>電話番号</dt>
      <dd>090-0000-0000</dd>
    </dl>
    <dl>
      <dt>住所</dt>
      <dd>東京都〇〇区△△1丁目2番3号<br>マンション■■■ 321号室</dd>
    </dl>
    <div class="button_wrap">
      <ButtonMainColor baseColor="reversal" size="md">変更する</ButtonMainColor>
    </div>

  </div>
</template>

<script setup lang="ts">
</script>

<style lang="scss" scoped>
.wrap{
  max-width: 600px;
  padding: 20px;
  margin: 0 auto;
}
.flex{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.button_wrap{
  max-width: 164px;
  margin-top: 16px;
  margin: 16px 0 0 auto;
}
.edit{
  display: inline-block;
  font-size: 12px;
  line-height: 1.2;
  color: #B18A3E;
  padding: 0;
  text-decoration: none;
  img{
    width: 18px;
    height: 18px;
    vertical-align: text-bottom;
  }
  &:hover{
    text-decoration: underline;
  }
}
h2{
  color: #B18A3E;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0;
}
dl{
  border-bottom: 1px solid #D9D9D9;
  padding: 10px 0;
}
dt{
  color: #9C9C9C;
  font-size: 10px;
  line-height: 1.2;
  margin-bottom: 2px;
}
dd{
  color: #333;
  font-size: 16px;
  line-height: 1.5;
}
</style>
