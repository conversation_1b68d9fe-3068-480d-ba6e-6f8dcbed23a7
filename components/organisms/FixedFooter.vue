<template>
<footer class="footer">
  <div class="section-inner">

    <div class="top">
      <h5 class="title">カテゴリ一覧</h5>
      <ul>
        <li>
          <ListWithTitle01 :list="ListFooterTop01.datail" :title="ListFooterTop01.title" />
        </li>
        <li>
          <ListWithTitle01 :list="paramsTop02.datail" :title="paramsTop02.title" />
        </li>
        <li>
          <ListWithTitle01 :list="paramsTop03.datail" :title="paramsTop03.title" />
        </li>
        <li>
          <ListWithTitle01 :list="paramsTop04.datail" :title="paramsTop04.title" />
        </li>
        <li>
          <ListWithTitle01 :list="paramsTop05.datail" :title="paramsTop05.title" />
        </li>
        <li>
          <ListWithTitle01 :list="paramsTop06.datail" :title="paramsTop06.title" />
        </li>
      </ul>
    </div>

    <div class="middle">
      <ul>
        <li>
          <ListWithTitle02 :list="paramsMiddle01.datail" :title="paramsMiddle01.title" />
        </li>
        <li>
          <ListWithTitle02 :list="paramsMiddle02.datail" :title="paramsMiddle02.title" />
        </li>
        <li>
          <ListWithTitle02 :list="paramsMiddle03.datail" :title="paramsMiddle03.title" />
        </li>
      </ul>
    </div>

    <FixedFooterBottom />

  </div>
</footer>
</template>

<script lang="ts" setup>
const ListFooterTop01 = ref({
  title: 'WEB招待状',
  datail: [
    {
      menu: "すべてのデザインを見る",
      link: "/products/webinvitation"
    }
  ],
});
onMounted(async () => {
  const { tagGroup, refetch } = useGetCommon();
  await refetch();
  // for (let i = 0; i < tagGroup.value.tags.length; i++) {
  //   const tag = tagGroup.value.tags[i];
  //   if (tag.id == 'all') continue;
  //   ListFooterTop01.value.datail.push({
  //     menu: tag.name,
  //     link: "/products/webinvitation?theme="+tag.id
  //   })
  // }
});


const ListFooterTop02 = {
  title: 'ペーパーアイテム',
  datail: [
    {
      menu: "結婚式招待状",
      link: "https://www.favori-cloud.com/invitation_designs"
    }, 
    {
      menu: "席次表",
      link: "https://www.favori-cloud.com/design"
    }, 
    {
      menu: "席札",
      link: "https://www.favori-cloud.com/design"
    }, 
    {
      menu: "メニュー表",
      link: "https://www.favori-cloud.com/design"
    }, 
    {
      menu: "ウェルカムボード",
      link: "https://www.favori-cloud.com/welcome_board_designs"
    }, 
    {
      menu: "結婚報告はがき",
      link: "https://www.favori-cloud.com/marriage_postcard_designs"
    },

  ],
}

const ListFooterTop03 = {
  title: '演出アイテム',
  datail: [
    {
      menu: "ゲストカード",
      link: "https://www.favori-diy.com/categories/5411616"
    }, 
    {
      menu: "芳名帳",
      link: "https://www.favori-diy.com/categories/5408450"
    }, 
    {
      menu: "ポチ袋",
      link: "https://www.favori-diy.com/categories/5408451"
    }, 
    {
      menu: "結婚証明書",
      link: "https://www.favori-diy.com/categories/5408468"
    }, 
    {
      menu: "リングピロー",
      link: "https://www.favori-diy.com/categories/5408493"
    }, 
    {
      menu: "イニシャルオブジェ",
      link: "https://www.favori-diy.com/categories/5408465"
    },
    {
      menu: "マスクケース",
      link: "https://www.favori-diy.com/categories/5408496"
    }, 
    {
      menu: "席札兼マスクケース",
      link: "https://www.favori-diy.com/categories/2908504"
    }, 
    {
      menu: "プロフィールブック",
      link: "https://www.favori-diy.com/categories/5408476"
    }, 
    {
      menu: "花嫁の手紙",
      link: "https://www.favori-diy.com/categories/5408470"
    }, 
    {
      menu: "両親贈呈品",
      link: "https://www.favori-diy.com/categories/3015356"
    }
  ]
}

const ListFooterTop04 = {
  title: '引き出物宅配',
  datail: [
    {
      menu: "カタログギフト",
      link: "https://www.favori-cloud.com/gift_designs"
    }, 
    {
      menu: "プロダクトギフト",
      link: "https://www.favori-cloud.com/gift_designs"
    }, 
  ],
}

const ListFooterTop05 = {
  title: 'プチギフト',
  datail: [
    {
      menu: "ドリップコーヒー",
      link: "https://www.favori-cloud.com/petit_gift_designs"
    }
  ],
}

const ListFooterTop06 = {
  title: '結婚報告はがき',
  datail: [
    {
      menu: "結婚報告はがき",
      link: "https://www.favori-cloud.com/marriage_postcard_designs"
    }
  ],
}

const ListFooterMiddle01 = {
  title: 'トップページ',
  datail: [
    {
      menu: "ホーム",
      link: "/"
    },
    {
      menu: "マイページ",
      link: "/mypage"
    },
    {
      menu: "お知らせ",
      link: "/information"
    },
  ],
}

const ListFooterMiddle02 = {
  title: 'Favoriについて',
  datail: [
    // {
    //   menu: "Favoriについて",
    //   link: "https://www.favori-cloud.com/about"
    // }, 
    {
      menu: "お客様の声",
      link: "/voice"
    }, 
    {
      menu: "サイトマップ",
      link: "/sitemap"
    }, 
  ],
}


const ListFooterMiddle03 = {
  title: 'ご利用ガイド',
  datail: [
    {
      menu: "サポートトップ",
      link: "/support"
    }, 
    {
      menu: "ご利用ガイド",
      link: "/guide"
    }, 
    {
      menu: "よくある質問",
      link: "/question"
    }, 
    {
      menu: "お問い合わせ",
      link: "/contact"
    }, 
  ],
}

const paramsTop02 = ListFooterTop02;
const paramsTop03 = ListFooterTop03;
const paramsTop04 = ListFooterTop04;
const paramsTop05 = ListFooterTop05;
const paramsTop06 = ListFooterTop06;

const paramsMiddle01 = ListFooterMiddle01;
const paramsMiddle02 = ListFooterMiddle02;
const paramsMiddle03 = ListFooterMiddle03;
</script>

<style lang="scss" scoped>

.footer {
  margin: 40px 0 0;
  .top ,
  .middle {
    padding-bottom: 35px;
    border-bottom: 1px solid #D9D9D9;
    color: #9c9c9c;
    .title {
      margin: 0 0 24px;
      line-height: 1.2;
      font-size: 16px;
      font-weight: bold;
    }
    ul {
      display: flex;
    }
  }
  .top{
    & > ul{
      :deep(){
        & > li{
          &:nth-child(1){
            max-width: 150px;
          }
          &:nth-child(2){
            max-width: 150px;
          }
          &:nth-child(3){
            max-width: 300px;
            flex-shrink: 0;
            ul {
              display: flex;
              flex-wrap: wrap;
            }
            li {
              width: 50%;
            }
          }
          &:nth-child(4){
            max-width: 160px;
          }
          @include sp {
            max-width: none !important;
            ul {
              display: block !important;
            }
            li {
              width: auto !important;
            }
          }
        }
      }
    }
  }
  .middle {
    padding: 24px 0 50px;
    border-bottom: 1px solid #D9D9D9;
    color: #9c9c9c;
    & > ul {
      & > li {
        min-width: 171px;
        padding-right: 10px;
      }
    }
  }
}

@include sp {
  .footer {
    padding: 0 17px 90px;
    .top ,
    .middle {
      padding-bottom: 20px;
      & > ul {
        display: block;
        & > li {
          border-top: 1px solid $color-lightgray;
        }
      }
    }
    .middle {
      margin-top: 21px;
      padding-bottom: 41px;
      & > ul {
        & > li {
          border-top: none;
          & + li {
            margin: 29px 0 0;
          }
        }
      }
    }
  }
}
</style>