<template>
<header class="header">
  <div class="header-inner">

    <nav class="navi">
      <NuxtLink class="backtop" to="/" v-if="partsHiddenSp.backTop === false && String(partsHiddenSp.backTop) !== 'undefined' && ! backlink">トップへ</NuxtLink>
      <button class="menu" @click="showDropdownMenu()"><img src="@/assets/images/icon-menu-w.svg" alt="メニュー" /></button>
      <NuxtLink class="backlink" v-if="isBacklinkAction" @click="onClickBacklink()"><img src="@/assets/images/icon-arrow_forward-w.svg" alt="戻る" /></NuxtLink>
      <NuxtLink class="backlink" :to="backlink" v-else-if="backlink"><img src="@/assets/images/icon-arrow_forward-w.svg" alt="戻る" /></NuxtLink>
      <NuxtLink class="logo" to="/" :data-sp-hidden="partsHiddenSp.logo"><img src="@/assets/images/logo-w.svg" alt="favori" /></NuxtLink>
      <h1 v-if="title !== ''" :data-sp-hidden="partsHiddenSp.headerTitle">{{ title }}</h1>
      <a href="#" class="sortButton" v-if="isShowSortButton" @click.prevent="webInvitationSort.set(true)">並び替え</a>
      <ul>
        <li class="navi01"><NuxtLink @click="clearUrlHistory()" to="/products/webinvitation">デザイン一覧</NuxtLink></li>
        <li class="navi02">
          <NuxtLink v-if="loginCheck?.loginCheck" @click="clearUrlHistory()" to="/mypage/webinvitation">My招待状</NuxtLink>
          <NuxtLink v-else @click="showModalLogin('/mypage/webinvitation')">My招待状</NuxtLink>        
        </li>
        <li class="navi03">
          <NuxtLink v-if="loginCheck?.loginCheck" @click="clearUrlHistory()" to="/mypage/guest-list">ゲストリスト</NuxtLink>
          <NuxtLink v-else @click="showModalLogin('/mypage/guest-list')">ゲストリスト</NuxtLink>        
        </li>
      </ul>
    </nav>

    <div class="ui">
      <!-- <form action="/" class="searchform">
        <InputSearch   
          name='header-search'
          id='header-search'
          placeholder='キーワード検索'
          v-model="searchKeyword" 
        />
      </form> -->
      <!-- <NuxtLink to="#tosearchpage" class="tosearchpage">商品検索</NuxtLink> -->
      <!-- <ul id="withbadge">
        <li class="notice">
          <NuxtLink to="#"><img src="@/assets/images/icon-notice-w.svg" alt="お知らせ" /></NuxtLink>
          <template v-if="badge.noticeBadges > 0">
            <span class="badge">{{ badge.noticeBadges }}</span>
          </template>
        </li>
        <li class="favorite">
          <NuxtLink to="#"><img src="@/assets/images/icon-favorite-w.svg" alt="お気に入り" />お気に入り</NuxtLink>
          <template v-if="badge.favoriteBadges > 0">
            <span class="badge">{{ badge.favoriteBadges }}</span>
          </template>
        </li>
        <li class="cart">
          <NuxtLink to="#"><img src="@/assets/images/icon-cart-w.svg" alt="カート" />カート</NuxtLink>
          <template v-if="badge.cartBadges > 0">
            <span class="badge">{{ badge.cartBadges }}</span>
          </template>
        </li>
      </ul> -->
      <NuxtLink v-if="loginCheck?.loginCheck" to="/mypage" class="btn btn-primary-outline">マイページ</NuxtLink>
      <template v-else>
        <NuxtLink @click="showModalLogin()" class="btn btn-primary-outline">ログイン</NuxtLink>
        <NuxtLink @click="clearUrlHistory()" to="/products/webinvitation" class="btn btn-secondary">無料で招待状を作る <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink>
      </template>
    </div>
    <SpNavMenuLoggedin v-if="loginCheck?.loginCheck" />
    <SpNavMenu v-else />
    <HeaderDrawerMenu :data="ListHeaderDrawerMenu" />
  </div>
</header>
</template>

<script lang="ts" setup>
import { provide } from "vue"
import { ref } from 'vue';
import { useWebInvitationSortStore } from '@/composables/useWebInvitationSort';
const router = useRouter();
const webInvitationSort = useWebInvitationSortStore();

const { loginCheck } = useGetLoginCheck();

const { showDropdownMenu } = useMenuState();
const { showModalLogin } = useModalLoginState();

const showListHeaderDropMenu = ref(false);

export interface Props {
  title?: string;
  backlink?: string;
  isBacklinkAction?: boolean;
  isShowSortButton?: boolean,
  partsHiddenSp?: {
    headerTitle?: boolean;
    logo?: boolean;
    // スマホのトップへ
    backTop?: boolean;
  };
}
const props = withDefaults(defineProps<Props>(), {
  title: '',
  backlink: '',
  isBacklinkAction: false,
  isShowSortButton: false,
  partsHiddenSp: () => {
    return {
      headerTitle: false,
      logo: false,
      backTop: true
    }
  },
});


const badge = {
  // 本来はAPIでデータを取得
  noticeBadges: 3,
  favoriteBadges: 2,
  cartBadges: 1,
};

const ListHeaderDrawerMenu01 = {
  title: 'For You',
  subtitle: 'おすすめ',
  datail: [
    {
      class: "favorite",
      menu: "お気に入り",
      link: "#01"
    }, 
    {
      class: "history",
      menu: "最近チェックした商品",
    }, 
    {
      class: "diagnosis",
      menu: "無料サンプル請求",
      link: "#01"
    }, 
    {
      class: "download",
      menu: "ダウンロードコンテンツ",
      link: "#01"
    }, 
  ],
}

const ListHeaderDrawerMenu02 = {
  title: 'Category',
  subtitle: '商品カテゴリ',
  datail: [
    {
      class: "category",
      menu: "セットアイテム",
      innermenu: [
        {
          menu: "セットアイテム01",
          link: "#01"
        },
        {
          menu: "セットアイテム02",
          link: "#02"
        },
        {
          menu: "セットアイテム03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ペーパーアイテム",
      innermenu: [
        {
          menu: "ペーパーアイテム01",
          link: "#01"
        },
        {
          menu: "ペーパーアイテム02",
          link: "#02"
        },
        {
          menu: "ペーパーアイテム03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ウェルカムスペース・演出アイテム",
      innermenu: [
        {
          menu: "ウェルカムスペース・演出アイテム01",
          link: "#01"
        },
        {
          menu: "ウェルカムスペース・演出アイテム02",
          link: "#02"
        },
        {
          menu: "ウェルカムスペース・演出アイテム03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "プチギフト",
      innermenu: [
        {
          menu: "プチギフト01",
          link: "#01"
        },
        {
          menu: "プチギフト02",
          link: "#02"
        },
        {
          menu: "プチギフト03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "両親贈呈品・子育て感謝状",
      innermenu: [
        {
          menu: "両親贈呈品・子育て感謝状01",
          link: "#01"
        },
        {
          menu: "両親贈呈品・子育て感謝状02",
          link: "#02"
        },
        {
          menu: "両親贈呈品・子育て感謝状03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "結婚式アルバム",
      innermenu: [
        {
          menu: "結婚式アルバム01",
          link: "#01"
        },
        {
          menu: "結婚式アルバム02",
          link: "#02"
        },
        {
          menu: "結婚式アルバム03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "結婚報告はがき・年賀状",
      innermenu: [
        {
          menu: "結婚報告はがき・年賀状01",
          link: "#01"
        },
        {
          menu: "結婚報告はがき・年賀状02",
          link: "#02"
        },
        {
          menu: "結婚報告はがき・年賀状03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "引き出物宅配",
      innermenu: [
        {
          menu: "引き出物宅配01",
          link: "#01"
        },
        {
          menu: "引き出物宅配02",
          link: "#02"
        },
        {
          menu: "引き出物宅配03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "内祝い・お返しギフト",
      innermenu: [
        {
          menu: "内祝い・お返しギフト01",
          link: "#01"
        },
        {
          menu: "内祝い・お返しギフト02",
          link: "#02"
        },
        {
          menu: "内祝い・お返しギフト03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ギフト・プレゼント・贈答品",
      innermenu: [
        {
          menu: "ギフト・プレゼント・贈答品01",
          link: "#01"
        },
        {
          menu: "ギフト・プレゼント・贈答品02",
          link: "#02"
        },
        {
          menu: "ギフト・プレゼント・贈答品03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "オリジナルグッズ",
      innermenu: [
        {
          menu: "オリジナルグッズ01",
          link: "#01"
        },
        {
          menu: "オリジナルグッズ02",
          link: "#02"
        },
        {
          menu: "オリジナルグッズ03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "結婚式ムービー",
      innermenu: [
        {
          menu: "結婚式ムービー01",
          link: "#01"
        },
        {
          menu: "結婚式ムービー02",
          link: "#02"
        },
        {
          menu: "結婚式ムービー03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ウェディング・ブライダルアクセサリー",
      innermenu: [
        {
          menu: "ウェディング・ブライダルアクセサリー01",
          link: "#01"
        },
        {
          menu: "ウェディング・ブライダルアクセサリー02",
          link: "#02"
        },
        {
          menu: "ウェディング・ブライダルアクセサリー03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ウェディングドレス",
      innermenu: [
        {
          menu: "ウェディングドレス01",
          link: "#01"
        },
        {
          menu: "ウェディングドレス02",
          link: "#02"
        },
        {
          menu: "ウェディングドレス03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "二次会景品",
      innermenu: [
        {
          menu: "二次会景品01",
          link: "#01"
        },
        {
          menu: "二次会景品02",
          link: "#02"
        },
        {
          menu: "二次会景品03",
          link: "#03"
        },
      ],
    }, 
  ],
}

const ListHeaderDrawerMenu03 = {
  title: 'Support',
  subtitle: 'お客様サポート',
  datail: [
    {
      class: "help",
      menu: "サポートトップ",
      link: "#01"
    }, 
    {
      class: "",
      menu: "納期について",
      link: "#02"
    }, 
    {
      class: "",
      menu: "引き出物お届けまでのスケジュール",
      link: "#03"
    }, 
    {
      class: "",
      menu: "ご利用ガイド",
      link: "#04"
    }, 
    {
      class: "",
      menu: "よくある質問",
      link: "/question"
    }, 
    {
      class: "",
      menu: "お問い合わせ",
      link: "#06"
    }, 
  ],
}

const ListHeaderDrawerMenu04 = {
  title: 'About Us',
  subtitle: 'Favoriについて',
  datail: [
    {
      class: "",
      menu: "Favoriについて",
      link: "#01"
    }, 
    {
      class: "",
      menu: "お客様の声",
      link: "#02"
    }, 
    {
      class: "",
      menu: "ブログ&マガジン",
      link: "#03"
    }, 
    {
      class: "",
      menu: "サイトマップ",
      link: "#04"
    }, 
  ],
}

const ListHeaderDrawerMenu = {
  ListHeaderDrawerMenu01, 
  ListHeaderDrawerMenu02,
  ListHeaderDrawerMenu03,
  ListHeaderDrawerMenu04,
}

const searchKeyword = ref('');

provide('stateMenu', ListHeaderDrawerMenu);

const emit = defineEmits(['click-backlink']);
const onClickBacklink = () => {
  emit('click-backlink');
};
</script>

<style lang="scss" scoped>
.header {
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 11;
}
.header-inner {
  display: flex;
  justify-content: space-between;
  position: relative;
  width: 100%;
  background: $color-main;
  .is-adminUserLogin & {
    background: #e84539;
  }
}
.navi {
  display: flex;
  .menu {
    width: 56px;
    height: 56px;
    position: relative;
    margin-right: 32px;
    display: block;
    flex-shrink: 0;
    @include sp {
      display: none;
    }
    &:after {
      content: " ";
      display: block;
      width: 1px;
      height: 36px;
      background: #fff;
      position: absolute;
      right: 0;
      top: 50%;
      margin-top: -18px;
    }
  }
  .backlink {
    display: none;
    position: absolute;
    top: 50%;
    left: 13px;
    transform: translateY(-50%) scale(-1,1);
  }
  .backtop {
    color: #fff;
    position: absolute;
    left: 0;
    top: 0;
    font-size: 12px;
    text-decoration: none;
    padding: 0 12px;
    line-height: 36px;
    height: 36px;
    display: none;
    @include sp {
      display: block;
    }
  }
  .logo {
    margin-top: 10px;
    width: 80px;
    display: block;
  }
  h1 {
    display: none;
    font-size: 14px;
    margin-left: 10px;
    color: $color-whitetext;
  }
  .sortButton{
    color: #fff;
    position: absolute;
    right: 0;
    top: 0;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    padding: 0 16px;
    line-height: 36px;
    height: 36px;
    @include pc {
      display: none;
    }
  }
  ul {
    display: flex;
    list-style: none;
    margin-left: 31px;
    li {
      a {
        display: flex;
        align-items: center;
        height: 100%;
        padding: 7px 12px 0 13px;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
        text-decoration: none;
        font-size: 14px;
        font-weight: 700;
        color: $color-whitetext;
        cursor: pointer;
        &:hover {
          border-bottom: 3px solid #fff;
        }
      }
      .drawer-dropmenu.close {
        right: -375px;
      }
    }
  }
  .header-dropmenu {
    position: absolute;
    top: 100%;
    &.close {
      display: none;
    }
  }
}
.ui {
  display: flex;
  align-items: center;
  .btn {
    font-size: 13px;
    font-weight: bold;
    border: none;
    margin-right: 16px;
    height: 36px;
    .icn-right {
      margin-right: -.5em;
    }
    &.btn-primary-outline {
      &:hover{
        background: #fff;
        color: $color-main;
        opacity: 0.6;
      }
    }
  }
  .searchform {
    position: relative;
    margin-bottom: 3px;
  }
  .tosearchpage {
    display: none;
  }
  ul {
    display: flex;
    list-style: none;
    margin-left: 12px;
    padding: 0 10px;
    li {
      position: relative;
      a {
        display: inline-block;
        position: relative;
        padding: 6px 10px;
      }
      .badge {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 4px;
        right: 4px;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        background: $color-alert2;
        font-family: 'Roboto';
        font-size: 11px;
        color: #FFFFFF;
      }
      &.favorite {
        img {
          width: 20px;
          margin: 3px 2px;
        }
      }
    }
  }
  .account {
    padding: 6px 15px 6px 16px;
    // border-left: 1px solid #fff;
    color: #fff;
    cursor: pointer;
  }
  ul .favorite a ,
  ul .cart a ,
  .account {
    font-size: 0;
  }
}
@include sp {
.header-inner {
  background-color: transparent !important;
  height: 36px;
  .navi {
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 36px;
    padding: 0 50px;
    background-color: $color-main;
    .is-adminUserLogin & {
      background-color: #e84539;
    }
    position: absolute;
    left: 0;
    right: 0;
    top: -36px;
    transition: 0.35s ease;
    [data-scroll="top"] & {
      position: absolute;
      top: 0;
    }
    .backlink {
      display: inline;
    }
    .logo {
      width: 50px;
      margin-top: 0;
      margin-right: 10px;
      &.onTitle {
        display: none;
      }
    }
    h1 {
      display: inline;
      max-width: calc(100% - 10px);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin: 0;
    }
    ul {
      display: none;
    }
  }
  .ui {
    display: none;
  }
}
}
</style>
<style lang="scss">
.header-inner {
  .nav-sp {
    display: none;
    @include sp {
      display: block;
      position: fixed;
      bottom: -60px;
      left: 0;
      right: 0;
      height: 55px;
      padding: 0;
      background-color: $color-lightgray;
      box-shadow: 0px -4px 4px rgba(0, 0, 0, 0.1);
      transition: 0.35s ease;
      [data-scroll="top"] & {
        bottom: 0;
      }
      ul {
        display: flex;
        li {
          width: 100%;
          > * {
            display: block;
            font-size: 10px;
            color: var(--49454Fblack_Light, #49454F);
            text-decoration: none;
            padding: 7px 0;
            margin: 0;
            width: 100%;
            text-align: center;
            &:before {
              content: " ";
              background: url(@/assets/images/icon-account-gl.svg) no-repeat center center;
              background-size: contain;
              display: block;
              height: 24px;
              margin-bottom: 4px;
            }
            &.router-link-active {
              color: #2F587C;
              font-weight: bold;
            }
            &.item-account.router-link-active:before {
              background-image: url(@/assets/images/icon-account-gl-active.svg);
            }
            &.item-account.is-account-alert-regist:before {
              background-image: url(@/assets/images/icon-account-alert.svg);
            }
            &.item-account.router-link-active.is-account-alert-regist:before {
              background-image: url(@/assets/images/icon-account-on-alert.svg);
            }
            &.item-login:before {
              background-image: url(@/assets/images/icon-login-gl.svg);
            }
            &.item-menu:before {
              background-image: url(@/assets/images/icon-menu-gl.svg);
              background-size: 18px 14px;
            }
            &.item-menu:before {
              background-image: url(@/assets/images/icon-menu-gl.svg);
            }
            &.item-guestlist:before {
              background-image: url(@/assets/images/icon-guestlist-gl.svg);
            }
            &.item-guestlist.router-link-active:before {
              background-image: url(@/assets/images/icon-guestlist-gl-active.svg);
            }
            &.item-webinvitation:before {
              background-image: url(@/assets/images/icon-webinvitation-gl.svg);
            }
            &.item-webinvitation.router-link-active:before {
              background-image: url(@/assets/images/icon-webinvitation-gl-active.svg);
            }
          }
        }
      }
    }
  }
}
</style>