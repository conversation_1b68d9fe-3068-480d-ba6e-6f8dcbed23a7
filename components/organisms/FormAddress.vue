<template>
  <div class="form">
    <div class="wrap">
      <div class="row" v-if="partsHidden.Delivery !== 'hidden'">
        <InputRadio
          title="お届け先"
          name="deliveryAddress"
          :items="[
            {
              value: 1,
              checked: true,
              label: '個人宅',
            },
            {
              value: 2,
              checked: false,
              label: '式場・ホテル・会場',
            }
          ]"
        />
      </div>
      <div class="row" v-if="partsHidden.Name !== 'hidden'">
        <InputText
          title="姓"
          value="城田"
          size="half"
        />
        <InputText
          title="名"
          value="城田"
          size="half"
        />
      </div>

      <h3>ご住所</h3>
      <div class="row">
        <InputZipCode/>
      </div>

      <div class="row">
        <InputSelect
          title="都道府県"
          required="true"
          size="full"
        />
      </div>
      <div class="row">
        <InputText
          title="市区町村"
          required="true"
          size="full"
        />
      </div>
      <div class="row">
        <InputText
          title="丁目・番地"
          required="true"
          size="full"
        />
      </div>
      <div class="row">
        <InputText
          title="建物名・部屋番号など"
          size="full"
        />
      </div>
      <div class="row">
        <InputText
          title="電話番号"
          size="md"
        />
      </div>
      <div class="row" v-if="partsHidden.Mail !== 'hidden'">
        <InputText
          title="メールアドレス"
          size="full"
        />
      </div>
      <div class="row" v-if="partsHidden.Message !== 'hidden'">
        <InputText
          title="その他 備考・ご質問・メッセージ等"
          size="full"
        />
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
export interface Props {
  partsHidden: Array,
}

const params = withDefaults(defineProps<Props>(), {
  partsHidden: {},
});
</script>

<style lang="scss" scoped>
.wrap{
  max-width: 680px;
  padding: 20px;
}
.row + .row{
  margin-top: 28px;
}
.note{
  color: #333;
  background: #F4F4F4;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 4px;
  padding: 10px;
  margin: 12px 0;
}
h3{
  font-size: 14px;
  font-weight: normal;
  margin: 20px 0;
  line-height: 1.2;
}
</style>
