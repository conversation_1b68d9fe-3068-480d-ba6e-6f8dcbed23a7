<template>
  <div class="form">
    <div class="wrap">

      <div class="section">
        <div class="row">
          <InputText
            title="銀行 / 支店"
            required="true"
            :size="120"
          />
          <span class="divider">/</span> 
          <InputText
            :size="120"
          />
          <a class="search" href="#">
            <img src="@/assets/images/icon-search-b.svg" />
            銀行 / 支店を検索
          </a>
        </div>
        <div class="row">
          <InputSelect
            title="口座種別"
            required="true"
            size="md"
          />
        </div>
        <div class="row">
          <InputText
            title="口座名義"
            placeholder="フクナガ コノミ"
            required="true"
            size="full"
          />
        </div>
        <div class="row">
          <InputText
            title="口座番号"
            required="true"
            size="full"
          />
          <p>口座番号が7桁未満の方は 頭に半角数字の「0」を追加してください</p>
          <a class="help" href="#">
            <img src="@/assets/images/icon-search-ac.svg" />
            ゆうちょ銀行の口座番号を調べたい方はこちら
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emits = defineEmits<{
  (e: 'submit', v: null): void;
}>()
</script>

<style lang="scss" scoped>
.row + .row{
  margin-top: 28px;
}
.divider {
  display: inline-block;
  margin: 0 8px;
  font-size: 13px;
}
.search{
  display: block;
  margin: 15px 0 0 3px;
  color: $color-main;
  font-size: 14px;
  font-weight: 700;
  line-height: 14px;
  letter-spacing: 1.4px;
  text-decoration: none;
}
.help{
  display: inline-block;
  color: $color-accent;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0.24px;
  text-decoration: none;

  img {
    vertical-align: middle;
  }
}
.note{
  color: #333;
  background: #F4F4F4;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 4px;
  padding: 10px;
  margin: 12px 0;
}
.unit{
  color: #333;
  margin: 0 4px;
  font-size: 12px;
}
.checkbox_note{
  font-size: 10px;
  color: #5A5A5A;
  line-height: 1.5;
  padding-left: 26px;
  margin-top: -6px;
  margin-left: 1em;
  text-indent: -1em;
}
.button_wrap{
  max-width: 164px;
  margin-top: 22px;
}
h2{
  color: #B18A3E;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 20px;
  img{
    vertical-align: text-bottom;
  }
}
h3{
  color: #B18A3E;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 20px 0;
}
p{
  font-size: 10px;
  line-height: 140%;
  letter-spacing: 0.2px;
  color: $color-blacktext2;
  margin-bottom: 4px;
  margin-top: 4px;
}
</style>
