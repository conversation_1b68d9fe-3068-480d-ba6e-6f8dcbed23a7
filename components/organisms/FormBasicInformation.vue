<template>
<div class="row" v-if="props.partsHidden.Name !== 'hidden'">
  <InputKanjiFullName 
    :required="true"
    :values="{last_name: input?.last_name, first_name: input?.first_name}"
    :errors="validate"
    :isSubModal="true"
    @showSubModal="emit('showSubModal')"
    @closeSubModal="emit('closeSubModal')"
    @change="$emit('update', { key: 'last_name',  value: $event.last_name });$emit('update', { key: 'first_name',  value: $event.first_name });"
  />
</div>
<div class="row" v-if="props.partsHidden.Kananame !== 'hidden'" style="margin-top: 0;">
  <InputHiragana
    title="せい"
    placeholder="ふくなが"
    size="half"
    :value="input.last_name_kana"
    :error="validate?.last_name_kana?.$errors?.[0]?.$message"
    @update="$emit('update', { key: 'last_name_kana',  value: $event })"
    />
  <InputHiragana
    title="めい"
    placeholder="このみ"
    size="half"
    :value="input.first_name_kana"
    :error="validate?.first_name_kana?.$errors?.[0]?.$message"
    @update="$emit('update', { key: 'first_name_kana',  value: $event })"
    />
</div>
<div class="row" v-if="props.partsHidden.Romanname !== 'hidden'">
  <InputAlphabet
    title="姓（ローマ字）"
    placeholder="Fukunaga"
    size="half"
    format="capitalize"
    :value="input.last_name_romaji"
    :error="validate?.last_name_romaji?.$errors?.[0]?.$message"
    @update="$emit('update', { key: 'last_name_romaji',  value: $event })"
    />
  <InputAlphabet
    title="名（ローマ字）"
    placeholder="Konomi"
    size="half"
    format="capitalize"
    :value="input.first_name_romaji"
    :error="validate?.first_name_romaji?.$errors?.[0]?.$message"
    @update="$emit('update', { key: 'first_name_romaji',  value: $event })"
    />
</div>
<div class="row" v-if="props.partsHidden.Honorific !== 'hidden'">
  <InputGuestHonor
    title="敬称"
    :value="input?.guest_honor"
    :error="validate?.guest_honor?.$errors?.[0]?.$message"
    :guestListId="guestListId"
    placeholder="様"
    size="full"
    target="guest_honor"
    @change="$emit('update' , {key: 'guest_honor', value: $event})"
  />
</div>

<div class="row">
  <InputGuestTitle
    title="肩書"
    :value="input?.guest_title"
    :error="validate?.guest_title?.$errors?.[0]?.$message"
    :guestListId="guestListId"
    placeholder="肩書を入力するか選択してください"
    size="full"
    @change="$emit('update' , {key: 'guest_title', value: $event})"
  />
</div>

<div class="row" v-for="(guest_event_answer, index) in guest_event_answers" :key="index">
  <InputAttendance
    :title="'出欠（'+guest_event_answer.name+'）'"
    :value="guest_event_answer?.attendance"
    :error="validate?.guest_event_answers?.[index]?.attendance?.$errors?.[0]?.$message"
    :isShowNull="true"
    @change="emit('update' , {key: 'guest_event_answers.' + String(index) + '.attendance', value: $event})"
  />
</div>

<div class="row">
  <InputGuestType
    title="新郎側ゲスト / 新婦側ゲスト"
    :value="String(input?.guest_type)"
    @change="$emit('update', { key: 'guest_type',  value: $event })"
  />
</div>

<div class="row" v-if="props.partsHidden.Gender !== 'hidden'">
  <InputGender
    title="性別"
    :value="String(input?.gender)"
    @change="$emit('update', { key: 'gender',  value: $event })"
  />
</div>

<!-- <div class="row">
  <InputSelect
    title="関係性"
    size="full"
    :options="relationShipNameOptions()"
    :value="input?.relationship_name"
    :error="validate?.relationship_name?.$errors?.[0]?.$message"
    @change="$emit('update' , {key: 'relationship_name', value: $event.target.value})"
  />
</div> -->

<div class="row">
  <InputSelectGroup
    class="guestAnswer_box_input"
    title="間柄"
    size="full"
    :options="relationShipOptions(input?.relationship_name)"
    :value="input?.relationship"
    :error="validate?.relationship?.$errors?.[0]?.$message"
    @change="$emit('update' , {key: 'relationship', value: $event.target.value})"
  />
</div>

<div class="row" v-if="props.partsHidden.Birthday !== 'hidden'">
  <InputDate
    title="お誕生日"
    :value="input?.birthdate"
    :error="validate?.birthdate?.$errors?.[0]?.$message"
    @change="$emit('update' , {key: 'birthdate', value: $event})"
  />
</div>

<div class="row" v-if="props.partsHidden.Allergy !== 'hidden'">
  <InputAllergy
    title="アレルギーについて"
    :value="{allergies: input?.allergies, allergy: input?.allergy}"
    :error="validate?.allergy?.$errors?.[0]?.$message"
    :isShowNull="true"
    @change="$emit('update', { key: 'allergies',  value: $event?.allergies}); $emit('update', { key: 'allergy', value: $event?.allergy});"
  />
</div>

<div class="row" v-if="props.partsHidden.Group !== 'hidden'">
  <InputGuestGroup
    title="グループ"
    size="full"
    :value="String(input?.guest_group_id)"
    :error="validate?.guest_group_id?.$errors?.[0]?.$message"
    :guestListId="guestListId"
    @change="$emit('update' , {key: 'guest_group_id', value: $event.target.value})"
    @showSubModal="emit('showSubModal')"
    @closeSubModal="emit('closeSubModal')"
  />
</div>

<div class="row" v-if="props.partsHidden.Tag !== 'hidden'">
  <InputGuestTags
    title="タグ"
    size="full"
    :value="input?.guest_tag_guests"
    :error="validate?.guest_tag_guests?.$errors?.[0]?.$message"
    :guestListId="guestListId"
    @change="$emit('update' , {key: 'guest_tag_guests', value: $event})"
    @showSubModal="emit('showSubModal')"
    @closeSubModal="emit('closeSubModal')"
  />
</div>

</template>

<script setup lang="ts">
export interface Props {
  guestListId: string;
  partsHidden: {
    Addcompanion?: string;
    Allergy?: string;
    Attendance?: string;
    Birthday?: string;
    Deletecompanion?: string;
    Gender?: string;
    Group?: string;
    Honorific?: string;
    Kananame?: string;
    Name?: string;
    Oldkanji?: string;
    Romanname?: string;
    Tag?: string;
    Title?: string;
  },
  // 上の階層で指定しているので型宣言は不要
  input: any,
  // 細かくなって見通し悪いのでanyに
  validate: any,
  guest_event_answers?: any,
  webInvitation?: any;
}

// 関係性と間柄のAPI
const { relation_ships, refetch } = useGetManyConstant(['CONSTANT_RELATIONSHIP', 'CONSTANT_RELATIONSHIP']);
const relationShipNameOptions = () => {
  let result = [{value: '', label: '関係性を選択してください'}];
  for (const key in RELATIONSHIP_NAME_MASTER) {
    const value = RELATIONSHIP_NAME_MASTER?.[key]
    result.push({value: value, label: value})
  }
  return result;
}
const relationShipOptions = (relationship_name = '') => {
  let relation_ship_data = relation_ships.value.map(category => ({
    groupLabel: category.category,
    groupItems: category.options.map(option => ({
      value: option.name,
      label: option.name
    }))
  }));
  relation_ship_data.unshift({
    value: '',
    label: '間柄を選択してください'
  });
  return relation_ship_data;
}

const props = withDefaults(defineProps<Props>(), {
  guestListId: '',
  partsHidden: () => {
    return {
      Addcompanion: '',
      Allergy: '',
      Attendance: '',
      Birthday: '',
      Deletecompanion: '',
      Gender: '',
      Group: '',
      Honorific: '',
      Kananame: '',
      Name: '',
      Oldkanji: '',
      Romanname: '',
      Tag: '',
      Title: ''
    }
  },
  input: () => { return {}},
  validate: () => { return {}},
  guest_event_answers: () => { return []},
  webInvitation: () => { return {}},
});

const emit = defineEmits(['showSubModal', 'closeSubModal', 'update']);
</script>

<style lang="scss" scoped>
.row + .row{
  margin-top: 28px;
}
.edit{
  display: inline-block;
  font-size: 14px;
  line-height: 1.2;
  color: #B18A3E;
  padding: 10px 0;
  text-decoration: none;
  img{
    width: 18px;
    height: 18px;
    vertical-align: text-bottom;
  }
  &:hover{
    text-decoration: underline;
  }
}
.button_wrap{
  max-width: 164px;
  margin: 0 0 0 auto;
}
.is-border-top{
  border-top: 1px solid #F4F4F4;
  padding-top: 16px;
}
.deleteRow {
  margin: 20px 0 24px;
  text-align: right;
  button {
    position: relative;
    padding: 0 0 0 22px;
    color: $color-alert;
    font-size: 12px;
    line-height: 120%;
    letter-spacing: 0.24px;
    &::before {
      @include BA;
      left: 0;
      width: 18px;
      height: 18px;
      background-image: url(@/assets/images/icon-delete.svg);
    }
  }
}

@include sp {
.wrap{
  padding: 0 17px 20px 16px;
}
}
</style>
