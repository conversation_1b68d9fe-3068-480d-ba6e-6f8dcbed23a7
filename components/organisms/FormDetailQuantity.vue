<template>
  <div class="list">
    <table>
      <thead>
        <tr>
          <th>商品</th>
          <th>単価</th>
          <th>数量</th>
          <th>価格</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th>
            <div class="list_flex">
              <div class="list_image">
                <img src="/images/sample/cartItem_menu_1.png" alt="">
                <a class="list_image_zoom"></a>
              </div>
              招待状
            </div>
          </th>
          <td class="is-center">
            無料
          </td>
          <td class="is-center">
            <div class="select">
              <inputSelect />
            </div>
          </td>
          <td class="is-right">
            ¥8,580
          </td>
        </tr>
        <tr>
          <th>
            <div class="list_flex">
              <div class="list_image">
                <img src="/images/sample/cartItem_menu_2.png" alt="">
                <a class="list_image_zoom"></a>
              </div>
              封筒<br>（宛名印刷あり）
            </div>
          </th>
          <td class="is-center">
            無料
          </td>
          <td class="is-center">
            2
          </td>
          <td class="is-right">
            無料
          </td>
        </tr>
        <tr>
          <th>
            <div class="list_flex">
              <div class="list_image">
                <img src="/images/sample/cartItem_menu_3.png" alt="">
                <a class="list_image_zoom"></a>
              </div>
              返信はがき
            </div>
          </th>
          <td class="is-center">
            無料
          </td>
          <td class="is-center">
            <div class="quantity">
              <span>新郎用</span>
              <InputQuantity />
              <span>新婦用</span>
              <InputQuantity />
            </div>
          </td>
          <td class="is-right">
            無料
          </td>
        </tr>
        <tr>
          <th>
            <div class="list_flex">
              <div class="list_image">
                <img src="/images/sample/cartItem_menu_3.png" alt="">
                <a class="list_image_zoom"></a>
              </div>
              はがき裏面カスタマイズ
            </div>
          </th>
          <td class="is-center">
            ¥3,300
          </td>
          <td class="is-center">
            <div class="button">
              希望
              <ButtonMainColor baseColor="reversal" size="md">変更</ButtonMainColor>
            </div>
          </td>
          <td class="is-right">
            ¥3,300
          </td>
        </tr>
        <tr>
          <th>
            <div class="list_flex">
              <div class="list_image">
                <img src="/images/sample/cartItem_menu_4.png" alt="">
                <a class="list_image_zoom"></a>
              </div>
              はがき裏面カスタマイズ
            </div>
          </th>
          <td class="is-center">
            ¥88/１セット
          </td>
          <td class="is-center">
            <InputQuantity />
          </td>
          <td class="is-right">
            ¥88
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
</script>

<style lang="scss" scoped>
table{
  max-width: 100%;
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  th, td{
    font-size: 12px;
    font-weight: normal;
    color: #5A5A5A;
    line-height: 1.2;
    padding: 12px;
  }
  tr + tr{
    border-top: 1px solid rgba(33, 33, 33, 0.08);
  }
  tr > *{
    &:first-child{
      padding-left: 32px;
    }
    &:last-child{
      padding-right: 32px;
    }
  }
  thead{
    th, td{
      background: $color-lightbackground;
    }
  }
  tbody{
    th{
      text-align: left;
    }
  }
}
.list_flex{
  display: flex;
  align-items: center;
}
.list_image{
  display: inline-block;
  margin-right: 14px;
  position: relative;
  &_zoom{
    display: block;
    width: 24px;
    height: 24px;
    position: absolute;
    top: -6px;
    right: -6px;
    cursor: pointer;
    background-image: url('~/assets/images/icon-zoom.svg');
  }
}
.is-center{
  text-align: center;
}
.is-right{
  text-align: right;
}
.quantity{
  span{
    display: block;
    color: #9C9C9C;
    font-size: 11px;
    line-height: 1.2;
    margin: 4px 0 2px;
    &:first-child{
      margin-top: 0;
    }
  }
}
.select{
  max-width: 72px;
  margin: 0 auto;
}
.button{
  max-width: 72px;
  margin: 0 auto;
  font-size: 12px;
  line-height: 1.6;
}
</style>
