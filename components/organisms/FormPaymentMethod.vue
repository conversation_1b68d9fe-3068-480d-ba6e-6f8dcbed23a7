<template>
  <div class="form">
    <div class="wrap">
      <p>ウォレット残高やポイントは 次の画面で選択できます </p>
      <h3>クレジットカード</h3>
      <InputRadio
        name="payment"
        :items="[
          {
            value: 1,
            checked: true,
            label: '新規カードを使う',
          }
        ]"
      />
      <div class="section">
        <div class="row">
          <InputText
            title="カード番号"
            size="full"
          />
        </div>
        <div class="row">
          <InputText
            type="number"
            title="有効期限"
            size="xs"
          />
          <span class="unit">月 / 20</span>
          <InputText
            type="number"
            size="xs"
          />
          <span class="unit">年</span>
        </div>
        <div class="row">
          <InputText
            title="カード名義"
            size="sm"
          />
          <InputText
            size="sm"
          />
          <p>カードに記載された名前を入力してください 半角英字入力（例：TARO YAMADA）</p>
        </div>
        <div class="row">
          <InputText
            title="セキュリティコード"
            size="xs"
          />
          <p>主にカード裏面の署名欄に記載されている末尾3〜4桁の数字を入力してください <br>半角数字入力（例：123）</p>
        </div>

        <div class="row">
          <InputCheck
            :items="[
              {
                value: 1,
                checked: false,
                label: 'このお届け先を保存する',
              }
            ]"
          />
          <div class="checkbox_note">
            ※次回以降入力する手間がなくなります <br>保存した内容はいつでも削除できます
          </div>
        </div>
      </div>

      <h3>その他のお支払い方法</h3>
      <div class="row other_payment">
        <InputRadio
          name="payment"
          :block="true"
          :items="[
            {
              value: 2,
              checked: false,
              label: '銀行振込',
            },
            {
              value: 3,
              checked: false,
              label: 'GMO後払い',
            }
          ]"
        />
        <div class="note">印刷を含む商品をご注文されている場合は クレジットカードまたはウォレット・ポイント以外のお支払い方法を選択することはできません </div>
      </div>

      <div class="button_wrap">
        <ButtonMainColor size="md" @click="emits('submit', null)">確定する</ButtonMainColor>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
const emits = defineEmits<{
  (e: 'submit', v: null): void;
}>()
</script>

<style lang="scss" scoped>
.section{
  padding-left: 24px;
}
.row + .row{
  margin-top: 28px;
}
.note{
  color: #333;
  background: #F4F4F4;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 4px;
  padding: 10px;
  margin: 12px 0;
}
.unit{
  color: #333;
  margin: 0 4px;
  font-size: 12px;
}
.checkbox_note{
  font-size: 10px;
  color: #5A5A5A;
  line-height: 1.5;
  padding-left: 26px;
  margin-top: -6px;
  margin-left: 1em;
  text-indent: -1em;
}
.button_wrap{
  max-width: 164px;
  margin-top: 22px;
}
h2{
  color: #B18A3E;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 20px;
  img{
    vertical-align: text-bottom;
  }
}
h3{
  color: #B18A3E;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 20px 0;
}
p{
  font-size: 12px;
  line-height: 1.45;
  color: #333;
  margin-bottom: 16px;
  margin-top: 6px;
}
</style>
