<template>
  <div class="input">
    <div class="wrap">
      <h2>基本情報</h2>
      <InputGuestProfileImage
        :value="input?.input?.image"
        :error="validate?.input?.image?.$errors?.[0]?.$message"
        @change="emit('update' , {key: 'image', value: $event})"
        @showSubModal="emit('showSubModal')" 
        @closeSubModal="emit('closeSubModal')" 
      />
      <FormBasicInformation 
        :input="input?.input" 
        :guest_event_answers="input?.guest_event_answers" 
        :validate="validate?.input"
        :guestListId="guestListId"
        :webInvitation="webInvitation"
        @update="emit('update' , $event)"
        @showSubModal="emit('showSubModal')" 
        @closeSubModal="emit('closeSubModal')" 
      />
    </div>
    <template v-for="(guest, index) in input.guests" :key="index">
      <hr>
      <div class="wrap">
        <h2>基本情報（連名ゲスト）</h2>
        <InputGuestProfileImage
          :value="guest?.image"
          :error="validate?.guests?.[index]?.image?.$errors?.[0]?.$message"
          @change="emit('update' , {key: 'guests.'+String(index)+'.image', value: $event})"
          @showSubModal="emit('showSubModal')" 
          @closeSubModal="emit('closeSubModal')" 
        />
        <FormBasicInformation 
          :input="guest" 
          :validate="validate?.guests?.[index]"
          :guestListId="guestListId"
          :webInvitation="webInvitation"
          @update="emit('update' , {key: 'guests.' + String(index) + '.' + $event.key, value: $event.value})"
          @showSubModal="emit('showSubModal')" 
          @closeSubModal="emit('closeSubModal')" 
        />
        <div class="deleteRow" @click="emit('deleteGuest', index)">
          <button>この連名ゲストの情報を削除する</button>
        </div>
      </div>
    </template>
    <div class="row is-border-top mb-20" v-if="! parentGuest && ! input?.input?.id">
      <div class="wrap button_wrap">
        <button v-if="input.guests.length < 10" type="button" class="btn btn-primary-outline" @click="emit('addGuest')"><i class="icn-left material-icons">add</i>連名ゲストを追加</button>
        <div v-else>
          <button type="button" class="btn btn-primary-outline" disabled="disabled"><i class="icn-left material-icons">add</i>連名ゲストを追加</button>
          <p>連名ゲストを追加できるのは10名までです</p>
        </div>
      </div>
    </div>
    <hr>
    <div class="wrap">
      <h2>詳細情報</h2>
      <h3>ご住所</h3>
      <div class="row">
        <InputZipCode 
          :value="input?.input?.postal_code"
          :error="validate?.input?.postal_code?.$errors?.[0]?.$message"
          @change="onChangePostalCode($event)"
        />
      </div>
      <div class="row">
        <InputSelect
          title="都道府県"
          size="full"
          :options="prefectureOptions"
          :value="input?.input?.prefecture"
          :error="validate?.input?.prefecture?.$errors?.[0]?.$message"
          @change="emit('update' , {key: 'prefecture', value: $event.target.value})"
        />
      </div>
      <div class="row">
        <InputText
          title="市区町村"
          placeholder="新宿区新宿"
          size="full"
          :value="input?.input?.city"
          :error="validate?.input?.city?.$errors?.[0]?.$message"
          @input="emit('update' , {key: 'city', value: $event.target.value})"
        />
      </div>
      <div class="row">
        <InputText
          title="丁目・番地"
          placeholder="1-36-2"
          size="full"
          :value="input?.input?.address"
          :error="validate?.input?.address?.$errors?.[0]?.$message"
          @input="emit('update' , {key: 'address', value: $event.target.value})"
        />
      </div>
      <div class="row">
        <InputText
          title="建物名・部屋番号など"
          placeholder="新宿第七葉山ビル 301"
          size="full"
          :value="input?.input?.building"
          :error="validate?.input?.building?.$errors?.[0]?.$message"
          @input="emit('update' , {key: 'building', value: $event.target.value})"
        />
      </div>
      <div class="row">
        <InputTel
          title="電話番号（半角数字・ハイフン）"
          placeholder="090-1234-5678"
          size="md"
          :value="input?.input?.phone"
          :error="validate?.input?.phone?.$errors?.[0]?.$message"
          @update="emit('update' , {key: 'phone', value: $event})"
        />
      </div>

      <div class="row">
        <InputEmail
          title="メールアドレス"
          placeholder="<EMAIL>"
          size="full"
          :value="input?.input?.email"
          :error="validate?.input?.email?.$errors?.[0]?.$message"
          @update="emit('update' , {key: 'email', value: $event})"
        />
      </div>

      <div class="row">
        <InputSelect
          title="招待状お渡し方法"
          size="full"
          :options="invitationDeliveryOptions"
          :value="String(input?.input?.invitation_delivery)"
          :error="validate?.input?.invitation_delivery?.$errors?.[0]?.$message"
          @change="emit('update' , {key: 'invitation_delivery', value: parseInt($event.target.value)})"
        />
      </div>
    </div>
    <hr>
    <div class="wrap freeItem">
      <h2>フリー項目</h2>
      <template v-for="(free_item_value, index) in input.free_item_values" :key="index">
        <h3>項目{{ index+1 }}</h3>
        <div class="row">
          <InputText
            title="項目名"
            size="full"
            placeholder="演出　など"
            :value="free_item_value?.name"
            :error="validate?.free_item_values?.[index]?.name?.$errors?.[0]?.$message"
            @input="emit('update' , {key: 'free_item_values.' + String(index) + '.name', value: $event.target.value})"
            />
        </div>

        <div class="row">
          <InputText
            title="内容"
            size="full"
            placeholder="お子様にサプライズを用意したい　など"
            :value="free_item_value?.content"
            :error="validate?.free_item_values?.[index]?.content?.$errors?.[0]?.$message"
            @input="emit('update' , {key: 'free_item_values.' + String(index) + '.content', value: $event.target.value})"
            />
          <div class="deleteRow">
            <button @click="emit('deleteFreeInput', index)">この項目を削除する</button>
          </div>
        </div>
      </template>
      
      <div class="row is-border-top">
        <div class="button_wrap">
          <button type="button" class="btn btn-primary-outline" @click="emit('addFreeInput')"><i class="icn-left material-icons">add</i>フリー項目を追加する</button>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  guestListId: string;
  // 上の階層で指定しているので型宣言は不要
  input: any,
  // 細かくなって見通し悪いのでanyに
  validate: any,
  parentGuest?: Guest | null;
  webInvitation?: any;
}

const props = withDefaults(defineProps<Props>(), {
  guestListId: '',
  input: () => { return {}},
  validate: () => { return {}},
  parentGuest: null,
  webInvitation: () => { return {}},
});

const emit = defineEmits([ 'update', 'showSubModal', 'closeSubModal', 'addGuest', 'deleteGuest', 'addFreeInput', 'deleteFreeInput']);


// 都道府県リスト
const prefectureOptions = computed(() => {
  let options = [{value: '', label: '選択してください'}];
  for (let i = 0; i < PREFECTURE_MASTER.length; i++) {
    const label = PREFECTURE_MASTER[i];
    options.push({
      value: label,
      label: label,
    });
  }
  return options;
});

// 招待状お渡し方法
const invitationDeliveryOptions = computed(() => {
  let options = [{value: '', label: '選択してください'}];
  for (const value in GUEST_INVITATION_DELIVERY_MASTER) {
    options.push({
      value: value,
      label: GUEST_INVITATION_DELIVERY_MASTER[value],
    });
  }
  return options;
});

const onChangePostalCode = (data:any) => {
  emit('update' , {key: 'postal_code', value: data.postal_code})
  if (data?.prefecture) emit('update' , {key: 'prefecture', value: data.prefecture})
  if (data?.city) emit('update' , {key: 'city', value: data.city})
  if (data?.address) emit('update' , {key: 'address', value: data.address})
};
</script>

<style lang="scss" scoped>
.wrap{
  max-width: 680px;
  padding: 23px 73px 20px 90px;
}
.row + .row{
  margin-top: 28px;
}
.edit{
  display: inline-block;
  font-size: 14px;
  line-height: 1.2;
  color: #B18A3E;
  padding: 10px 0;
  text-decoration: none;
  img{
    width: 18px;
    height: 18px;
    vertical-align: text-bottom;
  }
  &:hover{
    text-decoration: underline;
  }
}
.button_wrap{
  // max-width: 164px;
  text-align: right;
  margin: 0 0 0 auto;
  padding-top: 0;
  padding-bottom: 0;
  p {
    color: #aaa;
    margin-top: 5px;
  }
}
.text-right{
  text-align: right;
}
.is-border-top{
  border-top: 1px solid #F4F4F4;
  padding-top: 16px;
}
h2{
  color: #B18A3E;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 21px;
  img{
    vertical-align: text-bottom;
  }
}
h3{
  color: #333;
  font-size: 14px;
  font-weight: normal;
  margin: 20px 0;
  line-height: 1.2;
}
p{
  font-size: 12px;
  line-height: 1.45;
  color: #333;
  margin-bottom: 32px;
}
hr{
  border: 2px solid #F4F4F4;
}

.freeItem {
  .row {
    & + h3 {
      padding-top: 28px;
      border-top: 1px solid $color-lightgray;
    }
  }
}

.deleteRow {
  margin: 20px 0 0;
  text-align: right;
  button {
    position: relative;
    padding: 0 0 0 22px;
    color: $color-alert;
    font-size: 12px;
    line-height: 120%;
    letter-spacing: 0.24px;
    &::before {
      @include BA;
      left: 0;
      width: 18px;
      height: 18px;
      background-image: url(@/assets/images/icon-delete.svg);
    }
  }
}

@include sp {
.wrap{
  padding: 0 17px 20px 16px;
}
h2{
  font-size: 16px;
  margin: 0 0 21px;
}
.profileImage {
  .img {
    width: 72px;
    margin-right: 8px;
  }
  margin-bottom: 26px;
}
}
</style>
