<template>
  <div class="form">
    <div class="wrap">
      <p>セット商品 引き出物宅配などは別途手配のタイミングでお届け先情報の入力が必要になります </p>
      <InputRadio
        title="お届け先"
        name="deliveryAddress"
        :items="[
          {
            value: 1,
            checked: true,
            label: '個人宅',
          },
          {
            value: 2,
            checked: false,
            label: '式場・ホテル・会場',
          }
        ]"
      />
      <div class="row">
        <InputText
          title="姓"
          value="鈴木"
          size="half"
        />
        <InputText
          title="名"
          value="このみ"
          size="half"
        />
      </div>
      <div class="row">
        <InputText
          title="セイ"
          value="スズキ"
          size="half"
        />
        <InputText
          title="メイ"
          value="コノミ"
          size="half"
        />
      </div>

      <h3>ご住所</h3>
      <div class="row">
        <InputZipCode/>
      </div>

      <div class="row">
        <InputSelect
          title="都道府県"
          size="full"
        />
      </div>
      <div class="row">
        <InputText
          title="市区町村"
          size="full"
        />
      </div>
      <div class="row">
        <InputText
          title="丁目・番地"
          size="full"
        />
      </div>
      <div class="row">
        <InputText
          title="建物名・部屋番号など"
          size="full"
        />
      </div>
      <div class="row">
        <InputText
          title="電話番号"
          size="md"
        />
      </div>

      <div class="row">
        <InputCheck
          :items="[
            {
              value: 1,
              checked: false,
              label: 'このお届け先を保存する',
            }
          ]"
        />
        <div class="checkbox_note">
          ※次回以降入力する手間がなくなります <br>保存した内容はいつでも削除できます
        </div>
      </div>

      <div class="row">
        <InputRadio
          title="不在時の要望"
          name="deliveryOption"
          :items="[
            {
              value: 3,
              checked: true,
              label: '要望なし',
            },
            {
              value: 4,
              checked: false,
              label: '不在時は宅配BOXへ入れる',
            }
          ]"
        />
        <div class="note">上記要望を配送伝票に記載します <br>お名前やご住所を印刷する商品特性上 置き配は対応しておりません </div>
      </div>

      <div class="button_wrap">
        <ButtonMainColor size="md" @click="emits('submit', null)">確定する</ButtonMainColor>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
const emits = defineEmits<{
  (e: 'submit', v: null): void;
}>()
</script>

<style lang="scss" scoped>
.row + .row{
  margin-top: 28px;
}
.note{
  color: #333;
  background: #F4F4F4;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 4px;
  padding: 10px;
  margin: 12px 0;
}
.checkbox_note{
  font-size: 10px;
  color: #5A5A5A;
  line-height: 1.5;
  padding-left: 26px;
  margin-top: -6px;
  margin-left: 1em;
  text-indent: -1em;
}
.button_wrap{
  margin-top: 22px;
  max-width: 164px;
}
h2{
  color: #B18A3E;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 20px;
  img{
    vertical-align: text-bottom;
  }
}
h3{
  color: #333;
  font-size: 14px;
  font-weight: normal;
  margin: 20px 0;
  line-height: 1.2;
}
p{
  font-size: 12px;
  line-height: 1.45;
  color: #333;
  margin-bottom: 32px;
}
</style>
