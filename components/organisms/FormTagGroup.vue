<template>
  <div class="tagGroup">
    <div class="tagGroup_wrap">
      <h3 v-if="params.title">{{ params.title }}</h3>
      <p v-if="params.description">{{ params.description }}</p>
      <div class="tagInput">
        <div class="tagInput_title" v-if="params.formtitle">{{ params.formtitle }}</div>
        <div class="tagInput_wrap">
          <label>
            <input type="text">
          </label>
          <div class="tagInput_button">
            <ButtonMainColor baseColor="reversal">追加する</ButtonMainColor>
          </div>
        </div>
      </div>

      <h3 v-if="params.subtitle">{{ params.subtitle }}</h3>
      <ListTag
        v-for="item in tagList"
        :key="item.title"
        :title="item.title"
        :quantity="item.quantity"
        :required="item.required"
      ></ListTag>
    </div>
  </div>
</template>

<script lang="ts" setup>
export interface Props {
  title: String,
  description: String,
  formtitle: String,
  subtitle: String,
  data: Array,
}

const params = withDefaults(defineProps<Props>(), {
  title: '',
  description: '',
  formtitle: '',
  subtitle: '',
  data: [],
});

const tagList = params.data;
</script>

<style lang="scss" scoped>
.tagGroup{
  width: 100%;
  &_wrap{
    width: 100%;
    max-width: 460px;
    margin: 0 auto 20px;
  }
}
h3{
  font-size: 18px;
  font-weight: normal;
  color: #B18A3E;
  margin: 0;
  padding: 20px 0 13px;
}
.tagInput{
  margin: 27px 0 37px;
  &_title{
    font-size: 12px;
    color: #49454F;
    margin-bottom: 7px;
  }
  &_wrap{
    display: flex;
    align-items: center;
    label{
      max-width: calc(100% - 120px);
      width: 100%;
      margin-right: 16px;
    }
  }
  &_button{
    width: 100px;
    a.button--md.button--reversal {
      padding: 14px 10px 13px;
    }
  }
}
input{
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 12px;
  color: #333;
  padding: 14px 12px;
  width: 100%;
  box-sizing: border-box;
}
</style>
