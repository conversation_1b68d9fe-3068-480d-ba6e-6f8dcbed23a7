<template>
  <div class="form">
    <div class="wrap">
      <p>当社から確認が必要な際に ご連絡させていただきます </p>

      <div class="row">
        <InputText
          title="姓"
          value="鈴木"
          size="half"
        />
        <InputText
          title="名"
          value="このみ"
          size="half"
        />
      </div>
      <div class="row">
        <InputText
          title="セイ"
          value="スズキ"
          size="half"
        />
        <InputText
          title="メイ"
          value="コノミ"
          size="half"
        />
      </div>

      <div class="row">
        <InputText
          title="メールアドレス"
          :size="340"
        />
      </div>

      <div class="row">
        <InputText
          title="電話番号"
          size="md"
        />
      </div>

      <div class="row">
        <InputCheck
          :items="[
            {
              value: 1,
              checked: true,
              label: 'この情報で会員登録する',
            }
          ]"
        />
        <div class="checkbox_note">
          ※次回以降入力する手間がなくなります
        </div>
      </div>

      <div class="row">
        <InputText
          type="password"
          title="パスワード"
          :size="340"
        />
      </div>

      <div class="button_wrap">
        <ButtonMainColor size="md" @click="emits('submit', null);">確定する</ButtonMainColor>
      </div>

    </div>
  </div>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
}

const emits = defineEmits<{
  (e: 'submit', v: null): void;
}>()

</script>

<style lang="scss" scoped>
.row + .row{
  margin-top: 16px;
}
.note{
  color: #333;
  background: #F4F4F4;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 4px;
  padding: 10px;
  margin: 12px 0;
}
.checkbox_note{
  font-size: 10px;
  color: #5A5A5A;
  line-height: 1.5;
  padding-left: 26px;
  margin-top: -6px;
  margin-left: 1em;
  text-indent: -1em;
}
.button_wrap{
  max-width: 164px;
  margin-top: 20px;
}
h3{
  color: #333;
  font-size: 14px;
  font-weight: normal;
  margin: 20px 0;
  line-height: 1.2;
}
p{
  font-size: 12px;
  line-height: 1.45;
  color: #333;
  margin-bottom: 32px;
}
</style>
