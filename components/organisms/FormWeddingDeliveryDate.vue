<template>
  <div class="dates">
    <div class="row">
      <InputCalendar
        title="挙式日"
        placeholder="2027/04/01"
      />
      <div class="note">「挙式日」を設定すると〇〇することができます</div>
    </div>
    <div class="row">
      <InputCalendar
        title="お届け希望日"
      />
      <div class="note">「引き出物のお届け希望日」を設定すると〇〇することができます</div>
    </div>
  </div>
</template>

<script setup lang="ts">
</script>

<style lang="scss" scoped>
.dates{
  max-width: 640px;
  padding: 20px;
}
.row + .row{
  margin-top: 28px;
}
.note{
  color: #333;
  background: #F4F4F4;
  font-size: 12px;
  border-radius: 4px;
  padding: 10px;
  margin: 12px 0;
}
h3{
  font-size: 14px;
  font-weight: normal;
  margin: 20px 0;
  line-height: 1.2;
}
</style>
