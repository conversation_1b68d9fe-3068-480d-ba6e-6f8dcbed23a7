
<template>
<div class="drawer-menu" :class="{'close': ! getIsShowDropdownMenu()}">
  <div class="top">
    <button class="closerDrawermenu" @click="hideDropdownMenu()"><i class="material-symbols-outlined">close</i></button>
    <img src="@/assets/images/logo.svg" alt="favori" class="logo" />
  </div>

  <div class="inner">
    <div class="login mb-16">
      <template v-if="loginCheck?.loginCheck">
        <HeaderDrawerMenuLoggedIn />
      </template>
      <template v-else>
        <div class="login-inner unlogin">
          <p class="size--sm">ログインして 結婚式準備をもっと便利に！</p>
          <ButtonMainColor addClasses="btn-login" baseColor="accent" @click="showModalLogin">ログイン</ButtonMainColor>
          <ButtonMainColor addClasses="btn-registration" baseColor="reversal" @click="showModalRegister">新規会員登録</ButtonMainColor>
        </div>
      </template>
    </div>

    <Sidenavi />

    <div class="bottom">
      <ListSnsNavi />
    </div>

  </div>
</div>
<div v-if="getIsShowDropdownMenu()" class="bg" @click="hideDropdownMenu()"></div>
</template>

<script lang="ts" setup>
const { showModalLogin } = useModalLoginState();
const { showModalRegister } = useModalRegisterState();
const { getIsShowDropdownMenu, hideDropdownMenu } = useMenuState();

const { loginCheck } = useGetLoginCheck();
</script>

<style lang="scss" scoped>
.bg {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  background: rgba(#000, .4);
  cursor: pointer;
}
.drawer-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 100vh;
  height: 100dvh;
  padding: 0;
  background-color: $color-mainbackground;
  z-index: 10;
  transition: left 0.35s ease;
  &.close {
    left: -375px;
    @include sp {
      left: -100vw;
    }
  }
  .inner {
    height: calc(100vh - 55px);
    height: calc(100dvh - 55px);
    padding-bottom: 80px;
    overflow-y: auto;
  }

  .top {
    position: relative;
    height: 55px;
    padding: 15px 3px 13px 0;
    text-align: center;

    .closerDrawermenu {
      position: absolute;
      top: 0;
      right: 0;
      padding: 16px 12px;
    }

    .logo {
      width: 57px;
    }
  }

  .login {
    padding: 24px;
    // background-color: $color-lightbackground;
    background: $color-lightbackground url(/assets/images/bg-snav-box-info.jpg) center center no-repeat;
    background-size: cover;
    text-align: center;
    line-height: 145%;
    letter-spacing: 0.02em;
    font-size: 12px;

    .login-inner {
      padding: 24px;
      // background-color: $color-mainbackground;
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.95);

      &.unlogin {
        p {
          font-family: 'Noto Serif JP', serif;
          margin-bottom: 16px;
        }

        .btn-registration {
          margin-top: 12px;
        }
      }
    }
  }
}
@include sp {
.drawer-menu {
  width: calc(100vw - 55px);
  transition: left 0.35s ease;
  .inner {
    height: 100vh;
  }
  .top {
    img {
      display: none;
    }
    height: 0;
    padding: 0;
  }
  .top .closerDrawermenu {
    width: 55px;
    right: -55px;
    left: auto;
    color: #fff;
    background: url(@/assets/images/icon-close-b.svg) no-repeat center center/18px auto;
    background: none;
    i {
      font-size: 32px;
      line-height: 1;
    }
    img {
      visibility: hidden;
    }
  }
}
}
.drawer-menu {
  .bottom {
    margin-bottom: 40px;

    ul {
      margin-left: 25px;

      li {
        &~li {
          margin-left: 16px;
        }

        a {
          img {
            width: 40px;
          }
        }
      }
    }
  }
}
</style>