
<template>
  <div class="login-inner onlogin">
    <div class="mb-12"><img src="@/assets/images/txt-snav-loggedin.svg" alt="To a Lifetime of Happiness" width="180"></div>
    <strong class="loginname">{{ member?.memberMe?.last_name }} {{ member?.memberMe?.first_name }} <small>様</small></strong>
    <p class="weddingday" v-if="member?.memberMe?.wedding_info?.wedding_date">挙式日<span>{{ $dayjs(member?.memberMe?.wedding_info?.wedding_date).format('YYYY年MM月DD日') }}</span></p>
    <NuxtLink to="/mypage" class="btn btn-secondary btn-block"><i class="material-symbols-outlined icn-left">account_circle</i> マイページトップへ</NuxtLink>
  </div>
</template>

<script lang="ts" setup>
const { $dayjs } : any = useNuxtApp();
const { member, refetch } = useGetOneMemberMe();

onMounted(() => {
  refetch();
});
</script>

<style lang="scss" scoped>
.loginname {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1px;
  margin-bottom: 8px;
  font-weight: 400;
  font-size: 18px;
  line-height: 130%;
  letter-spacing: 0.04em;
  color: $color-blacktext2;
  font-family: 'Noto Serif JP', serif;
  align-items: baseline;
  // &::before {
  //   @include BA(relative);
  //   background-image: url(@/assets/images/icon-account_circle-big.svg);
  //   width: 33px;
  //   height: 33px;
  //   margin: -2px 10px 0 0;
  //   transform: none;
  // }
  small {
    display: inline-block;
    margin-left: 5px;
    line-height: 1;
  }
}
.weddingday {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  padding-left: 1px;
  font-size: 12px;
  line-height: 145%;
  letter-spacing: 0.02em;
  color: $color-accent;
  span {
    display: inline-flex;
    align-items: center;
    &::before {
      @include BA(relative);
      background-image: url(@/assets/images/icon-heart.svg);
      width: 16px;
      height: 16px;
      margin: 0 5px;
      transform: none;
    }
  }
}
.btn.btn-secondary {
  font-weight: bold;
}
</style>