<template>
<div v-for="(param, index) in articleData.param" :key="param">
  <ul class="listarticle">
    <ListBlogArticleBigDetail :data="param.datail" :assignMenu="showMenu" :assignClass="layout" :class="param.layout" @emitListBlogArticleDetail="eventListBlogArticleDetail" />
  </ul>
</div>
</template>
  
<script lang="ts" setup>
export interface Props {
  data: Array,
  assignClass?: String,
  assignMenu?: Array
}

const params = withDefaults(defineProps<Props>(), {
  assignClass: '',
  assignMenu: () => [
    {
      description: true,
      tags: true,
    }
  ]
});

const articleData = params.data;
const layout = params.assignClass;
const showMenu = params.assignMenu;

const emit = defineEmits(['eventListBlogArticleDetail']);

const eventListBlogArticleDetail = (target) => {
  emit('emitListBlogArticle' , target);
};
</script>
  
<style lang="scss" scoped>

.listarticle {
  display: flex;
  flex-wrap: wrap;
  width: 720px;
  margin: 0 auto;
}

@include sp {
.listarticle {
  display: block;
  width: 100%;
  border-bottom: 1px solid $color-grayborder;
}
}
</style>