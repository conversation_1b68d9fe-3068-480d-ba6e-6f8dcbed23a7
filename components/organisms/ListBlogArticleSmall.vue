<template>
<div v-for="(param, index) in articleData.param" :key="param">
  <ul class="listarticle" :class="layout">
    <ListBlogArticleSmallDetail :data="param.datail" :propsAssignArticle="showMenu" :class="param.layout" @emitListBlogArticleDetail="eventListBlogArticleDetail" />
  </ul>
</div>
</template>
  
<script lang="ts" setup>
export interface Props {
  data: Array,
  assignClass?: String,
  assignMenu?: Array
}

const params = withDefaults(defineProps<Props>(), {
  assignClass: '',
  assignMenu: () => [
    {
      description: true,
      tags: true,
    }
  ]
});

const articleData = params.data;
const layout = params.assignClass;
const showMenu = params.assignMenu;

const emit = defineEmits(['eventListBlogArticleDetail']);

const eventListBlogArticleDetail = (target) => {
  emit('emitListBlogArticle' , target);
};
</script>
  
<style lang="scss" scoped>

.listarticle {
  width: 300px;
  margin: 0 auto;
}

@include sp {
.listarticle {
  width: 100%;
}
}
</style>