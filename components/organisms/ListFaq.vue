<template>
  <div class="faq">
    <div class="faq_wrap">
      <div class="faq_title">
        <h2>よくある質問</h2>
        <div class="faq_button">
          <ButtonMainColor>追加する</ButtonMainColor>
        </div>
      </div>
      <ListFaqDetail
        v-for="(item, index) in faqList"
        :key="index"
        :question="item.question"
        :answer="item.answer"
        :link="item.link"
      ></ListFaqDetail>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const faqList = ref([
  {
    question: '通常納期で注文したのですが 後から特急納期に変更することはできますか？',
    answer: '1会員登録が完了していない可能性がございます \n登録完了のメールのログイン用メールアドレスをお確かめください \nパスワードを忘れた場合はログイン画面内の「パスワードを忘れた方」よりご変更ください ',
    link: 'https://www.google.com/'
  },
  {
    question: '2購入した商品をキャンセルしたいのですが ',
    answer: '会員登録が完了していない可能性がございます \n登録完了のメールのログイン用メールアドレスをお確かめください \nパスワードを忘れた場合はログイン画面内の「パスワードを忘れた方」よりご変更ください ',
    link: ''
  },
  {
    question: '3注文内容はどこで確認できますか？',
    answer: '会員登録が完了していない可能性がございます \n登録完了のメールのログイン用メールアドレスをお確かめください \nパスワードを忘れた場合はログイン画面内の「パスワードを忘れた方」よりご変更ください ',
    link: ''
  },
  {
    question: 'クーポンを入力するのを忘れてしまったのですが あとから適用することはできますか？',
    answer: '会員登録が完了していない可能性がございます \n登録完了のメールのログイン用メールアドレスをお確かめください \nパスワードを忘れた場合はログイン画面内の「パスワードを忘れた方」よりご変更ください ',
    link: ''
  }
]);
</script>

<style lang="scss" scoped>
h2{
  font-size: 20px;
  font-weight: bold;
  color: #6F8B81;
  border-bottom: 1px solid #6F8B81;
  padding: 0 0 12px 0;
  max-width: 336px;
  width: 100%;
}

.faq{
  &_wrap{
    max-width: 1064px;
    padding: 48px 20px 72px;
    margin: 0 auto;
  }
  &_title{
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &_button{
    max-width: 190px;
    width: 100%;
  }
}
</style>
