<template>
  <div class="products">
    <div class="wrap">
      <h2>こちらの商品がおすすめです</h2>
      <swiper-container
        class="slide"
        slides-per-view="5"
        speed="500"
        loop="true"
        :navigation="{
          clickable: true
        }"
        :space-between="spaceBetween"
        :breakpoints="{
          768: {
            slidesPerView: 5,
          },
        }"
      >
        <swiper-slide v-for="slide in params.items" :key="slide">
          <ListItemsFeaturedDetail
            :thumbnail="slide.thumbnail"
            :title="slide.title"
            :labels="slide.labels"
            :price="slide.price"
            :priceStrike="slide.priceStrike"
          />
        </swiper-slide>
      </swiper-container>
      <div class="button_wrap">
        <ButtonMainColor>一覧でもっと探す</ButtonMainColor>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { register } from 'swiper/element/bundle';
register();

const params = defineProps({
  items: Array,
});
</script>

<style lang="scss" scoped>
.wrap{
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}
.button_wrap{
  max-width: 164px;
  width: 100%;
  margin: 32px auto;
}
h2{
  font-size: 18px;
  font-weight: normal;
  color: #333;
  margin-bottom: 24px;
  text-align: center;
}

:root {
  --swiper-navigation-color: red;
}

:deep(.swiper-button-next){
  color: #fff;
}
</style>

<style>
:root{
  --swiper-navigation-color: rgba(0,0,0,0.5);
}
</style>