<template>
  <div class="products">
    <div class="wrap">

      <h2><slot /></h2>
      <ul class="listitems">
      <swiper-container controller-control=".swiper-2"
        class="slide"
        slides-per-view="5"
        speed="500"
        loop="true"
        :navigation="{
          clickable: true
        }"
        :space-between="spaceBetween"
        :breakpoints="{
          768: {
            slidesPerView: 5,
          },
        }"
      >
        <swiper-slide v-for="(slide, index) in datail.datail" :key="slide">
        <li class="items">
          <ListItemsDetail
             :data="slide"
             :ranking="datail.ranking"
             :index="index"
             :class="datail.layout"
             @emitToggleInFavorite="eventToggleInFavorite"
          />
        </li>
        </swiper-slide>
      </swiper-container>
      </ul>

    </div>
  </div>
</template>

<script lang="ts" setup>
import { register } from 'swiper/element/bundle';
register();

const params = defineProps({
  items: Array,
});


const datail = params.items[0]; 
// console.log(datail.datail);
</script>

<style lang="scss" scoped>
.wrap{
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}
.button_wrap{
  max-width: 164px;
  width: 100%;
  margin: 32px auto;
}
h2{
  font-size: 18px;
  font-weight: normal;
  color: #333;
  margin-bottom: 24px;
  text-align: center;
}

:root {
  --swiper-navigation-color: red;
}

swiper-container::part(button-next) ,
swiper-container::part(button-prev) {
  width: 32px;
  height: 52px;
  background: url(@/assets/images/icon-button-next.svg) no-repeat center center/contain;
  &:after {
    font-size: 0;
  }
}
swiper-container::part(button-prev) {
  transform: scale(-1, 1);
}
</style>

<style>
:root{
  --swiper-navigation-color: rgba(0,0,0,0.5);
}
</style>