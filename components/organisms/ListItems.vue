
<template>
<div v-for="(param, index) in itemsData.param" :key="param">

  <ul class="listitems">
    <li class="items" v-for="(datail, index) in param.datail" :key="datail">
    <ListItemsDetail :data="datail" :ranking="param.ranking" :index="index" :class="param.layout" @emitToggleInFavorite="eventToggleInFavorite" />
    </li>
  </ul>
</div>
</template>
  
<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const itemsData = params.data;

const emit = defineEmits(['eventToggleInFavorite']);

const eventToggleInFavorite = (target) => {
  emit('emitInFavorite' , target);
};
</script>

<style lang="scss" scoped>
.listitems {
  display: flex;
  flex-wrap: wrap;
  width: 1025px;
  max-width: 100%;
  margin: 0 auto;
  .items {
    width: 193px;
    & ~ .items {
      margin-left: 15px;
    }
    &:nth-of-type(5n+1) {
      margin-left: 0;
    }
    &:nth-of-type(n+6) {
      margin-top: 22px;
    }
  }
}

@include sp {
.listitems {
  justify-content: space-between;
  width: 100%;
  max-width: 350px;
  .items {
    width: 163px;
    & ~ .items {
      margin-left: 0;
    }
    &:nth-of-type(n+3) {
      margin-top: 18px;
    }
  }
}
}
</style>