<template>
<div v-for="(param, index) in itemsData.param" :key="param">
  <ListItemsRankingDetail :data="param.datail" :itemsMaxRanking="param.maxrank" :ranking="param.ranking" :class="param.layout" @emitToggleInFavorite="eventToggleInFavorite" />
</div>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const itemsData = params.data;

const emit = defineEmits(['eventToggleInFavorite']);

const eventToggleInFavorite = (target) => {
  emit('emitInFavorite' , target);
};
</script>

<style lang="scss" scoped>
// 
</style>