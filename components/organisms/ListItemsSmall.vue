<template>
<div v-for="(param, index) in itemsData.param" :key="param" :class="param.buttonlayout">
  <ListItemsSmallDetail :data="param.datail" :class="param.layout" @emitListItemsSmallDetail="eventListItemsSmallDetail" />
</div>
</template>
  
<script lang="ts" setup>
const params = defineProps({
  data: Array,
});
const itemsData = params.data;

const emit = defineEmits(['emitListItemsSmall']);

const eventListItemsSmallDetail = (target) => {
  emit('emitListItemsSmall' , target);
};
</script>

<style lang="scss" scoped>
.suttitle-footer-top {
  margin: 0 0 8px;
  line-height: 1.2;
  font-size: 12px;
  font-weight: bold;
}
ul {
  & ~ .suttitle-footer-top {
    margin-top: 40px;
  }
}
</style>