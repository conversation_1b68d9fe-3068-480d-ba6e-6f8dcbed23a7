
<template>
<Modal id="ModalBulkAddGuest">

  <template #header>
    {{ params.title }}
  </template>
  <template #main>
    <p v-html="params.lead"></p>
    <div class="selectList">
      <TabTrigger :data="tabList" />
    </div>
    <ul class="wrapGuestList tabsbj">
      <li v-for="(guestList, index) in equipGuestList.datail" :key="guestList" :class="index === 0?'is-active':''">
        <ShowGuestList :data="guestList" :partsHidden="apiPartsHidden" />
      </li>
    </ul>
  </template>
  <template #footer>
    <ShowFooterBarFrow :data="apiFooterBarFrow" />
  </template>
</Modal>

</template>

<script lang="ts" setup>
export interface Props {
  title: String,
  lead: String,
}

const params = withDefaults(defineProps<Props>(), {
  title: '',
  lead: '',
});

const apiPartsHidden = {
  manage: 'hidden',
  statusList: 'hidden',
  status: 'hidden',
  icon: 'hidden',
  group: 'hidden',
}

const apiFooterBarFrow = {
  datail: [
    {
      type: 'message',
      data: [
        {
          strong: '3',
          text: '件 選択されています',
        },
      ],
    },
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "キャンセル",
          link: "#01link",
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "連名に追加する",
          link: "#0link",
        },
      ],
    },
  ],
}

const apiGuestList = {
  datail: [
    {
      label: 'メインリスト',
      guests: [
        {
          name: '中谷 隆之介',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '佐伯 美佐',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '日下 勝久',
          icon: '/images/sample/guest_image.png',
          status: '欠席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '佐伯 美佐',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '日下 勝久',
          icon: '/images/sample/guest_image.png',
          status: '欠席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '佐伯 美佐',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '日下 勝久',
          icon: '/images/sample/guest_image.png',
          status: '欠席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
      ],

    },
    {
      label: '2次会',
      guests: [
        {
          name: '中谷 隆之介',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '佐伯 美佐',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
      ],
    },
  ]
}

const tabList = [
  'メインリスト',
  '2次会',
]

const equipGuestList = computed(() => {
  let data = apiGuestList;
  return data;
});
</script>

<style lang="scss" scoped>
#ModalBulkAddGuest {
  
  .selectList {
    display: flex;
    border-bottom: 1px solid $color-grayborder;
    :deep(ul) {
      li {
        span {
          display: inline-block;
          padding: 16px 28px 14px;
          border-bottom: 2px solid transparent;
          text-align: center;
          font-size: 14px;
          line-height: 120%;
        }
        .addGuestList {
          position: relative;
          padding-left: 22px;
          font-size: 14px;
          line-height: 100%;
          color: inherit;
          &::before {
            @include BA;
            left: 3px;
            width: 12px;
            height: 12px;
            background-image: url(@/assets/images/icon-plus-gl.svg);
          }
        }
        &:hover ,
        &.is-active {
          span {
            border-bottom-color: $color-main;
            color: $color-main;
          }
          .addGuestList::before {
            background-image: url(@/assets/images/icon-plus.svg);
          }
        }
      }
    }
  }

  :deep(.contents) {
    padding: 17px 60px;
    .contentsInner > p {
      margin: 0 0 11px;
      color: $color-blackLight;
      font-size: 14px;
      line-height: 150%;
      letter-spacing: 0.35px;
    }
  }
  :deep(.subTitle) {
    padding: 25px 0 3px;
    p {
      color: $color-blacktext2;
      text-align: right;
      font-size: 12px;
      line-height: 100%;
      letter-spacing: 0.1px;
      strong {
        color: $color-maindark;
        font-size: 18px;
        font-weight: 400;
        line-height: 100%;
        letter-spacing: 0.1px;
      }
    }
  }
  :deep(.searchList) {
    padding: 20px 0 24px;
    border-top-width: 0;
  }
  :deep(tr) {
    &.header {
      th ,
      td {
        font-size: 14px;
      }
    }
    .check {
      padding-left: 0;
    }
    td {
      p {
        margin-bottom: 0;
        line-height: 120%;
        color: $color-blacktext2;
      }
      &.name {
        width: 24.0%;
      }
    }
  }
}

@include sp {
#ModalBulkAddGuest {
  .selectList {
    :deep(ul) {
      li {
        span {
          padding: 16px 15px 14px;
        }
      }
    }
  }
  :deep(.contents) {
    padding: 11px 0;
    .contentsInner > p {
      margin-bottom: 22px;
      padding: 0 12px;
      line-height: 160%;
      letter-spacing: 0;
    }
  }
  :deep(.subTitle) {
    padding: 24px 16px 0;
    p {
      margin-bottom: 7px;
      font-size: 12px;
      strong {
        font-size: 20px;
      }
    }
  }
  :deep(.searchList) {
    padding: 13px 0 24px;
  }
  :deep(tr) {
    &.header {
      th {
        &.name {
          width: 19%;
        }
      }
    }
    th ,
    td {
      &.name {
        width: 19%;
      }
    }
  }
}
}
</style>