
<template>
  <Modal id="ModalManageTag">
    <template #main class="wrap">
      <div class="image">
        <img :style="{'minWidth': zoom + '%', 'width': zoom + '%'}" src="/images/sample/cartItemZoom.png">
      </div>
      <div class="btn_wrap">
        <a class="btn is-pdf"><img src="@/assets/images/icon-pdf-download.svg"></a>
        <a class="btn is-zoom is-out" @click="onChangeZoom('out')"><img src="@/assets/images/icon-zoom-out.svg"></a>
        <a class="btn is-zoom is-fit" @click="onChangeZoom('fit')"><img src="@/assets/images/icon-zoom-fit.svg"></a>
        <a class="btn is-zoom is-in" @click="onChangeZoom('in')"><img src="@/assets/images/icon-zoom-in.svg"></a>
      </div>
    </template>
    <template #footer>
      <div class="footer_wrap">
        <ButtonMainColor>編集する</ButtonMainColor>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>

let zoom = ref(100);
const onChangeZoom = (mode) => {
  if(mode === 'in'){
    zoom.value += 20;
  }else if(mode === 'fit'){
    zoom.value = 100;
  }else if(mode === 'out'){
    zoom.value -= 20;
  }
}

</script>

<style lang="scss" scoped>
:deep(.modalContainer) {
  .contents {
    background: #F1F1F1;
    padding: 30px 15px;
  }
}
:deep(.modalContainer .contents) {
  height: calc(100vh - 150px);
  height: calc(100dvh - 150px);
}
.wrap{
  position: relative;
}
.image{
  position: relative;
  text-align: center;
  overflow: auto;
}
.btn_wrap{
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 80px;
  left: 0;
  right: 0;
  margin: auto;
  a{
    display: block;
    width: 36px;
  }
}
.btn{
  margin-right: 8px;
  cursor: pointer;
  &.is-pdf{
    margin-right: 12px;
    padding-right: 12px;
    border-right: 1px solid #DCDCDC;
    box-sizing: content-box;
  }
}
.footer_wrap{
  padding: 14px 12px;
}
@include sp {
:deep(.modalContainer) {
  .contents {
    padding-top: 18px;
  }
}
}
</style>