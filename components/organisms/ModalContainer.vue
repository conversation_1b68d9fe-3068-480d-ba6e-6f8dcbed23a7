
<template>
<div class="modalContainer">

  <div class="header">
    <p><slot name="header" /></p>
    <button class="modalClose" @click.prevent="closeModal($event)"><img src="@/assets/images/icon-close-b.svg" alt="閉じる" /></button>
  </div>

  <div class="contents">
    <div class="contentsInner">
      <slot name="main" />
    </div>
  </div>

  <div class="footer">
    <slot name="footer" />
  </div>

</div>
</template>

<script lang="ts" setup>
const emit = defineEmits(['closeModal']);

const closeModal = (target) => {
  emit('emitClose' , target);
};
</script>

<style lang="scss" scoped>
.modalContainer {
  position: relative;
  width: 640px;
  background-color: $color-mainbackground;
  .header {
    position: relative;
    padding: 14px 40px 14px 20px;
    border-bottom: 1px solid $color-lightgray;
    p {
      font-size: 18px;
      line-height: 120%;
      letter-spacing: 0.04em;
      color: $color-blacktext2;
    }
    .modalClose {
      display: none;
      position: absolute;
      top: 12px;
      right: 12px;
    }
  }
  .contents {
    display: flex;
    justify-content: center;
    max-height: calc(100vh - 150px);
    max-height: calc(100dvh - 150px);
    padding: 30px 90px;
    overflow-y: auto;
    .contentsInner {
      width: 100%;
      min-width: 0;
      min-height: 135px;
    }
  }
  .footer {
    background-color: $color-mainbackground;
    :slotted(.frowFooter) {
      position: relative;
    }
  }
  &.standAlone {
    .modalClose {
      display: inline-block;
    }
  }
}

@include sp {
.modalContainer {
  width: 100%;
  min-height: 100vh;
  min-height: 100dvh;
  .header {
    padding: 15px 40px 10px 16px;
    border-bottom-width: 3px;
    p {
      font-size: 16px;
      line-height: 120%;
      letter-spacing: 0.02em;
      color: $color-blacktext2;
    }
    .modalClose {
      right: 15px;
    }
  }
  .contents {
    height: calc(100vh - 120px);
    height: calc(100dvh - 120px);
    padding: 18px 15px 30px;
  }
  .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }
}
}
</style>