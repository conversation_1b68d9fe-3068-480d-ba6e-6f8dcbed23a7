<template>
  <Modal v-if="isShow" @close="onClickCloseModal()">
    <template #header>
      ゲストリストを複製する
    </template>
    <template #main>
      <Loading v-if="isLoading" :isShowIcon="true"></Loading>
      <p v-if="error" class="input-error">{{ error }}</p>
      <strong>{{ guestList.name }} を複製して新しいゲストリストを作成します</strong>
      <form class="searchform">
        <InputText 
          title="リスト名"
          :required="true" 
          size="full" 
          :value="String(input.name)"
          placeholder="例) 2次会"
          :error="getValidationMessage(v$.name)"
          @input="input.name = $event.target.value"
          />
      </form>
      <div class="attention">
        <p>複製先のリストに登録されるゲスト情報は複製した時点での内容になります<br />
複製元のリストにゲストの情報を追加しても<br />
追加した内容は複製したゲストリストには反映されません</p>
      </div>
    </template>
    <template #footer>
      <footer class="modal-footer">
        <button class="btn btn-default-outline btn-block" @click="onClickCloseModal()">キャンセル</button>
        <button class="btn btn-secondary btn-block" @click="onClickSave()">完了</button>
      </footer>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { CreateGuestListInput } from '@/composables/generated';
import type { GraphQLValidationErrors } from "@/utils/graphql";

const props = defineProps<{
  guestList: {
    id: string;
    name: string;
    member: {
      id: string;
    }
  };
}>();

const emits = defineEmits<{
  (e: 'close'): void;
  (e: 'created', $event: string): void;
}>()

// 入力項目
const input = ref({
  name: ''
} as CreateGuestListInput)

const isShow = ref(true)

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return {
    name: { 
      required: helpers.withMessage(validationMessage.required('ゲストリスト名'), required) 
    },
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });


/**
 * 登録
 */
// モーダル表示時
onMounted(() => {
  isShow.value = true;
});

// モーダル非表示
const onClickCloseModal = () => {
  isShow.value = false;
  emits('close')
};

// 登録API
const { copy, errors } = useCopyGuestList();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await copy(props.guestList.id, input.value.name);
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    isLoading.value = false;
    return false;
  }

  // モーダル閉じる
  emits('created', 'ゲストリスト「'+input.value.name+'」を追加しました')
};
</script>

<style lang="scss" scoped>
.input-error {
  text-align: center;
  margin-bottom: 20px;
}
strong {
  display: block;
  margin-bottom: 22px;
  font-weight: 400;
  color: $color-blacktext2;
  font-size: 14px;
  line-height: 150%;
  letter-spacing: 0.28px;
}
:deep(input) {
  font-size: 14px;
  padding: 12px 12px 11px;
}
.attention {
  margin-top: 20px;
  border-radius: 4px;
  background: $color-lightgray;
  padding: 10px;
  margin-bottom: 60px;
  p {
    color: $color-blacktext2;
    font-size: 12px;
    line-height: 145%;
    letter-spacing: 0.24px;
    margin-bottom: 0;
  }
}
</style>