<template>
  <Modal ref="modal" class="modalCreateGuest" @close="$emit('close')" :isShowSubModal="isShowSubModal">
    <template #header>
      <span v-if="parentGuest">連名ゲストを追加</span>
      <span v-else>個別に登録する</span>
    </template>
    <template #main>
      <span v-if="error" class="input-error">{{ error }}</span>
      <Loading v-if="isLoading"></Loading>
      <FormPrivateInformation 
        :input="input"
        :guestListId="guestListId"
        :parentGuest="parentGuest"
        :validate="v$"
        @update="onUpdateInput" 
        @showSubModal="isShowSubModal = true" 
        @closeSubModal="isShowSubModal = false" 
        @addGuest="onClickAddGuest"
        @deleteGuest="onClickDeleteGuest"
        @addFreeInput="onClickAddFreeInput"
        @deleteFreeInput="onClickDeleteFreeInput"
      />
    </template>
    <template #footer>
      <footer class="modal-footer">
        <button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
        <button class="btn btn-secondary btn-block" @click="onClickSave()">登録する</button>
      </footer>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, email, numeric, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";

interface Props {
  guestListId: string;
  parentGuest?: Guest | null;
}

const props = withDefaults(defineProps<Props>(), {
  parentGuest: null
});

const emit = defineEmits<{
  close: []
  reload: []
}>()

const isShowSubModal = ref(false)

/**
 * 編集
 */
const input = ref({
  input: {
    guest_list_id: props.guestListId,
  } as GuestInput,
  guests: [] as GuestInput[],
  free_item_values: [
    {name: '', content: ''}
  ] as CreateGuestFreeItemValueInput[],
  guest_event_answers: [
  ] as CreateGuestEventAnswerInput[]
});

// APIから guestList を読み込み
const { guestList, refetch } = useGetOneGuestList(String(props.guestListId));
onMounted(async () => {
  setGuestEventAnswers();
  await refetch();
  setGuestEventAnswers();
});
const setGuestEventAnswers = () => {
  input.value.guest_event_answers = [];
  if (! guestList.value?.event_list) return false;
  const event_list = Object.values(guestList.value?.event_list);
  // 出欠をSET
  for (let i = 0; i < event_list.length; i++) {
    const event = event_list[i];
    input.value.guest_event_answers.push({
      name: event,
      // date: null,
      attendance: null
      // attendance: GUEST_ATTENDANCE_MASTER.PENDING,
      // payment_amount: null
    })
  }
};

const rules = computed(() => {
  // 基本情報バリデーション
  let rules = {
    input: {
      parent_guest_id: {},
      image: {
        noValidation
      },
      last_name: { 
        required: helpers.withMessage(validationMessage.required('姓'), required) 
      },
      first_name: { 
        required: helpers.withMessage(validationMessage.required('名'), required) 
      },
      last_name_romaji: { noValidation },
      first_name_romaji: { noValidation },
      last_name_kana: { noValidation },
      first_name_kana: { noValidation },
      guest_type: { noValidation },
      guest_honor: { noValidation },
      gender: { noValidation },
      guest_title: { noValidation },
      relationship: { noValidation },
      relationship_name: { noValidation },
      birthdate: {
        date: helpers.withMessage(validationMessage.date('お誕生日'), validationDate),
      },
      allergies: { noValidation },
      allergy: { noValidation },
      guest_list_id: { noValidation },
      postal_code: { 
        format: helpers.withMessage(validationMessage.format('郵便番号'), validationPostalCode),
      },
      prefecture: { noValidation },
      city: { noValidation },
      address: { noValidation },
      building: { noValidation },
      phone: {},
      email: {
        // required: helpers.withMessage(validationMessage.required('メールアドレス'), required),
        email: helpers.withMessage(validationMessage.email('メールアドレス'), email),
      },
      // message: { noValidation },
      invitation_delivery: { noValidation }
    }
  } as any;

  rules.guests = [];
  for (let i = 0; i < input.value.guests.length; i++) {
    rules.guests.push({
      parent_guest_id: { noValidation },
      image: {
      },
      last_name: { 
        required: helpers.withMessage(validationMessage.required('姓'), required) 
      },
      first_name: { 
        required: helpers.withMessage(validationMessage.required('名'), required) 
      },
      last_name_romaji: { noValidation },
      first_name_romaji: { noValidation },
      last_name_kana: { noValidation },
      first_name_kana: { noValidation },
      guest_honor: { noValidation },
      gender: { noValidation },
      guest_title: { noValidation },
      relationship: { noValidation },
      birthdate: {
        date: helpers.withMessage(validationMessage.date('お誕生日'), validationDate),
      },
      allergies: { noValidation },
      allergy: { noValidation },
    });
  }

  rules.free_item_values = [];
  for (let i = 0; i < input.value.free_item_values.length; i++) {
    if (input.value.free_item_values[i]?.name || input.value.free_item_values[i]?.content) {
      rules.free_item_values.push({
        name: { 
          required: helpers.withMessage(validationMessage.required('項目名'), required) 
        },
        content: { 
          required: helpers.withMessage(validationMessage.required('内容'), required) 
        },
      });
    } else {
      rules.free_item_values.push({});
    }
  }
  return rules;
});

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 値を更新
const onUpdateInput = async (e: {
  key: string;
  value: any;
})  => {
  // free_item_values.0.name 的なStringを free_item_values[0].name に変えて値を入れる
  if (e.key.indexOf('.') === -1) e.key = 'input.'+e.key;
  setNestedValue(input.value, e.key, e.value);
};

// 編集エリアTOPにスクロール
const modal = ref();
const scrollPageTop = () => {
  if (modal.value) modal.value.scrollTopModal();
};

// 更新API
const { create, errors } = useCreateGuest();

// 更新中のLoading
const isLoading = ref(false);

// タグの新規追加
const { guestTags, refetch:refetchGuestTag } = useGetManyGuestTag(props.guestListId)
const { create:createGuestTag } = useCreateGuestTag();

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    scrollPageTop();
    return false;
  }
  isLoading.value = true;

  // 要確認 : 住所とかが必須みたい
  let guests = JSON.parse(JSON.stringify(input.value.guests));
  for (let i = 0; i < guests.length; i++) {
    delete guests[i].image;
    guests[i].image_url = input.value.guests[i].image?.uuid;
    guests[i].postal_code = input.value.input.postal_code;
    guests[i].prefecture = input.value.input.prefecture;
    guests[i].city = input.value.input.city;
    guests[i].address = input.value.input.address;
    guests[i].building = input.value.input.building;
    guests[i].phone = input.value.input.phone;
    // guests[i].message = input.value.input.message;
    guests[i].guest_tag_guests = await getGuestTagGuests(guests[i].guest_tag_guests);
  }

  if (props.parentGuest) {
    input.value.input.parent_guest_id = props.parentGuest.id;
  } 

  // フリー項目
  let free_item_values = [];
  for (let i = 0; i < input.value.free_item_values.length; i++) {
    const free_item_value = input.value.free_item_values[i];
    if (free_item_value?.name && free_item_value?.content) {
      free_item_values.push(free_item_value);
    }
  }

  let guest = JSON.parse(JSON.stringify(input.value.input));
  delete guest.image;
  guest.guest_tag_guests = await getGuestTagGuests(guest.guest_tag_guests);
  guest.image_url = input.value.input.image?.uuid;

  const isSuccess = await create(guest, guests, free_item_values, input.value.guest_event_answers);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    scrollPageTop();
    return false;
  }

  // 一覧を再描画
  emit('reload');
  // モーダル閉じる
  emit('close');
};

const onClickAddGuest = () => {
  const guest = {
    guest_list_id: props.guestListId,
    image_url: '',
    last_name: '',
    first_name: '',
    last_name_romaji: '',
    first_name_romaji: '',
    last_name_kana: '',
    first_name_kana: '',
    guest_honor: '',
    // gender: 0,
    guest_title: '',
    relationship: '',
    allergies: [],
    allergy: '',
    birthdate: ''
  } as GuestInput;
  input.value.guests.push(guest);
}

const onClickDeleteGuest = (index:number) => {
  let newGuests = [];
  for (let i = 0; i < input.value.guests.length; i++) {
    if (i == index) continue;
    const guest = input.value.guests[i];
    newGuests.push(guest);
  }
  input.value.guests = newGuests;
};

const onClickAddFreeInput = () => {
  const free_item_value = {
    name: '',
    content: '',
  } as CreateGuestFreeItemValueInput;
  input.value.free_item_values.push(free_item_value);
}

const onClickDeleteFreeInput = (index:number) => {
  let newFreeInputs = [];
  for (let i = 0; i < input.value.free_item_values.length; i++) {
    if (i == index) continue;
    const free_item_value = input.value.free_item_values[i];
    newFreeInputs.push(free_item_value);
  }
  input.value.free_item_values = newFreeInputs;
};

const getGuestTagGuests = async (guest_tag_guests = [] as any[]) => {
  let result = []
  for (let i = 0; i < guest_tag_guests.length; i++) {
    let tag = guest_tag_guests[i].id;
    if (! tag) continue;

    // タグが完了かスペースで追加された場合、tagにはIDじゃない文字列が入るので、それは保存する
    const guestTagIndex = guestTags.value.findIndex(item => item.id == tag);
    if (guestTagIndex === -1) {
      await createGuestTag({guest_list_id: props.guestListId, tag: tag});
      await refetchGuestTag();
      const newGuestTag = guestTags.value.find(item => item.tag == tag);
      if (! newGuestTag) continue;
      result.push({
        guest_tag_id: newGuestTag.id
      })
    } else {
      // すでに登録されてるタグの場合
      result.push({
        guest_tag_id: guest_tag_guests[i].id
      })
    }
  }
  return result;
}
</script>

<style lang="scss" scoped>
.modalWrap.modalCreateGuest {
  :deep(.modalContainer) {
    .contents {
      padding: 3px 5px;
    }
  }
}
@include sp {
.modalWrap.modalCreateGuest {
  :deep(.modalContainer) {
    .contents {
      padding-top: 18px;
    }
  }
}
}
.input-error {
  text-align: center;
  margin-top: 20px;
}
</style>