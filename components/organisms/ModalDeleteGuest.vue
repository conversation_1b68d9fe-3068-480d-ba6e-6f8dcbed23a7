<template>
  <Modal class="modalLogout" size="sm" @close="emits('close')">
    <template #header>
      リストからゲストを削除
    </template>
    <template #main>
      <Loading v-if="isLoading"></Loading>
      <p v-if="error" class="input-error">{{ error }}</p>
      <p>以下の{{guest.children_guests.length + 1}}名のゲスト情報をまとめて削除してもよろしいですか？</p>
      <div>
        {{ guest?.last_name }} {{ guest?.first_name }} {{ guest?.guest_honor }}
      </div>
      <div v-for="(children_guest, index) in guest?.children_guests" :key="index">
        {{ children_guest?.last_name }} {{ children_guest?.first_name }} {{ children_guest?.guest_honor }}
      </div>
      <p class="mt-15 mb-0">削除したあと ゲスト情報を復元することはできません</p>
    </template>
    <template #footer>
      <a href="#" @click="emits('close')">キャンセル</a>
      <a href="#" class="color-alert" @click="onClickDelete()">削除する</a>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const router = useRouter();

export interface Props {
  guestListId: string;
  guest: Guest;
}

const props = withDefaults(defineProps<Props>(), {
  guestListId: '',
});

const emits = defineEmits<{
  (e: 'close'): void;
}>()

// 全体エラー
const error = ref('')

// 更新API
const { action: deleteAction, errors } = useDeleteGuest();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickDelete = async() => {
  // 全体エラーをリセット
  error.value = '';
  if (! props.guest?.id) return false;

  isLoading.value = true;

  const isSuccess = await deleteAction({id: props.guest.id, is_delete_all: true});
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }
  
  isLoading.value = false;
  router.push({ path: '/mypage/guest-list/'+props.guestListId })
};
</script>