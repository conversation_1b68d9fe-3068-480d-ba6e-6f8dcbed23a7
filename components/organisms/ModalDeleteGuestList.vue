<template>
  <Modal size="sm" v-if="isShow" @close="onClickCloseModal()">
    <template #header>
      ゲストリストを削除
    </template>
    <template #main>
      <Loading v-if="isLoading"></Loading>
      <p v-if="error" class="input-error">{{ error }}</p>
      <p>ゲストリスト「{{props.guestList.name}}」を削除してもよろしいですか？<br><br>削除したあとに復元することはできません</p>
    </template>
    <template #cancel><span @click="onClickCloseModal()">キャンセル</span></template>
    <template #do><span data-event="delete" @click="onClickDelete()">削除する</span></template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const props = defineProps<{
  guestList: {
    id: string;
    name: string;
    member: {
      id: string;
    }
  };
}>();

const emits = defineEmits<{
  (e: 'close'): void;
  (e: 'deleted', $event: string): void;
}>()

// 入力項目
const input = ref({
  id: props.guestList.id,
} as {
  id: string;
})

const isShow = ref(true)

// 全体エラー
const error = ref('')

/**
 * 登録
 */
// モーダル表示時
onMounted(() => {
  isShow.value = true;
});

// モーダル非表示
const onClickCloseModal = () => {
  isShow.value = false;
  emits('close')
};

// 登録API
const { action: deleteAction, errors } = useDeleteGuestList();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickDelete = async() => {
  isLoading.value = true;
  const isSuccess = await deleteAction(input.value);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }

  // モーダル閉じる
  emits('deleted', 'ゲストリスト「'+props.guestList.name+'」を削除しました')
};
</script>

<style lang="scss" scoped>
.input-error {
  text-align: center;
  margin-bottom: 20px;
}
</style>