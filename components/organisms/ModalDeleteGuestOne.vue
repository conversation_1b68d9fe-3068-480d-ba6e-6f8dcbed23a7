<template>
  <Modal class="modalLogout" size="sm" @close="emits('close')">
    <template #header>
      リストからゲストを削除
    </template>
    <template #main>
      <Loading v-if="isLoading"></Loading>
      <p v-if="error" class="input-error">{{ error }}</p>
      {{ guest?.last_name }} {{ guest?.first_name }} {{ guest?.guest_honor }}のゲスト情報を{{ guest?.guest_list?.name }}から削除してもよろしいですか？<br>
      <br>
      削除したあと ゲスト情報を復元することはできません
    </template>
    <template #footer>
      <a href="javascript:void(0);" @click="emits('close')">キャンセル</a>
      <a href="javascript:void(0);" class="color-danger" @click="onClickDelete()">削除する</a>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const router = useRouter();

export interface Props {
  guestListId: string;
  guest: Guest;
  isReload?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  guestListId: '',
  isReload: false
});

const emits = defineEmits<{
  (e: 'close'): void;
  (e: 'reload'): void;
}>()

// 全体エラー
const error = ref('')

// 更新API
const { action: deleteAction, errors } = useDeleteGuest();
// 連名者のゲスト情報の「筆頭者ID」を変更
const { update, errors: updateErrors } = useUpdateGuest();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickDelete = async() => {
  // 全体エラーをリセット
  error.value = '';
  if (! props.guest?.id) return false;

  isLoading.value = true;

  // 連名者のゲスト情報の「筆頭者ID」← NULLに更新する（GR-U-10-04 ゲスト情報変更を使用）
  if (props.guest?.children_guests) {
    for (let i = 0; i < props.guest?.children_guests.length; i++) {
      const input = {
        id: String(props.guest?.children_guests[i]?.id),
        guest_list_id: props.guestListId,
        parent_guest_id: null
      }
      const isSuccess = await update(input, [], []);
      if (! isSuccess) {
        isLoading.value = false;
        if (updateErrors.value?.v$?.[0]) error.value = updateErrors.value?.v$?.[0];
        return false;
      }
    }
  }

  const isSuccess = await deleteAction({id: props.guest.id});
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }

  if (props.isReload) {
    emits('reload')
    emits('close')
  } else {
    router.push({ path: '/mypage/guest-list/'+props.guestListId })
  }
};
</script>