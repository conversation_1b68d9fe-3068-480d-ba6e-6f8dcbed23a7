<template>
<Modal :isSubModal="props.isSubModal" @close="$emit('close')">
  <template #header>
    <a v-if="props.isSubModal" class="backword" @click.prevent="$emit('close')"><i class="material-symbols-outlined">arrow_back</i></a>
    画像を選択
  </template>
  <template #main>
    <div class="tab_menu">
      <ul>
        <li><a href="#" :class="{'current': (tabIndex == 0)}" @click="tabIndex = 0">アップロード画像</a></li>
        <li><a href="#" :class="{'current': (tabIndex == 1)}" @click="tabIndex = 1">Favoriのおすすめ</a></li>
      </ul>
    </div>
    <div class="tab">
      <ul class="image_list" v-if="tabIndex == 0">
        <li class="add_image" @click="onUploadImage"><a></a></li>
        <li v-for="(image, index) in imageSrc" :key="index">
          <img :src="image.presigned_url" @click="onSelectImage(image)" alt="">
        </li>
      </ul>
      <ul class="image_list" v-else>
      </ul>
    </div>
  </template>
</Modal>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  isSubModal?: boolean;
  imageUploadType?: 'user' | 'admin';
}>(), {
  isSubModal: true,
  imageUploadType: 'user'
});

const emits = defineEmits<{
  close: [],
  crop: [],
  add: [],
  select: [],
  uplaod: []
}>();

const tabIndex = ref(0);


const imageSrc = ref([]);
if(props.imageUploadType == 'admin'){
  imageSrc.value = [];
}else if(props.imageUploadType == 'user'){
  const { uploadImages, refetch, loading } = useGetManyUploadImage();
  imageSrc.value = uploadImages.value;
  watch(uploadImages, async(newVal) => {
    if(newVal){
      imageSrc.value = newVal;
    }
  }, {
    deep: true,
    immediate: true
  })
}

const onSelectImage = async(image) => {
  emits('select', {presigned_url: image.presigned_url, uuid: image.uuid});
};

const onUploadImage = async() => {
  emits('upload');
};

</script>

<style lang="scss" scoped>
.tab_menu{
  border-bottom: 1px solid $color-grayborder;
  margin-bottom: 16px;
  margin-left: -5px;
  margin-right: -5px;
  margin-top: -10px;
  ul {
    display: flex;
  }
  a{
    display: inline-block;
    font-size: 14px;
    line-height: 1;
    padding: 15px;
    color: $color-blackLight;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    padding: 10px 30px 15px;
    &.current {
      color: $color-main;
      font-weight: bold;
      border-bottom-color: $color-main;
    }
  }
}

.image_list{
  display: flex;
  flex-wrap: wrap;
  padding: 5px 15px;
  > * {
    margin: 4px;
  }
  .add_image{
    display: block;
    width: 100%;
    border: 2px dotted $color-accent;
    position: relative;
    cursor: pointer;
    width: 100px;
    height: 100px;
    &::before,
    &::after{
      content: '';
      background: $color-accent;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }
    &::before{
      width: 18px;
      height: 2px;
    }
    &::after{
      width: 2px;
      height: 18px;
    }
  }
  img{
    display: block;
    width: 100px;
    height: 100px;
    object-fit: contain;
  }
}

</style>