<template>
<Modal :isSubModal="props.isSubModal" @close="$emit('close')">
  <template #header>
    <a v-if="props.isSubModal" class="backword" @click.prevent="$emit('close')"><i class="material-symbols-outlined">arrow_back</i></a>
    トリミング
  </template>
  <template #main>
    <div class="box">
    <div class="img-cropper" v-if="src !== ''">
      <vue-cropper
        ref="cropper"
        :src="props.src"
        :style="{ height: '400px' }"
        :view-mode="1"
        :auto-crop-area="0.5"
        :guides="false"
        :check-cross-origin="false"
        :check-orientation="true"
        :restore="true"
        drag-mode="crop"
        :aspect-ratio="props.aspectWidth / props.aspectHeight"
      >
      </vue-cropper>
    </div>
    </div>
  </template>
  <template #footer>
    <div class="btn-wrap">
      <button class="btn btn-secondary btn-block" @click="onClickTrimming">適用する</button>
    </div>
  </template>
</Modal>
</template>

<script lang="ts" setup>
import VueCropper from 'vue-cropperjs';
import 'cropperjs/dist/cropper.css';

const props = withDefaults(defineProps<{
  isSubModal?: boolean;
  src: string,
  uuid: string,
  aspectWidth: number,
  aspectHeight: number
}>(), {
  isSubModal: true,
  src: '',
  aspectWidth: 9,
  aspectHeight: 16
});

const emits = defineEmits<{
  close: [],
  crop: []
}>();

const cropper = ref();
const src = ref();
const uuid = ref('');

watch(() => props, async(newValue, oldValue) => {
  if (newValue !== oldValue && cropper.value) {
    src.value = await convertBase64(newValue.src);
    uuid.value = newValue.uuid;
    cropper.value.replace(base64);
  }
}, { immediate: true });


const onClickTrimming = () => {
  let imageData = cropper.value.getData();
  let cropBoxData = cropper.value.getCropBoxData();
  let data = {
    src: props.src,
    uuid: props.uuid,
    x: imageData.x,
    y: imageData.y,
    width: imageData.width,
    height: imageData.height,
    rotate: imageData.rotate,
    scaleX: imageData.scaleX,
    scaleY: imageData.scaleY,
    cropBoxLeft: cropBoxData.left,
    cropBoxTop: cropBoxData.top,
    cropBoxWidth: cropBoxData.width,
    cropBoxHeight: cropBoxData.height,
    originalWidth: cropper.value.cropper.imageData.naturalWidth,
    originalHeight: cropper.value.cropper.imageData.naturalHeight,
  }
  emits('crop', data);
};

</script>

<style lang="scss" scoped>
.box {
  padding: 0 12px;
}
.btn-wrap {
  padding: 0 16px 16px;
}
</style>
