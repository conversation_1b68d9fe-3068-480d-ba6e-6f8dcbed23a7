<template>
<Modal :isSubModal="props.isSubModal" @close="$emit('close')">
  <template #header>
    <a v-if="props.isSubModal" class="backword" @click.prevent="$emit('close')"><i class="material-symbols-outlined">arrow_back</i></a>
    画像をアップロード
  </template>
  <template #main>
    <InputDropzone2
      :value="upload_image"
      :imageUploadType="props.imageUploadType"
      @change="onChangeFile"
    />
    <div class="upload_fromSp">
      <h2 class="upload_text_lg">スマホの写真をアップロード</h2>
      <p class="upload_text_sm">スマホで以下の二次元バーコードを読み込んでください</p>
    </div>
  </template>
</Modal>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  imageUploadType: 'user' | 'admin'
}>(), {
  imageUploadType: 'user'
});

const emits = defineEmits<{
  close: [],
  upload: []
}>();

const upload_image = ref('');
const upload_uuid = ref('');

// ファイル変更
const onChangeFile = async(image) => {
  upload_image.value = image.presigned_url;
  upload_uuid.value = image.uuid;
  emits('upload', image);
};

</script>

<style lang="scss" scoped>
.upload{
  cursor: pointer;
  transition: 0.35s;
  &:hover{
    opacity: 0.5;
  }
  &_text{
    &_lg{
      font-size: 20px;
      font-weight: bold;
    }
    &_md{
      font-size: 16px;
    }
    &_sm{
      font-size: 14px;
    }
  }
}

.upload_fromSp{
  text-align: center;
}
</style>