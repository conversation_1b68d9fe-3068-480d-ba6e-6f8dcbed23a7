
<template>
  <ModalContainer class="standAlone" @emitClose="closeModal">
  
    <template #header>
      <a class="backword" @click.prevent="convertibleModal('Modaldefault')"><img src="@/assets/images/icon-arrow_forward-b.svg" alt="戻る"></a>{{ props.title }}
    </template>
    <template #main>
      <div class="tabContents">
        <ModalItemSelectDetail :data="apiListItems" />
      </div>
    </template>
    <template #footer>
    </template>
  
  </ModalContainer>
  </template>
  
  
  <script lang="ts" setup>
  interface Props {
    title?: string;
  }
  
  const props = withDefaults(defineProps<Props>(), {
    title: '',
  });
  const emit = defineEmits(['closeModal , convertibleModal']);
  
  const closeModal = (target) => {
    emit('emitClose' , target);
  };
  
  const convertibleModal = (target) => {
    emit('emitShowModal' , target);
  };
  
  const apiListItemsPrice01 = {
    param: [
      {
        datail: [
          {
            id: "0000000001",
            title: 'カードカタログ 桜コース',
            thumbnail: "/images/sample/image08.png",
            price: "5800",
            unit: "セット",
            assignment: "3",
            purchased: "10",
            control: true,
          }, 
          {
            id: "00000000002",
            title: '3品セットDolce Duo PRIME CATALOG GIFT ポワール(¥3,800コース)',
            thumbnail: "/images/sample/image01.png",
            labels: [
            ],
            price: "5800",
            unit: "セット",
            assignment: "10",
            purchased: "10",
            control: true,
          }, 
        ],
      },
    ],
  }
  
  const apiListItemsPrice02 = {
    param: [
      {
        datail: [
          {
            id: "0000000001",
            title: 'カードカタログ 桜コース',
            thumbnail: "/images/sample/image08.png",
            price: "5800",
            unit: "セット",
            assignment: "3",
            control: true,
          }, 
          {
            id: "00000000002",
            title: '3品セットDolce Duo PRIME CATALOG GIFT ポワール(¥3,800コース)',
            thumbnail: "/images/sample/image01.png",
            labels: [
            ],
            price: "5800",
            unit: "セット",
            assignment: "7",
            control: true,
          }, 
          {
            id: "0000000003",
            title: 'カードカタログ 桜コース',
            thumbnail: "/images/sample/image08.png",
            price: "5800",
            unit: "部",
            assignment: "3",
            control: true,
          },
        ],
      },
    ],
  }
  
  const apiTabList = [
    'ギフトを選ぶ',
    '新しくギフトを探す',
  ]
  
  const apiListItems = {
    param: [
      {
        ranking: false,
        layout: 'twoColumn',
        datail: [
          {
            title: 'Lucky Gray[GN]',
            id: 'gift00000000001',
            link: '/gift/00000000001',
            thumbnail: "/images/sample/thumbnail06.png",
            price: "297",
            unit: "部",
            stars: 3.3,
            review: 12,
            tags: [
              "ゴールド箔",
              "リボン",
            ],
          }, 
          {
            title: '3品セットDolce Duo PRIME CATALOG GIFT ポワール(¥3,800コース)',
            id: 'gift00000000002',
            link: '/gift/00000000001',
            thumbnail: "/images/sample/thumbnail06.png",
            labels: [
              "30%OFF",
            ],
            price: "297",
            priceStrike: "500",
            unit: "部",
            stars: 4.7,
            review: 12,
            tags: [
              "ゴールド箔",
              "リボン",
            ],
          }, 
          {
            title: 'Dolce duo バウムクーヘン(モダンブラウン)',
            id: 'gift00000000003',
            link: '/gift/00000000001',
            thumbnail: "/images/sample/thumbnail06.png",
            price: "297",
            unit: "部",
            stars: 2,
            review: 12,
            tags: [
              "ゴールド箔",
              "リボン",
            ],
          }, 
          {
            title: 'Dolce duo バウムクーヘン(モダンブラウン)',
            id: 'gift00000000004',
            link: '/gift/00000000001',
            thumbnail: "/images/sample/thumbnail06.png",
            price: "297",
            unit: "部",
            stars: 0,
            review: 12,
            tags: [
              "ゴールド箔",
              "リボン",
            ],
          }, 
          {
            title: 'Dolce duo バウムクーヘン(モダンブラウン)',
            id: 'gift00000000005',
            link: '/gift/00000000001',
            thumbnail: "/images/sample/thumbnail06.png",
            price: "297",
            unit: "部",
            stars: 5,
            review: 12,
            tags: [
              "ゴールド箔",
              "リボン",
            ],
          }, 
        ],
      },
    ],
  }
  </script>
  
  <style lang="scss" scoped>
  .modalContainer {
    :deep(.contents) {
      padding: 0;
    }
  }
  
  .row {
    margin-top: 25px;
    padding: 0 20px;
    & ~ .row {
      margin-top: 27px;
      padding-top: 25px;
      border-top: 8px solid $color-lightgray;
    }
    &.check {
      margin: 13px 0 14px;
    }
  }
  
  .selectList {
    :deep(.tabtrg) {
      width: 100%;
      li {
        width: 50%;
        span {
          width: 100%;
        }
      }
    }
  }

  .tabContents {
    display: flex;
    padding: 40px 0 30px;
  }
  
  .giftinfo {
    display: flex;
    justify-content: space-between;
    .result {
      color: $color-blacktext2;
      font-size: 10px;
      line-height: 120%;
      letter-spacing: 0.2px;
      strong {
        color: $color-accent;
        font-size: 16px;
        font-weight: 400;
        line-height: 120%;
        letter-spacing: 0.32px;
      }
      span {
        font-size: 12px;
        line-height: 120%;
        letter-spacing: 0.24px;
      }
      b {
        color: $color-accent;
        font-size: 16px;
        font-weight: 700;
        line-height: 120%;
        letter-spacing: 0.32px;
      }
    }
    .sort {
      position: relative;
      padding: 0 0 0 20px;
      color: $color-blacktext2;
      font-size: 14px;
      &:before {
        @include BA;
        top: 47%;
        left: 0;
        width: 13px;
        height: 16px;
        background-image: url(@/assets/images/icon-swap_vert.svg);
      }
    }
  }
  
  @include sp {
  .row {
    margin-top: 23px;
    padding: 0 16px;
    & ~ .row {
      margin-top: 21px;
    }
    &.check {
      margin: 14px 0 0;
    }
  }
  }
  </style>