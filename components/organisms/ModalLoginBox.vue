<template>
  <ModalLoginContentsTop
    v-if="page == 'top'"
    :errors="getLoginErrors()"
    :isExternalLogin="isExternalLogin"
    @changePage="page = $event; emits('changePage', $event)"
  ></ModalLoginContentsTop>
  <ModalLoginContentsEmail
    v-else-if="page == 'email'"
    :isExternalLogin="isExternalLogin"
    @changePage="page = $event; emits('changePage', $event)"
  ></ModalLoginContentsEmail>
  <ModalLoginContentsPasswordStep1
    v-else-if="page == 'passwordStep1'"
    :isExternalLogin="isExternalLogin"
    @changePage="page = $event; emits('changePage', $event)"
  ></ModalLoginContentsPasswordStep1>
  <ModalLoginContentsPasswordStep2
    v-else-if="page == 'passwordStep2'"
    :isExternalLogin="isExternalLogin"
    @changePage="page = $event; emits('changePage', $event)"
  ></ModalLoginContentsPasswordStep2>
</template>

<script lang="ts" setup>
const { hideModalLogin, getLoginErrors } = useModalLoginState();
const page = ref('top')
const emits = defineEmits<{
  (e: 'changePage', value: string): void;
}>();

const props = withDefaults(defineProps<{
  isExternalLogin?: boolean;
}>(), {
  isExternalLogin: false
});
</script>


<style lang="scss">
.modalLogin {
  z-index: 3000 !important;
  .wrap {
    max-width: 420px;
    color: #666;
    margin: 0 auto;
    padding: 0 20px 40px;
  }
  .btn-2col {
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
    > a {
      width: 49%;
    }
  }
  .inputChecks {
    label {
      display: block;
      padding: 0;
      margin: 0;
      margin-bottom: 30px;
    }
  }
}
</style>