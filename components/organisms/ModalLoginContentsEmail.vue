<template>
<div class="wrap">
  <Loading v-if="isLoading"></Loading>
  <h2 class="cmn-title size--lg cmn-aligncenter mt-10">メールアドレスでログイン</h2>
  <div class="mb-25">
    <InputEmail 
      title="メールアドレス"
      size="full" 
      placeholder="<EMAIL>"
      :value="String(input.email)"
      :error="getValidationMessage(v$.email)"
      @update="input.email = $event"
    />
  </div>
  <div class="mb-25">
    <InputPassword 
      title="パスワード"
      size="full" 
      placeholder="パスワードを入力してください"
      :value="String(input.password)"
      :error="getValidationMessage(v$.password)"
      @update="input.password = $event"
      />
      <p v-if="error" class="input-error cmn-aligncenter" v-html="nl2br(error)"></p>
  </div>
  <p><NuxtLink to="/terms" target="_blank" class="link-accent">ご利用規約</NuxtLink> <NuxtLink to="/privacy" target="_blank" class="link-accent">プライバシーポリシー</NuxtLink>に同意の上 <br class="sp_only">ログインしてください。</p>
  <div class="cmn-aligncenter">
    <div class="mb-10"><ButtonMainColor baseColor="accent" @click="onClickLogin">上記に同意してログインする</ButtonMainColor></div>
    <div class="mb-30"><ButtonMainColor baseColor="glay" @click="emits('changePage', 'top')">戻る</ButtonMainColor></div>
    <a class="link-main" @click="emits('changePage', 'passwordStep1')">パスワードをお忘れの方</a>
  </div>
</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, email, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
const router = useRouter();
const { hideModalLogin } = useModalLoginState();

const props = withDefaults(defineProps<{
  isExternalLogin?: boolean;
}>(), {
  isExternalLogin: false
});

const emits = defineEmits<{
  (e: 'changePage', value: string): void;
}>();

// 入力項目
const input = ref({
  email:'',
  password:'',
} as {
  email: string;
  password: string;
})

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return {
    email: { 
      required: helpers.withMessage(validationMessage.required('メールアドレス'), required),
      email: helpers.withMessage(validationMessage.email('メールアドレス'), email)
    },
    password: { 
      required: helpers.withMessage(validationMessage.required('パスワード'), required)
    },
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { login, errors } = useLoginMember();
const { login:loginExternal, errors:errorsExternal } = useLoginMemberExternal();

// 更新中のLoading
const isLoading = ref(false);

// 編集モーダル非表示
const onClickLogin = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    // error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  if (props.isExternalLogin) {
    // 現行Favoriから呼び出されるログイン機能
    isLoading.value = true;
    const accessToken = await loginExternal(input.value.email, input.value.password);
    isLoading.value = false;
    if (! accessToken) {
      if (errorsExternal.value) $externalResults.value = errorsExternal.value;
      if (errorsExternal.value?.v$?.[0]) error.value = errorsExternal.value?.v$?.[0];
      return false;
    }
    // アクセストークンを送信する
    window.opener.postMessage({ accessToken: accessToken }, useRuntimeConfig().public.app.accept_external_login_domein);
    window.close();
  } else {
    // 通常ログイン
    isLoading.value = true;
    const isSuccess = await login(input.value.email, input.value.password);
    isLoading.value = false;
    if (! isSuccess) {
      if (errors.value) $externalResults.value = errors.value;
      if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
      return false;
    }
    // ログイン成功
    hideModalLogin();
    router.push({ path: `/mypage/` })
  }
};

// ブラウザバック時の処理
// import { onBeforeRouteLeave } from "vue-router";
// onBeforeRouteLeave((to, from, next) => {
//   console.log('onBeforeRouteLeave');
//   console.log(from.path);
//   console.log(to.path);
  // emits('changePage', 'top')
  // next(false);
  // next();
// });
</script>