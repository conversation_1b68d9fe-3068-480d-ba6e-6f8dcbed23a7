<template>
  <div class="wrap sp_ph0">
    <Loading v-if="isLoading"></Loading>
    <h2 class="cmn-title size--lg cmn-aligncenter mt-10">パスワードをお忘れの方</h2>
    <p class="cmn-aligncenter size--sm mb-45">ご登録時のメールアドレスを入力してください<br>パスワードの再設定を行うことができます</p>
    <div class="contener-sm" style="padding: 0 40px;">
      <div class="mb-40">
        <InputEmail 
          title="メールアドレス"
          size="full" 
          placeholder="<EMAIL>"
          :value="String(input.email)"
          :error="getValidationMessage(v$.email)"
          @update="input.email = $event"
        />
        <p v-if="error" class="input-error">{{ error }}</p>
      </div>
      <div class="cmn-aligncenter mb-30">
        <button class="btn btn-secondary btn-block btn-md mb-25" @click="onClickSend">再設定用メールを送信する</button>
      </div>
    </div>
    <div class="cmn-aligncenter">
      <a @click="emits('changePage', 'top')" class="link-text"><i class="material-icons icn-left">arrow_back</i> トップへ戻る</a>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, email, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
const router = useRouter();

const emits = defineEmits<{
  (e: 'changePage', value: string): void;
}>();

// 入力項目
const input = ref({
  email:''
} as {
  email: string;
})

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return {
    email: { 
      required: helpers.withMessage(validationMessage.required('メールアドレス'), required),
      email: helpers.withMessage(validationMessage.email('メールアドレス'), email)
    }
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { action, errors } = useSendResetPasswordUrl();

// 更新中のLoading
const isLoading = ref(false);

// 編集モーダル非表示
const onClickSend = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    // error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await action(input.value.email);
  isLoading.value = false;
  if (! isSuccess) {
    $externalResults.value = {};
    if (errors.value) $externalResults.value = errors.value;
    // if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    if (! errors.value) error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }
  // 成功
  emits('changePage', 'passwordStep2')
};
</script>