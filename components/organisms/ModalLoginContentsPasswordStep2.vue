<template>
  <div class="contener-sm sp_ph0">
    <h2 class="cmn-title size--lg cmn-aligncenter mt-20">パスワード再設定用の<br>URL送信完了しました</h2>
    <p class="cmn-aligncenter mb-40">メール内に記載されておりますURLから <br>パスワードの再設定を行なってください </p>
    <p class="cmn-aligncenter size--sm color-blacklight mb-45">※メールが送られてこない場合は <span class="d-ib">迷惑メールに振り分けられているか</span> <br>メールアドレスが間違っている可能性がございます <br>再度 ご確認をお願いいたします </p>
    <div class="cmn-aligncenter">
      <a @click="emits('changePage', 'passwordStep1')" class="link-text"><i class="material-icons icn-left">arrow_back</i> メールアドレス入力に戻る</a>
    </div>
  </div>
</template>

<script lang="ts" setup>
const emits = defineEmits<{
  (e: 'changePage', value: string): void;
}>();
</script>