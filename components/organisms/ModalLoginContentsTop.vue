<template>
  <div class="wrap">
    <Loading v-if="isLoading" :fullscreen="true"></Loading>
    <h2 class="cmn-title size--lg cmn-aligncenter mt-10">すでに会員の方</h2>
    <!-- <p class="cmn-aligncenter size--lg mb-20">はじめての方は<a @click="showModalRegister()" class="link-main bold">新規会員登録</a></p> -->
    <p class="mb-20"><NuxtLink to="/terms" target="_blank" class="link-accent">ご利用規約</NuxtLink> <NuxtLink to="/privacy" target="_blank" class="link-accent">プライバシーポリシー</NuxtLink>に同意の上 下記の方法でログインまたはサインインしてください </p>
    <div class="btns">
      <!-- <a class="is-apple" href="#">Appleでログイン</a> -->
      <a class="is-google" href="#" @click="onClickSnsLogin('google')">Googleでログイン</a>
      <p v-if="errors?.google" class="input-error cmn-aligncenter mb-20" style="margin-top: -5px;" v-html="nl2br(errors.google)"></p>
      <!-- <a class="is-yahoo" href="#">Yahoo! JAPAN IDでログイン</a> -->
      <!-- <a class="is-facebook" href="#">Facebookでログイン</a> -->
      <!-- <a class="is-twitter" href="#">Twitterでログイン</a> -->
      <a class="is-line" href="#" @click="onClickSnsLogin('line')">LINEでログイン</a>
      <p v-if="errors?.line" class="input-error cmn-aligncenter mb-20" style="margin-top: -5px;" v-html="nl2br(errors.line)"></p>
      <NuxtLink class="is-mail" @click="emits('changePage', 'email')">メールアドレスでログイン</NuxtLink>
    </div>
    <div class="cmn-aligncenter mt-30">
      <a @click="showModalRegister()" class="link-accent link-underline size--md">新規会員登録はこちら</a>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  errors?: any;
  isExternalLogin?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => {
    return {};
  },
  isExternalLogin: false
});


const runtimeConfig = useRuntimeConfig()
const { showModalRegister, setSnsLogin } = useModalRegisterState();
const emits = defineEmits<{
  (e: 'changePage', value: string): void;
}>();

const isLoading = ref(false);

const onClickSnsLogin = async(sns = '') => {
  setSnsLogin();
  isLoading.value = true;
  const apiEndpoint = runtimeConfig.public.apiEndpoint.replace('/graphql', '/api');
  let url = apiEndpoint+'/auth/'+sns+'/url';
  if (props.isExternalLogin) url += '?mode=externalLogin';
  const response = await fetch(url);
  const data = await response.json();
  isLoading.value = false;
  window.location.href = data?.url;
};
</script>

<style lang="scss" scoped>
.btns{
  a{
    display: block;
    text-align: center;
    text-decoration: none;
    font-size: 12px;
    line-height: 1;
    color: #333;
    border: 1px solid #9C9C9C;
    border-radius: 4px;
    padding: 12px;
    margin: 14px 0;
    background-repeat: no-repeat;
    background-size: 18px;
    background-position: left 16px center;
    cursor: pointer;
    &.is-apple{
      background-image: url('@/assets/images/icon-login-apple.png');
    }
    &.is-google{
      background-image: url('@/assets/images/icon-login-google.png');
    }
    &.is-yahoo{
      background-image: url('@/assets/images/icon-login-yahoo.png');
    }
    &.is-facebook{
      color: #FFF;
      background-color: #1877F2;
      border-color: #1877F2;
      background-image: url('@/assets/images/icon-login-facebook.png');
    }
    &.is-twitter{
      color: #FFF;
      background-color: #2E92D1;
      border-color: #2E92D1;
      background-image: url('@/assets/images/icon-login-twitter.png');
    }
    &.is-line{
      color: #FFF;
      background-color: #06C755;
      border-color: #06C755;
      background-image: url('@/assets/images/icon-login-line.png');
    }
    &.is-mail{
      background-image: url('@/assets/images/icon-login-mail.png');
    }
  }
}
</style>