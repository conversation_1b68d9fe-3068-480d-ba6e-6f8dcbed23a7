<template>
  <Modal @close="onClickClose()" class="modalLogin" :data-page="page" :isShowSubModal="isShowSubModal">
    <template #header>
      <span v-if="isShowModalLogin()">ログイン・新規会員登録</span>
      <span v-else-if="isShowModalRegister()">新規会員登録</span>
      <span v-else>ログイン</span>
    </template>
    <template #main>
      <div class="tab_menu" v-if="page == 'top'"><ul>
        <li><a @click="showModalLogin()" :class="{'current': isShowModalLogin() }">ログイン</a></li>
        <li><a @click="showModalRegister()" :class="{'current': isShowModalRegister() }">新規会員登録</a></li>
      </ul></div>
      <ModalLoginBox
        v-if="isShowModalLogin()"
        @changePage="page = $event"
      ></ModalLoginBox>
      <ModalRegisterBox
        v-else-if="isShowModalRegister()"
        @changePage="page = $event"
        @showSubModal="isShowSubModal = true" 
        @closeSubModal="isShowSubModal = false" 
      ></ModalRegisterBox>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const { showModalLogin, hideModalLogin, isShowModalLogin, getLoginErrors } = useModalLoginState();
const { showModalRegister, hideModalRegister, isShowModalRegister, getRegisterErrors } = useModalRegisterState();
const page = ref('top')

const router = useRouter();
const route = useRoute();

const isShowSubModal = ref(false)

onMounted(() => {
  const sns = route.query.sns || '';
  if(sns != ''){
    showModalLogin()
    router.push({});
  }

  if (Object.keys(getRegisterErrors()).length) {
    showModalRegister();
  } else if (Object.keys(getLoginErrors()).length) {
    showModalLogin()
  }
})

const onClickClose = async () => {
  hideModalLogin(); 
  hideModalRegister();
  if (route.path == '/login' || route.path == '/register') {
    router.push({ path: '/' })
  }
}
</script>


<style lang="scss">
.modalLogin {
  z-index: 3000 !important;
  .wrap {
    max-width: 420px;
    color: #666;
    margin: 0 auto;
    padding: 0 20px 40px;
  }
  .btn-2col {
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
    > a {
      width: 49%;
    }
  }
  .inputChecks {
    label {
      display: block;
      padding: 0;
      margin: 0;
      margin-bottom: 30px;
    }
  }
  &[data-page="top"] .contents {
    padding-top: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .tab_menu{
    border-bottom: 1px solid $color-grayborder;
    margin-bottom: 32px;
    ul {
      display: flex;
      li {
        width: 50%;
      }
    }
    a{
      text-align: center;
      font-size: 14px;
      line-height: 1;
      padding: 15px;
      color: $color-blackLight;
      text-decoration: none;
      border-bottom: 2px solid transparent;
      padding: 10px 30px 15px;
      display: block;
      cursor: pointer;
      &.current {
        color: $color-main;
        font-weight: bold;
        border-bottom-color: $color-main;
      }
    }
  }

  @include sp {
    .contents {
      height: calc(100dvh - 48px) !important;
    }
  }
}
</style>