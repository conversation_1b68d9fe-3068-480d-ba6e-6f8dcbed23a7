<template>
  <Modal class="modalLogout" size="sm" @close="hideModalLogout()">
    <template #header>
      ログアウト
    </template>
    <template #main>
      <Loading v-if="isLoading" />
      Favoriからログアウトしてもよろしいですか？
      <p v-if="error" class="input-error">{{ error }}</p>
    </template>
    <template #footer>
      <a href="#" @click="hideModalLogout()">キャンセル</a>
      <a href="#" class="color-danger" @click="onClickLogout()">ログアウト</a>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const { hideModalLogout } = useModalLogoutState();

const router = useRouter();

const emits = defineEmits<{
  (e: 'close'): void;
}>()

const { logout, errors } = useLogoutMember();

// 更新中のLoading
const isLoading = ref(false);

// 全体エラー
const error = ref('')

// ログアウト
const onClickLogout = async () => {
  error.value = '';
  isLoading.value = true;
  const isSuccess = await logout();
  isLoading.value = false;
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }
  // ログイン成功
  hideModalLogout();
  router.push({ path: '/login/logout' })

};
</script>