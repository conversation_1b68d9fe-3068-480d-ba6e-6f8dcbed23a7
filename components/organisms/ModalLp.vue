<template>
  <Modal size="sm" class="modalLp" @close="$emit('close')">
    <template #main>
      <a class="close" @click="$emit('close')"><i class="material-symbols-outlined">close</i></a>
      <div
        ref="contentRef"
        v-html="lpContent"
      ></div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps } from 'vue';
import { useRuntimeConfig } from '#app';
import { MicroCms } from '@/utils/microCms';
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();

const props = defineProps({
  view: {
    type: String,
    required: false,
    default: 'マイページ（「席次表・席札・メニュー表」ボタン）',
  },
});

const lpContent = ref<string>('');
const microCms = new MicroCms();
const contentRef = ref<HTMLElement | null>(null);

// 環境を判定する関数
const getEnvironment = (apiEndpoint: string): string => {
  switch (apiEndpoint) {
    case 'production':
      return '本番サーバー';
    case 'staging':
      return 'ステージングサーバー';
    default:
      return '開発サーバー';
  }
}

// HTMLエンコードされた文字列をデコードする関数
const decodeHtmlEntities = (str: string): string => {
  const textarea = document.createElement('textarea');
  textarea.innerHTML = str;
  return textarea.value;
};

// ショートコードをHTMLに置き換える関数
const replaceShortcodes = (content: string, data: any): string => {
  const shortcodeHandlers: Record<string, () => string> = {
    'coupon-code': () => `
      <div class="shortcode shortcodeCouponCode">
        <div class="shortcodeCouponCode_title">クーポンコード</div>
        <div class="shortcodeCouponCode_value" style="color: ${data.couponCodeTextColor};">${data.couponCodeNumber}</div>
        <button
          class="shortcodeCouponCode_button"
          style="background-color: ${data.couponCodeBtnBgColor}; color: ${data.couponCodeBtnColor};"
          onclick="return false;"
          data-copy="${data.couponCodeNumber}"
        >
          クーポンコードをコピーする
        </button>
      </div>`,
    'copy-box': () => `
      <div class="shortcode shortcodeCopyBox">
        <button
          class="shortcodeCopyBox_button"
          onclick="return false;"
          data-copy="${data.copyBoxText}"
        >
          ${data.copyBoxText}
        </button>
      </div>`,
  };

  // デコードされた文字列に対してショートコードを置き換える
  const decodedContent = decodeHtmlEntities(content);
  return decodedContent.replace(/\[([a-zA-Z0-9_-]+)\]/g, (_, shortcode) => {
    const handler = shortcodeHandlers[shortcode];
    return handler ? handler() : _;
  });
};

// コピーボタンの処理
const setupCopyButtons = () => {
  if (!contentRef.value) return;

  const copyButtons = contentRef.value.querySelectorAll('[data-copy]');
  copyButtons.forEach(button => {
    button.addEventListener('click', async (e) => {
      e.preventDefault();
      const textToCopy = (button as HTMLElement).dataset.copy || '';
      try {
        await navigator.clipboard.writeText(textToCopy);
        addToastMessage({message: `「${textToCopy}」をコピーしました`});
      } catch (err) {
        console.error('クリップボードへのコピーに失敗しました:', err);
      }
    });
  });
};

// microCMS APIからデータを取得
const fetchLandingPageData = async () => {
  const config = useRuntimeConfig();
  const environment = getEnvironment(config.public.serverEnvironment);

  try {
    const { data } = await microCms.fetch('/landing-page', {
      filters: `public[contains]${environment}[and]view[contains]${props.view}`,
    });

    if (data.value?.contents?.length > 0) {
      const content = data.value.contents[0];
      lpContent.value = replaceShortcodes(content.contents[0].body, content.contents[0]);

      // custom_cssが存在する場合、スタイルを適用
      if (content.custom_css) {
        const styleElement = document.createElement('style');
        styleElement.textContent = content.custom_css;
        document.head.appendChild(styleElement);
      }
    }
  } catch (error) {
    console.error(error);
  }
};

// コンテンツが反映された後にコピー処理のセットアップを行う
watch(lpContent, async (newContent) => {
  if (newContent) {
    await nextTick();
    setupCopyButtons();
  }
});

onMounted(() => {
  fetchLandingPageData();
});
</script>

<style lang="scss" scoped>
.modalLp {
  .close {
    position: absolute;
    top: 20px;
    right: 20px;
    @media(max-width: 680px) {
      position: fixed;
    }
  }
  :deep() {
    .modalContainer {
      background: #fff;
      width: auto;
      max-width: 480px;
      padding: 0;
      .contents {
        max-height: 100vh;
        max-height: 100dvh;
        padding: 0 !important;
        position: relative;
      }
    }

    figure {
      margin: 0;
    }
    img {
      width: 100%;
      height: auto;
    }

    button {
      cursor: pointer;
    }

    .shortcodeCouponCode {
      text-align: center;
      margin: 0;
      padding: 20px 20px 52px;
      &_title {
        color: #D7A241;
        font-size: 22px;
        font-weight: bold;
        line-height: 1.5;
        margin-bottom: 20px;
        padding-bottom: 6px;
        border-bottom: 1px solid #D7A241;
      }
      &_value {
        color: #D7A241;
        font-size: 32px;
        font-weight: bold;
        line-height: 1.2;
        margin-bottom: 20px;
      }
      &_button {
        color: #FFF;
        background: #FF8F79;
        font-size: 14px;
        font-weight: bold;
        line-height: 1.3;
        border: none;
        padding: 10px 12px;
        cursor: pointer;
        border-radius: 5px;
        box-shadow: -1px 2px 4px rgba(0,0,0,0.25);
        transition: 0.25s ease;
        &:hover {
          box-shadow: -1px 2px 8px rgba(0,0,0,0.3);
        }
        &:active {
          box-shadow: none;
        }
      }
    }

    .shortcodeCopyBox {
      text-align: center;
      margin: 24px auto;
      padding: 0 20px 30px;
      &_button {
        display: block;
        width: 100%;
        background-color: #fff;
        border: none;
        border-top: 1px solid #D9D9D9;
        border-bottom: 1px solid #D9D9D9;
        color: #555;
        padding: 14px;
        font-size: 16px;
        font-weight: bold;
        line-height: 1;
        cursor: pointer;
        transition: 0.25s ease;
        &:hover {
          opacity: 0.7;
        }
        &:active {
          opacity: 0.5;
        }
      }
    }

  }
}

</style>