
<template>
<ModalContainer class="standAlone" @emitClose="closeModal">

  <template #header>
    <a class="backword" @click.prevent="convertibleModal('Modaldefault')"><img src="@/assets/images/icon-arrow_forward-b.svg" alt="戻る"></a>{{ props.title }}
  </template>
  <template #main>
    <ShowItemDetails :data="apiShowItemDetails" />
  </template>
  <template #footer>
  </template>

</ModalContainer>
</template>

<script lang="ts" setup>
interface Props {
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
});
const emit = defineEmits(['closeModal , convertibleModal']);

const closeModal = (target) => {
  emit('emitClose' , target);
};

const convertibleModal = (target) => {
  emit('emitShowModal' , target);
};


const apiShowItemDetails = {
  param: [
    {
      datail: [
        {
          title: '3品セット Dolce Duo PRIME CATAROG GIFT ポワール（¥3,300コース）',
          number: 'X0000-00-00',
          thumbnail: [
            "/images/sample/thumbnail06.png",
            "/images/sample/smallthumbnail04.png",
            "/images/sample/smallthumbnail03.png",
            "/images/sample/smallthumbnail02.png",
            "/images/sample/smallthumbnail05.png",
          ],
          labels: [
            "数量限定キャンペーン",
          ],
          price: "4510",
          priceStrike: "6600",
          priceUnit: "セット",
          notices: [
            "商品毎にラッピングのデザインを選択することができます ",
            "ご案内カード・コットンバッグは ご注文数の合計が【10セット以上(複数種類可)】及び【挙式7営業日前】までの宅配手続き完了のお客様が対象です ",
            "沖縄県及び離島への発送は 別途送料(1件あたり税込1,650円)がかかります 引き出物宅配手続き画面にて お申込時にお支払いただきます ",
            "配送住所の不備があった場合やご指定の納品日から1週間以内にゲスト様がお受け取りいただけない場合 ご注文者様宛に転送されます その際の転送費用はご注文者様負担となります 予めご了承くださいませ ",
          ],
          about: "商品についての説明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明",
          setdetail: "セット内容の説明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明明",
        }, 
      ],
    },
  ],
}

</script>

<style lang="scss" scoped>
.modalContainer {
  :deep(.contents) {
    padding: 0;
  }
}
.divider {
  padding: 18px 90px 21px;
  & ~ .divider {
    padding-top: 25px;
    border-top: 8px solid $color-lightgray;
  }
}
p {
  margin-bottom: 10px;
  color: $color-blacktext2;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 0.3px;
  & + p {
    margin-top: 21px;
  }
}
.addList {
  margin-top: 12px;
  font-size: 12px;
  letter-spacing: 0.24px;
  b {
    font-size: 14px;
    font-weight: 700;
    line-height: 150%;
    color: $color-blackLight;
  }
}
h2{
  color: $color-accent;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 21px;
}
dl {
  margin-bottom: 16px;
}
.targetblank {
  display: inline-block;
  position: relative;
  margin: 7px 0 12px;
  padding-left: 20px;
  text-decoration: none;
  color: $color-accent;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  &::before {
    @include BA;
    left: 0;
    width: 17px;
    height: 20px;
    background-image: url(@/assets/images/icon-targetblank-g.svg);
  }
}
.row {
  margin-top: 23px;
  & ~ .row {
    margin-top: 27px;
  }
  &.check {
    margin: 13px 0 14px;
  }
}
:deep(.button--md) {
  display: block;
  max-width: 400px;
  margin: 0 auto 24px;
}
:deep(textarea) {
  min-height: 28em;
  color: #444;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 1.4px;
}
@include sp {
.divider {
  padding: 20px 16px 16px;
}
.addList {
  margin-top: 17px;
  b {
    margin-left: 5px;
    font-size: 15px;
  }
}
h2{
  font-size: 16px;
}
.row {
  margin-top: 21px;
  & ~ .row {
    margin-top: 21px;
  }
  &.check {
    margin: 14px 0 0;
  }
}
:deep(.button--md) {
  max-width: 343px;
  margin: 20px auto 24px;
}
:deep(textarea) {
  min-height: 32em;
}
}
</style>