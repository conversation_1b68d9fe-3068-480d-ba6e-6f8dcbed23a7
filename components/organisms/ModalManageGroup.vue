
<template>
<Modal :isSubModal="props.isSubModal" @close="$emit('close')">
  <template #header>
    <a v-if="props.isSubModal" class="backword" @click.prevent="$emit('close')"><i class="material-symbols-outlined">arrow_back</i></a>
    {{props.title}}
  </template>
  <template #main>
    <div class="modalManageGroup">
      <div v-if="isLoading" class="input-list"><Loading></Loading></div>
      <div v-else class="input-list">
        <div class="input-item" v-for="(group, i) in input.groups" :key="i">
          <div class="input-icn">
            <i v-if="i+1 < input.groups.length" class="material-symbols-outlined">group</i>
            <i v-else class="material-symbols-outlined">add</i>
          </div>
          <InputText 
            size="full" 
            placeholder="新規グループ名"
            :value="String(group.name)"
            :error="getValidationMessage(v$?.groups?.[i]?.name)"
            @input="group.name = $event.target.value"
          />
          <div class="intput-action" v-if="i+1 < input.groups.length">
            <div v-if="group.id">{{ guestGroups?.find(guestGroup => guestGroup.id == group.id)?.guests.length }} <small>名</small></div>
            <div v-else>0 <small>名</small></div>
            <button class="btn btn-icn" @click="deleteGroupIndex = i"><i class="material-symbols-outlined">delete</i></button>
          </div>
          <div class="intput-action" v-else>
            <button class="btn btn-primary-outline" @click="onClickAdd()">追加</button>
          </div>
        </div>
      </div>
    </div>
  </template>
  <template #footer>
    <footer class="modal-footer">
      <button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
      <button class="btn btn-secondary btn-block" @click="onClickSave()">保存して戻る</button>
    </footer>
  </template>
</Modal>
<Modal size="sm" v-if="deleteGroupIndex !== false" @close="deleteGroupIndex = false">
  <template #header>
    グループを削除
  </template>
  <template #main>
    <div>
      <p>グループ「{{input.groups?.[deleteGroupIndex]?.name}}」を削除してもよろしいですか？<br>
    （グループを割り当てられているゲスト情報は削除されません）</p>
    </div>
  </template>
  <template #footer>
    <a href="javascript:void(0);" @click="deleteGroupIndex = false">キャンセル</a>
    <a href="javascript:void(0);" class="color-alert" @click="onClickDelete()">削除する</a>
  </template>
</Modal>
</template>

<script lang="ts" setup>
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';

export interface Props {
  isSubModal: boolean;
  guestListId: string;
  title: string;
  limit: number;
}

const props = withDefaults(defineProps<Props>(), {
  isSubModal: false,
  guestListId: '',
  title: 'グループ編集',
  limit: 1
});

const emits = defineEmits<{
  close: [],
  reload: []
}>();

// APIから guestGroup を読み込み
const { guestGroups, refetch} = useGetManyGuestGroup(String(props.guestListId))
const { create, errors: createErrors } = useCreateGuestGroup();
const { update, errors: updateErrors } = useUpdateGuestGroup();
const { action: deleteAction, errors: deleteErrors } = useDeleteGuestGroup();

// 入力項目
const input = ref({
  groups: [] as GuestGroupInput[]
})

// 全体エラー
const error = ref('')

const rules = computed(() => {
  let rules = {} as any;
  rules.groups = [];
  for (let i = 0; i < input.value.groups.length; i++) {
    const group = input.value.groups[i];
    rules.groups.push({
      name: { 
        required: helpers.withMessage(validationMessage.required('グループ名'), required) 
      }
    });
  }
  return rules;
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// 更新中のLoading
const isLoading = ref(false);

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();

  // 最後が空でidがない場合は 削除してから保存
  let lastTagIndex = input.value.groups.length - 1;
  if (! input.value.groups?.[lastTagIndex]?.name && ! input.value.groups?.[lastTagIndex]?.id) {
    input.value.groups.splice(lastTagIndex, 1);
  }

  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  let hasError = false;
  let externalResults = {
    groups: []
  } as any;
  for (let i = 0; i < input.value.groups.length; i++) {
    const group = input.value.groups[i];
    if (! group?.name) continue;
    if (! group?.id) {
      const isSuccess = await create(group);
      // エラーの場合
      if (! isSuccess) {
        if (createErrors.value) $externalResults.value = createErrors.value;
        externalResults.groups[i] = {name: createErrors.value?.input?.name?.[0]};
        hasError = true
      }
    } else {
      const isSuccess = await update(group);
      // エラーの場合
      if (! isSuccess) {
        if (updateErrors.value) $externalResults.value = updateErrors.value;
        if (updateErrors.value?.v$?.[0]) error.value = updateErrors.value?.v$?.[0];
        externalResults.groups[i] = {name: updateErrors.value?.input?.name?.[0]};
        hasError = true
      }
    }
  }
  // 削除
  for (let i = 0; i < deleteGroupIds.value.length; i++) {
    const id = deleteGroupIds.value[i];
    await deleteAction({id: id});
  }

  if (hasError) {
    $externalResults.value = externalResults;
    isLoading.value = false;
    return false;
  }
  await refetch();
  isLoading.value = false;
  emits('reload');
  emits('close');
};

const onClickAdd = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  input.value.groups.push({
    guest_list_id: props.guestListId,
    name: ''
  })
};

// 更新中のLoading
const deleteGroupIndex = ref(false as number | false);
const deleteGroupIds = ref([] as string[]);

const onClickDelete = async() => {
  if (deleteGroupIndex.value === false) return false;
  const index = deleteGroupIndex.value;
  deleteGroupIndex.value = false
  const group = input.value.groups[index];
  if (group?.id) {
    deleteGroupIds.value.push(group?.id);
  }
  input.value.groups.splice(index, 1);

  // バリデーションメッセージをリセット
  v$.value.$reset();
};

const initInput = async() => {
  input.value.groups = [];
  for (let i = 0; i < guestGroups.value.length; i++) {
    const guestGroup = guestGroups.value[i];
    input.value.groups.push({
      id: guestGroup.id,
      guest_list_id: props.guestListId,
      name: guestGroup.name
    })
  }
  input.value.groups.push({
    guest_list_id: props.guestListId,
    name: ''
  })
};
onMounted(async () => {
  isLoading.value = true;
  await refetch();
  initInput();
  isLoading.value = false;
});
</script>

<style lang="scss">
.modalManageGroup .input-list {
  padding-top: 24px;
  padding-bottom: 24px;
  min-height: 300px;
  @include sp {
    padding-top: 0;
  }
}
</style>