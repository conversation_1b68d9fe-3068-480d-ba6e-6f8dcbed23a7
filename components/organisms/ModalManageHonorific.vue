
<template>
<Modal @close="$emit('close')">

  <template #header>
    <a class="backword" @click.prevent="$emit('close')"><img src="@/assets/images/icon-arrow_forward-b.svg" alt="戻る"></a>
    敬称を追加・編集
  </template>
  <template #main>
    <FormTagGroup title="敬称を追加・編集" formtitle="新規敬称" subtitle="敬称一覧" :data="ModalManageHonorifictagList" />
  </template>
  <template #footer>
    <ShowFooterBarFrow :data="ModalManageHonorificFooterBarFrow" />
  </template>

</Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const ModalManageHonorifictagList = ref([
  {
    title: '新郎側ゲスト',
    quantity: 10,
    required: true
  },
  {
    title: '新婦側ゲスト',
    quantity: 10,
    required: false
  },
  {
    title: '受付',
    quantity: 10,
    required: false
  }
]);

const ModalManageHonorificFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'full',
          color: 'glay',
          disabled: true,
          slot: "完了",
          link: "#01link",
        },
      ],
    },
  ],
}
</script>

<style lang="scss" scoped>
:deep(.modalContainer) {
  .contents {
    padding: 30px 15px;
  }
}
@include sp {
:deep(.modalContainer) {
  .contents {
    padding-top: 18px;
  }
}
}
</style>