
<template>
<Modal @close="$emit('close')">
  <template #header>
    <a class="backword" @click.prevent="$emit('close')"><i class="material-symbols-outlined">arrow_back</i></a>
    個別に登録する
  </template>
  <template #main>
    <div class="box">
      <h3 class="cmn-title">プロフィール写真・アイコン設定</h3>
      <div class="mb-30">
        <InputImages
          :images="(result) ? [result] : []"
          :max="1"
          @change="onChangeImages"
        ></InputImages>
      </div>
      <div class="btn-wrap">
        <button class="btn btn-secondary btn-block" @click="onClickChange">設定する</button>
      </div>
    </div>
  </template>
</Modal>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  image?: any,
}>(), {
  image: null
});

const emits = defineEmits<{
  (e: 'change', image: any): void;
  (e: 'close'): void;
}>();

const result = ref(props.image);

const onChangeImages = async (images:any) => {
  if (images.length) {
    result.value = images?.[0];
  } else {
    result.value = null;
  } 
};

const onClickChange = async () => {
  emits('change', result.value);
  emits('close');
};

</script>

<style lang="scss" scoped>
.box {
  padding: 0 16px;
}
</style>