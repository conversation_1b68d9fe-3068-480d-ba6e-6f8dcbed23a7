
<template>
<Modal :isSubModal="props.isSubModal" @close="$emit('close')">
  <template #header>
    <a v-if="props.isSubModal" class="backword" @click.prevent="$emit('close')"><i class="material-symbols-outlined">arrow_back</i></a>
    {{props.title}}
  </template>
  <template #main>
    <div class="modalManageTag">
      <div v-if="isLoading" class="input-list"><Loading></Loading></div>
      <div v-else class="input-list">
        <div class="input-item" v-for="(tag, i) in input.tags" :key="i">
          <div class="input-icn">
            <i v-if="i+1 < input.tags.length" class="material-symbols-outlined">sell</i>
            <i v-else class="material-symbols-outlined">add</i>
          </div>
          <InputText 
            size="full" 
            placeholder="新規タグ名"
            :value="String(tag.tag)"
            :error="getValidationMessage(v$?.tags?.[i]?.tag)"
            @input="tag.tag = $event.target.value"
          />
          <div class="intput-action" v-if="i+1 < input.tags.length">
            <div v-if="tag.id">{{ guestTags?.find(guestTag => guestTag.id == tag?.id)?.guests.length }} <small>名</small></div>
            <div v-else>0 <small>名</small></div>
            <button class="btn btn-icn" @click="deleteTagIndex = i"><i class="material-symbols-outlined">delete</i></button>
          </div>
          <div class="intput-action" v-else>
            <button class="btn btn-primary-outline" @click="onClickAdd()">追加</button>
          </div>
        </div>
      </div>
    </div>
  </template>
  <template #footer>
    <footer class="modal-footer">
      <button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
      <button class="btn btn-secondary btn-block" @click="onClickSave()">保存して戻る</button>
    </footer>
  </template>
</Modal>
<Modal size="sm" v-if="deleteTagIndex !== false" @close="deleteTagIndex = false">
  <template #header>
    タグを削除
  </template>
  <template #main>
    <div>
      <p>タグ「{{input.tags?.[deleteTagIndex]?.tag}}」を削除してもよろしいですか？<br>
    （タグを割り当てられているゲスト情報は削除されません）</p>
    </div>
  </template>
  <template #footer>
    <a href="javascript:void(0);" @click="deleteTagIndex = false">キャンセル</a>
    <a href="javascript:void(0);" class="color-alert" @click="onClickDelete()">削除する</a>
  </template>
</Modal>
</template>

<script lang="ts" setup>
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';

export interface Props {
  isSubModal: boolean;
  guestListId: string;
  title: string;
}

const props = withDefaults(defineProps<Props>(), {
  isSubModal: false,
  guestListId: '',
  title: 'タグ編集'
});

const emits = defineEmits<{
  close: [],
  reload: []
}>();

// APIから guestTag を読み込み
const { guestTags, refetch} = useGetManyGuestTag(String(props.guestListId))
const { create, errors: createErrors } = useCreateGuestTag();
const { update, errors: updateErrors } = useUpdateGuestTag();
const { action: deleteAction, errors: deleteErrors } = useDeleteGuestTag();

// 入力項目
const input = ref({
  tags: [] as GuestTagInput[]
})

// 全体エラー
const error = ref('')

const rules = computed(() => {
  let rules = {} as any;
  rules.tags = [];
  for (let i = 0; i < input.value.tags.length; i++) {
    const tag = input.value.tags[i];
    rules.tags.push({
      tag: { 
        required: helpers.withMessage(validationMessage.required('タグ名'), required) 
      }
    });
  }
  return rules;
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// 更新中のLoading
const isLoading = ref(false);

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();

  // 最後が空でidがない場合は 削除してから保存
  let lastTagIndex = input.value.tags.length - 1;
  if (! input.value.tags?.[lastTagIndex]?.tag && ! input.value.tags?.[lastTagIndex]?.id) {
    input.value.tags.splice(lastTagIndex, 1);
  }

  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  let hasError = false;
  let externalResults = {
    tags: []
  } as any;
  for (let i = 0; i < input.value.tags.length; i++) {
    externalResults.tags[i] = {tag: ''};
    const tag = input.value.tags[i];
    if (! tag?.tag) continue;
    if (! tag?.id) {
      const isSuccess = await create(tag);
      // エラーの場合
      if (! isSuccess) {
        if (createErrors.value?.v$?.[0]) error.value = createErrors.value?.v$?.[0];
        externalResults.tags[i] = {tag: createErrors.value?.input?.tag?.[0]};
        hasError = true
      }
    } else {
      const isSuccess = await update(tag);
      // エラーの場合
      if (! isSuccess) {
        if (updateErrors.value?.v$?.[0]) error.value = updateErrors.value?.v$?.[0];
        externalResults.tags[i] = {tag: updateErrors.value?.input?.tag?.[0]};
        hasError = true
      }
    }
  }
  // 削除
  for (let i = 0; i < deleteTagIds.value.length; i++) {
    const id = deleteTagIds.value[i];
    await deleteAction({id: id});
  }

  if (hasError) {
    $externalResults.value = externalResults;
    isLoading.value = false;
    return false;
  }
  await refetch();
  isLoading.value = false;
  emits('reload');
  emits('close');
};

const onClickAdd = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  input.value.tags.push({
    guest_list_id: String(props.guestListId),
    tag: ''
  })
};

// 更新中のLoading
const deleteTagIndex = ref(false as number | false);
const deleteTagIds = ref([] as string[]);

const onClickDelete = async() => {
  if (deleteTagIndex.value === false) return false;
  const index = deleteTagIndex.value;
  deleteTagIndex.value = false
  const tag = input.value.tags[index];
  if (tag?.id) {
    deleteTagIds.value.push(tag?.id);
  }
  input.value.tags.splice(index, 1);

  // バリデーションメッセージをリセット
  v$.value.$reset();
};

const initInput = async() => {
  input.value.tags = [];
  for (let i = 0; i < guestTags.value.length; i++) {
    const guestTag = guestTags.value[i];
    input.value.tags.push({
      id: guestTag.id,
      guest_list_id: props.guestListId,
      tag: guestTag.tag
    })
  }
  input.value.tags.push({
    guest_list_id: props.guestListId,
    tag: ''
  })
};
onMounted(async () => {
  isLoading.value = true;
  await refetch();
  initInput();
  isLoading.value = false;
});
</script>

<style lang="scss">
.modalManageTag .input-list {
  padding-top: 24px;
  padding-bottom: 24px;
  min-height: 300px;
  @include sp {
    padding-top: 0;
  }
}
</style>