
<template>
<Modal @close="$emit('close')" size="sm" class="modalMypagePopup">
  <template #main>
    <a class="close" @click.prevent="$emit('close')"><i class="material-symbols-outlined">close</i></a>
    <div class="mb-10"><img src="@/assets/images/mypage/popup/popup-01.png" alt="WEB招待状で集めたゲスト情報がそのまま席次表や席札に使える！"></div>
    <a target="_blank" href="https://www.favori-cloud.com/design" class="mb-10"><img src="@/assets/images/mypage/popup/popup-02.png" alt="席次表デザイン一覧へ"></a>
    <a target="_blank" href="https://www.favori-cloud.com/sample?seat=1" class="mb-10"><img src="@/assets/images/mypage/popup/popup-03.png" alt="席次表サンプル請求"></a>
  </template>
</Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{
  (e: 'close'): void;
}>();
</script>

<style lang="scss" scoped>
.close {
  color: #fff;
  float: right;
  i {
    font-size: 40px;
  }
}
</style>
<style lang="scss">
.modalWrap.modalMypagePopup {
  .modalContainer {
    background: none;
    width: auto;
    max-width: 480px;
    .contents {
      max-height: 100vh;
      padding-top: 36px !important;
    }
  }
}
</style>