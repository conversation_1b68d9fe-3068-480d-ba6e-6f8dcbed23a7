
<template>
<Modal :isSubModal="props.isSubModal" @close="$emit('close')" class="modalOldKanji">
  <template #header>
    <a v-if="props.isSubModal" class="backword" @click.prevent="$emit('close')"><i class="material-symbols-outlined">arrow_back</i></a>
    旧字を入力する
  </template>
  <template #main>
    <h2>1.文字を検索する</h2>
    <InputText
      title="旧字体検索"
      size="full"
      placeholder="ここに検索したい文字を入力してください"
      :value="inputText"
      @input="inputText = $event.target.value"
    />
    <h2>2.検索結果から文字を選ぶ</h2>
    <ul class="selectKanji" v-for="(strs, i) in kanjiList" :key="i" :style="{ display: isHidden(strs?.[0]) ? 'none' : '' }">
      <li class="items" :class="selected?.[i] == str ? 'is-selected' : ''" v-for="(str, n) in strs" :key="n">
        <span @click="onClickStr(i, str)">
          <img :src="SVG_API_ENDPOINT+'?size=48&pad=10&name=DFKaiShoRWPro6N-W5&ch='+str" :alt="str" onerror="this.onerror = null; this.src='';">
        </span>
      </li>
      <template v-if="strs.length === 1">
        <li class="empty">他に候補の文字はありません</li>
      </template>
    </ul>
    <div class="btn-wrap">
      <button v-if="selected.length" class="btn btn-secondary btn-block btn-md mt-30" @click="onClickChange">適用する</button>
      <button v-else class="btn btn-secondary btn-block btn-md mt-30" disabled>適用する</button>
    </div>

  </template>
</Modal>
</template>

<script lang="ts" setup>
import { debounce } from 'perfect-debounce'


export interface Props {
  isSubModal: boolean;
  refTarget: any;
}

const props = withDefaults(defineProps<Props>(), {
  isSubModal: false,
  refTarget: null
});

const inputText = ref('' as string)
const selectionStart = ref(0 as string)
const selectionText = ref('' as string)
const kanjiList = ref([] as any[])
const selected = ref([] as string[])

const emits = defineEmits<{
  (e: 'change', value: string): void;
  (e: 'close'): void;
}>();


onMounted(async () => {
  // const start = props.refTarget.selectionStart;
  // const end = props.refTarget.selectionEnd;
  // 入力された文字を選択していない状態でも旧字選択ダイアログに初期表示させるようにする
  selectionText.value = props.refTarget.value;
  // 選択範囲を検索対象にする場合
  // selectionText.value = props.refTarget.value.substring(start, end);
  // selectionStart.value = start;
  inputText.value = selectionText.value;
});

const isOnMounted = ref(false)
// 旧字検索
watch(inputText, async() => {
  if (! isOnMounted.value) {
    await onSearch();
    isOnMounted.value = true;
  } else {
    await onSearchDebounce();
  }
})

const SEARCH_API_ENDPOINT = useRuntimeConfig()?.public?.app?.oldchar_api_endpoint+'/search';
const SVG_API_ENDPOINT = useRuntimeConfig()?.public?.app?.oldchar_api_endpoint+'/glyph';

const onSearch = debounce(async() => {
  try {
    const response = await fetch(SEARCH_API_ENDPOINT+'?type=kanji-multi&font=DFKaiShoRWPro6N-W5&query='+inputText.value)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const result = await response.json();
    kanjiList.value = result?.kanji;
    selected.value = [];
    for (let i = 0; i < result?.input?.length; i++) {
      selected.value[i] = result?.input?.[i];
    }
    // console.log(result);
    // bankList.value = result.banks;
  } catch (error) {
    console.error('Error fetching JSON data:', error)
    console.log(error)
  }
});
const onSearchDebounce = debounce(async() => {
  await onSearch();
}, 1000);

const onClickChange = () => {
  const selectedText = selected.value.join('');
  let value = props.refTarget.value;
  if (selectionText.value) {
    value = value.replace(selectionText.value, selectedText);
  } else {
    value = value.slice(0, selectionStart.value) + selectedText + value.slice(selectionStart.value);
  }
  props.refTarget.value = value;
  emits('change', value);
  emits('close')
}

const onClickStr = (i:number, str:string) => {
  selected.value[i] = str;
  inputText.value = selected.value.join('');
}

const isHidden = (str:string) => {
  if (str == ' ' || str == '　') return true;
  return false
}
</script>

<style lang="scss" scoped>
h2{
  color: $color-accent;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 21px;
}

label + h2 {
  margin-top: 37px;
  margin-bottom: 20px;
}

.selectKanji {
  display: flex;
  flex-wrap: wrap;
  gap: 11px;
  li {
    display: flex;
    width: 80px;
    height: 80px;
    flex-shrink: 0;
    border-radius: 4px;
    border-radius: 4px;
    border: 1px solid $color-grayborder;
    background: $color-mainbackground;
    color: $color-blacktext2;
    font-size: 32px;
    font-weight: 500;
    line-height: 100%;
    cursor: pointer;
    &.is-selected {
      border: 1px solid $color-main;
      background: $color-lightbackground;
      cursor: default;
    }
    &.empty {
      align-items: center;
      width: auto;
      border: none;
      background-color: transparent;
      padding-left: 2px;
      color: $color-alert2;
      font-size: 12px;
      line-height: 145%;
      letter-spacing: 0.24px;
      cursor: default;
    }
    span {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      img[src=""] {
        display: none;
      }
    }
  }
  & ~ .selectKanji {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid $color-lightgray;
  }
}

:deep(.button--md) {
  display: block;
  max-width: 400px;
  margin: 25px auto 0;
}
@include sp {
h2{
  margin-bottom: 11px;
  font-size: 16px;
}

label + h2 {
    margin-bottom: 18px;
}

.selectKanji {
  gap: 12px;
  li {
    width: 72px;
    height: 72px;
  }
}

:deep(.button--md) {
  max-width: 343px;
  margin-bottom: 41px;
}
:deep(.title) {
  margin: 6px 0 7px;
  & + textarea {
    min-height: 10.6em;
  }
}
}
</style>
<style lang="scss">
.modalWrap.modalOldKanji .modalContainer .contents{
  font-family: 'Noto SansJP', sans-serif;
  padding: 25px 90px 0 !important;
  .btn-wrap {
    padding-bottom: 40px;
  }
  .contentsInner {
  }
  @include sp {
    padding: 0 !important;
    height: calc(100dvh - 50px) !important;
    .contentsInner {
      padding: 18px 15px 0 !important;
      height: 100%;
    }
  }
}


.webInvitationView {
  .modalOldKanji .modalContainer .contents .btn {
    text-align: center !important;
    color: var(--text-color, #ffffff) !important;
    background-color: var(--primary-color, #B18A3E) !important;
    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
}
</style>