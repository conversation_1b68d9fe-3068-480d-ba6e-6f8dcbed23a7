
<template>
<ModalContainer class="standAlone" @emitClose="closeModal">

  <template #header>
    <a class="backword" @click.prevent="convertibleModal('ModalRecipientGuestExample')"><img src="@/assets/images/icon-arrow_forward-b.svg" alt="戻る"></a>{{ props.title }}
  </template>
  <template #main>
    <h2>作成したメッセージを確認</h2>
    <p>メッセージの内容と 日時や会場を確認してください </p>
    <div class="enclosure">
      <p>久しぶり！<br>
        <br>
        本日はご報告があって連絡しました！<br>
        この度 かねてよりお付き合いをしていた◇◇さんと結婚することが決まりました！<br>
        <br>
        出席してくれるようであれば<br>
        あらためて招待状をお送りしたいので<br>
        名前や現住所などを下記URLより入力をお願いします <br>
        <br>
        開催日：2023年4月1日(土) <br>
        会場所在地：<br>
        会場名：<br>
        <br>
        以下のURLから 2023年1月1日(土) までにご記入ください <br>
        https://example.com/abcde/</p>
    </div>
    <ButtonMainColor baseColor="reversal" to="#">保存して端末にコピーする</ButtonMainColor>
    <div class="send">
      <p>SNSやメッセージアプリでゲストへ送る</p>
      <ul>
        <li><a href="#"><img src="@/assets/images/icon-sns04.svg" alt="LINE"></a></li>
        <li><a href="#"><img src="@/assets/images/icon-sns01.svg" alt="Facebook"></a></li>
        <li><a href="#"><img src="@/assets/images/icon-sns03.svg" alt="Twitter"></a></li>
        <li><a href="#"><img src="@/assets/images/icon-tomail.svg" alt="メール" class="tomail"></a></li>
      </ul>
    </div>
  </template>
  <template #footer>
    <ShowFooterBarFrow :data="ModalManageHonorificFooterBarFrow" />
  </template>

</ModalContainer>
</template>

<script lang="ts" setup>
interface Props {
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
});

const emit = defineEmits(['closeModal , convertibleModal']);

const closeModal = (target) => {
  emit('emitClose' , target);
};

const convertibleModal = (target) => {
  emit('emitShowModal' , target);
};


const ModalManageHonorificFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "戻る",
          link: "#01link",
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "完了",
          link: "#0link",
        },
      ],
    },
  ],
}
</script>

<style lang="scss" scoped>
.modalContainer {
  :deep(.contents) {
    padding: 25px 90px;
  }
}
p {
  margin-bottom: 10px;
  color: $color-blacktext2;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 0.3px;
  & + p {
    margin-top: 21px;
  }
}
h2{
  color: $color-accent;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 21px;
}
.enclosure {
  margin: 15px 0 24px;
  padding: 17px 25px 5px;
  border-radius: 4px;
  background: $color-lightgray;
  p {
    letter-spacing: 1.4px;
  }
}
:deep(.button--md) {
  display: block;
  max-width: 400px;
  margin: 0 auto 32px;
}
.send {
  padding: 11px;
  border-radius: 4px;
  background: $color-lightbackground;
  text-align: center;
  ul {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    img {
      width: 40px;
      &.tomail {
        width: 31px;
        margin-left: 5px;
      }
    }
  }
}
@include sp {
.modalContainer {
  :deep(.contents) {
    padding: 17px 16px;
  }
}
h2{
  font-size: 16px;
}
.enclosure {
    margin-bottom: 13px;
    padding: 17px 16px 5px;
}
}
</style>