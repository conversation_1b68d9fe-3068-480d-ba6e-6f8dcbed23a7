
<template>
<ModalContainer @emitClose="closeModal">

  <template #header>
    <a class="backword" @click.prevent="convertibleModal('Modaldefault')"><img src="@/assets/images/icon-arrow_forward-b.svg" alt="戻る"></a>{{ props.title }}
  </template>
  <template #main>
    <h2>例文から選ぶ</h2>
    <div class="selectList">
      <TabTrigger :data="apiTabList" />
    </div>
    <ul class="tabsbj">
      <li class="is-active">
        <p>久しぶり！<br>
          <br>
          本日はご報告があって連絡しました！<br>
          この度 かねてよりお付き合いをしていた◇◇さんと結婚することが決まりました！<br>
          <br>
          出席してくれるようであれば<br>
          あらためて招待状をお送りしたいので<br>
          名前や現住所などを下記URLより入力をお願いします <br>
          <br>
          開催日：2023年4月1日(土) <br>
          会場所在地：<br>
          会場名：<br>
          <br>
          以下のURLから 2023年1月1日(土) までにご記入ください <br>
          https://example.com/abcde/</p>
      </li>
      <li>
        <p>久しぶり！2<br>
          <br>
          本日はご報告があって連絡しました！<br>
          この度 かねてよりお付き合いをしていた◇◇さんと結婚することが決まりました！<br>
          <br>
          出席してくれるようであれば<br>
          あらためて招待状をお送りしたいので<br>
          名前や現住所などを下記URLより入力をお願いします <br>
          <br>
          開催日：2023年4月1日(土) <br>
          会場所在地：<br>
          会場名：<br>
          <br>
          以下のURLから 2023年1月1日(土) までにご記入ください <br>
          https://example.com/abcde/</p>
      </li>
      <li>
        <p>久しぶり！3<br>
          <br>
          本日はご報告があって連絡しました！<br>
          この度 かねてよりお付き合いをしていた◇◇さんと結婚することが決まりました！<br>
          <br>
          出席してくれるようであれば<br>
          あらためて招待状をお送りしたいので<br>
          名前や現住所などを下記URLより入力をお願いします <br>
          <br>
          開催日：2023年4月1日(土) <br>
          会場所在地：<br>
          会場名：<br>
          <br>
          以下のURLから 2023年1月1日(土) までにご記入ください <br>
          https://example.com/abcde/</p>
      </li>
    </ul>
    <ButtonMainColor @click.prevent="convertibleModal('ModalRecipientGuestConfirm')">この例文を使う</ButtonMainColor>
  </template>
  <template #footer>
    <ShowFooterBarFrow :data="ModalManageHonorificFooterBarFrow" />
  </template>

</ModalContainer>
</template>

<script lang="ts" setup>
interface Props {
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
});

const emit = defineEmits(['closeModal , convertibleModal']);

const closeModal = (target) => {
  emit('emitClose' , target);
};

const convertibleModal = (target) => {
  emit('emitShowModal' , target);
};


const apiTabList = [
  '友人',
  '同僚',
  '親族',
]

const ModalManageHonorificFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "戻る",
          link: "#01link",
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "完了",
          link: "#0link",
        },
      ],
    },
  ],
}
</script>

<style lang="scss" scoped>
.modalContainer {
  :deep(.contents) {
    padding: 25px 90px;
  }
}
p {
  margin-bottom: 10px;
  color: $color-blacktext2;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 0.3px;
  & + p {
    margin-top: 21px;
  }
}
h2{
  color: $color-accent;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 21px;
}

p {
  letter-spacing: 1.4px;
}
.selectList {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid $color-grayborder;
  :deep(ul) {
    li {
      span {
        display: inline-block;
        padding: 16px 20px 14px;
        border-bottom: 2px solid transparent;
        text-align: center;
        font-weight: 400;
        font-size: 14px;
        line-height: 120%;
      }
      .addGuestList {
        position: relative;
        padding-left: 22px;
        font-size: 14px;
        line-height: 100%;
        color: inherit;
        &::before {
          @include BA;
          left: 3px;
          width: 12px;
          height: 12px;
          background-image: url(@/assets/images/icon-plus-gl.svg);
        }
      }
      &:hover ,
      &.is-active {
        span {
          border-bottom-color: $color-main;
          font-weight: 700;
          color: $color-main;
        }
        .addGuestList::before {
          background-image: url(@/assets/images/icon-plus.svg);
        }
      }
    }
  }

}
.tabsbj {
  margin-bottom: 30px;
}
:deep(.button--md) {
  display: block;
  max-width: 350px;
  margin: 0 auto 24px;
}

@include sp {
.modalContainer {
  :deep(.contents) {
    padding: 17px 16px;
  }
}
h2{
  font-size: 16px;
}
p {
  letter-spacing: 0.3px;
}
.selectList {
  width: calc(100% + 32px);
  margin: 0 -16px 19px; 
  :deep(ul) {
    li {
      span {
        padding: 16px 20px 14px;
      }
    }
  }
}
.tabsbj {
  margin-bottom: 13px;
}
}
</style>