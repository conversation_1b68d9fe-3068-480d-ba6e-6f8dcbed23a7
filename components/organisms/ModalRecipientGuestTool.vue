
<template>
<ModalContainer class="standAlone" @emitClose="closeModal">

  <template #header>
    <a class="backword" @click.prevent="convertibleModal('Modaldefault')"><img src="@/assets/images/icon-arrow_forward-b.svg" alt="戻る"></a>{{ props.title }}
  </template>
  <template #main>
    <div class="divider">
      <p>ゲストの情報を ゲスト用URLからゲスト本人に登録してもらうツールです 招待メッセージを作成して メールやLINEなどで招待予定のゲストに送ることができます </p>
      <p>入力してもらった情報は 現在選択されているゲストリストに自動的に登録されます </p>
      <p class="addList">追加先リスト：<b>メインリスト</b></p>
    </div>
    <div class="divider">
      <h2>入力フォームURL</h2>
      <dl>
        <template v-for="(info, index) in apiRecipientUrl.basicinfo" :key="info">
          <PieceDisplayList :data="info" />
        </template>
      </dl>
      <p>招待文を作成せずに URLのみをコピーして使用することも可能です </p>
      <a href="#" class="targetblank">入力フォームのURLを端末にコピーする</a>
    </div>
    <div class="divider">
      <h2>日時・会場と回答期限</h2>
      <div class="row">
        <InputCalendar
          title="実施日"
          :size="266"
        />
      </div>
      <div class="row">
        <InputText
          title="会場名"
          size="full"
          placeholder="●●●●●●●ホテル"
        />
      </div>
      <div class="row">
        <InputText
          title="会場所在地"
          size="full"
          placeholder="会場所在地"
        />
      </div>
      <div class="row">
        <InputCalendar
          title="回答期限"
          :size="266"
        />
      </div>
      <div class="row check">
        <InputCheck
          :items="[
            {
              value: 1,
              checked: false,
              label: '期限を過ぎてからの回答を受け付ける'
            }
          ]"
        />
      </div>
    </div>
    <div class="divider">
      <h2>メッセージ作成</h2>
      <p>メッセージの例文を選んで 自由に編集して使用できます </p>
      <ButtonMainColor baseColor="reversal" @click.prevent="convertibleModal('ModalRecipientGuestExample')">例文から選ぶ</ButtonMainColor>
      <InputTextarea
        size="full"
        value="久しぶり！

本日はご報告があって連絡しました！
この度 かねてよりお付き合いをしていた◇◇さんと結婚することが決まりました！

出席してくれるようであれば
あらためて招待状をお送りしたいので
名前や現住所などを下記URLより入力をお願いします

開催日：2023年4月1日(土) 
会場所在地：
会場名：

以下のURLから 2023年1月1日(土) までにご記入ください
https://example.com/abcde/"
      />

    </div>
  </template>
  <template #footer>
    <ShowFooterBarFrow :data="ModalManageHonorificFooterBarFrow" />
  </template>

</ModalContainer>
</template>

<script lang="ts" setup>
interface Props {
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
});
const emit = defineEmits(['closeModal , convertibleModal']);

const closeModal = (target) => {
  emit('emitClose' , target);
};

const convertibleModal = (target) => {
  emit('emitShowModal' , target);
};

const apiRecipientUrl = {
  basicinfo: [
    {
      menu: 'URL',
      content: 'https://example.com/abcde/',
    },
  ],
}

const ModalManageHonorificFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "キャンセル",
          link: "#01link",
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "次へ",
          link: "#0link",
        },
      ],
    },
  ],
}
</script>

<style lang="scss" scoped>
.modalContainer {
  :deep(.contents) {
    padding: 0;
  }
}
.divider {
  padding: 18px 90px 21px;
  & ~ .divider {
    padding-top: 25px;
    border-top: 8px solid $color-lightgray;
  }
}
p {
  margin-bottom: 10px;
  color: $color-blacktext2;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 0.3px;
  & + p {
    margin-top: 21px;
  }
}
.addList {
  margin-top: 12px;
  font-size: 12px;
  letter-spacing: 0.24px;
  b {
    font-size: 14px;
    font-weight: 700;
    line-height: 150%;
    color: $color-blackLight;
  }
}
h2{
  color: $color-accent;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 21px;
}
dl {
  margin-bottom: 16px;
}
.targetblank {
  display: inline-block;
  position: relative;
  margin: 7px 0 12px;
  padding-left: 20px;
  text-decoration: none;
  color: $color-accent;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  &::before {
    @include BA;
    left: 0;
    width: 17px;
    height: 20px;
    background-image: url(@/assets/images/icon-targetblank-g.svg);
  }
}
.row {
  margin-top: 23px;
  & ~ .row {
    margin-top: 27px;
  }
  &.check {
    margin: 13px 0 14px;
  }
}
:deep(.button--md) {
  display: block;
  max-width: 400px;
  margin: 0 auto 24px;
}
:deep(textarea) {
  min-height: 28em;
  color: #444;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 1.4px;
}
@include sp {
.divider {
  padding: 20px 16px 16px;
}
.addList {
  margin-top: 17px;
  b {
    margin-left: 5px;
    font-size: 15px;
  }
}
h2{
  font-size: 16px;
}
.row {
  margin-top: 21px;
  & ~ .row {
    margin-top: 21px;
  }
  &.check {
    margin: 14px 0 0;
  }
}
:deep(.button--md) {
  max-width: 343px;
  margin: 20px auto 24px;
}
:deep(textarea) {
  min-height: 32em;
}
}
</style>