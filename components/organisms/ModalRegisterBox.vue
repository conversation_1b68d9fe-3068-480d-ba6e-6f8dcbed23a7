<template>
  <Loading v-if="isLoading"></Loading>
  <ModalRegisterContentsTop
    v-if="page == 'top'"
    :errors="getRegisterErrors()"
    @changePage="page = $event; emits('changePage', $event)"
  ></ModalRegisterContentsTop>
  <ModalRegisterContentsEmailStep1
    v-else-if="page == 'emailStep1'"
    :errors="errors"
    :input="inputStep1"
    @change="inputStep1 = $event; emits('changePage', $event)"
    @back="page = 'top';  emits('changePage', 'top')"
    @next="page = 'emailStep2';  emits('changePage', 'emailStep2')"
  ></ModalRegisterContentsEmailStep1>
  <ModalRegisterContentsEmailStep2
    v-else-if="page == 'emailStep2'"
    :errors="errors"
    :input="inputStep2"
    @showSubModal="emits('showSubModal')" 
    @closeSubModal="emits('closeSubModal')" 
    @change="inputStep2 = $event; emits('changePage', $event)"
    @back="page = 'emailStep1';  emits('changePage', 'emailStep1')"
    @next="page = 'emailStep3';  emits('changePage', 'emailStep3')"
  ></ModalRegisterContentsEmailStep2>
  <ModalRegisterContentsEmailStep3
    v-else-if="page == 'emailStep3'"
    :errors="errors"
    :input="inputStep3"
    @change="inputStep3 = $event; emits('changePage', $event)"
    @back="page = 'emailStep2';  emits('changePage', 'emailStep2')"
    @next="page = 'emailConf';  emits('changePage', 'emailConf')"
  ></ModalRegisterContentsEmailStep3>
  <ModalRegisterContentsEmailConf
    v-else-if="page == 'emailConf'"
    :inputStep1="inputStep1"
    :inputStep2="inputStep2"
    :inputStep3="inputStep3"
    @back="page = 'emailStep3';  emits('changePage', 'emailStep3')"
    @next="onClickSave"
  ></ModalRegisterContentsEmailConf>
</template>

<script lang="ts" setup>
import type { CreateMemberInput, MemberRegistQuestionnaireInput, WeddingInfoInput } from '@/composables/generated';
import type { InputStep1 } from '@/components/organisms/ModalRegisterContentsEmailStep1.vue';
import { InputStep1Default } from '@/components/organisms/ModalRegisterContentsEmailStep1.vue';
import type { InputStep2 } from '@/components/organisms/ModalRegisterContentsEmailStep2.vue';
import { InputStep2Default } from '@/components/organisms/ModalRegisterContentsEmailStep2.vue';
import type { InputStep3 } from '@/components/organisms/ModalRegisterContentsEmailStep3.vue';
import { InputStep3Default } from '@/components/organisms/ModalRegisterContentsEmailStep3.vue';

const router = useRouter();
const { hideModalRegister, getModalStep, getRegisterErrors } = useModalRegisterState();
const { hideModalLogin } = useModalLoginState();

const page = ref(getModalStep() ? getModalStep() : 'top')

const emits = defineEmits<{
  (e: 'changePage', value: string): void;
  (e: 'showSubModal'): void;
  (e: 'closeSubModal'): void;
}>();

const isShowSubModal = ref(false)

// 入力用データ
const inputStep1 = ref(InputStep1Default as InputStep1)
const inputStep2 = ref(InputStep2Default as InputStep2)
const inputStep3 = ref(InputStep3Default as InputStep3)

// 更新用データ
const input = ref({} as CreateMemberInput)
const weddingInfo = ref({} as WeddingInfoInput)
const memberRegistQuestionnaires = ref([] as MemberRegistQuestionnaireInput[])

// 全体エラー
const error = ref('')

// 更新中のLoading
const isLoading = ref(false);

// 更新API
const { create, errors } = useTmpCreateMember();

// 保存ボタンクリック
const onClickSave = async() => {
  // ↓ は 本当はAPI側で任意にすべきだが とりあえず空で送信
  input.value.email = inputStep1.value.email;
  input.value.password = inputStep1.value.password;
  input.value.last_name = inputStep2.value.last_name;
  input.value.first_name = inputStep2.value.first_name;

  if (! inputStep2.value.wedding_date_is_null) {
    weddingInfo.value.wedding_date = inputStep2.value.wedding_date;
  }
  if (! inputStep2.value.wedding_venue_is_null) {
    weddingInfo.value.wedding_venue = inputStep2.value.wedding_venue;
  }
  weddingInfo.value.guest_count = inputStep2.value.guest_count;

  memberRegistQuestionnaires.value = [];
  const question = 'ファヴォリをどこで初めて知りましたか？';
  for (let i = 0; i < inputStep3.value.answers.length; i++) {
    const answer = inputStep3.value.answers[i];
    memberRegistQuestionnaires.value.push({question: question, answer: answer})
  }
  if (inputStep3.value.answer_text) {
    memberRegistQuestionnaires.value.push({question: question, answer: inputStep3.value.answer_text})
  }

  // 全体エラーをリセット
  error.value = '';
  isLoading.value = true;
  const isSuccess = await create(input.value, memberRegistQuestionnaires.value, weddingInfo.value);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    // メールアドレスがすでに登録されてる場合
    if (errors.value?.error?.[0] == '登録済みの会員です' || errors.value?.error?.[0] == '登録済みの会員です。') {
      errors.value = {
        input: {
          email: ['すでに会員登録されているメールアドレスです ']
        }
      }
      page.value = 'emailStep1';
    }
    // stepを判定して戻る
    if ((typeof errors.value?.input?.email !== 'undefined') || 
        (typeof errors.value?.input?.password !== 'undefined')) {
      page.value = 'emailStep1';
    } else if ((typeof errors.value?.input?.last_name !== 'undefined') || 
        (typeof errors.value?.input?.first_name !== 'undefined') ||
        (typeof errors.value?.input?.wedding_date !== 'undefined') ||
        (typeof errors.value?.input?.wedding_venue !== 'undefined') ||
        (typeof errors.value?.input?.guest_count !== 'undefined')) {
      page.value = 'emailStep2';
    } else if (typeof errors.value?.member_regist_questionnaire !== 'undefined') {
      page.value = 'emailStep3';
    }
    return false;
  }

  // 登録成功 メールアドレス送信済み
  hideModalRegister();
  hideModalLogin();
  router.push({ path: '/register/send' })
};


</script>


<style lang="scss">
.modalRegister {
  .wrap {
    max-width: 420px;
    color: #666;
    margin: 0 auto;
    padding: 0 20px 40px;
  }
  .btn-2col {
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
    > a {
      width: 49%;
    }
  }
  .inputChecks {
    label {
      display: block;
      padding: 0;
      margin: 0;
      margin-bottom: 30px;
    }
  }
}
</style>