<template>
  <div class="wrap">
    <h2 class="cmn-title size--lg cmn-aligncenter mt-10">登録内容確認</h2>
    <p class="cmn-aligncenter mb-30">登録内容を確認してください</p>
    <ul class="list-info mb-30">
      <li v-if="inputStep1.email"><dl>
        <dt>メールアドレス</dt>
        <dd>{{ inputStep1.email }}</dd>
      </dl></li>
      <li v-if="inputStep1.email"><dl>
        <dt>パスワード</dt>
        <dd>******</dd>
      </dl></li>
      <li><dl>
        <dt>お名前</dt>
        <dd>{{ inputStep2.last_name }} {{ inputStep2.first_name }}</dd>
      </dl></li>
      <li><dl>
        <dt>挙式日</dt>
        <dd>
          <div v-if="inputStep2.wedding_date_is_null">
            まだ決まっていない
          </div>
          <div v-else>
            {{ $dayjs(inputStep2.wedding_date).format('YYYY/MM/DD') }}
          </div>
        </dd>
      </dl></li>
      <li><dl>
        <dt>挙式会場名</dt>
        <dd>
          <div v-if="inputStep2.wedding_venue_is_null">
            まだ決まっていない
          </div>
          <div v-else>
            {{ inputStep2.wedding_venue }}
          </div>
        </dd>
      </dl></li>
      <li><dl>
        <dt>招待人数（目安）</dt>
        <dd>
          <span v-if="! inputStep2.guest_count">未定</span>
          <span v-else>{{ GUEST_COUNT_OPTIONS.find(option => option.value == String(inputStep2.guest_count))?.label }}</span>
        </dd>
      </dl></li>
      <li><dl>
        <dt>アンケート</dt>
        <dd>
          <div v-for="(answer, index) in sortAnswers(inputStep3.answers)" :key="index">
            {{ answer }}
            <span v-if="answer == 'その他' && inputStep3.answer_text">- {{ inputStep3.answer_text }}</span>
          </div>
        </dd>
      </dl></li>
    </ul>
    <p class="mb-20"><NuxtLinkToSSR to="/terms" target="_blank" class="link-accent">ご利用規約</NuxtLinkToSSR> <NuxtLinkToSSR to="/privacy" target="_blank" class="link-accent">プライバシーポリシー</NuxtLinkToSSR>に同意の上 ご登録ください </p>
    <div class="mb-10"><ButtonMainColor size="lg" baseColor="accent" @click="emits('next')">上記に同意して登録する</ButtonMainColor></div>
    <ButtonMainColor size="lg" baseColor="glay" @click="emits('back')">戻る</ButtonMainColor>
  </div>
</template>
<script lang="ts" setup>
import type { InputStep1 } from '@/components/organisms/ModalRegisterContentsEmailStep1.vue';
import { InputStep1Default } from '@/components/organisms/ModalRegisterContentsEmailStep1.vue';

import type { InputStep2 } from '@/components/organisms/ModalRegisterContentsEmailStep2.vue';
import { InputStep2Default } from '@/components/organisms/ModalRegisterContentsEmailStep2.vue';

import type { InputStep3 } from '@/components/organisms/ModalRegisterContentsEmailStep3.vue';
import { InputStep3Default } from '@/components/organisms/ModalRegisterContentsEmailStep3.vue';

const { $dayjs } : any = useNuxtApp();

interface Props {
  inputStep1: InputStep1;
  inputStep2: InputStep2;
  inputStep3: InputStep3;
}
const props = withDefaults(defineProps<Props>(), {
  inputStep1: () => InputStep1Default,
  inputStep2: () => InputStep2Default,
  inputStep3: () => InputStep3Default,
});

const emits = defineEmits<{
  (e: 'back'): void;
  (e: 'next'): void;
}>();

const sortAnswers = (answers = []) => {
  return answers.sort((a, b) => {
    return MEMBER_REGIST_QUESTIONNAIRE_WAYS.indexOf(a) - MEMBER_REGIST_QUESTIONNAIRE_WAYS.indexOf(b);
  });
}
</script>