<template>
<div class="wrap">
  <h2 class="cmn-title size--lg cmn-aligncenter mt-10">メールアドレスで登録</h2>
  <p class="cmn-aligncenter mb-30">以下の内容を入力してください </p>
  <div class="input-wrap mb-5">
    <InputEmail 
      title="メールアドレス"
      size="full" 
      placeholder="<EMAIL>"
      :required="true"
      :value="String(data.email)"
      :error="getValidationMessage(v$.email)"
      @update="data.email = $event"
    />
  </div>
  <p class="indent-1em mb-25 size--sm">※半角英数字</p>
  <div class="input-wrap mb-5">
    <InputPassword 
      title="パスワード"
      size="full" 
      placeholder="パスワードを入力してください"
      :required="true"
      :value="String(data.password)"
      :error="getValidationMessage(v$.password)"
      @update="data.password = $event"
      />
      <p v-if="error" class="input-error">{{ error }}</p>
  </div>
  <p class="indent-1em mb-25 size--sm">※英小文字 英大文字 数字をそれぞれ1文字以上含み 8～50文字になるよう入力してください </p>
  <div class="input-wrap mb-40">
    <InputPassword 
      title="パスワード (確認用)"
      size="full" 
      placeholder="パスワードを再度入力してください"
      :required="true"
      :value="String(data.password_conf)"
      :error="getValidationMessage(v$.password_conf)"
      @update="data.password_conf = $event"
      />
      <p v-if="error" class="input-error">{{ error }}</p>
  </div>
  <div class="btn-2col">
    <ButtonMainColor size="lg" baseColor="accent" @click="onClickNext">次へ</ButtonMainColor>
    <ButtonMainColor size="lg" baseColor="glay" @click="emits('back')">戻る</ButtonMainColor>
  </div>
</div>
</template>

<script lang="ts">
export type InputStep1 = {
  email: string;
  password: string;
  password_conf: string;
};
export const InputStep1Default = {
  email: '',
  password: '',
  password_conf: ''
} as InputStep1;
</script>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, email, helpers, minLength, maxLength, sameAs } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import { useCloned } from '@vueuse/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";

interface Props {
  input: InputStep1;
  errors: GraphQLValidationErrors | null;
}

const props = withDefaults(defineProps<Props>(), {
  input: () => InputStep1Default,
  errors: () => {
    return {};
  }
});

const emits = defineEmits<{
  (e: 'back'): void;
  (e: 'next'): void;
  (e: 'change', input: InputStep1): void;
}>();

// 入力項目
const data = ref(useCloned(props.input).cloned.value as InputStep1)

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

const rules = computed(() => {
  return {
    email: { 
      required: helpers.withMessage(validationMessage.required('メールアドレス'), required),
      email: helpers.withMessage(validationMessage.email('メールアドレス'), email)
    },
    password: { 
      required: helpers.withMessage(validationMessage.required('パスワード'), required),
      minLength: helpers.withMessage(validationMessage.minLength('パスワード', 8), minLength(8)),
      maxLength: helpers.withMessage(validationMessage.maxLength('パスワード', 50), maxLength(50)),
      regex:  helpers.withMessage(validationMessage.password('パスワード'), validationPassword)
    },
    password_conf: { 
      required: helpers.withMessage(validationMessage.required('パスワード (確認用)'), required),
      minLength: helpers.withMessage(validationMessage.minLength('パスワード', 8), minLength(8)),
      maxLength: helpers.withMessage(validationMessage.maxLength('パスワード', 50), maxLength(50)),
      regex:  helpers.withMessage(validationMessage.password('パスワード'), validationPassword),
      sameAs: helpers.withMessage('パスワードが一致していません ', sameAs(data.value.password))
    },
  };
});

// バリデーション
const v$ = useVuelidate(rules, data, { $externalResults });

// 編集モーダル非表示
const onClickNext = async() => {
  // 全体エラーをリセット
  error.value = '';

  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();

  await v$.value.$validate();
  if (v$.value.$error) {
    return false;
  }

  // エラーなし
  emits('change', data.value)
  emits('next')
};

onMounted(async() => {
  // サーバサイドエラーがあれば  
  if (props.errors) {
    $externalResults.value = useCloned(props.errors.input).cloned.value;
    if ($externalResults.value?.email?.[0] == 'メールアドレスの値は既に存在しています ') {
      $externalResults.value.email[0] = 'すでに会員登録されているメールアドレスです ';
    }
    await v$.value.$validate();
  }
});
</script>