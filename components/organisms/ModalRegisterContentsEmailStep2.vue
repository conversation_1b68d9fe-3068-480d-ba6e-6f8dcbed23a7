<template>
  <div class="wrap">
    <h2 class="cmn-title size--lg cmn-aligncenter mt-10">会員情報の登録</h2>
    <p class="cmn-aligncenter mb-30">以下の内容を入力してください </p>
    <div class="mb-5">
      <InputKanjiFullName 
        :required="true"
        :values="{last_name: data.last_name, first_name: data.first_name}"
        :errors="v$"
        :isSubModal="true"
        @showSubModal="emits('showSubModal')" 
        @closeSubModal="emits('closeSubModal')" 
        @change="data.last_name = $event.last_name; data.first_name = $event.first_name"
      />
    </div>

    <div class="mb-25">
      <div class="mb-10"><InputCalendar
        title="挙式日"
        size="full" 
        placeholder="2027/04/01"
        :required="true"
        :value="(! data.wedding_date_is_null) ? String(data.wedding_date) : ''"
        :error="getValidationMessage(v$.wedding_date)"
        :disabled="(data.wedding_date_is_null)"
        @change="data.wedding_date = $event"
      /></div>
      <InputCheck
        :value="(data.wedding_date_is_null) ? ['1'] : []"
        :items="[{value: '1', label: 'まだ決まっていない'}]"
        @change="($event[0] == '1') ? data.wedding_date_is_null = true : data.wedding_date_is_null = false"
      />
    </div>
    <div class="mb-25">
      <div class="mb-10"><InputText
        title="挙式会場名"
        size="full" 
        placeholder="会場名を入力してください"
        :required="true"
        :value="(! data.wedding_venue_is_null) ? data.wedding_venue : ''"
        :error="getValidationMessage(v$.wedding_venue)"
        :disabled="(data.wedding_venue_is_null)"
        @input="data.wedding_venue = $event.target.value"
      /></div>
      <InputCheck
        :value="(data.wedding_venue_is_null) ? ['1'] : []"
        :items="[{value: '1', label: 'まだ決まっていない'}]"
        @change="($event[0] == '1') ? data.wedding_venue_is_null = true : data.wedding_venue_is_null = false"
      />
    </div>
    <div class="mb-40">
      <InputSelect
        title="招待人数（目安）"
        size="full" 
        :required="true"
        :value="String(data.guest_count)"
        :options="GUEST_COUNT_OPTIONS"
        :error="getValidationMessage(v$.guest_count)"
        @change="data.guest_count = parseInt($event.target.value)"
      />
    </div>
    <div class="btn-wrap" v-if="isBackHide">
      <ButtonMainColor size="lg" baseColor="accent" @click="onClickNext">次へ</ButtonMainColor>
    </div>
    <div class="btn-2col" v-else>
      <ButtonMainColor size="lg" baseColor="accent" @click="onClickNext">次へ</ButtonMainColor>
      <ButtonMainColor size="lg" baseColor="glay" @click="emits('back')">戻る</ButtonMainColor>
    </div>
  </div>
</template>

<script lang="ts">
export type InputStep2 = {
  last_name: string;
  first_name: string;
  wedding_date: string;
  wedding_date_is_null: boolean;
  wedding_venue: string;
  wedding_venue_is_null: boolean;
  guest_count: number;
};

export const InputStep2Default = {
  last_name: '',
  first_name: '',
  wedding_date: '',
  wedding_date_is_null: false,
  wedding_venue: '',
  wedding_venue_is_null: false,
  guest_count: 0
};
</script>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import { useCloned } from '@vueuse/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
const { $dayjs } : any = useNuxtApp();

interface Props {
  input: InputStep2;
  errors: GraphQLValidationErrors | null;
  isBackHide?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  input: () => InputStep2Default,
  errors: () => {
    return {};
  },
  isBackHide: false
});

const emits = defineEmits<{
  (e: 'back'): void;
  (e: 'next'): void;
  (e: 'change', input: InputStep2): void;
  (e: 'showSubModal'): void;
  (e: 'closeSubModal'): void;
}>();

// 入力項目
const data = ref(useCloned(props.input).cloned.value as InputStep2)

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

const rules = computed(() => {
  let rules = {
    last_name: { 
      required: helpers.withMessage(validationMessage.required('姓'), required),
    },
    first_name: { 
      required: helpers.withMessage(validationMessage.required('名'), required)
    },
    wedding_date: { 
      required: helpers.withMessage(validationMessage.required('挙式日'), required)
    },
    wedding_date_is_null: { 
    },
    wedding_venue: { 
      required: helpers.withMessage(validationMessage.required('挙式会場名'), required)
    },
    wedding_venue_is_null: { 
    },
    guest_count: { 
      required: helpers.withMessage(validationMessage.required('招待人数（目安）'), required)
    },
  };

  if (data.value.wedding_date_is_null) rules.wedding_date = {} as any;
  if (data.value.wedding_venue_is_null) rules.wedding_venue = {} as any;
  return rules;
});

// バリデーション
const v$ = useVuelidate(rules, data, { $externalResults });

// 編集モーダル非表示
const onClickNext = async() => {
  // 全体エラーをリセット
  error.value = '';

  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();

  await v$.value.$validate();
  if (v$.value.$error) {
    return false;
  }

  // エラーなし
  emits('change', data.value)
  emits('next')
};

onMounted(async() => {
  // サーバサイドエラーがあれば  
  if (props.errors) {
    $externalResults.value = useCloned(props.errors).cloned.value;
    await v$.value.$validate();
  }
});
</script>