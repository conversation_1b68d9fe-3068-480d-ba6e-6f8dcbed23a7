<template>
  <div class="wrap">
    <h2 class="cmn-title size--lg cmn-aligncenter mt-10">アンケート</h2>
    <p class="cmn-aligncenter mb-30">ファヴォリをどこで初めて知りましたか？</p>
    <div class="inputChecks">
      <InputCheck
        :value="data.answers"
        :items="QuestionnaireWayOptions"
        :error="getValidationMessage(v$.answers)"
        @change="data.answers = $event"
      />
    </div>
    <div class="mb-5"><InputText
      v-if="data.answers.indexOf('その他') !== -1"
      size="full" 
      placeholder="その他を入力してください"
      :required="true"
      :value="data.answer_text"
      :error="getValidationMessage(v$.answer_text)"
      @input="data.answer_text = $event.target.value"
    /></div>
    <div class="btn-2col mt-60">
      <ButtonMainColor size="lg" baseColor="accent" @click="onClickNext">次へ</ButtonMainColor>
      <ButtonMainColor size="lg" baseColor="glay" @click="emits('back')">戻る</ButtonMainColor>
    </div>
  </div>
</template>

<script lang="ts">
export type InputStep3 = {
  answers: string[];
  answer_text: string;
};

export const InputStep3Default = {
  answers: [],
  answer_text: ''
};
</script>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, maxLength ,helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import { useCloned } from '@vueuse/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";

interface Props {
  input: InputStep3;
  errors: GraphQLValidationErrors | null;
}
const props = withDefaults(defineProps<Props>(), {
  input: () => InputStep3Default,
  errors: () => {
    return {};
  }});

const emits = defineEmits<{
  (e: 'back'): void;
  (e: 'next'): void;
  (e: 'change', input: InputStep3): void;
}>();

// グループリスト
const QuestionnaireWayOptions = computed(() => {
  return MEMBER_REGIST_QUESTIONNAIRE_WAYS.map(label => {
    return {value: label, label: label};
  });
});

// 入力項目
const data = ref(useCloned(props.input).cloned.value as InputStep3)

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

const rules = computed(() => {
  let rules = {
    answers: { 
      required: helpers.withMessage(validationMessage.required('アンケート'), required),
    },
    answer_text: { 
      required: helpers.withMessage(validationMessage.required('アンケート(その他)'), required),
      maxLength: helpers.withMessage(validationMessage.maxLength('アンケート(その他)', 255), maxLength(255)),
    },
  };

  if (data.value.answers.indexOf('その他') === -1) rules.answer_text = {} as any;
  return rules;
});

// バリデーション
const v$ = useVuelidate(rules, data, { $externalResults });

// 編集モーダル非表示
const onClickNext = async() => {
  // 全体エラーをリセット
  error.value = '';

  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();

  await v$.value.$validate();
  if (v$.value.$error) {
    return false;
  }

  if (data.value.answers.indexOf('その他') === -1) data.value.answer_text = '';
  // エラーなし
  emits('change', data.value)
  emits('next')
};


onMounted(async() => {
  // サーバサイドエラーがあれば  
  if (props.errors) {
    $externalResults.value = useCloned(props.errors).cloned.value;
    if (props.errors?.member_regist_questionnaire?.[1]?.answer) {
      $externalResults.value.answer_text = props.errors?.member_regist_questionnaire?.[1]?.answer;
    }

    await v$.value.$validate();
  }
});
</script>