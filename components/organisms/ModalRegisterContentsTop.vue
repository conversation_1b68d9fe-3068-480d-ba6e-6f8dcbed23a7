<template>
  <div class="wrap">
    <Loading v-if="isLoading" :fullscreen="true"></Loading>
    <h2 class="cmn-title size--lg cmn-aligncenter mt-10">はじめてご利用の方</h2>
    <!-- <p class="cmn-aligncenter size--lg mb-20">すでに会員の方は
      <a v-if="router.currentRoute.value.path == '/login'" @click="hideModalRegister()" class="link-main">ログイン</a>
      <a v-else @click="showModalLogin" class="link-main bold">ログイン</a>
    </p> -->
    <p class="mb-20"><NuxtLinkToSSR to="/terms" target="_blank" class="link-accent">ご利用規約</NuxtLinkToSSR> <NuxtLinkToSSR to="/privacy" target="_blank" class="link-accent">プライバシーポリシー</NuxtLinkToSSR>に同意の上 下記の方法でご登録またはサインアップしてください </p>
    <div class="btns">
      <!-- <a class="is-apple" href="#">Appleで登録</a> -->
      <a class="is-google" href="#" @click="onClickSnsRegister('google')">Googleで登録</a>
      <p v-if="errors?.google" class="input-error cmn-aligncenter mb-20" style="margin-top: -5px;" v-html="nl2br(errors.google)"></p>
      <!-- <a class="is-yahoo" href="#">Yahoo! JAPAN IDで登録</a> -->
      <!-- <a class="is-facebook" href="#">Facebookで登録</a> -->
      <!-- <a class="is-twitter" href="#">Twitterで登録</a> -->
      <a class="is-line" href="#" @click="onClickSnsRegister('line')">LINEで登録</a>
      <p v-if="errors?.line" class="input-error cmn-aligncenter mb-20" style="margin-top: -5px;" v-html="nl2br(errors.line)"></p>
      <NuxtLink class="is-mail" @click="emits('changePage', 'emailStep1')">メールアドレスで登録</NuxtLink>
    </div>
    <div class="cmn-aligncenter mt-30">
      <a @click="showModalLogin()" class="link-accent link-underline size--md">すでに会員の方はこちら</a>
    </div>
  </div>
</template>

<script lang="ts" setup>
interface Props {
  errors?: any;
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => {
    return {};
  }
});

const { showModalLogin } = useModalLoginState();
const { hideModalRegister, setSnsRegister } = useModalRegisterState();
const emits = defineEmits<{
  (e: 'changePage', value: string): void;
}>();

const runtimeConfig = useRuntimeConfig()
const router = useRouter(); 

const isLoading = ref(false);

const onClickSnsRegister = async(sns = '') => {
  setSnsRegister();
  isLoading.value = true;
  const apiEndpoint = runtimeConfig.public.apiEndpoint.replace('/graphql', '/api');
  const response = await fetch(apiEndpoint+'/auth/'+sns+'/url');
  const data = await response.json();
  isLoading.value = false;
  window.location.href = data?.url;
};
</script>

<style lang="scss" scoped>
.btns{
  a{
    display: block;
    text-align: center;
    text-decoration: none;
    font-size: 12px;
    line-height: 1;
    color: #333;
    border: 1px solid #9C9C9C;
    border-radius: 4px;
    padding: 12px;
    margin: 14px 0;
    background-repeat: no-repeat;
    background-size: 18px;
    background-position: left 16px center;
    cursor: pointer;
    &.is-apple{
      background-image: url('@/assets/images/icon-login-apple.png');
    }
    &.is-google{
      background-image: url('@/assets/images/icon-login-google.png');
    }
    &.is-yahoo{
      background-image: url('@/assets/images/icon-login-yahoo.png');
    }
    &.is-facebook{
      color: #FFF;
      background-color: #1877F2;
      border-color: #1877F2;
      background-image: url('@/assets/images/icon-login-facebook.png');
    }
    &.is-twitter{
      color: #FFF;
      background-color: #2E92D1;
      border-color: #2E92D1;
      background-image: url('@/assets/images/icon-login-twitter.png');
    }
    &.is-line{
      color: #FFF;
      background-color: #06C755;
      border-color: #06C755;
      background-image: url('@/assets/images/icon-login-line.png');
    }
    &.is-mail{
      background-image: url('@/assets/images/icon-login-mail.png');
    }
  }
}
</style>