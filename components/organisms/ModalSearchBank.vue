
<template>
  <Modal class="modalMemberEdit" @close="$emit('close')">
    <!-- <template #header>
      <a v-if="props.isSubModal" class="backword" @click.prevent="$emit('close')"><i class="material-symbols-outlined">arrow_back</i></a>
      銀行 / 支店を探す
    </template> -->
    <template #header>
      <span>銀行 / 支店を探す</span>
    </template>

    <template #main>
      <div class="modalBank">
        <div v-if="isLoading" class="input-list"><Loading></Loading></div>
        <div class="boxInputBank" v-if="isShowBank">
          <h2 class="cmn-title">銀行を選択</h2>
          <p>選択肢の中にお探しの銀行がない場合は、銀行名または銀行コードを検索ボックスに入力してください。</p>
          <InputSearch
            placeholder="銀行名 または 銀行コードを入力"
            size="full"
            class="input-search"
            :value="inputBankSearch"
            @input="inputBankSearch = $event.target.value"
          />
          <ul class="inputBankList">
            <li v-for="bank in bankList" :key="bank.code">
              <a @click.prevent="onClickBank(bank)">
                {{ bank.name }}
                ({{ bank.code }})
                <i class="icn-right material-symbols-outlined">chevron_right</i>
              </a>
            </li>
          </ul>
        </div>
        <div class="boxInputBank" v-else-if="isShowBranch">
          <h2 class="cmn-title">支店を選択</h2>
          <p>選択肢の中にお探しの支店がない場合は、支店名または支店コードを検索ボックスに入力してください。</p>
          <div class="box-info mb-12"><div class="box-inner">
            <p>銀行名　　 ： {{ inputBank?.name }}</p>
            <p>銀行コード ： {{ inputBank?.code }}</p>
          </div></div>
          <InputSearch
            placeholder="支店名 または 支店コードを入力"
            size="full"
            class="input-search"
            :value="inputBranchSearch"
            @input="inputBranchSearch = $event.target.value"
          />
          <ul class="inputBankList">
            <li v-for="branch in branchList" :key="branch.code">
              <a @click.prevent="onClickBranch(branch)">
                {{ branch.name }}
                ({{ branch.code }})
              </a>
            </li>
          </ul>
        </div>
        <div class="boxInputBank" v-else>
          <h2 class="cmn-title">銀行 / 支店の確認</h2>
          <p>以下の内容でよろしいですか？</p>
          <div class="box-info"><div class="box-inner">
            <p>銀行名　　 ： {{ inputBank?.name }}</p>
            <p>銀行コード ： {{ inputBank?.code }}</p>
            <p>支店名　　 ： {{ formattedBranchName }}</p>
            <p>支店コード ： {{ inputBranch?.code }}</p>
          </div></div>
        </div>
        <div class="boxInputBankText" v-if="! inputBranch?.code">
          <a @click.prevent="isShowInputText = ! isShowInputText" class="boxInputBankTextLink link-accent size--md bold mb-20" :class="{'is-show': isShowInputText}">
            銀行 / 支店が見つからないとき（ご自身で入力）
            <i class="icn-right material-symbols-outlined">chevron_right</i>
          </a>
          <div v-if="isShowInputText">
            <div class="info-text">銀行、支店情報を以下に入力してください。</div>
            <p v-if="error" class="input-error">{{ error }}</p>
            <div class="row mb-24">
              <InputText
                title="銀行名 / 銀行コード"
                :required="true"
                placeholder="銀行名"
                :value="input.bank_name"
                :error="getValidationMessage(v$.bank_name)"
                @input="input.bank_name = $event.target.value"
              />
              <span class="attr">/</span>
              <InputNumber
                :type="'number'"
                :required="true"
                placeholder="銀行コード"
                class="mt-19"
                :value="input.bank_code"
                :error="getValidationMessage(v$.bank_code)"
                @input="input.bank_code = $event.target.value"
              />
            </div>
            <div class="row mb-24">
              <InputText
                title="支店名 / 支店コード"
                :required="true"
                placeholder="支店名"
                :value="input.branch_name"
                :error="getValidationMessage(v$.branch_name)"
                @input="input.branch_name = $event.target.value"
              />
              <span class="attr">/</span>
              <InputNumber
                :type="'number'"
                :required="true"
                placeholder="支店コード"
                class="mt-19"
                :value="input.branch_code"
                :error="getValidationMessage(v$.branch_code)"
                @input="input.branch_code = $event.target.value"
              />
            </div>
            <InputText
              title="支店名（カナ）"
              :required="true"
              placeholder="シンジユク"
              size="full"
              :value="input.branch_name_kana"
              :error="getValidationMessage(v$.branch_name_kana)"
              @input="input.branch_name_kana = $event.target.value"
            />
          </div>
        </div>
      </div>
    </template>
    <template #footer>
      <footer v-if="!isShowBank && !isShowBranch" class="modal-footer">
        <button class="btn btn-default-outline btn-block" @click.prevent="isBankConfirm ? onResetBank() : onResetBranch()">入力に戻る</button>
        <button class="btn btn-secondary btn-block" @click.prevent="onClickSave()">次へ</button>
      </footer>
      <footer v-else-if="!isShowBank" class="modal-footer">
        <button class="btn btn-default-outline btn-block" @click.prevent="onResetBank()">銀行検索に戻る</button>
        <button v-if="isShowInputText" class="btn btn-secondary btn-block" @click.prevent="isBranchConfirm = true; onClickConfirm()" :disabled="!isFormValid">入力内容の確認</button>
        <button v-else class="btn btn-secondary btn-block" disabled>入力内容の確認</button>
      </footer>
      <footer v-else-if="isShowInputText" class="modal-footer">
        <button class="btn btn-secondary btn-block" @click.prevent="isBankConfirm = true; onClickConfirm()" style="max-width:none;" :disabled="!isFormValid">入力内容の確認</button>
      </footer>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { required, helpers, minLength, maxLength, sameAs, numeric } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { debounce } from 'perfect-debounce'

const config = useRuntimeConfig();
const apiKey = config.public.bankCode.apiKey;
const props = withDefaults(defineProps<{
  value?: {
		bank_name: string;
		bank_code: string;
		branch_code: string;
		branch_name: string;
		branch_kana: string;
	},
  isSubModal?: boolean
}>(), {
  value: {
		bank_name: "",
		bank_code: "",
		branch_code: "",
		branch_name: "",
		branch_kana: "",
	},
  isSubModal: false,
});

const emits = defineEmits<{
  update: [],
  close: [],
  reload: []
}>();


const isShowInputText = ref(false)

// 検索項目
const inputBankSearch = ref('')
const inputBank = ref({} as any)
const bankList = ref([] as any[])

const inputBranchSearch = ref('')
const inputBranch = ref({} as any)
const branchList = ref([] as any[])

const isShowBank = ref(true);
const isShowBranch = ref(true);
const isBankConfirm = ref(false);
const isBranchConfirm = ref(false);


// 入力項目
const input = ref({
  bank_name: '',
  bank_code: '',
  branch_code: '',
  branch_name: '',
  branch_name_kana: '',
} as {
  bank_name: string;
  bank_code: string;
  branch_code: string;
  branch_name: string;
  branch_name_kana: string;
})

const isFormValid = computed(() => {
  return Object.values(input.value).every(field => field.trim() !== '');
})

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return {
    bank_name: { 
      required: helpers.withMessage(validationMessage.required('銀行名'), required),
      maxLength: helpers.withMessage(validationMessage.maxLength('銀行名', 50), maxLength(50)),
    },
    bank_code: { 
        required: helpers.withMessage(validationMessage.required('銀行コード'), required),
				numeric: helpers.withMessage(validationMessage.numeric('銀行コード'), numeric),
				minLength: helpers.withMessage(validationMessage.minLength('銀行コード', 4), minLength(4)),
				maxLength: helpers.withMessage(validationMessage.maxLength('銀行コード', 4), maxLength(4)),
    },
    branch_name: { 
      required: helpers.withMessage(validationMessage.required('支店名'), required),
      maxLength: helpers.withMessage(validationMessage.maxLength('支店名', 50), maxLength(50)),
    },
    branch_code: { 
        required: helpers.withMessage(validationMessage.required('支店コード'), required),
				numeric: helpers.withMessage(validationMessage.numeric('支店コード'), numeric),
				minLength: helpers.withMessage(validationMessage.minLength('支店コード', 3), minLength(3)),
				maxLength: helpers.withMessage(validationMessage.maxLength('支店コード', 3), maxLength(3)),
    },
    branch_name_kana: {
      required: helpers.withMessage(validationMessage.required('支店名（カナ）'), required),
      maxLength: helpers.withMessage(validationMessage.maxLength('支店名（カナ）', 50), maxLength(50)),
      regex: helpers.withMessage('支店名（カナ）はカナ・アルファベットで入力してください', helpers.regex(/^[ァ-ヶー－Ａ-Ｚａ-ｚA-Za-z]+$/u)),
    }
  }
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// 更新中のLoading
const isLoading = ref(false);

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }
  if(isBankConfirm.value || isBranchConfirm.value){
    input.value.branch_name = input.value.branch_name + (input.value.branch_name_kana ? ` (${input.value.branch_name_kana})` : '');
  }
  isLoading.value = false;
  emits('update', input.value);
  emits('close');
};
onMounted(async () => {
  isLoading.value = true;
  await onSearchBank();
  isLoading.value = false;
});

// name_kanaが空でない場合に(name_kana)を付け加える
const formattedBranchName = computed(() => {
  if(isBankConfirm.value || isBranchConfirm.value){
    return inputBranch.value.name + (input.value.branch_name_kana ? ` (${input.value.branch_name_kana})` : '');
  }else{
    return inputBranch.value.name;
  }
});

const onClickBank = async(bank:any) => {
  isLoading.value = true;
  inputBank.value = JSON.parse(JSON.stringify(bank));
  input.value.bank_name = inputBank.value?.name;
  input.value.bank_code = inputBank.value?.code;
  input.value.branch_name = '';
  input.value.branch_code = '';
  input.value.branch_name_kana = '';
  isShowBank.value = false;
  isBankConfirm.value = false;
  isShowInputText.value = false;
  await onSearchBranch();
  isLoading.value = false;
}
const onResetBank = async() => {
  inputBank.value = {};
  inputBranch.value = {};
  // input.value.bank_name = '';
  // input.value.bank_code = '';
  isBankConfirm.value = false;
  isShowBank.value = true;
  isBranchConfirm.value = false;
  isShowBranch.value = true;
}
const onClickBranch = async(branch:any) => {
  isLoading.value = true;
  inputBranch.value = JSON.parse(JSON.stringify(branch));
  input.value.branch_name = inputBranch.value?.name;
  input.value.branch_code = inputBranch.value?.code;
  input.value.branch_name_kana = inputBranch.value.fullWidthKana;
  isShowBranch.value = false;
  isBranchConfirm.value = false;
  isLoading.value = false;
}
const onResetBranch = async() => {
  inputBranch.value = {};
  // input.value.branch_name = '';
  // input.value.branch_code = '';
  // input.value.branch_name_kana = '';
  isBranchConfirm.value = false;
  isShowBranch.value = true;
}

const onClickConfirm = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }
  isLoading.value = true;
  inputBank.value.name = input.value.bank_name;
  inputBank.value.code = input.value.bank_code;

  inputBranch.value.name = input.value.branch_name;
  inputBranch.value.code = input.value.branch_code;
  inputBranch.value.fullWidthKana = input.value.branch_name_kana;

  isShowBank.value = false;
  isShowBranch.value = false;
  isLoading.value = false;
}

// 数字4桁の正規表現
const isFourDigitNumber = /^\d{4}$/;

// 銀行検索
watch(inputBankSearch, async() => {
  await onSearchBank();
})
const onSearchBank = debounce(async() => {
  try {
    let url = 'https://apis.bankcode-jp.com/v3/banks/?&filter=name%3D%3D*' + inputBankSearch.value + '*&apikey=' + apiKey + '&cursor=&limit=5';
    if(inputBankSearch.value){
      if (isFourDigitNumber.test(inputBankSearch.value)) {
        url = 'https://apis.bankcode-jp.com/v3/banks/?&filter=code%3D%3D*' + inputBankSearch.value + '*&apikey=' + apiKey + '&cursor=&limit=50';
      } else {
        url = 'https://apis.bankcode-jp.com/v3/banks/?&filter=name%3D%3D*' + inputBankSearch.value + '*&apikey=' + apiKey + '&cursor=&limit=50';
      }
    }
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const result = await response.json();
    bankList.value = result.banks;
  } catch (error) {
    console.error('Error fetching JSON data:', error)
    console.log(error)
  }
}, 1000);


// 支店検索
watch(inputBranchSearch, async() => {
  await onSearchBranch();
})
const onSearchBranch = debounce(async() => {
  try {
    let url = 'https://apis.bankcode-jp.com/v3/banks/'+inputBank.value?.code+'/branches/?&filter=name%3D%3D*'+inputBranchSearch.value+'*&apikey='+apiKey+'&cursor=&limit=50';
    if(inputBranchSearch.value) {
      if (isFourDigitNumber.test(inputBankSearch.value)) {
        url = 'https://apis.bankcode-jp.com/v3/banks/'+inputBank.value?.code+'/branches/?&filter=code%3D%3D*'+inputBranchSearch.value+'*&apikey='+apiKey + '&cursor=&limit=50';
      }
    }
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    const result = await response.json();
    branchList.value = result.branches;
  } catch (error) {
    console.error('Error fetching JSON data:', error)
    console.log(error)
  }
}, 1000);

</script>

<style lang="scss" scoped>
.modalBank {
  padding-bottom: 40px;
}
.boxInputBankTextLink {
  .icn-right {
    transform: rotate(90deg);
    transition: transform 0.35s ease;
  }
  &.is-show .icn-right {
    transform: rotate(-90deg);
  }
}
.inputBankList {
  margin-top: 30px;
  li {
    border-bottom: 1px solid var(--Gray, #D9D9D9);
  }
  a {
    color: var(--black, var(--text-black, #333));
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.7px;
    display: block;
    padding: 17px 12px;
    position: relative;
    .icn-right {
      position: absolute;
      right: 10px;
      top: 50%;
      margin-top: -.5em;
    }
  }
}
</style>
<style lang="scss">
.boxInputBankText {
  margin-top: 24px;
  .row {
    display: flex;
    .attr {
      display: inline-block;
      padding: 35px 10px 0;
      font-size: 12px;
    }
    label {
      width: 120px;
    }
  }
  .info-text {
    font-size: 14px;
    margin-bottom: 17px;
  }
}
.input-search .inputWrap{
  width: 100%;
  border-radius: 5px;
  border: 1px solid #B18A3E;
}
</style>