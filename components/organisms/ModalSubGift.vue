<template>
<div class="modalWrap">
  <div class="modalBg" @click.prevent="closeModal($event)"></div>
  
    <component :is="currentModalComponent" :title="currentModalComponentTitle" @emitClose="closeModal" @emitShowModal="convertibleModal" />
  
</div>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array;
}

const params = withDefaults(defineProps<Props>(), {
  data: {},
});

import Modaldefault from '@/components/organisms/ModalSubGiftAssign.vue'
import ModalSubGiftSelect from '@/components/organisms/ModalSubGiftSelect.vue'
import ModalSubGiftDetail from '@/components/organisms/ModalSubGiftDetail.vue'
const components = {
  'Modaldefault': Modaldefault , 
  'ModalSubGiftSelect': ModalSubGiftSelect , 
  'ModalSubGiftDetail': ModalSubGiftDetail , 
}

let currentModalComponent = ref(components['Modaldefault']);
let currentModalComponentTitle = ref(params.data['Modaldefault']);

const convertibleModal = (index) => {
  currentModalComponent.value = components[index];
  currentModalComponentTitle = params.data[index];
}

const closeModal = (e) => {
  const target = e.currentTarget.closest('.modalWrap');
  target.classList.add('closing');
  setTimeout(() => {target.classList.add('close')}, 350);
};
</script>

<style lang="scss" scoped>
.modalWrap {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  opacity: 1;
  transition: opacity 0.35s ease;
  z-index: 777;
  .modalBg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
  }
  &.close {
    display: none;
    z-index: 0;
  }
  &.closing {
    opacity: 0;
    pointer-events: none;
    * {
      pointer-events: none;
    }
  }
  :deep(.backword) {
    margin-right: 10px;
    cursor: pointer;
    img {
      transform: scale(-1,1);
    }
  }
  :deep(.standAlone) {
    .backword {
      display: none;
    }
  }
}

@include sp {
:deep(.contents) {
  .contentsInner {
    padding-top: 7px;
  }
}

.modalWrap {
  .modalBg {
    display: none;
  }
}
}
</style>