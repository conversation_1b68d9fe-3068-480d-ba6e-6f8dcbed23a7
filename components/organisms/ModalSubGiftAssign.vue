
<template>
<ModalContainer class="standAlone" @emitClose="closeModal">

  <template #header>
    <a class="backword" @click.prevent="convertibleModal('Modaldefault')"><img src="@/assets/images/icon-arrow_forward-b.svg" alt="戻る"></a>{{ props.title }}
  </template>
  <template #main>
    <div class="row">
      <h3>2品目</h3>
      <div class="detail">
        <div class="state">
          未選択
        </div>
        <div class="control">
          <InputCheck
            :items="[
              {
                value: 1,
                checked: false,
                label: 'あとで選ぶ',
              }
            ]"
            class="later"
          />
          <div class="button_wrap">
            <ButtonMainColor baseColor="reversal" :buttonsize="120" to="./">ギフトを選ぶ</ButtonMainColor>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <h3>3品目</h3>
      <div class="detail">
        <div class="state">
          <ShowGiftDetails :data="apiListGift" />
        </div>
        <div class="control">
          <InputCheck
            :items="[
              {
                value: 1,
                checked: false,
                disabled: true,
                label: 'あとで選ぶ',
              }
            ]"
            class="later"
          />
          <div class="button_wrap">
            <ButtonMainColor baseColor="reversal" :buttonsize="120" to="./">変更する</ButtonMainColor>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <h3>4品目</h3>
      <div class="detail">
        <div class="state">
          未選択
        </div>
        <div class="control">
          <InputCheck
            :items="[
              {
                value: 1,
                checked: false,
                label: 'あとで選ぶ',
              }
            ]"
            class="later"
          />
          <div class="button_wrap">
            <ButtonMainColor baseColor="reversal" :buttonsize="120" to="./">ギフトを選ぶ</ButtonMainColor>
          </div>
        </div>
      </div>
    </div>
  </template>
  <template #footer>
    <ShowFooterBarFrow :data="ModalSubGiftAssignFooterBarFrow" />
  </template>

</ModalContainer>
</template>

<script lang="ts" setup>
interface Props {
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
});
const emit = defineEmits(['closeModal , convertibleModal']);

const closeModal = (target) => {
  emit('emitClose' , target);
};

const convertibleModal = (target) => {
  emit('emitShowModal' , target);
};

const apiListGift = {
  param: [
    {
      type: "listgiftwrapping",
      datail: [
        {
          title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
          category: "選べる縁起物",
          close: true,
          number : "",
          thumbnail: "/images/sample/thumbnail07.png",
          labels: [
          ],
          price: "",
          priceStrike: "",
          priceUnit: "",
          orderd: "",
        }, 
      ],
    },
  ],
}
const ModalSubGiftAssignFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "戻る",
          link: "#01link",
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "次へ",
          link: "#0link",
        },
      ],
    },
  ],
}
</script>

<style lang="scss" scoped>
.modalContainer {
  :deep(.contents) {
    padding: 0;
  }
}

h3 {
  margin: 0;
  color: $color-accent;
  font-size: 18px;
  font-weight: 400;
  line-height: 120%;
  letter-spacing: 0.72px;
}

.detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 19px 0 21px;
  .control {
    display: flex;
    align-items: center;
    span {
      white-space: nowrap;
    }
    a {
      margin-bottom: 0;
      margin-left: 25px;
    }
  }
}
.row {
  padding: 0 20px;
  & ~ .row {
    margin-top: 3px;
    padding-top: 24px;
    border-top: 4px solid $color-lightgray2;
  }
}

@include sp {
.row {
  padding: 0 16px;
  &:last-of-type {
    border-bottom: 4px solid $color-lightgray2;
  }
}

h3 {
  font-size: 16px;
}

.detail {
  display: block;
  padding: 21px 0 21px;
  font-size: 14px;
  .state {
    :deep(.listgift) {
      .thumbnail {
        width: 63px;
        .close {
          top: 0;
          right: 0;
        }
      }
      .detail {
        .title {
          margin-top: 0;
          font-size: 12px;
        }
      }
    }
  }
  .control {
    justify-content: space-between;
    width: 100%;
    margin-top: 12px;
  }
}
}
</style>