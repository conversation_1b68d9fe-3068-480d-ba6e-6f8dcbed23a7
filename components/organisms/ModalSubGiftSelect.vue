
<template>
<ModalContainer class="standAlone" @emitClose="closeModal">

  <template #header>
    <a class="backword" @click.prevent="convertibleModal('Modaldefault')"><img src="@/assets/images/icon-arrow_forward-b.svg" alt="戻る"></a>{{ props.title }}
  </template>
  <template #main>
    <ListItemsPrice :data="apiListItemsPrice" :subGift="true" />
  </template>
  <template #footer>
  </template>

</ModalContainer>
</template>

<script lang="ts" setup>
interface Props {
  title?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
});
const emit = defineEmits(['closeModal , convertibleModal']);

const closeModal = (target) => {
  emit('emitClose' , target);
};

const convertibleModal = (target) => {
  emit('emitShowModal' , target);
};

const apiListItemsPrice = {
  param: [
    {
      datail: [
        {
          id: "0000000001",
          title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
          thumbnail: "/images/sample/image02.png",
          labels: [
            "数量限定キャンペーン",
          ],
          price: "4510",
          priceStrike: "6600",
          unit: "セット",
          assignment: "3",
          purchased: "10",
          control: true,
        }, 
        {
          id: "00000000002",
          title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
          thumbnail: "/images/sample/image03.png",
          labels: [
          ],
          price: "4510",
          unit: "部",
          assignment: "3",
          purchased: "10",
          control: true,
        }, 
        {
          id: "0000000003",
          title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
          thumbnail: "/images/sample/image02.png",
          labels: [
            "数量限定キャンペーン",
          ],
          price: "4510",
          priceStrike: "6600",
          unit: "部",
          assignment: "10",
          purchased: "10",
          control: true,
        }, 
        {
          id: "0000000004",
          title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
          thumbnail: "/images/sample/image03.png",
          labels: [
          ],
          price: "4510",
          assignment: "10",
          purchased: "10",
          control: true,
        }, 
        {
          id: "0000000005",
          title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
          thumbnail: "/images/sample/image02.png",
          labels: [
            "数量限定キャンペーン",
          ],
          price: "4510",
          priceStrike: "6600",
          unit: "セット",
          assignment: "10",
          purchased: "10",
          control: true,
        }, 
        {
          id: "0000000006",
          title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
          thumbnail: "/images/sample/image03.png",
          labels: [
          ],
          price: "4510",
          assignment: "10",
          purchased: "10",
          control: true,
        }, 
      ],
    },
  ],
}
</script>

<style lang="scss" scoped>
.modalContainer {
  :deep(.contents) {
    padding: 0;
  }
}
.divider {
  padding: 18px 90px 21px;
  & ~ .divider {
    padding-top: 25px;
    border-top: 8px solid $color-lightgray;
  }
}
p {
  margin-bottom: 10px;
  color: $color-blacktext2;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 0.3px;
  & + p {
    margin-top: 21px;
  }
}
.addList {
  margin-top: 12px;
  font-size: 12px;
  letter-spacing: 0.24px;
  b {
    font-size: 14px;
    font-weight: 700;
    line-height: 150%;
    color: $color-blackLight;
  }
}
h2{
  color: $color-accent;
  font-size: 18px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 21px;
}
dl {
  margin-bottom: 16px;
}
.targetblank {
  display: inline-block;
  position: relative;
  margin: 7px 0 12px;
  padding-left: 20px;
  text-decoration: none;
  color: $color-accent;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  &::before {
    @include BA;
    left: 0;
    width: 17px;
    height: 20px;
    background-image: url(@/assets/images/icon-targetblank-g.svg);
  }
}
.row {
  margin-top: 23px;
  & ~ .row {
    margin-top: 27px;
  }
  &.check {
    margin: 13px 0 14px;
  }
}
:deep(.button--md) {
  display: block;
  max-width: 400px;
  margin: 0 auto 24px;
}
:deep(textarea) {
  min-height: 28em;
  color: #444;
  font-size: 14px;
  line-height: 21px;
  letter-spacing: 1.4px;
}
@include sp {
.divider {
  padding: 20px 16px 16px;
}
.addList {
  margin-top: 17px;
  b {
    margin-left: 5px;
    font-size: 15px;
  }
}
h2{
  font-size: 16px;
}
.row {
  margin-top: 21px;
  & ~ .row {
    margin-top: 21px;
  }
  &.check {
    margin: 14px 0 0;
  }
}
:deep(.button--md) {
  max-width: 343px;
  margin: 20px auto 24px;
}
:deep(textarea) {
  min-height: 32em;
}
}
</style>