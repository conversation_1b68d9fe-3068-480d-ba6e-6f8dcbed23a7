<template>
  <Modal class="modalCreateGuest" @close="$emit('close')" :isShowSubModal="isShowSubModal">
    <template #header>
      ゲスト情報編集
    </template>
    <template #main>
      <span v-if="error" class="input-error">{{ error }}</span>
      <Loading v-if="isLoading"></Loading>
      <FormPrivateInformation 
        :input="input"
        :guestListId="guestListId"
        :webInvitation="guest?.web_invitation"
        :validate="v$"
        @update="onUpdateInput" 
        @showSubModal="isShowSubModal = true" 
        @closeSubModal="isShowSubModal = false" 
        @addFreeInput="onClickAddFreeInput"
        @deleteFreeInput="onClickDeleteFreeInput"
      />
    </template>
    <template #footer>
      <footer class="modal-footer">
        <button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
        <button class="btn btn-secondary btn-block" @click="onClickSave()">完了</button>
      </footer>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, email, numeric, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";

export interface Props {
  guestListId: string;
  guest: Guest;
  parentGuestId: number|null;
}

const props = withDefaults(defineProps<Props>(), {
  guestListId: '',
  parentGuestId: null,
});

const emits = defineEmits<{
  (e: 'close'): void;
}>()

const isShowSubModal = ref(false)

// 更新用データ
const input = ref({
  input: {
    id: props.guest.id,
    guest_list_id: props.guestListId,
    image: {},
    parent_guest_id: props.parentGuestId,
    last_name: props.guest?.last_name,
    first_name: props.guest?.first_name,
    last_name_romaji: props.guest?.last_name_romaji,
    first_name_romaji: props.guest?.first_name_romaji,
    last_name_kana: props.guest?.last_name_kana,
    first_name_kana: props.guest?.first_name_kana,
    guest_honor: props.guest?.guest_honor,
    gender: props.guest?.gender,
    guest_type: props.guest?.guest_type,
    guest_title: props.guest?.guest_title,
    relationship: props.guest?.relationship,
    relationship_name: props.guest?.relationship_name,
    allergies: props.guest?.allergies,
    allergy: props.guest?.allergy,
    birthdate: props.guest?.birthdate,
    postal_code: props.guest?.postal_code,
    prefecture: props.guest?.prefecture,
    city: props.guest?.city,
    address: props.guest?.address,
    building: props.guest?.building,
    phone: props.guest?.phone,
    email: props.guest?.email,
    // message: props.guest?.message,
    invitation_delivery: props.guest?.invitation_delivery,
    guest_group_id: props.guest?.guest_group?.id,
    guest_tag_guests: props.guest?.guest_tags.map(guest_tag => ({id: guest_tag.id})),
  } as UpdateGuestInput,
  guests: [],
  free_item_values: props.guest?.guest_free_item_values.map(item => ({name: item.name, content: item.content})) as UpdateGuestFreeItemValueInput[],
  guest_event_answers: [] as CreateGuestEventAnswerInput[]
});

if (! input.value.free_item_values.length) {
  input.value.free_item_values = [{name: '', content: ''}];
}
// if (! input.value.guest_event_answers.length) {
//   input.value.guest_event_answers = [{attendance: null}];
// }

// APIから guestList を読み込み
const { guestList, refetch } = useGetOneGuestList(String(props.guestListId));

onMounted(async () => {
  if (props.guest?.image_url) {
    input.value.input.image = {uuid: props.guest?.image_url};
  }
  setGuestEventAnswers();
});
const setGuestEventAnswers = async() => {
  // 出欠をSET
  let guest_event_answers = [];
  for (let i = 0; i < props.guest?.guest_event_attendances.length; i++) {
    const guest_event_attendance = props.guest?.guest_event_attendances[i];
    guest_event_answers.push({
      id: guest_event_attendance.id,
      // date: guest_event_answer.date,
      name: guest_event_attendance.name,
      attendance: guest_event_attendance.attendance,
      // payment_amount: guest_event_answer.payment_amount
    });
  }
  input.value.guest_event_answers = guest_event_answers;
}

const rules = computed(() => {
  // 基本情報バリデーション
  let rules = {
    input: {
      parent_guest_id: {},
      image: {
        noValidation
      },
      last_name: { 
        required: helpers.withMessage(validationMessage.required('姓'), required) 
      },
      first_name: { 
        required: helpers.withMessage(validationMessage.required('名'), required) 
      },
      last_name_romaji: { noValidation },
      first_name_romaji: { noValidation },
      last_name_kana: { noValidation },
      first_name_kana: { noValidation },
      guest_type: { noValidation },
      guest_honor: { noValidation },
      gender: { noValidation },
      guest_title: { noValidation },
      relationship: { noValidation },
      relationship_name: { noValidation },
      birthdate: {
        date: helpers.withMessage(validationMessage.date('お誕生日'), validationDate),
      },
      allergies: { noValidation },
      allergy: { noValidation },
      guest_list_id: { noValidation },
      postal_code: { 
        format: helpers.withMessage(validationMessage.format('郵便番号'), validationPostalCode),
      },
      prefecture: { noValidation },
      city: { noValidation },
      address: { noValidation },
      building: { noValidation },
      phone: {},
      email: {
        // required: helpers.withMessage(validationMessage.required('メールアドレス'), required),
        email: helpers.withMessage(validationMessage.email('メールアドレス'), email),
      },
      // Cannot return null for non-nullable field \"Guest.message\".",
      // message: { noValidation },
      invitation_delivery: { noValidation },
      guest_group_id: { noValidation }
    }
  } as any;

  rules.free_item_values = [];
  for (let i = 0; i < input.value.free_item_values.length; i++) {
    if (input.value.free_item_values[i]?.name || input.value.free_item_values[i]?.content) {
      rules.free_item_values.push({
        name: { 
          required: helpers.withMessage(validationMessage.required('項目名'), required) 
        },
        content: { 
          required: helpers.withMessage(validationMessage.required('内容'), required) 
        },
      });
    } else {
      rules.free_item_values.push({});
    }
  }
  return rules;
});

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 値を更新
const onUpdateInput = async (e: {
  key: string;
  value: string;
})  => {
  // free_item_values.0.name 的なStringを free_item_values[0].name に変えて値を入れる
  if (e.key.indexOf('.') === -1) e.key = 'input.'+e.key;
  setNestedValue(input.value, e.key, e.value);
};

// 編集エリアTOPにスクロール
const scrollPageTop = () => {
  // document.getElementById('modalWindow01').getElementsByClassName('contents')[0].scrollTo({top: 0, behavior:'smooth' });
};

// 更新API
const { update, errors } = useUpdateGuest();

// 更新中のLoading
const isLoading = ref(false);

// タグの新規追加
const { guestTags, refetch:refetchGuestTag } = useGetManyGuestTag(props.guestListId)
const { create:createGuestTag } = useCreateGuestTag();

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    scrollPageTop();
    return false;
  }

  isLoading.value = true;

  // 値がある場合のみ送信
  let free_item_values = [];
  for (let i = 0; i < input.value.free_item_values.length; i++) {
    const free_item_value = input.value.free_item_values[i];
    if (free_item_value.name && free_item_value.content) free_item_values.push(free_item_value);
  }

  if (input.value.input.guest_tag_guests) {
    for (let i = 0; i < input.value.input.guest_tag_guests.length; i++) {
      let tag = input.value.input.guest_tag_guests[i].id;

      // タグが完了かスペースで追加された場合、tagにはIDじゃない文字列が入るので、それは保存する
      const guestTagIndex = guestTags.value.findIndex(item => item.id == tag);
      if (guestTagIndex === -1) {
        await createGuestTag({guest_list_id: props.guestListId, tag: tag});
        await refetchGuestTag();
        const newGuestTag = guestTags.value.find(item => item.tag == tag);
        if (! newGuestTag) continue;
        input.value.input.guest_tag_guests[i] = {
          guest_tag_id: newGuestTag.id
        };
      } else {
        // すでに登録されてるタグの場合
        input.value.input.guest_tag_guests[i] = {
          guest_tag_id: input.value.input.guest_tag_guests[i].id
        };
      }
    }
  }

  // アンケート項目をそのまま送信
  let guest_survey_answers = [];
  for (let i = 0; i < props.guest.guest_survey_answers.length; i++) {
    const guest_survey_answer = props.guest.guest_survey_answers[i];
    guest_survey_answers.push({
      answer_content: guest_survey_answer?.answer_content,
      question: guest_survey_answer?.question,
      ui_type: guest_survey_answer?.ui_type
    });
  }

  let guest = JSON.parse(JSON.stringify(input.value.input));
  delete guest.image;
  guest.image_url = input.value.input.image?.uuid;

  let guest_event_answers = [];
  for (let i = 0; i < input.value.guest_event_answers.length; i++) {
    const guest_event_answer = input.value.guest_event_answers[i];
    guest_event_answers.push(guest_event_answer);
  }

  const isSuccess = await update(guest, [], free_item_values, guest_event_answers, guest_survey_answers);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    scrollPageTop();
    return false;
  }

  // モーダル閉じる
  emits('reload');
  emits('close');
};


const onClickAddFreeInput = () => {
  const free_item_value = {
    name: '',
    content: '',
  } as UpdateGuestFreeItemValueInput;
  input.value.free_item_values.push(free_item_value);
}

const onClickDeleteFreeInput = (index:number) => {
  let newFreeInputs = [];
  for (let i = 0; i < input.value.free_item_values.length; i++) {
    if (i == index) continue;
    const free_item_value = input.value.free_item_values[i];
    newFreeInputs.push(free_item_value);
  }
  input.value.free_item_values = newFreeInputs;
};
</script>

<style lang="scss" scoped>
.modalWrap.modalCreateGuest {
  :deep(.modalContainer) {
    .contents {
      padding: 3px 5px;
    }
  }
}
@include sp {
.modalWrap.modalCreateGuest {
  :deep(.modalContainer) {
    .contents {
      padding-top: 18px;
    }
  }
}
}
.input-error {
  text-align: center;
  margin-top: 20px;
}
</style>