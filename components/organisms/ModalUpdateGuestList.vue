<template>
  <Modal v-if="isShow" @close="onClickCloseModal()">
    <template #header>
      ゲストリスト名を変更
    </template>
    <template #main>
      <Loading v-if="isLoading"></Loading>
      <p v-if="error" class="input-error">{{ error }}</p>
      <form class="searchform">
        <InputText 
          title="ゲストリスト名"
          placeholder="例) 2次会"
          :required="true" 
          size="full" 
          :value="String(input.name)"
          :error="getValidationMessage(v$.name)"
          @input="input.name = $event.target.value"
          />
      </form>
    </template>
    <template #footer>
      <footer class="modal-footer">
        <button class="btn btn-default-outline btn-block" @click="onClickCloseModal()">キャンセル</button>
        <button class="btn btn-secondary btn-block" @click="onClickSave()">OK</button>
      </footer>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { UpdateGuestListInput } from '@/composables/generated';
import type { GraphQLValidationErrors } from "@/utils/graphql";

const props = defineProps<{
  guestList: {
    id: string;
    name: string;
    member: {
      id: string;
    }
  };
}>();

const emits = defineEmits<{
  (e: 'close'): void;
}>()

// 入力項目
const input = ref({
  id: props.guestList.id,
  name: props.guestList.name
} as UpdateGuestListInput)

const isShow = ref(true)

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return {
    name: { 
      required: helpers.withMessage(validationMessage.required('ゲストリスト名'), required) 
    },
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });


/**
 * 編集
 */
// 編集モーダル表示時
onMounted(() => {
  isShow.value = true;
});

// 編集モーダル非表示
const onClickCloseModal = () => {
  isShow.value = false;
  emits('close')
};

// 更新API
const { update, errors } = useUpdateGuestList();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await update(input.value);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }

  // モーダル閉じる
  onClickCloseModal();
};
</script>

<style lang="scss" scoped>
.input-error {
  text-align: center;
  margin-bottom: 20px;
}
</style>