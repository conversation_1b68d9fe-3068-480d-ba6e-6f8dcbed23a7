<template>
  <Modal ref="modal" @close="emits('close')" :isShowSubModal="isShowSubModal">
    <template #header>
      お二人のプロフィールの変更
    </template>
    <template #main>
      <ShowProfileForm
        ref="ShowProfileFormRef"
        @showSubModal="isShowSubModal = true" 
        @closeSubModal="isShowSubModal = false" 
        @scrollTop="scrollTopModal()"
        @update="emits('update')"
      ></ShowProfileForm>
    </template>
    <template #footer>
      <footer class="modal-footer" v-if="ShowProfileFormRef?.mode == 'input'">
        <button class="btn btn-default-outline btn-block" @click="onCloseModal()">戻る</button>
        <button class="btn btn-secondary btn-block" @click="onClickConfirm()">変更内容を確認</button>
      </footer>
      <footer class="modal-footer" v-if="ShowProfileFormRef?.mode == 'confirm'">
        <button class="btn btn-default-outline btn-block" @click="onClickBack()">戻る</button>
        <button class="btn btn-secondary btn-block" @click="onClickSave()">この内容で登録</button>
      </footer>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
const props = defineProps<{
  profiles: {};
}>();

const emits = defineEmits<{
  (e: 'close'): void;
  (e: 'update'): void;
}>()

const isShowSubModal = ref(false)

// モーダル表示
const isShowModal = ref(false);

const ShowProfileFormRef = ref()

// 編集エリアTOPにスクロール
const modal = ref();

// 「この内容で登録」ボタンクリック
const scrollTopModal = async() => {
  if (modal.value) modal.value.scrollTopModal();
};

// 「変更内容を確認」ボタンクリック
const onClickConfirm = async() => {
  await ShowProfileFormRef.value.onClickConfirm();
  scrollTopModal();
};

// 「この内容で登録」ボタンクリック
const onClickSave = async() => {
  await ShowProfileFormRef.value.onClickSave();
  scrollTopModal();
};

// 「戻る」ボタンクリック
const onClickBack = async() => {
  await ShowProfileFormRef.value.onClickBack();
  scrollTopModal();
};


// プロフィール削除ボタンのクリック
const onCloseModal = async() => {
  emits('close');
};
</script>