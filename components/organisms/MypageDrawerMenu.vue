
<template>
<div class="mypage-drawer-menu" :class="{'close': ! getIsShowMypageMenu()}">
  <div class="inner">

    <div class="closerMypagemenuWrap">
      <button class="closerMypagemenu" @click.prevent="toggleMypageMenu()">
        <span class="icon"><img src="@/assets/images/icon-toggle-mypage-menu.svg" /></span>
      </button>
    </div>

    <!-- <div class="login">
      <div class="login-inner">
        <strong class="loginname">{{ member?.memberMe?.last_name }} {{ member?.memberMe?.first_name }} <small>様</small></strong>
        <p class="weddingday" v-if="member?.memberMe?.wedding_info?.wedding_date">挙式日<span>{{ $dayjs(member?.memberMe?.wedding_info?.wedding_date).format('YYYY/MM/DD') }}</span></p>
        <div class="notice" v-if="paramsBadge.noticeBadges">
          <NuxtLink to="#01">TODOリストがあります<span>{{ paramsBadge.noticeBadges }}</span></NuxtLink>
        </div>
      </div>
    </div> -->

    <div class="navi" v-on:mouseover="showMypageMenu()">
      <NuxtLink @click="clearUrlHistory()" to="/mypage" class="btn btn-primary-outline btn-block mb-34"><i class="icn-left material-symbols-outlined">account_circle</i> <span class="txt">マイページトップへ</span></NuxtLink>
      <ul>
        <li>
          <p class="title"><i class="icn-left material-symbols-outlined">edit</i> <span class="txt">メインメニュー</span></p>
          <ul>
            <li><NuxtLink @click="clearUrlHistory()" to="/mypage/webinvitation">My招待状</NuxtLink></li>
            <li><NuxtLink @click="clearUrlHistory()" to="/mypage/guest-list">ゲストリスト<div class="badge" v-if="newGuestCnt != 0">{{ newGuestCnt }}</div></NuxtLink></li>
            <li v-if="useRuntimeConfig()?.public?.app?.is_active_prepaid"><NuxtLink @click="clearUrlHistory()" to="/mypage/celebration">会費・ご祝儀</NuxtLink></li>
          </ul>
        </li>
        <li>
          <p class="title"><i class="icn-left material-symbols-outlined">id_card</i> <span class="txt">会員情報</span></p>
          <ul>
            <li v-if="useRuntimeConfig()?.public?.app?.is_active_prepaid">
              <NuxtLink @click="clearUrlHistory()" to="/mypage/bank">
                振込口座の登録・変更
              </NuxtLink>
                <span class="label-danger mt-10" v-if="! memberLoading && member?.memberMe?.isAccountAlertRegist" style="display:block;">
                  <i class="icn material-icons">error</i> 振込口座をご登録ください
                </span>
            </li>
            <li><NuxtLink @click="clearUrlHistory()" to="/mypage/member">会員情報の確認・変更</NuxtLink></li>
            <li><NuxtLink @click="clearUrlHistory()" to="/mypage/member/email">パスワード、メール<br>アドレスの登録・変更</NuxtLink></li>
            <li><a @click="showModalLogout()">ログアウト</a></li>
          </ul>
        </li>
        <li>
          <p class="title"><i class="icn-left material-symbols-outlined">support_agent</i> <span class="txt">サポート</span></p>
          <ul>
            <li><NuxtLink to="/support">サポートトップ</NuxtLink></li>
            <li><NuxtLink to="/guide">ご利用ガイド</NuxtLink></li>
            <li><NuxtLink to="/question">よくある質問</NuxtLink></li>
            <li><NuxtLink to="/contact">お問い合わせ</NuxtLink></li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</div>
</template>

<script lang="ts" setup>
const { showModalLogout } = useModalLogoutState();
const { toggleMypageMenu, showMypageMenu, getIsShowMypageMenu } = useMenuState();

const { $dayjs } : any = useNuxtApp();
const { member, loading:memberLoading } = useGetOneMemberMe();

const paramsBadge = {
  // 本来はAPIでデータを取得
  noticeBadges: 3,
  favoriteBadges: 2,
  cartBadges: 1,
};

const isShowMenu = ref(false);

// APIから guestLists を読み込み
const { newGuestCnt } = useGetOneMemberMe();
</script>

<style lang="scss" scoped>
.mypage-drawer-menu {
  // position: fixed;
  // top: 95px;
  // left: 0;
  // width: 100%;
  // height: 100%;
  // height: calc(100vh - 96px);
  // height: calc(100dvh - 96px);
  padding: 0;
  // border-right: 10px solid $color-lightgray2;
  background-color: $color-mainbackground;
  // overflow: auto;
  // z-index: 10;
  transition: width 0.35s ease;
  &.close {
    overflow: hidden;
    .closerMypagemenu {
      // right: -57px;
      margin-right: 10px;
      .icon {
        transform: rotate(180deg);
      }
    }
  }
  .closerMypagemenuWrap {
    text-align: right;
    padding: 12px 16px 24px;
  }
  .closerMypagemenu {
    // position: absolute;
    // top: 24px;
    // right: 16px;
    display: inline-block;
    width: 32px;
    height: 40px;
    text-align: center;
    // background: $color-main;
    // box-shadow: 0px 1px 4px 2px rgba(0, 0, 0, 0.1);
    // border-radius: 20px;
    transition: auto 0.35s ease;
    .icon {
      display: inline-block;
      position: relative;
      width: 24px;
      height: 24px;
      transition: transform 0.7s ease;
      // transform: rotate(180deg);
      img {
        position: absolute;
        top: 0;
        left: 0;
        transition: opacity 0.7s ease;
      }
    }
  }

  // .login {
  //   padding: 32px 24px 32px 27px;
  //   line-height: 145%;
  //   letter-spacing: 0.02em;
  //   font-size: 12px;

  //   .login-inner {
  //     padding: 16px 22px;
  //     border: 1px solid $color-main;
  //     border-radius: 4px;
  //     background-color: $color-mainbackground;

  //     .loginname {
  //       display: flex;
  //       align-items: center;
  //       margin-top: 1px;
  //       margin-bottom: 8px;
  //       font-weight: 400;
  //       font-size: 18px;
  //       line-height: 130%;
  //       letter-spacing: 0.04em;
  //       color: $color-blacktext2;
  //       font-family: 'Noto Serif JP', serif;
  //       // &::before {
  //       //   @include BA(relative);
  //       //   background-image: url(@/assets/images/icon-account_circle-big.svg);
  //       //   width: 33px;
  //       //   height: 33px;
  //       //   margin: -2px 10px 0 0;
  //       //   transform: none;
  //       // }
  //       :deep(small) {
  //         margin-left: 5px;
  //       }
  //     }
  //     .weddingday {
  //       display: flex;
  //       align-items: center;
  //       margin-bottom: 5px;
  //       padding-left: 1px;
  //       font-size: 12px;
  //       line-height: 145%;
  //       letter-spacing: 0.02em;
  //       color: $color-accent;
  //       span {
  //         display: inline-flex;
  //         align-items: center;
  //         &::before {
  //           @include BA(relative);
  //           background-image: url(@/assets/images/icon-favorite-g.svg);
  //           width: 14px;
  //           height: 13px;
  //           margin: 0 5px;
  //           transform: none;
  //         }
  //       }
  //     }
  //     .notice {
  //       margin-top: 13px;
  //       padding-top: 15px;
  //       border-top: 1px solid $color-grayborder;
  //       a {
  //         display: block;
  //         position: relative;
  //         width: 100%;
  //         text-decoration: none;
  //         font-size: 12px;
  //         line-height: 100%;
  //         color: $color-blacktext2;
  //         span {
  //           display: inline-flex;
  //           justify-content: center;
  //           align-items: center;
  //           width: 15px;
  //           height: 15px;
  //           margin-left: 5px;
  //           border-radius: 50%;
  //           background: $color-alert2;
  //           font-family: "Roboto";
  //           font-size: 11px;
  //           color: $color-whitetext;
  //         }
  //         &::after {
  //           @include BA;
  //           right: 7px;
  //           width: 6px;
  //           height: 9px;
  //           background-image: url(@/assets/images/icon-chevron-right-b.svg);
  //         }
  //       }
  //     }
  //   }
  // }
}
.navi {
  padding: 10px 20px 60px;
  white-space: nowrap;
  .btn {
    .mypage-drawer-menu.close & {
      text-align: center;
      .icn-left {
        margin: 0;
      }
      .txt {
        display: none;
      }
    }
  }
  & > ul {
    & > li {
      & ~ li {
        margin-top: 48px;
      }
      p.title {
        font-weight: 500;
        font-size: 14px;
        letter-spacing: 0.1em;
        color: $color-accent;
        .icn-left {
          vertical-align: middle;
          margin-right: 5px;
          font-size: 28px;
        }
        .mypage-drawer-menu.close & {
          .icn-left {
            margin-left: 5px;
          }
          .txt { display: none; }
        }
      }
      ul {
        .mypage-drawer-menu.close & {
          display: none;
        }
        margin-left: 20px;
        li {
          margin-top: 24px;
        }
      }
      a {
        display: block;
        position: relative;
        width: 100%;
        text-decoration: none;
        font-size: 14px;
        letter-spacing: 0.1em;
        color: $color-blacktext2;
        cursor: pointer;
        &::after {
          @include BA;
          right: 0;
          width: 9px;
          height: 12px;
          background-image: url(@/assets/images/icon-chevron-right-b.svg);
        }
        .badge {
          position: absolute;
          right: 40px;
          top: 50%;
          margin-top: -10px;
          font-size: 14px;
          font-family: Roboto;
          border-radius: 50px;
          color: #fff;
          padding: 2px 6px;
          background: #AD871E;
        }
      }
    }
  }
}
@include sp {
.mypage-drawer-menu {
  display: none;
}
}
</style>