<template>
<Loading v-if="isLoading"></Loading>
<div class="wrap" v-else>
  <ShowProfileForm
    ref="ShowProfileFormRef"
    @scrollTop="scrollTopPage()"
    @update="emits('update')"
  ></ShowProfileForm>
  <ShowFooterBarFrow
    :data="modalFooterBarFrow"
    @onClickBtn0="emits('close')"
    @onClickBtn1="onClickSave()"
  />
</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
const props = defineProps<{
  profiles: {};
}>();

const emits = defineEmits<{
  (e: 'close'): void;
  (e: 'update'): void;
}>()

// モーダル表示
const isShowModal = ref(false);

const ShowProfileFormRef = ref()

const { member, refetch } = useGetOneMemberMe();
const isLoading = ref(false);
onMounted(async () => {
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
});


// 「この内容で登録」ボタンクリック
const scrollTopPage = () => {
  window.scrollTo({top: 0, behavior: "smooth"})
};


// 「この内容で登録」ボタンクリック
const onClickSave = async() => {
  await ShowProfileFormRef.value.onClickSave();
  scrollTopPage();
};

// プロフィール削除ボタンのクリック
const onCloseModal = async() => {
  emits('close');
};

// #modalWindow01へのデータ
const modalFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "戻る",
          link: false,
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "作成する",
          link: false,
        },
      ],
    },
  ],
}
</script>