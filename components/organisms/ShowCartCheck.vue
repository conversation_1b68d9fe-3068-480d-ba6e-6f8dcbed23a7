<template>
  <div class="items">
    <div class="thumbnail" v-if="props.thumbnail != ''">
      <img :src="props.thumbnail" :alt="props.title">
    </div>
    <div class="datail">
      <strong class="category" v-if="props.category">{{ props.category }}</strong>
      <p class="title" v-if="props.title">{{ props.title }}</p>
    </div>
    <div class="datail is-bottom">
      <InputTextarea title="備考欄" size="full"/>
      <p class="description">
        WEB上で選択いただけないオプション及びカスタマイズ制作をご希望のお客様へ<br>
        ・本サイト掲載内容またはスタッフよりご案内している内容を備考欄にご入力ください <br>
        ・本サイトに掲載していない内容につきましては 必ずご注文前にお問い合わせをお願いします
      </p>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  title?: string,
  thumbnail?: string,
  category?: string
}>(), {
});
</script>

<style lang="scss" scoped>
.items {
  overflow: hidden;
}
.thumbnail {
  width: 140px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  float: left;
}
.datail {
  width: calc(100% - 152px);
  margin-left: 12px;
  float: left;
  .category {
    display: block;
    margin-top: 1px;
    font-size: 12px;
    line-height: 18px;
    letter-spacing: 0.1em;
    color: $color-blacktext3;
  }
  .title {
    margin-top: 2px;
    margin-bottom: 18px;
    font-size: 16px;
    line-height: 18px;
    letter-spacing: 0.1em;
    color: $color-blacktext2;
  }
  .description{
    color: #333;
    font-size: 10px;
    line-height: 1.4;
    margin-top: 6px;
  }
}
@include sp {
  .items {
    padding: 0 17px;
  }
  .thumbnail {
    width: 83px;
  }
  .detail{
    width: calc(100% - 95px);
    width: 100%;
    .category {
      margin-top: 2px;
      font-size: 10px;
      line-height: 15px;
      letter-spacing: 0.1em;
    }
    .title {
      font-size: 14px;
      line-height: 18px;
    }
    & + .is-bottom{
      width: 100%;
      clear: both;
      margin-left: 0;
    }
  }
}
</style>