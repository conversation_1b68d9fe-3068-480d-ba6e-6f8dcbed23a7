<template>
<div v-for="(param, index) in cartData?.param" :key="param">
  <ul class="itemcart">
    <CartDetail :data="param.datail" :mode="mode" @emitEventtodoDetail="eventEventtodoDetail" />
  </ul>
</div>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
  mode: String
}

const params = withDefaults(defineProps<Props>(), {
});

const cartData = params.data;

const emit = defineEmits(['eventEventtodoDetail']);

const eventEventtodoDetail = (target) => {
  emit('emitEventtodo' , target);
};
</script>

<style lang="scss" scoped>
.itemcart {
  width: 800px;
  max-width: 100%;
  margin: 0 auto;
}

@include sp {
.itemcart {
  width: 100%;
}
}
</style>