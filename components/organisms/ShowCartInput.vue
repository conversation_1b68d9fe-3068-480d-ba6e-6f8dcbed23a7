<template>
  <div class="form">
    <div class="wrap">
      <h2>
        <img :src="onConvertImageSrc(props.icon)" />
        {{ props.title }}
      </h2>
      <div class="text">
        <p
          v-for="(item, index) in props.list"
          :key="index"
        >{{ item }}</p>
        <div class="credit" v-if="props.credit">
          <p>{{ props.credit.title }}</p>
          <p>
            <img :src="onSetCardImage(props.credit.type)" />
            {{ props.credit.number }}
          </p>
          <p>{{ props.credit.name }} {{ props.credit.date }}</p>
        </div>
        <p class="info" v-if="props.info">
          <img src="@/assets/images/icon-information-g.svg" />
          {{ props.info }}
        </p>
        <div class="coupon" v-if="props.coupon">
          クーポンを使用する（{{props.coupon.number}}）
        </div>
        <div class="wallet" v-if="props.wallet">
          <div>
            <InputCheck :items="[{name:'wallet'}]" />
            <span>￥{{props.wallet}}分のウォレット残高</span>を使用する
          </div>
          <div class="small">※WEBご祝儀(会費)で集めた金額を使用できます </div>
        </div>
        <div class="point" v-if="props.point">
          <div>
            <InputCheck :items="[{name:'point'}]" />
            <span>{{props.point}}pt</span>（￥{{props.point}}相当）の<span>ポイント保有分</span>を使用する
          </div>
        </div>
      </div>
      <a class="edit" href="#" v-if="props.edit">
        <img src="@/assets/images/icon-edit.svg" width="18" />
        変更する
      </a>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  icon?: string,
  list?: string[],
  credit?: {
    title?: string,
    type?: string,
    number?: string,
    name?: string,
    date?: string
  },
  coupon?: {
    number?: number,
    link?: string
  },
  wallet?: string,
  point?: string,
  info?: string,
  edit?: boolean
}>(), {
  title: 'ご注文者情報',
  icon: '../../assets/images/icon-address.svg',
  list: [],
  edit: true
});

const onConvertImageSrc = (src: string): string => {
  return new URL(src, import.meta.url).href
}

const onSetCardImage = (type: string): string => {
  let src:string = '';
  switch (type) {
    case 'visa':
      src = '../../assets/images/credit-card-visa.svg'
      break;
    default:
      break;
  }
  return new URL(src, import.meta.url).href
}

</script>

<style lang="scss" scoped>
h2{
  color: #B18A3E;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 20px;
  img{
    vertical-align: text-bottom;
  }
}
p{
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  margin-top: 4px;
  img{
    vertical-align: -7px;
  }
}
.credit {
  img{
    vertical-align: -2px;
  }
}
.text{
  max-width: 350px;
}
.info{
  color: #5A5A5A;
  font-size: 12px;
}
.edit{
  display: inline-block;
  font-size: 12px;
  line-height: 1.2;
  color: #B18A3E;
  padding: 0;
  text-decoration: none;
  position: absolute;
  top: 12px;
  right: 22px;
  img{
    width: 18px;
    height: 18px;
    vertical-align: text-bottom;
  }
  &:hover{
    text-decoration: underline;
  }
}

.wrap{
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  position: relative;
  h2{
    max-width: 140px;
    width: 100%;
    margin-right: 24px;
  }
}
.small{
  font-size: 10px;
  padding-left: 26px;
  color: #5A5A5A;
}
.coupon{
  color: #49454F;
  font-size: 16px;
  line-height: 1.5;
}
.wallet{
  font-size: 16px;
  color: #49454F;
  span{
    font-size: 17px;
    font-weight: bold;
  }
}
.point{
  font-size: 16px;
  color: #49454F;
  span{
    font-size: 17px;
    font-weight: bold;
  }
}
</style>