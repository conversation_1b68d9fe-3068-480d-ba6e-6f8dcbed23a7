<template>
  <div class="inputCard" v-if="showInputCard">
    <p class="note-logo">※WEBご祝儀・決済は<br />
    <img src="@/assets/images/web-invitation/logo-gmopg.svg" alt="GMO Payment Gateway" /> が代行しています。</p>
    <div class="mb-20">
      <InputNumber
        title="カード番号"
        name="card_number"
        placeholder="1111222233334444"
        size="lg"
        maxlength="16"
        autocomplete="cc-number"
        :value="form.cardno"
        @update="form.cardno = $event"
      />
    </div>
    <div class="mb-20 expire-wrap">
      <InputNumber
        title="カード有効期限 (MM/YYYY)"
        name="card_exp_month"
        placeholder="12"
        size="xs"
        maxlength="2"
        autocomplete="cc-exp-month"
        :value="form.expire_m"
        @update="form.expire_m = $event"
      />
      <span class="attr">月／</span>
      <InputNumber
        placeholder="2025"
        name="card_exp_year"
        size="xs"
        maxlength="4"
        autocomplete="cc-exp-year"
        :value="form.expire_y"
        @update="form.expire_y = $event"
      />
      <span class="attr">年</span>
    </div>
    <div class="mb-20 cvv-wrap">
      <InputNumber
        title="セキュリティコード"
        name="card_csc"
        placeholder="123"
        size="xs"
        maxlength="4"
        autocomplete="cc-csc"
        :value="form.securitycode"
        @update="form.securitycode = $event"
      />
      <img src="@/assets/images/card_cvv.svg">
      <p class="note">※主にカード裏面の署名欄に記載されている末尾３桁～４桁の数字をご記入下さい 半角入力 (例: 123)</p>
    </div>
    <div class="mb-20">
      <InputAlphabet
        title="カード名義人"
        name="card_owner"
        placeholder="YAMADA HANAKO"
        size="lg"
        autocomplete="cc-name"
        :value="form.holdername"
        @update="form.holdername = $event"
      />
    </div>
    <p v-if="error" class="input-error">{{ error }}</p>
    <div v-if="props.isShowBtn">
      <ButtonMainColor :buttonsize="200" @click="onClickSave">確定する</ButtonMainColor>
    </div>
  </div>
</template>

<script setup lang="ts">
import VueMultiPayment  from '@mul-pay/mptoken-vue-js';
const props = withDefaults(defineProps<{
  isShowBtn?: boolean
}>(), {
  isShowBtn: true
});

const emit = defineEmits(['update']);

const error = ref('' as string)

const form = ref({
  cardno: '',
  expire_y: '',
  expire_m: '',
  securitycode: '',
  holdername: '',
})

const showInputCard = ref(false as boolean)
onMounted(async () => {
  showInputCard.value = false;
  setTimeout(async function(){
    await gmoPayment.load();
    showInputCard.value = true;
  }, 500);
});

// 保存ボタンクリック
const onClickSave = async () => {
  try {
    const result = await new Promise((resolve, reject) => {
      Multipayment.getToken(
        {
          cardno: form.value.cardno,
          expire: form.value.expire_y + form.value.expire_m,
          securitycode: form.value.securitycode,
          holdername: form.value.holdername,
          tokennumber: '1'
        },
        (result) => {
          if (result.resultCode != '000') {
            reject(result);
          } else {
            resolve(result);
          }
        }
      );
    });

    // createTokenResultの成功時の処理
    // console.log(result.tokenObject.token[0]);
    error.value = '';
    emit('update', {
      cardToken: result.tokenObject.token[0]
    });
  } catch (errorResult) {
    // createTokenResultのエラー時の処理
    error.value = gmoPayment.getError(errorResult.resultCode);
  }
}

// 親からも実行可能に
defineExpose({
  onClickSave
});
</script>

<style lang="scss" scoped>
.inputCard {
  border-radius: 4px;
  background: #FFF;
  padding: 18px;
  .expire-wrap {
    // color: var(--text_, #797979);
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.24px;
    display: flex;
    align-items: flex-end;
    .attr {
      margin-left: 5px;
      margin-right: 10px;
      display: block;
      margin-bottom: 5px;
    }
  }
  .cvv-wrap {
    img {
      vertical-align: top;
      margin-top: 30px;
      margin-left: 10px;
    }
    .note {
      color: var(--text_, #797979);
      font-size: 12px;
      font-weight: 400;
      letter-spacing: 0.24px;
      padding-left: 1em;
      text-indent: -1em;
      margin-top: 5px;
    }
  }
}
.note-logo {
  margin-bottom: 20px;
  font-size: 12px;
  img {
    vertical-align: baseline;
    margin-top: 2px;
    width: 260px;
  }
  @include sp {
    font-size: 8px;
    img {
      width: 180px; 
    }
  }
}
</style>