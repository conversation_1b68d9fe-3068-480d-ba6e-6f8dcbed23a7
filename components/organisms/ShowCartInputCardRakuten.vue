<template>
  <div class="inputCard" v-if="showInputCard">
    <h5>カード番号</h5>
    <div class="input-wrapper">
      <div id="my-card-mount"></div>
    </div>
    <h5>カード有効期限 (MM/YYYY)</h5>
    <div class="input-wrapper expiry-input expiry-input-month">
      <div id="my-expiration-month-mount"></div>
    </div>
    <span class="date-divider">月／</span>
    <div class="input-wrapper expiry-input expiry-input-year">
      <div id="my-expiration-year-mount"></div>
    </div>
    <span class="date-divider">年</span>
    <h5>セキュリティコード</h5>
    <div class="cvv-wrap">
      <div class="input-wrapper cvv-input">
        <div id="my-cvv-mount"></div>
      </div>
      <img src="@/assets/images/card_cvv.svg">
      <p class="note">※主にカード裏面の署名欄に記載されている末尾３桁～４桁の数字をご記入下さい 半角入力 (例: 123)</p>
    </div>
    <p v-if="error" class="input-error">{{ error }}</p>
    <div v-if="props.isShowBtn">
      <ButtonMainColor :buttonsize="200" @click="onClickSave">確定する</ButtonMainColor>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  isShowBtn?: boolean
}>(), {
  isShowBtn: true
});

const emit = defineEmits(['update']);

const error = ref('' as string)

const showInputCard = ref(false as boolean)
onMounted(async () => {
  showInputCard.value = false;
  setTimeout(async function(){
    // 楽天ペイボルトAPIをロード
    await rakutenPayment.load();
    showInputCard.value = true;
  }, 500);
});

// 保存ボタンクリック
const onClickSave = async() => {
  await rakutenPayment.createToken();
  if (! rakutenPayment.error) {
    error.value = '';
    emit('update', rakutenPayment.result);
  } else {
    error.value = rakutenPayment.error;
  }
}

// 親からも実行可能に
defineExpose({
  onClickSave
});
</script>

<style lang="scss">
.inputCard {
  border-radius: 4px;
  background: #FFF;
  padding: 18px;
  h5 {
    color: var(--49454Fblack_Light, #49454F);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    letter-spacing: 0.24px;
    margin: 0 0 5px; 
  }
  .input-wrapper {
    background: #fff;
    border-radius: 4px;
    height: 45px;
    position: relative;
    margin-bottom: 24px;
    > div {
      height: 45px;
      > * {
        height: 45px !important;
      }
    }
  }


  .pseudo-card-mask-toggle {
    position: absolute;
    color: lightgray;
    cursor: pointer;
    right: 6px;
    top: 3px;
    font-size: 1.8em;
    transition: all ease-in-out 0.3s;
  }

  .pseudo-card-mask-toggle:hover {
    transition: all ease-in-out 0.3s;
    color: gray;
  }

  .input-wrapper.cvv-input {
    width: 100px;
    display: inline-block;
  }

  p#result-message {
    font-size: 14px;
    background: lightgoldenrodyellow;
    margin: 22px auto;
    padding: 14px;
    width: 500px;
    font-weight: bold;
  }

  .date-divider {
    font-size: 14px;
    color: #333;
    margin: 20px 10px 0 5px;
    vertical-align: top;
    margin-top: 20px;
    display: inline-block;
  }

  .hidden {
    display: none;
  }
  .input-wrapper.expiry-input-month {
    width: 60px;
    display: inline-block;
    #my-expiration-month-mount {
      width: 60px;
    }
  }
  .input-wrapper.expiry-input-year {
    width: 120px;
    display: inline-block;
    #my-expiration-year-mount {
      width: 120px;
    }
  }

  .cvv-wrap {
    .cvv-input {
      margin-bottom: 5px;
    }
    img {
      vertical-align: top;
      margin-top: 10px;
      margin-left: 10px;
    }
    .note {
      color: var(--text_, #797979);
      font-size: 12px;
      font-weight: 400;
      letter-spacing: 0.24px;
      padding-left: 1em;
      text-indent: -1em;
    }
  }

}
</style>