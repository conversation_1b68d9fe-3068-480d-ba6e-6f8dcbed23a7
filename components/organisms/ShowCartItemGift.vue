<template>
  <div class="invitation">
    <div class="itemWrap">
      <ShowCartDetails :data="apiShowCartDetails" mode="select" />
    </div>
  </div>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
}

const apiShowCartDetails = {
  param: [
    {
      datail: [
        {
          title: 'Memphis A Bluege[GG]',
          id: 'gift00000000001',
          category: "引き出物宅配",
          thumbnail: "/images/sample/thumbnail07.png",
          price: "429",
          priceUnit: "１セット",
          orderd: "",
          option: [
          ],
          addevent: [
            {
              class: "toedititem",
              menu: "作成中のアイテムに戻す",
            },
            {
              class: "tolater",
              menu: "あとで買う",
            },
          ],
        },
      ],
    },
  ],
}


</script>

<style lang="scss" scoped>
.itemcart {
  width: 800px;
  max-width: 100%;
  margin: 0 auto;
}
.itemWrap{
  padding: 24px;
}

@include sp {
  .itemcart {
    width: 100%;
  }
}
</style>