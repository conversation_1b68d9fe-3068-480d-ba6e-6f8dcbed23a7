<template>
  <div class="invitation">
    <div class="invitation_header">「あとで買う」に入っている商品（1点）</div>
    <div class="invitation_wrap">
      <ShowCartDetails :data="apiShowCartDetails" mode="small" />
    </div>
  </div>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
}

const apiShowCartDetails = {
  param: [
    {
      datail: [
        {
          title: 'Memphis A Bluege[GG]',
          id: 'gift00000000001',
          category: "引き出物宅配",
          thumbnail: "/images/sample/thumbnail07.png",
          price: "429",
          priceUnit: "１セット",
          orderd: "",
          option: [
          ],
          addevent: [
            {
              class: "toedititem",
              menu: "作成中のアイテムに戻す",
            },
            {
              class: "tolater",
              menu: "あとで買う",
            },
          ],
        },
      ],
    },
  ],
}


</script>

<style lang="scss" scoped>
.invitation{
  &_header{
    padding: 16px;
    border-bottom: 1px solid rgba(33,33,33,0.08);
    color: #333;
    font-size: 16px;
    line-height: 1.2;
  }
  &_wrap{
    padding: 16px;
  }
}

</style>