<template>
  <div class="invitation">
    <div class="itemWrap">
      <div class="info is-alert"><img src="@/assets/images/icon-error-check.svg">注文確定後  現在の編集内容で印刷開始します</div>
      <ShowCartDetails :data="apiShowCartDetails" mode="button" />
      <a class="chevron" @click="isTabOpen = !isTabOpen" :class="{'is-open': isTabOpen}"><img src="@/assets/images/icon-chevron.svg"></a>
    </div>
    <div class="tab" v-if="isTabOpen">
      <FormDetailQuantity />
    </div>
  </div>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
}

const isTabOpen = ref(true);

const apiShowCartDetails = {
  param: [
    {
      datail: [
        {
          title: 'Memphis A Bluege[GG]',
          id: 'gift00000000001',
          category: "席次表",
          thumbnail: "/images/sample/thumbnail07.png",
          price: "429",
          priceUnit: "１セット",
          orderd: "",
          option: [
          ],
          addevent: [
            {
              class: "toedititem",
              menu: "作成中のアイテムに戻す",
            },
            {
              class: "tolater",
              menu: "あとで買う",
            },
          ],
        },
      ],
    },
  ],
}

</script>

<style lang="scss" scoped>
.itemcart {
  width: 800px;
  max-width: 100%;
  margin: 0 auto;
}
.itemWrap{
  padding: 24px;
  position: relative;
}
.chevron{
  display: block;
  width: 44px;
  height: 44px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 10px;
  margin: auto;
  padding: 10px;
  transition: 0.5s;
  cursor: pointer;
  &.is-open{
    transform: rotate(180deg);
  }
}
.info{
  &.is-alert{
    font-size: 12px;
    color: #E65C7A;
    margin-bottom: 12px;
  }
  img{
    margin-bottom: -3px;
  }
}

@include sp {
  .itemcart {
    width: 100%;
  }
}
</style>