<template>
  <div class="invitation">
    <div class="itemWrap">
      <ShowCartDetails :data="apiShowCartDetails" mode="select" />
      <a class="chevron" @click="isTabOpen = !isTabOpen" :class="{'is-open': isTabOpen}"><img src="@/assets/images/icon-chevron.svg"></a>
    </div>
    <div class="tab" v-if="isTabOpen">
      <h4>セット内容</h4>
      <ShowGiftDetails :data="apiShowGiftDetails" />
    </div>
  </div>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
}

const isTabOpen = ref(true);

const apiShowCartDetails = {
  param: [
    {
      datail: [
        {
          title: 'Memphis A Bluege[GG]',
          id: 'gift00000000001',
          category: "セット商品",
          thumbnail: "/images/sample/thumbnail07.png",
          price: "429",
          priceUnit: "１セット",
          orderd: "",
          option: [
          ],
          addevent: [
            {
              class: "toedititem",
              menu: "作成中のアイテムに戻す",
            },
            {
              class: "tolater",
              menu: "あとで買う",
            },
          ],
        },
      ],
    },
  ],
}

const apiShowGiftDetails = {
  param: [
    {
      type: "listgiftwrapping",
      datail: [
        {
          title: '選べるメニュー表(1部 200円分)',
          number : "",
          thumbnail: "/images/sample/thumbnail03.png",
          labels: [
          ],
          price: "",
          priceStrike: "",
          priceUnit: "",
          orderd: "",
        },
        {
          title: '選べる席札(1部 120円分)',
          number : "",
          thumbnail: "/images/sample/thumbnail03.png",
          labels: [
          ],
          price: "",
          priceStrike: "",
          priceUnit: "",
          orderd: "",
        }
      ]
    }
  ]
}


</script>

<style lang="scss" scoped>
h4{
  font-size: 10px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 8px;
}
.itemcart {
  width: 800px;
  max-width: 100%;
  margin: 0 auto;
}.itemWrap{
  padding: 24px;
  position: relative;
}
.chevron{
  display: block;
  width: 44px;
  height: 44px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 10px;
  margin: auto;
  padding: 10px;
  transition: 0.5s;
  cursor: pointer;
  &.is-open{
    transform: rotate(180deg);
  }
}
.tab{
  padding: 0 18px 18px;
}
@include sp {
  .itemcart {
    width: 100%;
  }
}
</style>