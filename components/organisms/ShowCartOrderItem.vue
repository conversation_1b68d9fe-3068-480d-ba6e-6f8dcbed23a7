<template>
  <div class="item">
    <div class="wrap">
      <div class="radioList">
        <label>
          <input type="radio" name="orderDate">
          <span class="label">通常納期(¥0)<small>2023/1/26(木)</small></span>
        </label>
        <label>
          <input type="radio" name="orderDate">
          <span class="label">特急納期(¥3,300)<small>2023/1/25(水)</small></span>
        </label>
        <label>
          <input type="radio" name="orderDate">
          <span class="label">超特急納期(¥5,500) <small>2023/1/24(火)</small></span>
        </label>
      </div>
      <div class="date">
        <div class="date_left">
          <p class="date_label">お届け予定日</p>
          <p class="date_text"><span>2023/1/25（水）</span>迄にお届けします </p>
          <p class="date_small">（出荷準備ができ次第 日時指定なしで配送します）</p>
        </div>
        <div class="date_right">
          <div class="button_wrap">
            <ButtonMainColor baseColor="reversal" size="md">日時指定する</ButtonMainColor>
          </div>
        </div>
      </div>
      <div class="detail">
        <ShowCartDetails :data="apiShowCartDetails" />
      </div>
      <div class="total">
        <dl>
          <dt>小計（税込）</dt>
          <dd>￥11,968</dd>
        </dl>
        <dl>
          <dt>送料</dt>
          <dd>￥880</dd>
        </dl>
        <dl>
          <dt>特急料金</dt>
          <dd>￥3,300</dd>
        </dl>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(defineProps<{
  title?: string,
  icon?: string,
  list?: string[],
  credit?: {
    title?: string,
    type?: string,
    number?: string,
    name?: string,
    date?: string
  },
  coupon?: {
    number?: number,
    link?: string
  },
  wallet?: string,
  point?: string,
  info?: string,
  edit?: boolean
}>(), {
  title: 'ご注文者情報',
  icon: '../../assets/images/icon-address.svg',
  list: [],
  edit: true
});

const apiShowCartDetails = {
  param: [
    {
      datail: [
        {
          title: 'Memphis A Bluege[GG]',
          id: 'gift00000000002',
          category: "招待状",
          thumbnail: "/images/sample/thumbnail07.png",
          price: "495",
          priceUnit: "１セット",
          orderd: "20",
          orderdUnit: "セット",
          option: [
            {
              item: "封筒",
              substance: "宛名印刷あり 2枚｜宛名印刷なし 18枚",
            },
            {
              item: "返信はがき",
              substance: "新郎用 10枚｜新婦用 10枚",
            },
            {
              item: "はがき裏面カスタマイズ",
              substance: "希望する",
            },
            {
              item: "付箋",
              substance: "5枚",
            },
            {
              item: "予備封筒",
              substance: "1枚",
            },
          ],
          addevent: [
          ],
        },
      ],
    },
  ],
}

</script>

<style lang="scss" scoped>
h2{
  color: #B18A3E;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.2;
  margin: 0 0 20px;
  img{
    vertical-align: text-bottom;
  }
}
p{
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  margin-top: 4px;
  img{
    vertical-align: -7px;
  }
}
.radioList{
  margin-bottom: 14px;
  label {
    display: block;
    cursor: pointer;
    position: relative;
    margin-bottom: 4px;
    &:last-child {
      margin-right: 0;
    }
    &.is-block {
      display: block;
    }
    input {
      display: none;
      &:checked {
        & + .label {
          background-color: rgba(177,138,62,0.1);
          border: 1px solid #B18A3E;
          border-radius: 4px;
          &::before{
            border-color: $color-main;
          }
          &::after {
            content: "";
            display: inline-block;
            background-color: $color-main;
            width: 8px;
            height: 8px;
            border-radius: 8px;
            position: absolute;
            left: 11px;
            top: 13px;
          }
        }
      }
      &:disabled {
        & + .label {
          opacity: 0.5;
          pointer-events: none;
        }
      }
    }
    & > .label {
      display: block;
      font-size: 14px;
      font-weight: bold;
      color: #333;
      line-height: 18px;
      position: relative;
      padding: 8px 10px 8px 30px;
      &::before {
        content: "";
        display: inline-block;
        width: 18px;
        height: 18px;
        border-radius: 18px;
        border: solid 2px #9C9C9C;
        background: #FFF;
        position: absolute;
        left: 6px;
      }
    }
  }
  small{
    color: #B18A3E;
    font-size: 14px;
    font-weight: normal;
    margin-left: 14px;
  }
}

.wrap{
  position: relative;
  h2{
    max-width: 140px;
    width: 100%;
    margin-right: 24px;
  }
}
.button_wrap{
  max-width: 150px;
  width: 100%;
  margin: 24px auto;
}
.date{
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  color: #5A5A5A;
  margin: 14px 0;
  &_label{
    font-size: 14px;
  }
  &_text{
    font-size: 14px;
    span{
      font-size: 20px;
      color: #6F8B81;
    }
  }
  &_small{
    font-size: 12px;
  }
}
.detail{
  border-top: 1px solid #EEE;
  padding: 18px 0;
}
.total{
  border-top: 1px solid #EEE;
  padding-top: 18px;
  dl{
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4px;
  }
  dt{
    color: #5A5A5A;
    font-size: 12px;
  }
  dd{
    color: #333;
    font-size: 14px;
  }
}
</style>