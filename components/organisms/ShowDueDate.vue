
<template>
<section class="duedate">

  <div class="wrap-duedate">
    <strong>※申込期日について</strong>
    <dl>
      <dt>引き出物宅配</dt>
      <dd>
        <b>ご指定のお届け希望日より12営業日前</b>までに 宅配手続きをすべて完了してください </dd>
      <dt>ご案内カード・コットンバッグ</dt>
      <dd>
        <b>挙式日の7営業日前</b>までに宅配手続きをすべて完了させてください 挙式日の約1週間前までにご指定のお届け先にお届けいたします （※一部地域や離島は除きます）</dd>
    </dl>
  </div>

</section>
</template>

<style lang="scss" scoped>
.duedate {
  .wrap-duedate {
    max-width: 656px;
    margin: 0 auto;
    padding: 20px 18px 19px;
    border: 1px solid $color-alert;
    border-radius: 4px;
    background-color: $color-mainbackground;
    strong {
      display: block;
      padding-left: 2px;
      line-height: 1.35;
      letter-spacing: 2.7px;
      font-size: 16px;
      color: $color-alert;
    }
    dl {
      margin: 15px 0 0;
      padding-left: 1px;
      font-size: 14px;
      dt {
        font-size: 15px;
        font-weight: bold;
        letter-spacing: 1px;
        color: $color-blacktext2;
        &::before {
          content: '-';
          margin-right: 3px;
        }
      }
      dd {
        margin-top: 3px;
        line-height: 1.5;
        letter-spacing: 0.25px;
        b {
          line-height: 1.3;
          letter-spacing: 0.3px;
          font-weight: normal;
          color: $color-alert;
        }
        & ~ dt {
          margin-top: 16px;
        }
      }
    }
  }
}


@include sp {
.duedate {
  padding: 12px 17px;
  background-color: $color-lightgray;
  .wrap-duedate {
    max-width: 100%;
    padding: 18px 13px;
    strong {
      letter-spacing: 0.3px;
    }
    dl {
      margin-top: 13px;
      dt {
        letter-spacing: -0.7px;
        &::before {
          margin-right: 4px;
        }
      }
      dd {
        margin-top: 5px;
        font-size: 12px;
        line-height: 145%;
        letter-spacing: 0.02em;
        & ~ dt {
          margin-top: 14px;
        }
      }
    }
  }
}
}
</style>
