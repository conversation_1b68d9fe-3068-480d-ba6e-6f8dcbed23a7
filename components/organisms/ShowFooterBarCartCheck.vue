<template>
  <div class="makingToolbar">
    <div class="info is-alert"><img src="@/assets/images/icon-error-check.svg">ご注文前に 最終確認をお願いします </div>
    <p>作成いただいた内容で印刷を開始します  入念な確認にご協力をお願いします <br>以下の内容をご確認いただけましたら チェックを入れてご注文へ進んでください </p>
    <p class="is-right"><a href="#"><img src="@/assets/images/icon-question-gl.svg">ご注文の流れ</a></p>
    <div class="checkBox">
      最後に編集したデータを保存しましたか?<br>入力内容に誤字や脱字はありませんか?<br>プランナーさんと確認は行いましたか?
    </div>
    <div class="check">
      <InputCheck :items="[{value:0, label:'全ての内容を確認しました'}]"/>
    </div>
    <div class="flow">
      <ButtonMainColor size="lg" spsize="spmd" to="#">次へ進む</ButtonMainColor>
    </div>

  </div>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const paymentData = params.data;

const publishedTitle = computed(() => (data) => {
  return data;
})

const publishedLink = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>
.info{
  &.is-alert{
    font-size: 12px;
    color: #E65C7A;
    margin-bottom: 8px;
  }
  img{
    margin-bottom: -3px;
  }
}
.makingToolbar {
  width: 100%;
  padding: 16px;
  padding-bottom: 24px;
  background: #FFF;
}
.is-right{
  text-align: right;
  a{
    color: #B18A3E;
    font-size: 12px;
    font-weight: lighter;
    &:hover{
      text-decoration: none;
    }
    img{
      margin-right: 3px;
      margin-bottom: 1px;
    }
  }
}
.checkBox{
  color: #333;
  font-size: 12px;
  line-height: 1.8;
  font-weight: bold;
  text-align: center;
  padding: 12px;
  margin: 16px 0;
  border: 1px solid $color-main;
  border-radius: 4px;
}
.check{
  text-align: center;
  margin-bottom: 8px;
}
p{
  font-size: 12px;
  color: #333;
  line-height: 1.45;
  margin-bottom: 6px;
}
@include sp {
}
</style>