<template>
<footer class="frowFooter">
  <div class="frowToolbar" v-for="(param, index) in params.data.datail" :key="param">

    <div class="wrapButton">
      <template v-if="param.type === 'button'" v-for="(data, index) in param.data" :key="data">
        <template v-if="data.type === 'message'" v-for="(data, index) in data.data" :key="data">
          <p class="message"><strong v-if="data.strong">{{ publishedStrong(data.strong) }}</strong>{{ publishedMessage(data.text) }}</p>
        </template>
        <template v-else>
          <template v-if="data.buttonsize === 'full'">
            <div :class="param.data[index+1]?'fullsize is-borderon':'fullsize'">
              <ButtonMainColor :baseColor="data.color" :disabled="data.disabled" :to="publishedLink(data.link)" @click="$emit('onClickBtn'+index, $event)">{{ publishedTitle(data.slot) }}</ButtonMainColor>
            </div>
          </template>
          <template v-else>
            <ButtonMainColor :baseColor="data.color" :disabled="data.disabled" :to="publishedLink(data.link)" @click="$emit('onClickBtn'+index, $event)">{{ publishedTitle(data.slot) }}</ButtonMainColor>
          </template>
        </template>
      </template>
    </div>
  </div>
</footer>
</template>

<script lang="ts" setup>
const params = defineProps<{
  data: {},
}>();

const publishedStrong = computed(() => (data) => {
  return data;
})

const publishedMessage = computed(() => (data) => {
  return data;
})

const publishedTitle = computed(() => (data) => {
  return data;
})

const publishedLink = computed(() => (data) => {
  return data || undefined;
});
</script>

<style lang="scss" scoped>
.frowFooter {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px 0 10px;
  background-color: $color-mainbackground;
  box-shadow: 0px -2px 4px rgba(0, 0, 0, 0.06);
  z-index: 11;
  &.frowFooterFixed {
    position: fixed;
  }
  &.frowFooterStatic {
    position: static;
    @include sp {
      position: fixed;
    }
  }
  .frowToolbar {
    & ~ .frowToolbar {
      margin-top: 10px;
    }
    .message {
      width: 100%;
      text-align: center;
      font-size: 12px;
      line-height: 14px;
      color: $color-blacktext2;
      strong {
        font-weight: 700;
        font-size: 14px;
        letter-spacing: 0.1em;
      }
    }
    .wrapButton {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      a {
        max-width: 210px;
        margin: 0 10px;
      }
      .fullsize {
        width: 100%;
        text-align: center;
        &.is-borderon {
          margin-bottom: 10px;
          padding: 2px 10px 12px;
          border-bottom: 1px solid $color-lightgray;
        }
        a {
          max-width: 100%;
          max-width: 440px;
        }
      }
    }
  }
}

@include sp {

.frowFooter {
  padding: 13px 0 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  .frowToolbar {
    padding-top: 3px;
    padding-left: 5px;
    padding-right: 5px;
    & ~ .frowToolbar {
      margin-top: 7px;
    }
    .wrapButton {
      a {
        width: 48%;
        margin: 0 1%;
        padding: 8px 7px;
        min-height: 34px;
      }
      .fullsize {
        &.is-borderon {
          margin-bottom: 15px;
          padding: 4px 10px 17px;
        }
        a {
          width: 100%;
          padding: 12px 10px;
        }
      }
    }
  }

  &.frowFooterGuestList {
    .frowToolbar {
      .message {
        width: 100%;
      }
      .button--main ,
      .button--accent {
        max-width: 100%;
        width: 100%;
        margin-bottom: 10px;
      }
    }
  }
}
}
</style>