<template>
<div class="makingToolbar" :class="[(paymentData.payment.display[0] || paymentData.payment.subtotal[0] || paymentData.payment.calculation[0] || paymentData.payment.discount[0]) ? 'onpayment' : '']">
  <template v-if="paymentData.payment">
    <ShowPaymentAmount class="toolbar" :data="paymentData.payment" />
  </template>
  <div class="flow">
    <ButtonMainColor size="lg" spsize="spmd" :to="publishedLink(paymentData.button.link)">{{ publishedTitle(paymentData.button.slot) }}</ButtonMainColor>
  </div>

</div>
</template>

<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const paymentData = params.data;

const publishedTitle = computed(() => (data) => {
  return data;
})

const publishedLink = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>

.makingToolbar {
  width: 375px;
  padding-bottom: 24px;
  &.onpayment {
    background-color: $color-mainbackground;
  }
  .flow {
      
    padding: 0 24px;
  }
}

@include sp {

.makingToolbar {
  width: 100%;
}
}
</style>