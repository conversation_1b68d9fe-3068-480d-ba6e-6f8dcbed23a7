<template>
<div class="footerTop">
  <hr class="hr-full">
  <div class="banners">
    <div class="section-inner">
      <ul>
        <li
          class="banner"
          v-for="(item, index) in bannerData"
          :key="index"
        >
          <a target="_blank" class="link-img" :href="item.plan_text">
            <img :src="item?.image?.url" alt="">
          </a>
        </li>
      </ul>
    </div>
  </div>
  <hr class="hr-full hr-lg">

  <div class="schedule" v-if="false">
    <!-- どのページで使うのか不明ですが一応取っておきます  -->
    <div class="section-inner">
      <p><b>今日</b>の<b>23時59分</b>までにご注文の場合の</p>
      <p class="about">出荷日について</p>
      <ul class="tabtrg">
        <li v-for="(schedule, index) in scheduleData.datail" :key="schedule" :class="index === 0?'is-active':''" @click="useMethodsTabChange($event)"><span v-html="publishedSchedule(schedule.label)"></span></li>
      </ul>
      <ul class="tabsbj">
        <li v-for="(schedule, index) in scheduleData.datail" :key="schedule" :class="index === 0?'is-active':''">
          <ul>
            <li v-for="(date, index) in schedule.schedule" :key="date">
              <strong>{{ publishedType(date.type) }}</strong>
              <p>{{ publishedShipdate(date.shipdate) }}</p>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>

</div>
</template>

<script lang="ts" setup>

import { useMethodsTabChange } from '@/assets/js/customFunction.js'

const scheduleData = {
  datail: [
    {
      label: '招待状',
      schedule: [
        {
          type: '通常納期',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期',
          shipdate: '8月16日(月)',
        },
      ],
    },
    {
      label: '席次表・席札<wbr>メニュー表',
      schedule: [
        {
          type: '通常納期2',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期2',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期2',
          shipdate: '8月16日(月)',
        },
      ],
    },
    {
      label: 'ウェルカム<wbr>ボード',
      schedule: [
        {
          type: '通常納期3',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期3',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期3',
          shipdate: '8月16日(月)',
        },
      ],
    },
    {
      label: 'プチ<wbr>ギフト',
      schedule: [
        {
          type: '通常納期4',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期4',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期4',
          shipdate: '8月16日(月)',
        },
      ],
    },
    {
      label: '結婚報告<wbr>はがき',
      schedule: [
        {
          type: '通常納期5',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期5',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期5',
          shipdate: '8月16日(月)',
        },
      ],
    },
  ],
};

const publishedImage = computed(() => (data) => {
  return data;
});

const publishedLink = computed(() => (data) => {
  return data;
});

const publishedAlt = computed(() => (data) => {
  return data;
});

const publishedSchedule = computed(() => (data) => {
  return data;
});

const publishedType = computed(() => (data) => {
  return data;
});

const publishedShipdate = computed(() => (data) => {
  return data + 'に出荷';
});

// microCMSから共通フッターバナーを取得
const microCms = new MicroCms();
const { data: microCmsData, error: microCmsError } = await microCms.fetch('/contents', {
  filters: 'code[equals]footer_banner'
});
const bannerData = computed(() => {
  try {
    let results = JSON.parse(JSON.stringify(microCmsData.value?.contents?.[0]?.item));
    return results ? results : [];
  } catch (error) {
    return [];    
  }
});

</script>

<style lang="scss" scoped>

.footerTop {
  .hr-full{
    border-bottom: 8px solid #F9F9F9;
  }
  .hr-full.hr-lg{
    border-bottom: 92px solid #F9F9F9;
  }
  .banners {
    padding: 57px 0 48px;
    background-color: $color-mainbackground;
    .section-inner{
      max-width: 678px;
    }
    ul {
      display: block;
    }
  }
  .banner {
    padding: 0 14px 20px;
    margin: 0;
    &:last-child {
      padding-bottom: 0;
    }
  }
  .schedule {
    padding: 72px 0 69px;
    background-color: $color-lightgray2;
    text-align: center;
    .section-inner {
      & > p {
        font-size: 16px;
        line-height: 19px;
        letter-spacing: 0.1em;
        b {
          font-weight: 700;
        }
        &.about {
          margin-top: 5px;
          font-size: 28px;
          line-height: 45px;
        }
      }
      .tabtrg {
        justify-content: center;
        gap: 25px;
        margin-top: 28px;
        li {
          padding: 2px 3px 3px;
          border-bottom: 4px solid transparent;
          font-size: 16px;
          line-height: 1.5;
          letter-spacing: 0.1em;
          opacity: 0.3;
          cursor: pointer;
          transition: padding 0.15s ease , opacity 0.15s ease;
          &:hover ,
          &.is-active {
            padding: 0 3px 5px;
            border-color: $color-grayborder;
            opacity: 1;
          }
          span {
            white-space: nowrap;
          }
        }
      }
      .tabsbj {
        & > li {
          padding: 30px 0 40px;
          background-color: $color-mainbackground;
          ul {
            display: flex;
            justify-content: center;
            gap: 17px;
            li {
              min-width: 263px;
              padding: 4px 20px 12px;
              border: 1px solid $color-grayborder;
              strong {
                font-weight: 700;
                font-size: 16px;
                line-height: 40px;
                letter-spacing: 0.1em;
              }
              p {
                margin-top: 3px;
                font-size: 22px;
                line-height: 40px;
                letter-spacing: 0.08em;
              }
            }
          }
        }
      }
    }
  }
  .abouts {
    background-color: $color-lightgray;
    ul {
      display: flex;
      justify-content: center;
      gap: 15px;
      padding: 53px 0 86px;
    }
  }
}

@include sp {

.footerTop {
  .abouts {
    display: none;
  }
  .banners {
    padding: 24px 0 0;
    margin: 0;
  }
  .banners ul {
    display: block;
    .banner {
      padding: 0 14px 20px;
      margin: 0;
    }
  }
  .schedule {
    padding: 43px 10px 41px;
    .section-inner {
      & > p {
        font-size: 12px;
        line-height: 15px;
        letter-spacing: 0.1em;
        &.about {
          letter-spacing: 0.1em;
          font-size: 22px;
          line-height: 32px;
        }
      }
      .tabtrg {
        gap: 0;
        margin-top: 19px;
        padding: 0;
        li {
          min-height: 3.2em;
          padding: 2px 8px 3px;
          font-size: 12px;
          line-height: 14px;
          &:hover ,
          &.is-active {
            padding: 0 8px 5px;
          }
          span {
            display: inline-flex;
            align-items: center;
            height: 100%;
          }
        }
      }
      .tabsbj {
        & > li {
          padding: 25px 0;
          ul {
            display: block;
            li {
              display: flex;
              justify-content: center;
              align-items: center;
              min-width: 100%;
              padding: 3px 15px;
              border-width: 0;
              strong {
                display: inline-block;
                min-width: 6em;
                font-size: 12px;
                line-height: 1.5;
              }
              p {
                margin: 0 0 0 1.5em;
                font-size: 16px;
                line-height: 1.5;
              }
            }
          }
        }
      }
    }
  }
}
}
</style>