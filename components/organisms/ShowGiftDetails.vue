<template>
<div v-for="(param, index) in ListGift.param" :key="param">
  <ul class="listgift" :class="param.type">
    <ListGiftDetail :data="param" />
  </ul>
</div>
</template>
  
<script lang="ts" setup>
export interface Props {
  data: Array,
}

const params = withDefaults(defineProps<Props>(), {
});

const ListGift = params.data;
</script>

<style lang="scss" scoped>
.suttitle-footer-top {
  margin: 0 0 8px;
  line-height: 1.2;
  font-size: 12px;
  font-weight: bold;
}
ul {
  & ~ .suttitle-footer-top {
    margin-top: 40px;
  }
}
.listgift {
  width: 600px;
  max-width: 100%;
}
.listgift.listsubgift {
  margin-top: 25px;
  .title {
    margin-top: 7px;
    font-size: 16px;
    line-height: 160%;
    color: $color-blacktext2;
  }
}
.listgift.listgiftrequestconfirm {
  min-width: 320px;
}

@include sp {
.listgift {
  width: 100%;
  min-width: 100%;
}
}
</style>