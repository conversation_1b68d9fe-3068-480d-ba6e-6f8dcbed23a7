<template>
<div class="showList" :class="params.data.datail[0]?'':'emptyList'">
  <template v-if="params.data.datail[0]">
  <table>
    <thead>
      <tr class="header">
        <td class="empty"></td>
        <td class="check">
          <label><input type="checkbox" id="checkbox" v-model="checked" />
          <span class="checkbox"></span></label>
        </td>
        <th class="name">ゲスト</th>
        <th class="gift01">メイン</th>
        <th class="gift02">2品目</th>
        <th class="gift03">3品目</th>
        <td class="empty"></td>
      </tr>
    </thead>
    <tbody>
      <PieceGiftSendingList :data="params.data" />
    </tbody>
  </table>
  </template>
  <template v-else>
    <p>まだギフト情報がありません </p>
  </template>
</div>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
}

const params = withDefaults(defineProps<Props>(), {
  data: {},
});
</script>

<style lang="scss" scoped>
.showList {
  table {
    border-collapse: collapse;
    width: 100%;
    color: $color-blacktext2;
  }
  tr ,
  :deep(tr) {
    text-align: left;
    &.header {
      background-color: $color-lightbackground;
      th ,
      td {
        font-size: 14px;
        font-weight: 400;
        line-height: 24px;
        &.name {
          width: 315px;
          padding-left: 5px;
          font-size: 16px;
          letter-spacing: 1.6px;
        }
        &[class^="gift"] {
          width: 73px;
        }
      }
      td {
        padding-top: 15px;
        padding-bottom: 9px;
      }
    }
    td {
      padding-top: 13px;
      padding-bottom: 11px;
      &.empty {
        width: 18px;
        & ~ .empty {
          width: 263px;
        }
      }
      &.name {
        padding-top: 14px;
        padding-bottom: 15px;
      }
      &[class^="gift"] {
        padding-left: 12px;
        padding-top: 8px;
      }
      input[type="checkbox"] {
        display: none;
      }
      .checkbox {
        display: inline-block;
        width: 18px;
        height: 18px;
        border: 2px solid $color-graytext2;
        border-radius: 3px;
        background-color: $color-mainbackground;
        cursor: pointer;
      }
      input:checked ~ .checkbox {
        border-color: $color-main;
        background: url(@/assets/images/icon-check-w.svg) no-repeat center center;
        background-color: $color-main;
        background-size: 14px;
      }
      &.check {
        width: 25px;
      }
    }
  }
  tbody {
    border-bottom: 1px solid $color-grayborder;
    :deep(tr.principal) {
      td {
        border-top: 1px solid $color-grayborder;
      }
    }
    :deep(tr):first-of-type {
      td {
        border-top: 0 solid $color-grayborder;
      }
    }
  }
  &.emptyList {
    padding: 45px 24px 71px;
    border-top: 1px solid #F4F4F4;
    color: $color-graytext2;
  }
}

@include sp {
.showList {
  overflow-x: auto;
  &.emptyList {
    margin: 0 15px;
    padding-top: 63px;
    text-align: center;
    font-size: 14px;
  }
  table {
    width: 100%;
    min-width: 600px;
    margin: 0;
  }
  :deep(tr) {
    &.header {
      th {

        &.name {
          width: calc(100% - 186px);
          padding: 0;
          font-size: 14px;
        }
        &[class^="gift"] {
          width: 43px;
          font-size: 12px;
        }
      }
      td {
        padding-top: 9px;
        padding-bottom: 5px;
      }
    }
    td {
      &.check {
        width: 30px;
      }
    }
    th ,
    td {
      &.name {
        width: calc(100% - 186px);
        padding-top: 11px;
        padding-left: 0;
        padding-bottom: 12px;
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 1.4px;
      }
    }
    td {
      padding-top: 11px;
      padding-bottom: 9px;
      &.empty {
        width: 18px;
        & ~ .empty {
          width: 18px;
        }
      }
      &.name {
        letter-spacing: 0;
      }
      &[class^="gift"] {
        width: 43px;
        padding-left: 9px;
        font-size: 12px;
        img {
          width: 15px;
        }
      }
      .checkbox {
        width: 17px;
        height: 17px;
      }
    }
  }
}
}
</style>