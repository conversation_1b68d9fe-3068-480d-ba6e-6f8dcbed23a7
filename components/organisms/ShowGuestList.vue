<template>
  <div v-if="guests.length" class="searchWrap">
    <div class="manageSearch" v-if="! isShowSearch">
      <button class="searchButton" @click="isShowSearch = (! isShowSearch)">絞り込む</button>
      <ul class="manageAttribute" v-if="! isModal">
        <li><button class="manageGroup" @click.prevent="isShowModalGroup = true">グループ</button></li>
        <li><button class="manageTag" @click.prevent="isShowModalTag = true">タグ</button></li>
      </ul>
      <ModalManageGroup
        v-if="isShowModalGroup"
        :guestListId="guestListId"
        @close="isShowModalGroup = false"
        @reload="emits('onReloaded')"
      ></ModalManageGroup>
      <ModalManageTag
        v-if="isShowModalTag"
        :guestListId="guestListId"
        @close="isShowModalTag = false"
        @reload="emits('onReloaded')"
      ></ModalManageTag>
    </div>
    <div class="searchList" v-if="isShowSearch">
      <div class="searchItems">
        <div class="searchItem" v-for="event, index in events" :key="index">
          <InputSelect
            :title="event.name"
            size="md"
            :value="filter.attendance[index]"
            :options="GuestAttendanceOptions"
            @input="filter.attendance[index] = $event.target.value"
          />
        </div>
      </div>
      <div class="searchItems">
        <div class="searchKeyword">
          <i class="icn-left material-symbols-outlined">search</i>
          <InputText 
            size="full"
            :value="debounceFilter.q"
            placeholder="ゲストの名前で検索"
            @input="debounceFilter.q = $event.target.value"
          />
        </div>
      </div>
      <div class="searchItems">
        <div class="searchItem">
          <InputRadioBtns
            title="回答した招待状"
            :value="filter.web_invitation"
            :options="WebInvitationOptions"
            @change="filter.web_invitation = $event"
          />
        </div>
        <div class="searchItem">
          <InputRadioBtns
            title="新郎側 / 新婦側"
            :value="filter.guest_type"
            :options="GuestTypeOptions"
            @change="filter.guest_type = $event"
          />
        </div>
      </div>
      <div class="searchItems">
        <div class="searchItem">
          <InputRadioBtns
            title="グループ・タグ"
            :value="filter.group_tag"
            :options="GuestGroupTagOptions"
            @change="filter.group_tag = $event"
          />
        </div>
      </div>
      <div class="searchBtns">
        <button class="link-text size--md" @click="onClickClear()"><i class="icn-left material-symbols-outlined">refresh</i> クリア</button>
        <button class="btn btn-default-outline" @click="isShowSearch = false">閉じる</button>
      </div>
    </div>
  </div>
  <div v-if="guests.length && ! isModal" class="subTitle" :class="{'is-onguests': partsHidden.manage === 'hidden'}">
    <template v-if="partsHidden.manage !== 'hidden'">
      <div class="manageGuestList">
        <div class="manageGuestList_date">
          <span v-if="[...new Set(guestList?.web_invitations?.map((item:any) => item.editor_settings?.blocks?.find((block:any) => block.id == 'information')?.contents?.date).filter(date => date != null))]?.[0]">
            開催日：{{ $dayjs([...new Set(guestList?.web_invitations?.map((item:any) => item.editor_settings?.blocks?.find((block:any) => block.id == 'information')?.contents?.date).filter(date => date != null))]?.[0]).format('YYYY/MM/DD') }}
          </span>
          <span v-else>
            開催日：——/—/—
          </span>
        </div>
        <ControlWindowBtn data-mode="sort">
          <template #button>
            <button class="link-text size--md">
              <i class="icn-left material-symbols-outlined mr-5">swap_vert</i>
              <span v-if="orderBy.column === QueryGuestsOrderByColumn.CreatedAt && orderBy.order === SortOrder.Desc">新着順</span>
              <span v-if="orderBy.column === QueryGuestsOrderByColumn.CreatedAt && orderBy.order === SortOrder.Asc">回答順</span>
              <span v-else-if="orderBy.column === QueryGuestsOrderByColumn.Name && orderBy.order === SortOrder.Asc">お名前順</span>
            </button>
          </template>
          <ul>
            <li :class="{'is--current': (orderBy.column === QueryGuestsOrderByColumn.CreatedAt && orderBy.order === SortOrder.Desc)}" @click.prevent="onChangeSort({column: QueryGuestsOrderByColumn.CreatedAt, order: SortOrder.Desc})">新着順</li>
            <li :class="{'is--current': (orderBy.column === QueryGuestsOrderByColumn.CreatedAt && orderBy.order === SortOrder.Asc)}" @click.prevent="onChangeSort({column: QueryGuestsOrderByColumn.CreatedAt, order: SortOrder.Asc})">回答順</li>
            <li :class="{'is--current': (orderBy.column === QueryGuestsOrderByColumn.Name && orderBy.order === SortOrder.Asc)}" @click.prevent="onChangeSort({column: QueryGuestsOrderByColumn.Name, order: SortOrder.Asc})">お名前順</li>
          </ul>
        </ControlWindowBtn>
      </div>
    </template>
    <div class="statusList" v-if="partsHidden.statusList !== 'hidden'">
      <dl class="total">
        <dt>登録：</dt>
        <dd>
          <strong>{{ filterdGuests.length }}</strong>名
          <a v-if="events.length" class="link-text" href="javascript:void(0);" @click="isShowListSummary = (! isShowListSummary)">
            <span :class="{'icn-left material-symbols-outlined': true, 'is-close': ! isShowListSummary}">keyboard_arrow_up</span>
            内訳
          </a>
        </dd>
      </dl>
      <div :class="{'statusListEvents': true, 'acdsbj': true, 'is-close': ! isShowListSummary}">
        <ul v-for="event, index in events" :key="index">
          <li class="name">
            <strong>{{ event.name }}</strong>
          </li>
          <li class="attendance">
            <strong>出席：{{ filterdGuests.filter(guest => guest.guest_event_attendances.findIndex(attendance => attendance.name === event.name && attendance.attendance === GUEST_ATTENDANCE_MASTER.PRESENT) !== -1).length }}</strong>
          </li>
          <li class="pending">
            <strong>未定：{{ filterdGuests.filter(guest => guest.guest_event_attendances.findIndex(attendance => attendance.name === event.name && attendance.attendance === GUEST_ATTENDANCE_MASTER.PENDING) !== -1).length }}</strong>
          </li>
          <li class="absence">
            <strong>欠席：{{ filterdGuests.filter(guest => guest.guest_event_attendances.findIndex(attendance => attendance.name === event.name && attendance.attendance === GUEST_ATTENDANCE_MASTER.ABSENT) !== -1).length }}</strong>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div v-if="! guests.length" class="showList emptyList">
    <h2>ゲスト情報が未登録です</h2>
    <p>ゲストがWEB招待状に回答すると<br>
      その内容が自動で登録されていきます </p>
    <img src="@/assets/images/image-tutorialguest01.svg" alt="" class="tutorialguest01">
    <p>まずは招待状を作って <br class="sp_only">ゲストに回答してもらいましょう！</p>
    <div class="mb-20"><NuxtLink to="/products/webinvitation/" class="btn btn-secondary btn-block size--sm"><i class="icn-left material-icons-outlined">mail</i> 招待状を作る</NuxtLink></div>
    <div class="mb-20"><NuxtLink to="/mypage/webinvitation" class="link-accent size--sm">作成した招待状を見る</NuxtLink></div>
    <hr>
    <p>手動でゲスト情報を登録する場合はこちら</p>
    <ControlWindowBtn data-mode="addbtn" data-position="bottom">
    <template #button>ゲスト情報を追加</template>
      <ul>
        <li @click.prevent="showCreateModal = true">個別に登録する</li>
        <li @click.prevent="showBulkCreateGuestModal = true">一括で登録する</li>
      </ul>
    </ControlWindowBtn>
  </div>
  <div v-else class="showList" id="windowScroll">
    <table>
      <thead>
        <tr class="header">
          <td class="check">
            <label><input type="checkbox" id="checkbox" :checked="checkedGuestIds.length == filterdGuests.length && filterdGuests.length" @change="onChangeGuestCheck('all');" />
            <span class="checkbox"></span></label>
          </td>
          <th colspan="2">ゲスト</th>
          <th class="size--sm" v-for="event, index in events" :key="index">
            {{ event.name }}
            <ControlWindowBtn data-mode="sort" position="fixed">
              <template #button>
                <button class="link-text size--md">
                  <i v-if="! filter?.attendance?.[index]" class="icn-left material-symbols-outlined mr-5">filter_alt</i>
                  <i v-else class="icn-left material-icons mr-5 color-accent">filter_alt</i>
                </button>
              </template>
              <ul>
                <li :class="{'is--current': ! filter?.attendance?.[index]}" @click="filter.attendance[index] = null">すべて</li>
                <li :class="{'is--current': filter?.attendance?.[index] == '1'}" @click="filter.attendance[index] = '1'"><i class="icn-img icn-attendance-present mr-5"></i>出席</li>
                <li :class="{'is--current': filter?.attendance?.[index] == '9'}" @click="filter.attendance[index] = '9'"><i class="icn-img icn-attendance-absent mr-5"></i>欠席</li>
                <li :class="{'is--current': filter?.attendance?.[index] == '0'}" @click="filter.attendance[index] = '0'"><i class="icn-img icn-attendance-pending mr-5"></i>保留</li>
              </ul>
            </ControlWindowBtn>
          </th>
          <th v-if="partsHidden.group !== 'hidden'">グループ</th>
          <th v-if="partsHidden.tags !== 'hidden'">タグ</th>
          <th v-if="partsHidden.tags !== 'hidden'">アレルギー</th>
          <th v-if="partsHidden.tags !== 'hidden'">事前決済</th>
          <th v-if="partsHidden.registration !== 'hidden'">登録日</th>
        </tr>
      </thead>
      <tbody>
        <PieceGuestList 
          :isModal="isModal"
          :guestListId="guestListId" 
          :guests="filterdGuests" 
          :partsHidden="props.partsHidden" 
          :checkedGuestIds="checkedGuestIds"
          :events="events"
          :checkLimit="checkLimit"
          @reload="emits('onReloaded')"
          @onChangeGuestCheck="onChangeGuestCheck" />
      </tbody>
    </table>
    <p v-if="! filterdGuests.length" class="no-result">検索結果が0件です </p>
  </div>
  <div v-if="guests.length" class="addGuestWrap">
    <ControlWindowBtn position="bottom" mode="btn">
      <template #button>
        <img src="@/assets/images/icon-plus-w.svg" style="vertical-align: middle; margin-right: 5px;"> このリストにゲストを追加
      </template>
      <ul>
        <li @click.prevent="showCreateModal = true">個別に登録する</li>
        <li @click.prevent="showBulkCreateGuestModal = true">一括で登録する</li>
      </ul>
    </ControlWindowBtn>
  </div>
  <ModalCreateGuest 
    v-if="showCreateModal"
    :guestListId="guestListId"
    @close="showCreateModal = false"
    @reload="emits('onReloaded')"
  />
  <ModalBulkCreateGuest 
    v-if="showBulkCreateGuestModal"
    :guestListId="guestListId"
    @close="showBulkCreateGuestModal = false"
    @reload="emits('onReloaded')"
  />

  <ModalBulkUpdateGuest 
    v-if="showBulkUpdateGuestModal"
    :events="events"
    :guests="filterdGuests" 
    :guestListId="guestListId"
    :guestIds="checkedGuestIds"
    @close="showBulkUpdateGuestModal = false"
    @reload="emits('onReloaded')"
    @reset="checkedGuestIds = []"
  />
  <ModalCopyGuests 
    v-if="showCopyGuestsModal"
    :guestListId="guestListId"
    :guestIds="checkedGuestIds"
    @close="showCopyGuestsModal = false"
    @reload="emits('onReloaded')"
  />
  <ModalDeleteGuests
    v-if="showDeleteModal"
    :guestListId="guestListId"
    :guestIds="checkedGuestIds"
    @close="showDeleteModal = false"
    @reload="emits('onReloaded')"
    @reset="checkedGuestIds = []"
  />

  <ShowFooterBarFrow 
    v-if="checkedGuestIds.length > 0" 
    class="frowFooterFixed frowFooterGuestList" 
    :data="apiFooterBarFrow"
    @onClickBtn1="showBulkUpdateGuestModal = true"
    @onClickBtn2="showDeleteModal = true"
    @onClickBtn3="showCopyGuestsModal = true"
    @onClickBtn4="checkedGuestIds = []"
  />
</template>

<script lang="ts" setup>
import ModalBulkUpdateGuest from '@/components/organisms/mypage/guest-list/ModalBulkUpdateGuest.vue'
import ModalBulkCreateGuest from '@/components/organisms/mypage/guest-list/ModalBulkCreateGuest.vue'
import ModalCopyGuests from '@/components/organisms/mypage/guest-list/ModalCopyGuests.vue'
import ModalDeleteGuests from '@/components/organisms/mypage/guest-list/ModalDeleteGuests.vue'
import type { GuestListFragmentFragment } from '@/composables/generated';
import { useCloned } from '@vueuse/core'
import { debounce } from 'perfect-debounce'

const router = useRouter();
const route = useRoute();
const { $dayjs } : any = useNuxtApp();

export interface Props {
  guestListId: string,
  guests: GuestListFragmentFragment['guests'],
  isReload: boolean,
  // モーダル内からの利用(連名ゲストを追加する)
  isModal: boolean;
  partsHidden: {
    manage?: string;
    statusList?: string;
    check?: string;
    name?: string;
    icon?: string;
    status?: string;
    group?: string;
    tags?: string;
    registration?: string;
  },
  events?: any[];
  orderBy: any;
  checkLimit?: number|null;
}

const props = withDefaults(defineProps<Props>(), {
  guestListId: '',
  guests: () => { return []},
  isReload: false,
  isModal: false,
  partsHidden: () => { return {}},
  events: () => { return []},
  orderBy: () => { return {}},
  checkLimit: null
});

const emits = defineEmits<{
  (e: 'onChangeSort'): void;
  (e: 'onReloaded'): void;
  (e: 'onChangeGuestCheck', value?: any): void;
}>();

const showCreateModal = ref(false)
const showBulkCreateGuestModal = ref(false)


const isShowSearch = ref(false);
const isShowListSummary = ref(true);

// モーダル表示
const isShowModalGroup = ref(false);
const isShowModalTag = ref(false);

const { guestList } = useGetOneGuestList(String(props.guestListId));

// APIから guestTag を読み込み
const {guestTags} = useGetManyGuestTag(String(props.guestListId))
// APIから guestGroup を読み込み
const {guestGroups} = useGetManyGuestGroup(String(props.guestListId))
// グループリスト
const GuestGroupTagOptions = computed(() => {
  const options1 = guestGroups.value.map(guestGroup => {
    return {value: 'group_'+guestGroup.id, label: guestGroup.name};
  });
  const options2 = guestTags.value.map(tag => {
    return {value: 'tag_'+tag.id, label: tag.tag};
  });
  return [{value: '', label: '全て'}].concat(options1).concat(options2);
});

// グループリスト
const GuestAttendanceOptions = computed(() => {
  return [{value:'', label: 'ー'}, {value: '1', label: '出席'}, {value: '9', label: '欠席'}, {value: '0', label: '保留'}];
  // return [{value:'', label: 'ー'}, {value: '1', label: '出席'}, {value: '9', label: '欠席'}, {value: '0', label: '保留'}, {value: '-1', label: '未回答'}];
});
const WebInvitationOptions = computed(() => {
  const options = guestList.value.web_invitations.map(web_invitation => {
    return {value: web_invitation.id, label: web_invitation.name};
  });
return [{value:'', label: '全て'}].concat(options);
});
// グループリスト
const GuestTypeOptions = computed(() => {
  return [{value:'', label: '全て'}, {value: 'GROOM', label: '新郎側'}, {value: 'BRIDE', label: '新婦側'}];
});


type FilterType = {
  q: string;
  attendance: string[];
  web_invitation: string;
  guest_type: string;
  group_tag: string;
  // group: string;
  // tags: string[];
  // sort_key: QueryGuestsOrderByColumn.CreatedAt | 'ATTENDANCE' | 'GRUOP';
  // sort_by: 'DESC' | 'ASC';
};

const debounceFilter = ref({
  q: '',
});
// 検索条件
const filter = ref({
  q: '',
  attendance: [],
  web_invitation: '',
  guest_type: '',
  group_tag: '',
  // group: '',
  // tags: [],
  // sort_by: 'DESC'
} as FilterType);

const hasFilter = () => {
  if (filter.value?.q) return true;
  if (filter.value?.attendance) return true;
  if (filter.value?.web_invitation) return true;
  if (filter.value?.guest_type) return true;
  if (filter.value?.group_tag) return true;
  // if (filter.value?.group) return true;
  // if (filter.value?.tags) {
  //   if (filter.value?.tags.length) return true;
  // }
  return false;
};
// 検索時
watch(props, async () => {
  onSetGuests()
})
// 検索時
watch(() => JSON.stringify(filter.value), async () => {
  onSearch();
});

// 検索時
watch(() => JSON.stringify(debounceFilter.value), async () => {
  await onChengeSearchKeyword();
});

const onChengeSearchKeyword = debounce(async() => {
  filter.value.q = debounceFilter.value.q;
}, 1000);


const onSearch = () => {
  saveQuery();
  // チェックデータをリセット
  checkedGuestIds.value = [];
  emits('onChangeGuestCheck', checkedGuestIds.value);
  // 検索実行
  onSetGuests();
};

const onClickClear = () => {
  debounceFilter.value.q = '';
  filter.value.q = '';
  filter.value.attendance = [];
  filter.value.web_invitation = '';
  filter.value.guest_type = '';
  filter.value.group_tag = '';
  onSearch();
};

import { QueryGuestsOrderByColumn, SortOrder } from '@/composables/generated';
const onChangeSort = (data: {column:QueryGuestsOrderByColumn, order: SortOrder}) => {
  emits('onChangeSort', data);
  saveQuery();
}

const saveQuery = () => {
  // getパラメータに保存
  let query = {} as any;
  if (filter.value.q) query.q = filter.value.q;
  for (let i = 0; i < props.events.length; i++) {
    const event = props.events[i];
    if (filter.value.attendance[i]) query['attendance['+i+']'] = filter.value.attendance[i];
  }
  if (filter.value.web_invitation) query.web_invitation = filter.value.web_invitation;
  if (filter.value.guest_type) query.guest_type = filter.value.guest_type;
  if (filter.value.group_tag) query.group_tag = filter.value.group_tag;
  // if (filter.value.group) query.group = filter.value.group;
  // if (filter.value.tags) {
  //   if (filter.value?.tags.length) query.tags = filter.value.tags;
  // }
  
  if (props.orderBy.column !== QueryGuestsOrderByColumn.CreatedAt) query.order_key = props.orderBy.column;
  if (props.orderBy.order !== SortOrder.Desc) query.order_by = props.orderBy.order;
  
  router.push({ query: query });
  setTimeout(function(){
    overwriteUrlHistory();
  }, 100);

}

// 検索フィルタ結果を返却
const filterdGuests = ref(props.guests);

// APIの返却結果を見る

// アクティブなタブをセット
onMounted(() => {
  onClickClear();
  // getパラメータから復元
  if (route.query.q) {
    filter.value.q = route.query.q as string;
    debounceFilter.value.q = route.query.q as string;
  }
  for (let i = 0; i < props.events.length; i++) {
    if (route.query['attendance['+i+']']) filter.value.attendance[i] = route.query['attendance['+i+']'] as string;
  }
  // if (route.query.group) filter.value.group = route.query.group as string;
  // if (route.query.tags) filter.value.tags = route.query.tags as string[];
  if (route.query.web_invitation) filter.value.web_invitation = route.query.web_invitation as string;
  if (route.query.guest_type) filter.value.guest_type = route.query.guest_type as string;
  if (route.query.group_tag) filter.value.group_tag = route.query.group_tag as string;
  filter.value = useCloned(filter.value).cloned.value
  onSetGuests();
});

// 検索実行
const onSetGuests = () => {
  if (typeof props.guests === 'undefined') {
    filterdGuests.value = [];
    return false;
  }
  let items = useCloned(props.guests).cloned.value;
  if (filter.value.q) {
    items = items.filter((item:any) => {
      let isTarget = false;
      if (String(item.last_name).indexOf(filter.value.q) !== -1) isTarget = true;
      if (String(item.first_name).indexOf(filter.value.q) !== -1) isTarget = true;
      if (String(item.last_name_romaji).indexOf(filter.value.q) !== -1) isTarget = true;
      if (String(item.first_name_romaji).indexOf(filter.value.q) !== -1) isTarget = true;
      if (String(item.last_name_kana).indexOf(filter.value.q) !== -1) isTarget = true;
      if (String(item.first_name_kana).indexOf(filter.value.q) !== -1) isTarget = true;
      return isTarget;
    })
  }

  // 出欠
  for (const i in filter.value.attendance) {
    const event = props.events[i];
    let attendance = filter.value.attendance[i];
    if (! attendance) continue;
    items = items.filter((item:any) => {
      const guest_event_answer = item.guest_event_attendances.find((item:any) => item.name == event.name);
      if (attendance == '0') {
        // 未定
        if (! guest_event_answer) return true; 
      }
      if (! guest_event_answer) return false;
      if (attendance == '1') {
        // 出席
        return (guest_event_answer.attendance == GUEST_ATTENDANCE_MASTER.PRESENT);
      } else if (attendance == '9') {
        // 出席
        return (guest_event_answer.attendance == GUEST_ATTENDANCE_MASTER.ABSENT);
      } else if (attendance == '0') {
        // 出席
        return (guest_event_answer.attendance == GUEST_ATTENDANCE_MASTER.PENDING);
      } else if (attendance == '-1') {
        // 出席
        if (! guest_event_answer) return true; 
        return ! (guest_event_answer.attendance == GUEST_ATTENDANCE_MASTER.PRESENT || guest_event_answer.attendance == GUEST_ATTENDANCE_MASTER.ABSENT || guest_event_answer.attendance == GUEST_ATTENDANCE_MASTER.PENDING);
      }
    });
  }

  // グループ・タグ
  if (filter.value.group_tag) {
    const id = filter.value.group_tag.replace(/^.*?_/, '');
    if (filter.value.group_tag.indexOf('group_') !== -1) {
      const group = guestGroups.value.find(group => group.id == id);
      items = items.filter((item:any) => {
        let guest_group_name = '';
        if (item?.guest_group_name) guest_group_name = item?.guest_group_name;
        if (item?.guest_group?.name) guest_group_name = item?.guest_group?.name;
        if (guest_group_name == group?.name) return true
        return false;
      })
    } else if (filter.value.group_tag.indexOf('tag_') !== -1) {
      items = items.filter((item:any) => {
        let guest_event_tags = [];
        if (item?.guest_event_tags) guest_event_tags = item?.guest_event_tags;
        if (item?.guest_tags) guest_event_tags = item?.guest_tags;
        if (guest_event_tags.findIndex((tag:any) => tag.id == id) !== -1) return true
        return false;
      })
    }
  }

  // 新婦側 / 新郎側
  if (filter.value.web_invitation) {
    items = items.filter((item:any) => {
      if (item.web_invitation_id == filter.value.web_invitation) return true
      return false;
    });
  }

  // 新婦側 / 新郎側
  if (filter.value.guest_type) {
    items = items.filter((item:any) => {
      if (item.guest_type == filter.value.guest_type) return true
      return false;
    });
  }

  filterdGuests.value = items;
}

// チェックされたユーザID
const checkedGuestIds = ref([] as string[]);

// チェック時
const onChangeGuestCheck = (id:string) => {
  if (id === 'all') {
    if (typeof filterdGuests.value !== 'object') return false;
    // 全件選択時は チェック解除
    if (filterdGuests.value.length === checkedGuestIds.value.length || checkedGuestIds.value.length === props.checkLimit) {
      checkedGuestIds.value = [];
    } else {
      const ids:string[] = filterdGuests.value.map(guest => guest.id);
      if (props.checkLimit !== null) {
        checkedGuestIds.value = ids.slice(0, props.checkLimit);
      } else {
        checkedGuestIds.value = ids;
      }
    }
  } else {
    if (checkedGuestIds.value.indexOf(id) === -1 && (props.checkLimit === null || checkedGuestIds.value.length < props.checkLimit)) {
      checkedGuestIds.value.push(id);
    } else {
      const ids = checkedGuestIds.value.filter(itemId => itemId !== id);
      checkedGuestIds.value = ids;
    }
  }

  emits('onChangeGuestCheck', checkedGuestIds.value);
  if (apiFooterBarFrow.value.datail?.[0].data?.[0].data?.[0].strong) {
    apiFooterBarFrow.value.datail[0].data[0].data[0].strong = String(checkedGuestIds.value.length);
  }
};


let apiFooterBarFrow = ref({
  datail: [
    {
      type: 'button',
      data: [
        {
          type: 'message',
          data: [
            {
              strong: '0',
              text: '件 選択されています',
            },
          ],
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "一括で編集する",
          link: false,
        },
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "削除する",
          link: false,
        },
        {
          buttonsize: 'harf',
          color: 'reversal',
          disabled: false,
          slot: "他のリストに複製する",
          link: false,
        },
        {
          buttonsize: 'full',
          color: 'clear',
          disabled: false,
          slot: "キャンセル",
          link: false,
        },
      ],
    },
  ],
});

const showBulkUpdateGuestModal = ref(false)
const showCopyGuestsModal = ref(false)
const showDeleteModal = ref(false)
</script>
<style lang="scss">
.searchList .searchKeyword label input {
  padding-left: 40px;
}
</style>
<style lang="scss" scoped>
.subTitle {
  position: relative;
  padding: 16px 27px;

  &.is-onguests {
    display: flex;
    justify-content: space-between;
  }
  h2 {
    margin: 0;
    font-weight: 400;
    font-size: 18px;
    line-height: 120%;
    letter-spacing: 0.04em;
    color: $color-accent;
  }
}
.manageGuestList {
  position: absolute;
  top: 28px;
  right: 31px;
  @include sp {
    top: 25px !important;
    right: 5px !important;
  }
  > * {
    display: inline-block;
  }
  .manageGuestList_date {
    font-size: 14px;
    line-height: 120%;
    margin-right: 20px;
    color: $color-blacktext2;
    @include sp {
      margin-right: 5px;
    }
  }
}
.statusList {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 27px 0 0;
  .statusListEvents {
    display: block;
    width: 100%;
    padding: 0 !important;
    .name {
      min-width: 7.5em;
    }
    > * {
      padding: 0 !important;
      margin-top: 5px;
    }
  }
  dl {
    font-size: 14px;
    line-height: 120%;
    color: $color-blacktext2;
    &.total {
      display: flex;
      align-items: center;
      position: relative;
    }
    &.total {
      justify-content: center;
      margin-bottom: 8px;
      margin-left: 0;
      padding-top: 0;
      padding-bottom: 0;
      padding-left: 0;
      dt {
        position: relative;
        width: 15px;
        height: 15px;
        font-size: 0;
        &::before {
          @include BA;
          left: 0;
          width: 17px;
          height: 17px;
          background-image: url(@/assets/images/icon-person-gl.svg);
        }
      }
      dd {
        padding-left: 6px;
        strong {
          font-size: 20px;
          font-weight: 400;
          line-height: 130%;
          letter-spacing: 0.04em;
        }
        .link-text {
          margin-left: 20px;
          font-size: 14px;
          @include sp {
            margin-left: 5px;
          }
          .icn-left {
            transition: 0.35s ease-in-out;
            font-weight: bold;
            font-size: 20px;
            margin-top: -2px;
            &.is-close {
              transform: rotate(180deg);
            }
          }
        }
      }
    }
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    padding: 0 0 0;
    li {
      position: relative;
      font-size: 12px;
      line-height: 120%;
      letter-spacing: 0.24px;
      strong {
        font-size: 13px;
        font-weight: 400;
        line-height: 120%;
        letter-spacing: 0.72px;
        color: $color-blacktext2;
      }
      &.name {
        padding-left: 0;
        strong {
          // display: flex;
          padding: 6px;
          align-items: center;
          border-radius: 4px;
          background: 3px;
          font-size: 12px;
          background-color: $color-lightbackground;
          display: inline-block;
        }
      }
      & ~ li {
        margin-left: 13px;
      }
      &::before {
        @include BA;
        left: 0;
        width: 16px;
        height: 15px;
      }
      &.attendance {
        margin-left: 21px;
        padding-left: 20px;
      }
      &.attendance::before {
        background-image: url(@/assets/images/icon-check.svg);
      }
      &.pending {
        padding-left: 20px;
      }
      &.pending::before {
        background-image: url(@/assets/images/icon-question_mark.svg);
      }
      &.absence {
        padding-left: 20px;
      }
      &.absence::before {
        background-image: url(@/assets/images/icon-absence.svg);
      }
    }
  }

}
.searchWrap {
  .manageSearch {
    display: flex;
    justify-content: space-between;
    padding: 10px 18px 9px 28px;
    @include sp {
      padding-left: 14px;
    }
    button {
      position: relative;
      padding-left: 32px;
      &::before {
        @include BA;
        left: 0;
        width: 28px;
        height: 28px;
        background-image: url(@/assets/images/icon-search-ac.svg);
      }
    }
    .searchButton {
      color: $color-accent;
      font-size: 16px;
      font-weight: 700;
      line-height: 100%;
      letter-spacing: 0.32px;
    }
    .manageAttribute {
      display: flex;
      button {
        color: $color-main;
        font-size: 14px;
        font-weight: 700;
        line-height: 100%;
        letter-spacing: 0.28px;
        padding-left: 28px;
        &::before {
          width: 24px;
          height: 24px;
        }
      }
      li {
        padding: 3px 0;
        & ~ li {
          margin-left: 10px;
          padding-left: 15px;
          border-left: 1px solid $color-grayborder;
        }
      }
      .manageGroup {
        &::before {
          background-image: url(@/assets/images/icon-group.svg);
        }
      }
      .manageTag {
        &::before {
          background-image: url(@/assets/images/icon-tag.svg);
        }
      }
    }
  }
  .searchResult {
    padding: 0 23px;
    .searchResultInner {
      display: flex;
      justify-content: space-between;
      padding: 20px 0 25px;
      border-top: 1px solid $color-lightgray;
      .detailResult {
        display: flex;
        align-items: center;
        & > p {
          margin-right: 20px;
          padding-right: 20px;
          border-right: 1px solid $color-lightgray;
          color: $color-blacktext2;
          font-size: 16px;
          line-height: 120%;
          letter-spacing: 0.32px;
          strong {
            font-size: 24px;
            font-weight: 400;
            line-height: 130%;
            letter-spacing: 0.48px;
          }
        }
        .conditions {
          display: flex;
          flex-wrap: wrap;
          align-items: baseline;
          color: $color-blacktext2;
          font-size: 12px;
          line-height: 100%;
          .word {
            width: 100%;
            margin-bottom: 13px;
            font-size: 14px;
            line-height: 100%;
            letter-spacing: 0.56px;
          }
          .status ,
          .group ,
          .tags {
            position: relative;
            margin-right: 18px;
            padding-left: 23px;
            &::before {
              @include BA;
              left: 0;
              width: 15px;
              height: 20px;
            }
            &.attendance::before {
              background-image: url(@/assets/images/icon-check.svg);
            }
            &.pending::before {
              background-image: url(@/assets/images/icon-question_mark.svg);
            }
            &.absence::before {
              background-image: url(@/assets/images/icon-absence.svg);
            }
          }
          .group::before {
            width: 20px;
            margin-right: 16px;
            background-image: url(@/assets/images/icon-group-g.svg);
          }
          .tags::before {
            width: 20px;
            background-image: url(@/assets/images/icon-tag-g.svg);
          }
        }
      }
      .restart {
        position: relative;
        margin-top: 5px;
        padding-left: 23px;
        color: $color-accent;
        font-size: 14px;
        line-height: 100%;
        &::before {
          @include BA;
          left: 0;
          width: 20px;
          height: 20px;
          background-image: url(@/assets/images/icon-restart-g.svg);
        }
      }
    }
  }
}

.searchList {
  padding: 15px 24px 24px;
  background-color: $color-lightgray;
  .searchItems {
    // margin-bottom: 16px;
  }
  .searchKeyword {
    position: relative;
    margin-bottom: 16px;
    .icn-left {
      position: absolute;
      left: 0;
      top: 50%;
      color: #9C9C9C;
      z-index: 2;
      font-size: 24px;
      line-height: 1;
      margin-top: -12px;
      padding: 0 10px;
    }
    label {
      position: relative;
      z-index: 1;      
      // input {
      //   padding-left: 40px !important;
      // }
    }
  }
  .searchItem {
    // padding-top: 16px;
    display: inline-block;
    margin-right: 24px;
    min-width: 146px;
    margin-bottom: 16px;
  }
  .searchBtns {
    text-align: right;
    .btn {
      margin-left: 24px;
      width: 192px;
      font-weight: normal;
    }
  }
}

.showList {
  overflow-x: auto;
  margin-bottom: 100px;
  table {
    border-collapse: collapse;
    width: 100%;
    font-weight: 700;
    font-size: 12px;
    line-height: 120%;
    letter-spacing: 0.02em;
    color: $color-blacktext2;
  }
  :deep(tr) {
    &.header {
      background-color: $color-lightbackground;
      .check {
        padding-left: 22px;
        & ~ th ,
        & ~ td {
          padding-left: 11px;
          text-align: left;
        }
      }
    }
    th, td {
      font-weight: normal;
      font-size: 14px;
      white-space: nowrap;
      .controlWindowWrap {
        display: inline-block;
      }
      &.size--sm {
        font-size: 12px;
      }
    }
    td {
      padding-top: 11px;
      padding-bottom: 11px;
      vertical-align: middle;
      font-weight: normal;
      &.empty {
        width: 18px;
      }
      input[type="checkbox"] {
        display: none;
      }
      .checkbox {
        display: inline-block;
        width: 18px;
        height: 18px;
        border: 2px solid $color-graytext2;
        border-radius: 3px;
        background-color: $color-mainbackground;
        cursor: pointer;
      }
      input:checked ~ .checkbox {
        border-color: $color-main;
        background: url(@/assets/images/icon-check-w.svg) no-repeat center center;
        background-color: $color-main;
        background-size: 14px;
      }
      &.check {
        width: 3%;
      }
      &.name {
        width: 16.8%;
      }
      &.status {
        width: 8.5%;
      }
      &.group {
        width: 9.3%;
      }
    }
  }
  tbody {
    :deep(tr) {
      td:not(.empty) {
        border-top: 1px solid $color-grayborder;
      }
      cursor: pointer;
      transition: 0.35s ease;
      &:hover {
        background: $color-lightbackground;
      }
    }
    :deep(tr):first-child td {
      border-top: none;
    }
    :deep(tr):last-child td {
      border-bottom: 1px solid $color-grayborder;
    }
  }
  &.emptyList {
    width: 610px;
    margin: 0 auto;
    padding: 31px 24px 71px;
    border-top: 1px solid #F4F4F4;
    text-align: center;
    color: $color-graytext2;
    @include sp {
      width: auto;
      padding: 0;
    }
    h2 {
      margin: 0 0 24px;
      color: $color-accent;
      font-size: 18px;
      font-weight: 400;
      line-height: 120%;
      letter-spacing: 0.72px;
    }

    p {
      color: $color-blacktext2;
      font-size: 14px;
      line-height: 150%;
      letter-spacing: 0.28px;
    }
    .tutorialguest01 {
      width: 244px;
      margin: 33px 4px 33px 0;
    }
    .btn {
      max-width: 440px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}


.addGuestWrap {
  position: fixed;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  @include sp {
    bottom: 10px;
    transform: none;
    text-align: center;
    left: 50%;
    margin-left: -135px;
    max-width: 270px;
    transition: 0.35s ease;
    [data-scroll="top"] & {
      bottom: 70px;
    }
  }
}
.ModalAddGuest {
  :deep(.modalContainer .contents) {
    padding: 10px 0 0;
    overflow-y: scroll;
  }
}
@include sp {
.subTitle {
  padding: 21px 10px;
  h2 {
    font-size: 16px;
  }
}
.manageGuestList {
  top: 28px;
  right: 15px;
  transform: none;
}
.statusList {
  padding: 0;
  dl {
    justify-content: center;
    .total {
      justify-content: center;
      width: 100%;
      dt {
        &::before {
          width: 15px;
          height: 15px;
        }
      }
      dd {
        strong {
          font-size: 20px;  
        }
      }
    }
    & > .total ~ div {
      margin: 0 15px;
      padding-left: 16px;
      &::before {
        @include BA;
        left: 0;
        width: 13px;
        height: 15px;
      }
      &.attendance {
        margin-left: 15px;
      }
    }
    dd {
      strong {
        font-weight: 400;
        font-size: 16px;  
      }
    }
  }
  ul {
    padding: 0;
    li {
      // padding: 7px 0 7px 16px;
      strong {
        font-size: 12px;
        letter-spacing: 0.24px;
      }
      &.name {
        strong {
          font-size: 11px;
        }
      }
      &.attendance {
        margin-left: 11px;
      }
      &::before {
        width: 12px;
        height: 12px;
      }
    }
  }
}

.searchWrap {
  .manageSearch {
    .searchButton ,
    .manageAttribute button {
      font-size: 12px;
    }
  }
  .searchResult {
    display: none;
  }
}
.showList {
  overflow-x: auto;
  &.emptyList {
    margin: 0 15px;
    padding-top: 63px;
    text-align: center;
    font-size: 14px;
  }
  table {
    width: 100%;
    min-width: 600px;
    margin: 0;
  }
  :deep(tr) {
    &.header {
      th {
        &.name {
          padding-left: 0;
        }
      }
      // td {
      //   &.check {
      //     padding-left: 2px;
      //   }
      // }
    }
    td {
      &.check {
        width: 30px;
        padding-left: 2px;
      }
    }
    th ,
    td {
      &.name {
        width: 24.0%;
        padding-left: 0;
      }
      &.status {
        width: 13.3%;
      }
      &.group {
        width: 13.3%;
      }
      &.registration {
        width: 20%;
      }
    }
  }
}
}

.no-result {
  color: var(--Gray_dark, #9C9C9C);
  font-size: 18px;
  font-weight: 400;
  padding-top: 40px;
  padding-left: 10px;
  @include sp {
    text-align: center;
    padding: 60px 0;
    font-size: 14px;
  }
}
</style>

<style lang="scss">
.emptyList .controlWindowBtn button {
  width: 440px;
  @include sp {
    width: 100%;
  }
}

.searchItem {
  .input-btn input[value^="group_"] ~ span {
    &:before {
      content: " ";
      display: inline-block;
      background: url(@/assets/images/icon-group-gl.svg) center center no-repeat;
      background-size: contain;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      margin-top: -3px;
      margin-right: 2px;
    }
  }
}
</style>