<template>
<template v-for="(param, index) in itemData.param" :key="param">
<div class="items" v-for="(datail, index) in param.datail" :key="datail">

  <div class="datail">
    <p class="title" v-if="datail.title != ''">{{ publishedTitle(datail.title) }}</p>
    <small class="number" v-if="datail.number != ''">{{ publishedNumber(datail.number) }}</small>
    <ItemCarousel :data="datail" />
    <div class="labels" v-if="datail.labels">
      <span class="label" v-for="(label, index) in datail.labels" :key="label">{{ publishedLabel(label) }}</span>
    </div>
    <div class="wrapprice" v-if="datail.price != ''">
      <strong class="price">{{ publishedPrice(datail.price) }}</strong>
      <s class="strike" v-if="datail.priceStrike">{{ publishedStrike(datail.priceStrike) }}</s>
      <span class="tax">(税込)</span>
      <span class="unit" v-if="datail.priceUnit != ''">{{ publishedUnit(datail.priceUnit) }}</span>
    </div>
    <div class="notices" v-if="datail.notices">
      <ul>
        <li class="notice" v-for="(notice, index) in datail.notices" :key="notice">{{ publishedLabel(notice) }}</li>
      </ul>
    </div>
  </div>

  <div class="about" v-if="datail.about != ''">
    <strong class="acdtrg is-close" @click="useMethodsAccordion($event)">商品について</strong>
    <div class="acdsbj is-close">
      {{ publishedAbout(datail.about) }}
    </div>
  </div>
  <div class="setdetail" v-if="datail.setdetail != ''">
    <strong class="acdtrg is-close" @click="useMethodsAccordion($event)">セット内容</strong>
    <div class="acdsbj is-close">
      {{ publishedSetdetail(datail.setdetail) }}
    </div>
  </div>
</div>
</template>
</template>
  
<script lang="ts" setup>
import { useMethodsAccordion } from '@/assets/js/customFunction.js'

export interface Props {
  data: Array,
}

const params = withDefaults(defineProps<Props>(), {
});

const itemData = params.data;

const publishedNumber = computed(() => (data) => {
  return '商品番号 : ' + data;
});

const publishedTitle = computed(() => (data) => {
  return data;
});

const publishedLabel = computed(() => (data) => {
  return data;
});

const publishedPrice = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const publishedStrike = computed(() => (data) => {
  return '¥' + Number(data).toLocaleString();
});

const publishedUnit = computed(() => (data) => {
  return data;
});

const publishedCategory = computed(() => (data) => {
  return data;
});

const publishedAbout = computed(() => (data) => {
  return data;
});

const publishedSetdetail = computed(() => (data) => {
  return data;
});
</script>

<style lang="scss" scoped>
.items {
  width: 640px;
  margin: 0 auto;
  border-bottom: 1px solid $color-grayborder;
  .datail {
    padding: 0 21px;
    .title {
      font-size: 18px;
      line-height: 123%;
      letter-spacing: 0.04em;
      color: $color-blacktext2;
    }
    .number {
      display: block;
      margin-top: 9px;
      font-size: 14px;
      line-height: 120%;
      color: $color-graytext2;
    }
    .labels {
      margin-top: 15px;
      .label {
        display: inline-block;
        margin-right: 15px;
        padding: 4px 7px;
        background-color: $color-alert;
        font-size: 12px;
        line-height: 120%;
        letter-spacing: 0.02em;
        color: $color-whitetext;
      }
    }
    .wrapprice {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-end;
      margin-top: 8px;
      .price {
        font-size: 20px;
        color: $color-alert;
      }
      .strike {
        margin-left: 10px;
        font-weight: 700;
        font-size: 14px;
      }
      .tax {
        margin-bottom: 2px;
        margin-left: 3px;
        font-size: 10px;
      }
      .unit {
        display: block;
        letter-spacing: 0;
        margin-bottom: 2px;
        font-size: 10px;
        &::before {
          content: '／';
        }
      }
    }
    .notices {
      margin-top: 15px;
      padding: 17px 0 22px;
      border-top: 1px solid $color-grayborder;
      .notice {
        padding-left: 1em;
        text-indent: -1em;
        font-family: 'Lato';
        font-size: 12px;
        line-height: 16px;
        color: $color-blacktext2;
        &::before {
          content: '・';
        }
        & ~ .notice {
          margin-top: 16px;
        }
      }
    }
  }
  .about ,
  .setdetail {
    position: relative;
    border-top: 1px solid $color-grayborder;
  }
}

@include sp {
.items {
  width: 100%;
  .datail {
    padding: 0;
    .title {
      padding: 0 15px;
      font-size: 16px;
      line-height: 120%;
      letter-spacing: 0.02em;
    }
    .number {
      margin-top: 3px;
      padding: 0 15px;
      font-size: 12px;
      line-height: 120%;
      letter-spacing: 0.02em;
    }
    .labels {
      margin-top: 21px;
      padding: 0 16px;
      .label {
        padding: 3px 7px;
      }
    }
    .wrapprice {
      margin-top: 2px;
      padding: 0 17px;
      .strike {
        margin: 0 0 1px 7px;
      }
    }
    .notices {
      margin: 13px 15px;
      padding: 12px 0 9px;
    }
  }
}
}
</style>