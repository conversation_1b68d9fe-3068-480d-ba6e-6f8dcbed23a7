<template>
<div class="wrappayment" v-if="(paymentData.display[0] || paymentData.subtotal[0] || paymentData.calculation[0] || paymentData.discount[0] || paymentData.total[0] || paymentData.point[0])">
  <dl>

    <div class="displayAmount" v-if="paymentData.display[0]">
      <template v-for="(display, index) in paymentData.display" :key="display">
        <dt>{{ display.article }}</dt>
        <dd class="display" :class="publishedCalculationClass(display.amount)">{{ publishedCalculation(display.amount) }}</dd>
      </template>
    </div>

    <div class="subtotal" v-if="paymentData.subtotal[0]">
      <template v-for="(subtotal, index) in paymentData.subtotal" :key="subtotal">
        <dt>{{ subtotal.article }}</dt>
        <dd>{{ publishedCalculation(subtotal.amount) }}</dd>
      </template>
    </div>

    <div class="payment" v-if="paymentData.calculation[0]">
      <template v-for="(calculation, index) in paymentData.calculation" :key="calculation">
        <dt>{{ calculation.article }}</dt>
        <dd>{{ publishedCalculation(calculation.amount) }}</dd>
      </template>
    </div>

    <div class="discount" v-if="paymentData.discount[0]">
      <template v-for="(discount, index) in paymentData.discount" :key="discount">
        <dt>{{ discount.article }}</dt>
        <dd class="minus">{{ publishedCalculation(discount.amount) }}</dd>
      </template>
    </div>

    <div class="discountdetail" v-if="paymentData.discountdetail">
      <dt>内</dt>
      <dd></dd>
      <template v-for="(discountdetail, index) in paymentData.discountdetail" :key="discountdetail">
        <dt>{{ discountdetail.article }}</dt>
        <dd>{{ publishedCalculation(discountdetail.amount) }}</dd>
      </template>
    </div>

    <div class="totalAmount" v-if="paymentData.total[0]">
      <template v-for="(total, index) in paymentData.total" :key="total">
        <dt>{{ total.article }}</dt>
        <dd class="total" :class="publishedCalculationClass(total.amount)">{{ publishedCalculation(total.amount) }}</dd>
      </template>
    </div>
    <div class="point" v-if="paymentData.point[0]">
      <template v-for="(point, index) in paymentData.point" :key="point">
        <dt class="getpoint">{{ point.article }}</dt>
        <dd class="getpoint">{{ publishedPoint(point.amount) }}</dd>
      </template>
    </div>

  </dl>
</div>
</template>


<script lang="ts" setup>
const params = defineProps({
  data: Array,
});

const paymentData = params.data;

const publishedCalculationClass = computed(() => (data) => {
  if (typeof data === "number") {
    if (data < 0) {
      return 'minus';
    }
  }
});

const publishedCalculation = computed(() => (data) => {
  if (typeof data === "number") {
    return '¥ ' + Number(data).toLocaleString();
  }
  else {
    return data;
  }
});

const publishedPoint = computed(() => (data) => {
  return '+' + Math.trunc(data).toLocaleString() + 'pt';
});
</script>

<style lang="scss" scoped>
.wrappayment {
  width: 375px;
  padding: 16px 15px 16px 16px;
  background-color: $color-mainbackground;
  dl {
    font-size: 12px;
    line-height: 22px;
    letter-spacing: 0.1em;
    color: $color-blacktext3;
    & > div {
      display: flex;
      flex-wrap: wrap;
      position: relative;
      & ~ div {
        margin-top: 8px;
        padding-top: 13px;
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 1px;
          background: rgba(33, 33, 33, 0.08);
        }
      }
      dt {
        width: 65%;
        &.getpoint {
          margin-top: 4px;
        }
      }
      dd {
        width: 35%;
        text-align: right;
        line-height: 20px;
        text-align: right;
        font-size: 14px;
        font-weight: 500;
        color: $color-blacktext2;
        &.display {
          color: $color-graytext2;
        }
        &.minus {
          color: $color-alert2;
        }
        &.total {
          margin-top: 2px;
          font-size: 18px;
          line-height: 18px;
          letter-spacing: 0.07em;
        }
        &.getpoint {
          margin-top: 5px;
          color: $color-accent;
        }
      }
    }
  }
  &.toolbar {
    padding: 18px 13px 20px;
    .subtotal {
      padding: 0 10px 0px 11px;
    }
    dl > div.totalAmount {
      padding: 0 10px 0 11px;
    }
    dl > div ~ div.totalAmount {
      margin-top: 10px;
      padding-top: 11px;
    }
    dl > div.point {
      padding: 0 10px 0px 11px;
    }
  }
  .point {
    margin-top: 2px;
    padding-top: 0;
    &::before {
      display: none;
    }
  }
}

@include sp {
.wrappayment {

  width: 100%;
  &.toolbar {
    padding-bottom: 10px;
  }
  .flow {
    padding: 0 12px;
  }
}
}
</style>