<template>
  <Loading v-if="isLoading"></Loading>
  <div class="profile_input" v-if="mode == 'input'">
    <div
      v-for="(profile , index) in profiles"
      :key="index"
      class="profile"
    >
      <h2 v-if="index == 0" class="cmn-title">あなたのプロフィール</h2>
      <h2 v-else-if="index == 1" class="cmn-title">パートナーのプロフィール</h2>
      <h2 v-else class="cmn-title">ご家族のプロフィール {{ index - 1 }}</h2>
      <InputKanjiFullName 
          :required="true"
          :values="{last_name: profile?.last_name, first_name: profile?.first_name}"
          :errors="v$?.input"
          :isSubModal="true"
          @showSubModal="emits('showSubModal')" 
          @closeSubModal="emits('closeSubModal')" 
          @change="onUpdateData('last_name', index, $event.last_name); onUpdateData('first_name', index, $event.first_name)"
        />
      <div class="row">
        <InputText
          title="姓（ローマ字）"
          :required="true"
          :placeholder="(index == 1) ? 'Iwai' : 'Fukunaga'"
          size="half"
          :value="profile.last_name_romaji"
          :error="errorList?.[index]?.last_name_romaji?.$message"
          @input="onUpdateData('last_name_romaji', index, $event.target.value)"
        />
        <InputText
          title="名（ローマ字）"
          :required="true"
          :placeholder="(index == 1) ? 'Shunpei' : 'Konomi'"
          size="half"
          :value="profile.first_name_romaji"
          :error="errorList?.[index]?.first_name_romaji?.$message"
          @input="onUpdateData('first_name_romaji', index, $event.target.value)"
        />
      </div>
      <div class="row" v-if="index == 0 || index == 1">
        <InputRadio
          title="新郎 / 新婦"
          :required="true"
          :name="'profileType_' + index"
          :items="[
            {
              value: 'GROOM',
              label: '新郎',
            },
            {
              value: 'BRIDE',
              label: '新婦',
            }
          ]"
          :value="profile.type"
          :error="errorList?.[index]?.type?.$message"
          @change="onUpdateData('type', index, $event)"
        />
      </div>
      <div class="row" v-else>
        <InputRadio
          title="性別"
          :name="'profileType_' + index"
          :items="[
            {
              value: 'MALE',
              label: '男性',
            },
            {
              value: 'FEMALE',
              label: '女性',
            },
            {
              value: 'OTHER',
              label: 'その他',
            }
          ]"
          :value="profile.type"
          :error="errorList?.[index]?.type?.$message"
          @change="onUpdateData('type', index, $event)"
        />
      </div>
      <div class="row">
        <InputDate
          title="お誕生日"
          :value="profile.birth_date"
          :error="errorList?.[index]?.birth_date?.$message"
          :placeholder="(index == 1) ? '1995-07-30' : '1995-12-07'"
          @change="onUpdateData('birth_date', index, $event)"
        />
      </div>

      <div v-if="index >= 2" class="row cmn-alignright">
        <a class="delete" @click="onClickDeleteProfile(index)">家族プロフィール{{ index - 1 }}を削除する</a>
      </div>
      <div v-if="index == profiles.length - 1" class="button_wrap">
        <ButtonMainColor
          baseColor="reversal"
          class="icn-add"
          :buttonsize="200"
          @click="onClickAddProfile"
        >家族プロフィールを追加する</ButtonMainColor>
      </div>

    </div>
  </div>
  <div class="profile_confirm" v-if="mode == 'confirm'">
    <div
      v-for="(profile , index) in profiles"
      :key="index"
      class="profile"
    >
      <h2 v-if="index == 0" class="cmn-title">あなたのプロフィール</h2>
      <h2 v-else-if="index == 1" class="cmn-title">パートナーのプロフィール</h2>
      <h2 v-else class="cmn-title">ご家族のプロフィール {{ index - 1 }}</h2>
      <dl>
        <dt>お名前</dt>
        <dd>{{ profile?.last_name ? profile.last_name + ' ' : '' }} {{ profile.first_name }}</dd>
      </dl>
      <dl>
        <dt>お名前（ローマ字）</dt>
        <dd>{{ profile?.last_name_romaji ? profile.last_name_romaji + ' ' : '' }} {{ profile.first_name_romaji }}</dd>
      </dl>
      <dl>
        <dt v-if="index == 0 || index == 1">新郎 / 新婦</dt>
        <dt v-else>性別</dt>
        <dd>{{ onSetProfileType(profile.type) }}</dd>
      </dl>
      <dl>
        <dt>お誕生日</dt>
        <dd>{{ profile.birth_date ? $dayjs(profile.birth_date).format('YYYY/MM/DD'): '' }}</dd>
      </dl>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, maxLength, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { UpdateGuestListInput } from '@/composables/generated';
import type { GraphQLValidationErrors } from "@/utils/graphql";
const { showLoading, hideLoading } = useLoadingState();
const { $dayjs } = useNuxtApp() as any;

const props = withDefaults(defineProps<{
  mode?: 'input' | 'confirm';
}>(), {
  mode: 'input'
});

const emits = defineEmits<{
  (e: 'update'): void;
  (e: 'scrollTop'): void;
  (e: 'showSubModal'): void;
  (e: 'closeSubModal'): void;
}>()

const { member, refetch } = useGetOneMemberMe();

// データ
const profiles = ref([] as any[]);
const isLoading = ref(false);
const mode = ref(props.mode);

onMounted(async () => {
  const memberProfiles = JSON.parse(JSON.stringify(member?.value?.memberMe?.family_profiles));
  while (memberProfiles.length < 2) {
    memberProfiles.push({
      last_name: '',
      first_name: '',
      order: 0,
      last_name_kana: '',
      first_name_kana: '',
      last_name_romaji: '',
      first_name_romaji: '',
      birth_date: '',
      type: null,
    });
  }
  profiles.value = memberProfiles;
});

// API
const { update, errors } = useUpsertFamilyProfile();

// データの更新
const onUpdateData = async(id, index, updatedData) => {
  profiles.value[index][id] = updatedData;
};

// プロフィールタイプの表示
const onSetProfileType = (type) => {
  let typeText = '';
  switch (type) {
    case 'BRIDE':
      typeText = '新婦';
      break;
    case 'FEMALE':
      typeText = '女性';
      break;
    case 'GROOM':
      typeText = '新郎';
      break;
    case 'MALE':
      typeText = '男性';
      break;
    case 'OTHER':
      typeText = 'その他';
      break;
    default:
      break;
  }
  return typeText
};

// プロフィール追加ボタンのクリック
const onClickAddProfile = () => {
  profiles.value.push({})
};

// プロフィール削除ボタンのクリック
const onClickDeleteProfile = (index:number) => {
  profiles.value.splice(index, 1);
};

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return profiles.value.map((profile, index) => {
      let rules = {
        last_name: {
          required: helpers.withMessage(validationMessage.required('姓'), required),
          maxLength: helpers.withMessage(validationMessage.maxLength('姓', 255), maxLength(255)),
        },
        first_name: {
          required: helpers.withMessage(validationMessage.required('名'), required),
          maxLength: helpers.withMessage(validationMessage.maxLength('名', 255), maxLength(255)),
        },
        last_name_romaji: {
          required: helpers.withMessage(validationMessage.required('姓（ローマ字）'), required),
          maxLength: helpers.withMessage(validationMessage.maxLength('姓（ローマ字）', 255), maxLength(255)),
        },
        first_name_romaji: {
          required: helpers.withMessage(validationMessage.required('名（ローマ字）'), required),
          maxLength: helpers.withMessage(validationMessage.maxLength('名（ローマ字）', 255), maxLength(255)),
        },
        birth_date: {
          birth_date: helpers.withMessage(validationMessage.date('お誕生日'), validationDate)
        },
      } as any; 
      if (index == 0 || index == 1) {
        rules.type = {
          required: helpers.withMessage(validationMessage.required('新郎 / 新婦'), required)
        }
      }
      return rules;
    })
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, profiles, { $externalResults });

const errorList = computed(() => {
  if (!error.value) {
    return null
  }
  const result = {};

  error.value.forEach(item => {
    // $propertyPathをピリオドで分割してパスの各セグメントを取得
    const pathSegments = item.$propertyPath.split('.');
    // 現在の作業対象のオブジェクトを参照するための変数
    let current = result;

    // 最後のセグメントを除くすべてのセグメントに対して処理
    for (let i = 0; i < pathSegments.length - 1; i++) {
      const segment = pathSegments[i];
      // 次のレベルのネストが存在しなければ作成
      if (!current[segment]) {
        current[segment] = {};
      }
      // 参照を更新
      current = current[segment];
    }

    // 最後のセグメントを取得
    const lastSegment = pathSegments[pathSegments.length - 1];

    // 最後のセグメントに対応するオブジェクトを設定
    current[lastSegment] = {
      $property: item.$property,
      $validator: item.$validator,
      $uid: item.$uid,
      $message: item.$message,
      $params: item.$params,
      $response: item.$response,
      $pending: item.$pending
    };
  });

  return result ? result : {}
});

// プロフィールのアップデート処理
const onUpdateProfile = async(is_save: boolean) => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = v$.value?.$errors;
    emits('scrollTop')
    return false;
  }

  let profileData = [];
  if(profiles.value){
    profiles.value.forEach((profile, profileIndex) => {
      profileData.push({
        birth_date: profile.birth_date,
        first_name: profile.first_name,
        first_name_romaji: profile.first_name_romaji,
        last_name: profile.last_name,
        last_name_romaji: profile.last_name_romaji,
        order: profileIndex,
        type: profile.type
      })
    });
  }

  // isLoading.value = true;
  showLoading();
  const isSuccess = await update(is_save, profileData);
  hideLoading();
  // isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }
  return true;
};

// 「変更内容を確認」ボタンクリック
const onClickConfirm = async() => {
  let is_statud = await onUpdateProfile(false);
  if(is_statud){
    mode.value = 'confirm';
  }
};

// 「この内容で登録」ボタンクリック
const onClickSave = async() => {
  let is_statud = await onUpdateProfile(true);
  if(is_statud){
    emits('update');
  }
};

// 「戻る」ボタンクリック
const onClickBack = async() => {
  mode.value = 'input';
};

defineExpose({
  mode,
  onClickConfirm,
  onClickSave,
  onClickBack
});
</script>

<style lang="scss" scoped>
.input-error {
  text-align: center;
  margin-bottom: 20px;
}
.profile {
  margin-bottom: 50px;
  .cmn-title {
    margin-top: 0;
    font-size: 19px;
  }
}
.profile_input,
.profile_confirm{
  padding-bottom: 20px;
}
.profile_input {
  .profile {
    margin-bottom: 40px;
    &:last-child {
      margin-bottom: 0;
    }
    &:after {
      content: " ";
      display: block;
      margin-right: calc(50% - 50vw);
      margin-left: calc(50% - 50vw);
      padding-left: calc(50vw - 50%);
      padding-right: calc(50vw - 50%);
      height: 4px;
      background: #F4F4F4;
      margin-top: 40px;
    }
    &:last-child:after {
      display: none;
      margin-bottom: 0;
    }
  }
}
.row{
  margin-bottom: 24px;
}
dl{
  border-bottom: 1px solid #D9D9D9;
  padding: 10px 0;
  dt{
    color: #9C9C9C;
    font-size: 10px;
    line-height: 1.2;
    margin-bottom: 2px;
  }
  dd{
    color: #333;
    font-size: 16px;
    line-height: 1.5;
    min-height: 1.5em;
  }
}
.button_wrap{
  text-align: right;
  .icn-add{
    &::before{
      content: '';
      display: inline-block;
      width: 10px;
      height: 10px;
      margin-right: 4px;
      background-color: currentColor;
      mask-image: url('@/assets/images/icon-plus-gl.svg');
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: cover;
    }
  }
}
.delete{
  text-decoration: none;
  position: relative;
  padding: 0 0 0 22px;
  color: $color-alert;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  cursor: pointer;
  &::before {
    @include BA;
    left: 0;
    width: 18px;
    height: 18px;
    background-image: url('@/assets/images/icon-delete.svg');
  }
}
.btn{
  &.btn-default-outline{
    @include sp {
      &:hover{
        opacity: 1;
      }
      &:active{
        opacity: 0.6;
      }
    }
  }
  &.btn-secondary{
    @include sp {
      &:hover{
        opacity: 1;
      }
      &:active{
        opacity: 0.6;
      }
    }
  }
}
</style>