
<template>
<section class="progress">

    <div class="progress-graph">
      <ProgressGraph :params="props" />
      <div class="progress">
        <p>
          <strong>{{ props.title }}</strong>
          <span>{{ props.description }}</span>
        </p>
      </div>
    </div>

</section>
</template>

<script lang="ts" setup>
export interface Props {
  data: Array,
}

const params = withDefaults(defineProps<Props>(), {
});

const props = params.data;
</script>

<style lang="scss" scoped>
.progress-graph {
  display: flex;
  align-items: center;
  p {
    strong {
      display: block;
      line-height: 1.35;
      letter-spacing: 0.5px;
      font-size: 24px;
      font-weight: normal;
      color: #6f8b81;
    }
    span {
      display: inline-block;
      line-height: 1.3;
      letter-spacing: 0.3px;
      font-size: 16px;
    }
  }
}

@include sp {
.progress-graph {
  .wrap-graph {
    margin-right: 16px;
  }
  .progress {
    padding-bottom: 4px;
    p {
      strong {
        font-size: 16px;
        color: $color-blacktext2;
      }
      span {
        font-size: 12px;
      }
    }
  }
}
}
</style>