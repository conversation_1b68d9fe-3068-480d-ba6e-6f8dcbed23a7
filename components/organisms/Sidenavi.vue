<template>
<aside class="snav">
<section class="section-snav">
  <nav class="nav-links">
    <ul>
      <li><NuxtLink to="/" class="nav-item"><i class="icn-left material-symbols-outlined">home</i><span class="txt">ホーム</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
      <li><NuxtLink @click="clearUrlHistory()" to="/products/webinvitation" class="nav-item"><img class="icn-left" src="@/assets/images/icon-invitation.svg" style="width: 22px"><span class="txt">WEB招待状 デザイン一覧</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
      <!-- <li>
        <a @click="onClickNav(1)" class="nav-item" :class="{'nav-item-active': (navIds.indexOf(1) !== -1)}"><i class="icn-left material-symbols-outlined">mail</i><span class="txt">WEB招待状</span><i class="icn-right material-symbols-outlined">keyboard_arrow_down</i></a>
        <ul v-if="navIds.indexOf(1) !== -1">
          <li><NuxtLink to="/products/webinvitation" class="nav-item size--sm color-accent"><span class="txt">すべてのデザインを見る</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
          <li v-for="tag in tagGroup.tags.filter(tag => tag.id != 'all')" :key="tag.id">
            <NuxtLink :to="'/products/webinvitation?theme='+tag.id" class="nav-item nav-item-img"><i class="icn-left icn-img"><img :src="tag.image_url"></i><span class="txt">{{ tag.name }}</span></NuxtLink>
          </li>
        </ul>
      </li> -->
    </ul>
  </nav>
</section>
<section class="section-snav bg">
  <!-- <h2 class="nav-title">Favori CLOUD</h2>
  <div class="bnr">
    <NuxtLink to="/"><img src="@/assets/images/sidenavi-banner.png" alt="会費・ご祝儀"></NuxtLink>
  </div> -->
  <h2 class="cmn-title cmn-title-en">
    <span class="en">Items</span>
    <span class="ja">アイテムを探す</span>
  </h2>
  <nav class="nav-links">
    <ul>
      <li>
        <a @click="onClickNav('direction-item')" class="nav-item" :class="{'nav-item-active': (navIds.indexOf('direction-item') !== -1)}"><i class="icn-left"><img src="@/assets/images/icon-direction-item.svg"></i><span class="txt">ペーパーアイテム</span><i class="icn-right material-symbols-outlined">keyboard_arrow_down</i></a>
        <ul v-if="navIds.indexOf('direction-item') !== -1">
          <li><a target="_blank" href="https://www.favori-cloud.com/invitation_designs" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category01-01.png"></i><span class="txt">結婚式招待状</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-cloud.com/design" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category01-02.png"></i><span class="txt">席次表・席札・メニュー表</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-cloud.com/welcome_board_designs" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category01-03.png"></i><span class="txt">ウェルカムボード</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-cloud.com/marriage_postcard_designs" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category01-04.png"></i><span class="txt">結婚報告はがき</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
        </ul>
      </li>
      <li>
        <a @click="onClickNav('paper-item')" class="nav-item" :class="{'nav-item-active': (navIds.indexOf('paper-item') !== -1)}"><i class="icn-left"><img src="@/assets/images/icon-paper-item.svg"></i><span class="txt">演出アイテム</span><i class="icn-right material-symbols-outlined">keyboard_arrow_down</i></a>
        <ul v-if="navIds.indexOf('paper-item') !== -1">
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5411616" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-01.png"></i><span class="txt">ゲストカード</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5408450" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-02.png"></i><span class="txt">芳名帳</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5408451" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-03.png"></i><span class="txt">ポチ袋</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5408476" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-04.png"></i><span class="txt">プロフィールブック</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5408452" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-05.png"></i><span class="txt">受付サイン</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5408465" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-06.png"></i><span class="txt">イニシャルオブジェ</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5408466" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-07.png"></i><span class="txt">リングピロー</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5408468" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-08.png"></i><span class="txt">結婚証明書</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5408470" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-09.png"></i><span class="txt">花嫁の手紙</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/3015356" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-10.png"></i><span class="txt">両親贈呈品</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-diy.com/categories/5408492" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category02-11.png"></i><span class="txt">手作りキット</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
        </ul>
      </li>
      <li>
        <a @click="onClickNav('shipping')" class="nav-item" :class="{'nav-item-active': (navIds.indexOf('shipping') !== -1)}"><i class="icn-left material-symbols-outlined">local_shipping</i><span class="txt">引き出物宅配</span><i class="icn-right material-symbols-outlined">keyboard_arrow_down</i></a>
        <ul v-if="navIds.indexOf('shipping') !== -1">
          <li><a target="_blank" href="https://www.favori-cloud.com/gift_designs" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category03-01.png"></i><span class="txt">カタログギフト</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
          <li><a target="_blank" href="https://www.favori-cloud.com/gift_designs" class="nav-item nav-item-img"><i class="icn-left icn-img"><img src="@/assets/images/side/category03-02.png"></i><span class="txt">プロダクトギフト</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
        </ul>
      </li>
      <li><a target="_blank" href="https://favori-item.com/products/favori_giftcard" class="nav-item"><i class="icn-left"><img src="@/assets/images/icon-giftcard.svg"></i><span class="txt">引き出物カード</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
      <li><a target="_blank" href="https://www.favori-cloud.com/petit_gift_designs" class="nav-item"><i class="icn-left material-symbols-outlined">redeem</i><span class="txt">プチギフト</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
      <li><a target="_blank" href="https://www.favori-cloud.com/marriage_postcard_designs" class="nav-item"><i class="icn-left"><img src="@/assets/images/icon-postcard.svg"></i><span class="txt">結婚報告はがき</span><i class="icn-right material-symbols-outlined color-accent">open_in_new</i></a></li>
    </ul>
  </nav>
</section>
<section class="section-snav">
  <h2 class="cmn-title cmn-title-en">
    <span class="en">Support</span>
    <span class="ja">お客様サポート</span>
  </h2>
  <nav class="nav-links">
    <ul>
      <li><NuxtLink to="/support" class="nav-item"><i class="icn-left material-symbols-outlined">help</i><span class="txt">サポートトップ</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
      <li><NuxtLink to="/guide" class="nav-item size--sm"><span class="txt">ご利用ガイド</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
      <li><NuxtLink to="/question" class="nav-item size--sm"><span class="txt">よくある質問</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
      <li><NuxtLink to="/contact" class="nav-item size--sm"><span class="txt">お問い合わせ</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
    </ul>
  </nav>
</section>
<section class="section-snav">
  <h2 class="cmn-title cmn-title-en">
    <span class="en">About Us</span>
    <span class="ja">Favoriについて</span>
  </h2>
  <nav class="nav-links">
    <ul>
      <!-- <li><a href="https://www.favori-cloud.com/about" target="_blank" class="nav-item size--sm"><span class="txt">Favoriについて</span><i class="icn-right material-symbols-outlined">chevron_right</i></a></li> -->
      <li><NuxtLink to="/voice" class="nav-item size--sm"><span class="txt">お客様の声</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
      <li><NuxtLinkToSSR to="/sitemap" class="nav-item size--sm"><span class="txt">サイトマップ</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLinkToSSR></li>
    </ul>
  </nav>
</section>
</aside>
</template>

<script lang="ts" setup>
const navIds = ref([] as any);

const onClickNav = (index:any) => {
  if (navIds.value.indexOf(index) === -1) {
    navIds.value.push(index);
  } else {
    navIds.value = navIds.value.filter((item:any) => item !== index);
  }
};

// let { tagGroup } = useGetManyMSpecificationProducts({}, 6, 1, null);
</script>

<style lang="scss" scoped>
.cmn-title-en .en ,
.snav {
  font-family: 'Noto Serif JP', serif;
}
.nav-title {
  color: var(--x_navy, #243F5F);
  font-family: Lato;
  font-size: 20px;
  font-weight: 400;
  letter-spacing: 0.6px;
  margin: 0;
  padding: 0;
  margin-bottom: 16px;
  padding-left: 20px;
  padding-top: 24px;
}
.section-snav {
  padding-bottom: 32px;
  &.bg {
    border-top: 1px solid var(--Gray, #D9D9D9);
    border-bottom: 1px solid var(--Gray, #D9D9D9);
    background: #F8F8F8;
  }
  .cmn-title {
    padding-top: 16px;
    margin-bottom: 12px;
    padding-left: 20px;
  }
  .bnr {
    padding: 5px 20px;
  }
}
.nav-links {
  .nav-item {
    font-size: 16px;
    color: #333;
    text-decoration: none;
    position: relative;
    display: block;
    // min-height: 48px;
    line-height: 1.6;
    padding: 14px 20px;
    // background: #fff;
    transition: 0.35s ease;
    cursor: pointer;
    &:hover {
      background: rgba(#000, .05);
    }
    &.size--sm {
      font-size: 14px;
      font-family: 'Noto Sans JP', sans-serif;
    }
    .icn-left {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      font-weight: 300;
      color: #2F587C;
      line-height: 1;
      // font-size: 28px;
      ~ .txt {
        padding-left: 30px;
      }
    }
    .icn-right {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      transition: 0.35s ease;
    }
    &.nav-item-img {
      margin-top: 5%;
      margin-bottom: 5%;
      font-size: 14px;
      .icn-left {
        width: 50px;
        height: 50px;
      }
      .txt {
        padding-left: 60px;
      }
    }
    &.nav-item-active {
      .icn-right {
        transform: translateY(-50%) rotate(180deg);
      }
    }
  }
  li > ul {
    padding-left: 28px;
  }
}
</style>