
<template>
<nav class="nav-sp"><ul>
  <li><button class="item-menu" @click="showDropdownMenu()">メニュー</button></li>
  <li><NuxtLink @click="clearUrlHistory()" to="/products/webinvitation" :class="{'item-webinvitation': true, 'router-link-active': (router.currentRoute.value.path.match(/^\/products\/webinvitation/))}">デザイン一覧</NuxtLink></li>
  <li><NuxtLink @click="clearUrlHistory()" to="/guest-list" :class="{'item-guestlist': true, 'router-link-active': (router.currentRoute.value.path.match(/^\/guest-list/))}">ゲストリスト</NuxtLink></li>
  <li><a @click="showModalLogin()" :class="{'item-login': true}">ログイン</a></li>
</ul></nav>
</template>

<script lang="ts" setup>
const router = useRouter(); 
const { showDropdownMenu } = useMenuState();
const { showModalLogin } = useModalLoginState();
</script>