
<template>
<nav class="nav-sp"><ul>
  <li><button class="item-menu" @click="showDropdownMenu()">メニュー</button></li>
  <li v-if="! webInvitationsCnt"><NuxtLink @click="clearUrlHistory()" to="/products/webinvitation" :class="{'item-webinvitation': true, 'router-link-active': (router.currentRoute.value.path.match(/^\/mypage\/webinvitation/) || router.currentRoute.value.path.match(/^\/products\/webinvitation/))}">My招待状</NuxtLink></li>
  <li v-else><NuxtLink @click="clearUrlHistory()" to="/mypage/webinvitation" :class="{'item-webinvitation': true, 'router-link-active': (router.currentRoute.value.path.match(/^\/mypage\/webinvitation/) || router.currentRoute.value.path.match(/^\/products\/webinvitation/))}">My招待状</NuxtLink></li>
  <li><NuxtLink @click="clearUrlHistory()" to="/mypage/guest-list" :class="{'item-guestlist': true, 'router-link-active': (router.currentRoute.value.path.match(/^\/mypage\/guest-list/))}">ゲストリスト</NuxtLink></li>
  <li><NuxtLink @click="clearUrlHistory()" to="/mypage" :class="{'item-account': true, 'router-link-active': (router.currentRoute.value.path == '/mypage' || router.currentRoute.value.path.match(/^\/mypage\/member/)), 'is-account-alert-regist': member?.memberMe?.isAccountAlertRegist}">マイページ</NuxtLink></li>
</ul></nav>
</template>

<script lang="ts" setup>
const router = useRouter(); 
const { showDropdownMenu } = useMenuState();
const { webInvitationsCnt, member } = useGetOneMemberMe();
</script>