<template>
	<Modal class="modalBankEdit" @close="$emit('close')">
		<template #header>
			<span v-if="mode == 'input'">{{ title }}</span>
			<span v-else>{{ confTitle }}</span>
		</template>
		<template #main>
			<Loading v-if="isLoading"></Loading>
			<span v-if="error" class="input-error cmn-aligncenter mb-12">{{ error }}</span>
			<div class="form-input" v-if="mode == 'input'">
				<div class="row">
					<InputBank
						:required="true"
						:value="input.member_bank_account"
						@update="onChangeBank($event)"
					/>
				</div>
				<div class="row">
					<InputSelect
						title="口座種別"
						size="md"
						:options="selectAccountTypeEnum"
						:value="input.member_bank_account.account_type"
						:error="getValidationMessage(v$.member_bank_account.account_type)"
						@change="input.member_bank_account.account_type = $event.target.value"
					/>
				</div>
				<div class="row">
					<InputText
						title="口座番号(7桁の半角数字)"
						:required="true"
						placeholder="1234567"
						size="half"
						:value="String(input.member_bank_account.account_number ?? '')"
						:error="getValidationMessage(v$.member_bank_account.account_number)"
						@input="input.member_bank_account.account_number = $event.target.value"
					/>
					<div class="small-text">口座番号が7桁未満の方は 頭に半角数字の「0」を追加してください</div>
					<div v-if="YUCHO_BANK_CODE == input.member_bank_account.bank_code" class="small-text-yuutyo">
						<a href="https://www.jp-bank.japanpost.jp/kojin/sokin/furikomi/kouza/kj_sk_fm_kz_1.html" target="_blank" style="text-decoration: none; color: inherit;">
							<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" fill="currentColor" class="bi bi-search" viewBox="0 0 16 16">
								<path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001a1.007 1.007 0 0 0-.082.073l3.85 3.85a1 1 0 0 0 1.415-1.415l-3.85-3.85a1.007 1.007 0 0 0-.073-.082zm-5.442 1.105a5.5 5.5 0 1 1 0-11 5.5 5.5 0 0 1 0 11z"/>
							</svg>
							ゆうちょ銀行の口座番号を調べたい方はこちら
						</a>
					</div>
				</div>
				<div class="row">
					<InputText
						title="口座名義（全角カタカナもしくは全角大文字英字）"
						:required="true"
            placeholder="フクナガ コノミ"
						size="half"
						:value="String(input.member_bank_account.account_name)"
						:error="getValidationMessage(v$.member_bank_account.account_name)"
						@input="input.member_bank_account.account_name = $event.target.value"
					/>
				</div>
				<div class="row">
					<InputNumber
						title="電話番号"
						:required="true"
						placeholder="***********"
						size="half"
						:value="String(input.member_bank_account.phone)"
						:error="getValidationMessage(v$.member_bank_account.phone)"
						@update="input.member_bank_account.phone = $event"
					/>
				</div>
			</div>
			<div class="form-input" v-else>
				<ul class="list-info mb-40">
					<li><dl>
						<dt>銀行名 / 銀行コード</dt>
						<dd>{{ input.member_bank_account.bank_name }} / {{ input.member_bank_account.bank_code }}</dd>
					</dl></li>
					<li><dl>
						<dt>支店名 / 支店コード</dt>
						<dd>{{ input.member_bank_account.branch_name }} / {{ input.member_bank_account.branch_code }}</dd>
					</dl></li>
					<li><dl>
						<dt>口座種別</dt>
						<dd>{{ getLabelForAccountType(input.member_bank_account.account_type ) }}</dd>
					</dl></li>
					<li><dl>
						<dt>口座番号</dt>
						<dd>{{ input.member_bank_account.account_number }}</dd>
					</dl></li>
					<li><dl>
						<dt>口座名義</dt>
						<dd>{{ input.member_bank_account.account_name }}</dd>
					</dl></li>
					<li><dl>
						<dt>電話番号</dt>
						<dd>{{ input.member_bank_account.phone }}</dd>
					</dl></li>
				</ul>
				<div class="notice-box" v-if="mode == 'input'">
					<h2 class="notice-title">
					<span class="notice-icon">!</span>
					注意事項
					</h2>
					<div class="notice-content">
					<div class="notice-item">
						<span class="notice-mark">※</span>
						<p>集まった会費・ご祝儀は、「カ）テトテ」からこちらのページで登録された振込先口座へ、選択頂いた振込日にお振込みします。</p>
					</div>
					<div class="notice-item">
						<span class="notice-mark">※</span>
						<p>振込口座に誤りがあった場合、再申請からお振り込みまで1週間程度かかります。開催日に間に合わない可能性がございますので、あらかじめご了承ください。</p>
					</div>
					</div>
				</div>
			</div>
		</template>
		<template #footer>
			<footer class="modal-footer" v-if="mode == 'input'">
				<button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
				<button class="btn btn-secondary btn-block" @click="onClickConf()">{{ confBtnText }}</button>
			</footer>
			<footer class="modal-footer" v-else>
				<button class="btn btn-default-outline btn-block" @click="mode = 'input'">戻る</button>
				<button class="btn btn-secondary btn-block" @click="onClickSave()">{{ saveBtnText }}</button>
			</footer>
		</template>
	</Modal>
</template>
  
<style lang="scss" scoped>
.row {
	padding-bottom: 24px;
}
.form-input {
	padding-bottom: 20px;
}

.row {
	margin-bottom: 10px;
}
.small-text {
	font-size: 0.8rem;
	margin-top: 10px;
}
.small-text-yuutyo {
	font-size: 0.8rem;
	margin-top: 10px;
	color:#B18A3E;
}
.contener {
	margin: 20px;
}

.notice-box {
	padding: 0px 18px 15px;
	border: 1px solid #ff0000;
	border-radius: 5px;
	margin: 20px auto 20px;
	max-width: 804px;
  width: 100%;
}

.notice-title {
  color: #ff0000;
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 10px;
  display: flex;
  align-items: center;

	@include sp {
		font-size: 16px;
	}
}

.notice-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  font-size: 16px;
  border: 1px solid #ff0000;
  border-radius: 50%;
  margin-top: 2px;
  margin-right: 10px;
	@include sp {
		width: 18px;
		height: 18px;
		font-size: 14px;
	}
}

.notice-content {
  font-size: 14px;
  line-height: 1.5;
	@include sp {
		font-size: 12px;
	}
}

.notice-item {
  display: flex;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.notice-mark {
  flex-shrink: 0;
  margin-right: 5px;
}
</style>
  
<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers, minLength, maxLength, sameAs, numeric } from '@vuelidate/validators';

import { useCloned } from '@vueuse/core';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();

interface Props {
	member: Member;
}

const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
	close: []
	reload: []
}>()

//口座種別
const selectAccountTypeEnum = ref([
	{ value: AccountTypeEnum.Normal, label: '普通預金' },
	{ value: AccountTypeEnum.Current, label: '当座預金' },
	{ value: AccountTypeEnum.Savings, label: '貯蓄預金' },
]);

const input = ref({
	member_bank_account: {
		account_name: "",
		account_number: "",
		account_type: AccountTypeEnum.Normal,
		bank_code: "",
		bank_name: "",
		branch_code: "",
		branch_name: "",
		phone: "",
	} as UpdateMemberBankAccountInput,
});

const mode = ref('input' as string);
const isLoading = ref(true);
const title = ref("振込口座登録");
const confTitle = ref("登録内容の確認");
const confBtnText = ref("登録内容を確認");
const saveBtnText = ref("この内容で登録");


onMounted(async() => {
	if(props.member?.member_bank_account){
		if(props.member.member_bank_account.id) {
			input.value.member_bank_account.id = props.member.member_bank_account.id;
			title.value = "振込口座更新";
			confTitle.value = "更新内容の確認";
			confBtnText.value = "更新内容を確認";
			saveBtnText.value = "この内容で更新";
		};
		if(props.member.member_bank_account.account_name) input.value.member_bank_account.account_name = props.member.member_bank_account.account_name;
		if(props.member.member_bank_account.phone) input.value.member_bank_account.phone = props.member.member_bank_account.phone;
		if(props.member.member_bank_account.account_number) input.value.member_bank_account.account_number = props.member.member_bank_account.account_number;
		if(props.member.member_bank_account.account_type) input.value.member_bank_account.account_type = props.member.member_bank_account.account_type;
		if(props.member.member_bank_account.bank_code) input.value.member_bank_account.bank_code = props.member.member_bank_account.bank_code;
		if(props.member.member_bank_account.bank_name) input.value.member_bank_account.bank_name = props.member.member_bank_account.bank_name;
		if(props.member.member_bank_account.branch_code) input.value.member_bank_account.branch_code = props.member.member_bank_account.branch_code;
		if(props.member.member_bank_account.branch_name) input.value.member_bank_account.branch_name = props.member.member_bank_account.branch_name;
	}

	isLoading.value = false;
});

// 口座種別のラベルを取得
const getLabelForAccountType = (accountType: any) => {
	const accountTypeItem = selectAccountTypeEnum.value.find(
		item => item.value === accountType
	);
	return accountTypeItem ? accountTypeItem.label : '';
};

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

const rules = computed(() => {
  return {
    member_bank_account: {
			account_name: {
				required: helpers.withMessage(validationMessage.required('口座名義'), required),
				maxLength: helpers.withMessage(validationMessage.maxLength('口座名義', 30), maxLength(30)),
				regex: helpers.withMessage(validationMessage.accountName("口座名義"), helpers.regex(/^[ァ-ヴー０-９Ａ-Ｚ（）ー\s　]+$/u)),
			},
			account_number: {
				required: helpers.withMessage(validationMessage.required('口座番号'), required),
				numeric: helpers.withMessage(validationMessage.numeric('口座番号'), numeric),
				regex: helpers.withMessage(validationMessage.format("口座番号"), helpers.regex(/^\d{7}$/))
			},
			account_type: {
				required: helpers.withMessage(validationMessage.required('口座種別'), required),
			},
			bank_code: {
				required: helpers.withMessage(validationMessage.required('銀行コード'), required),
				numeric: helpers.withMessage(validationMessage.numeric('銀行コード'), numeric),
				minLength: helpers.withMessage(validationMessage.minLength('銀行コード', 4), minLength(4)),
				maxLength: helpers.withMessage(validationMessage.maxLength('銀行コード', 4), maxLength(4)),
			},
			bank_name: {
				required: helpers.withMessage(validationMessage.required('銀行名'), required),
				maxLength: helpers.withMessage(validationMessage.maxLength('銀行名', 50), maxLength(50)),
			},
			branch_code: {
				required: helpers.withMessage(validationMessage.required('支店コード'), required),
				numeric: helpers.withMessage(validationMessage.numeric('支店コード'), numeric),
				minLength: helpers.withMessage(validationMessage.minLength('支店コード', 3), minLength(3)),
				maxLength: helpers.withMessage(validationMessage.maxLength('支店コード', 3), maxLength(3)),
			},
			branch_name: {
				required: helpers.withMessage(validationMessage.required('支店名'), required),
				maxLength: helpers.withMessage(validationMessage.maxLength('支店名', 50), maxLength(50)),
			},
			phone: {
				required: helpers.withMessage(validationMessage.required('電話番号'), required),
				regex: helpers.withMessage(validationMessage.format("電話番号"), helpers.regex(/^0\d{9,10}$/)),
			},
    },
  };
});

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { create, errors: createErrors } = useCreateMemberbankAccount();
const { update, errors: updateErrors } = useUpdateMemberbankAccount();

// 保存ボタンクリック
const onClickConf = async() => {
	// 全体エラーをリセット
	error.value = '';
	// サーバサイドメッセージをリセット
	v$.value.$clearExternalResults();
	// バリデーション実行
	await v$.value.$validate();
	if (v$.value.$error) {
		error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
		return false;
	}

	mode.value = 'conf';
}

const onChangeBank = (data:{
		bank_name: string;
		bank_code: string;
		branch_code: string;
		branch_name: string;
	}) => {
		input.value.member_bank_account.bank_name = data?.bank_name;
		input.value.member_bank_account.bank_code = data?.bank_code;
		input.value.member_bank_account.branch_code = data?.branch_code;
		input.value.member_bank_account.branch_name = data?.branch_name;
}


// 保存ボタンクリック
const onClickSave = async() => {
	// 全体エラーをリセット
	error.value = '';
	// サーバサイドメッセージをリセット
	v$.value.$clearExternalResults();
	// バリデーション実行
	await v$.value.$validate();
	if (v$.value.$error) {
		error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
		mode.value = 'input';
		return false;
	}
	const message = ref("");

	isLoading.value = true;
	if (props.member?.member_bank_account) {
    const isSuccess = await update(input.value.member_bank_account);
    isLoading.value = false;
    // エラーの場合
    if (! isSuccess) {
      if (updateErrors.value) $externalResults.value = updateErrors.value;
      if (updateErrors.value?.v$?.[0]) error.value = updateErrors.value?.v$?.[0];
			isLoading.value = false;
			mode.value = 'input';
      return false;
    }
		message.value = "銀行口座を更新しました";
  } else {
    const isSuccess = await create(input.value.member_bank_account);
    isLoading.value = false;
    // エラーの場合
    if (! isSuccess) {
      if (createErrors.value) $externalResults.value = createErrors.value;
      if (createErrors.value?.v$?.[0]) error.value = createErrors.value?.v$?.[0];
			isLoading.value = false;
			mode.value = 'input';
      return false;
    }
		message.value = "銀行口座を登録しました";
  }

	isLoading.value = false;
	addToastMessage({ message: message });
	// モーダル閉じる
	emit('reload');
	emit('close');
};
</script>