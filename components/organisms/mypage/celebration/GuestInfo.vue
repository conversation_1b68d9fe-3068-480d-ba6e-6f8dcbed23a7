<template>
	<div class="box-guest">
		<div class="guest-main">
			<h3 class="name">{{ props.guest?.last_name }} {{ props.guest?.first_name }} <small>{{ props.guest?.guest_honor }}</small></h3>
			<div class="guest-amount">
				<div v-if="props.guest?.payment_method == 'BRING_ON_THE_DAY'">
					<p class="amount">当日持参</p>
				</div>
				<div v-else-if="props.guest?.payment_method == 'PAID'">
					<p class="amount">既に支払済</p>
				</div>
				<div v-else>
					<p class="amount">{{ setNumberFormat(props.guest?.amount) }} <small>円</small></p>
				</div>
			</div>
		</div>
		<div class="guest-sub">
			<div class="guest-attendance">
				<span v-if="props.guest?.is_attendance && props.guest?.attendance == GUEST_ATTENDANCE_MASTER.PRESENT" class="btn btn-attendance btn-attendance-present">出席</span>
				<span v-else-if="props.guest?.is_attendance && props.guest?.attendance == GUEST_ATTENDANCE_MASTER.ABSENT" class="btn btn-attendance btn-attendance-absent">欠席</span>
				<span v-else-if="props.guest?.is_attendance" class="btn btn-attendance btn-attendance-pending">保留</span>
			</div>
			<div class="guest-tags">
				<div class="guest-tag is-system" v-if="props.guest?.is_system_fee">システム料</div>
				<div class="guest-tag is-gift" v-if="props.guest?.is_gift_amount">お気持ちあり</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination } from 'swiper';
import { CelebrationFeeGuest } from '@/composables/generated';


const { $dayjs } : any = useNuxtApp();
const props = defineProps({
  guest: {
    type: Object as PropType<CelebrationFeeGuest>,
  }
});
</script>

<style lang="scss" scoped>
.box-guest {
  padding: 10px 0;
  & + .box-guest{
    border-top: 1px dotted #D9D9D9;
  }
  h3, p {
    margin: 0;
    padding: 0;
    line-height: 1.6;
  }
  .guest-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
  }
  .guest-sub {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .guest-attendance {
      width: 70px;
      flex-shrink: 0;
    }
  }
  .guest-tag {
    display: inline-block;
    font-size: 12px;
    line-height: 1.2;
    padding: 4px 8px;
    border: 1px solid currentColor;
    border-radius: 12px;
    margin-left: 8px;
    &.is-system{
      color: #9C9C9C;
    }
    &.is-gift{
      color: #E65C7A;
    }
  }
  .name {
    font-size: 18px;
    font-weight: normal;
    small {
      font-size: 12px;
    }
  }
  .date {
    color: #9C9C9C;
    font-size: 12px;
    line-height: 1.6;
  }
  .amount {
    font-size: 18px;
    font-weight: normal;
    text-align: right;
    small {
      font-size: 11px;
    }
  }
  .gift-amount {
    color: #9C9C9C;
    font-size: 12px;
    line-height: 1.6;
    text-align: right;
    .txt {
      color: #E65C7A;
      margin-right: .5em;
      .icn {
        font-size: 15px;
        vertical-align: middle;
      }
    }
  }
}
</style>