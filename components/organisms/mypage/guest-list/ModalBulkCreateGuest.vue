<template>
  <Modal class="modalBulkCreateGuest" @close="$emit('close')" :isShowSubModal="isShowSubModal">
    <template #header>
      <span v-if="mode == 'input'">一括で登録</span>
      <span v-else>登録内容確認</span>
    </template>
    <template #main>
      <Loading v-if="isLoading" :isShowIcon="true"></Loading>
      <span v-if="error" class="input-error cmn-aligncenter mb-12">{{ error }}</span>
      <div v-if="mode == 'input' || mode == 'inputError'">
        <div v-if="errors.length && mode == 'inputError'">
          <div class="alert mb-24">
            <h4 class="alert-heading"><i class="material-icons icn-left">error_outline</i> データ取り込みエラー</h4>
            <p>入力データの取り込み時にエラーが発生しました<br>
エクセルファイルに入力された内容をご確認いただき再度お試しください<br>
データを修正しても問題が解決しない場合はお手数ですがサポートからお問い合わせください</p>
          </div>
          <table class="tabel-default mb-31">
            <thead>
              <tr>
                <th>行数</th>
                <th>エラー内容</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in errors" :key="index">
                <td>{{ item.line }}行目</td>
                <td>{{ item.message }}</td>
              </tr>
            </tbody>
          </table>
          <h2 class="cmn-title">もう一度取り込む</h2>
        </div>
        <div v-else>
          <p class="mb-16">まずはエクセルファイルのフォーマットに沿ってゲストの情報を入力してください</p>
          <div class="mb-40">
            <a class="btn btn-primary-outline sp_btn-block" href="/template/import_guests.xlsx">フォーマットをダウンロードする</a>
          </div>
        </div>
        <p class="mb-16">情報を入力したエクセルからセルをコピーして下の入力欄にペーストしてください </p>
        <div class="pb-md">
          <InputTextarea
            title="ゲスト情報"
            size="full"
            placeholder="エクセルからコピーしたセルをここにペーストしてください "
            maxlength="65535"
            :value="input?.csv"
            :error="getValidationMessage(v$?.csv)"
            @change="input.csv = $event.target.value"
            @showSubModal="isShowSubModal = true"
            @closeSubModal="isShowSubModal = false"
          />
        </div>
      </div>
      <div v-else>
        <p class="mb-30">取り込まれたデータが正しいか確認してください<br>
問題がないことを確認して「ゲスト情報を取り込む」ボタンを押してください</p>
        <div class="inputCheckAll">
          <InputCheck
            :value="(isCheckedAll) ? ['1'] : []"
            :items="[{value: '1', label: '　全て選択'}]"
            @change="onClickCheckAll($event[0] == '1')"
          />
        </div>
        <ul class="inputGuests">
          <li class="guest" v-for="(guest, index) in guests" :key="index">
            <div class="guestCheck">
              <InputCheck
                :value="(guest.isChecked) ? ['1'] : []"
                :items="[{value: '1', label: ''}]"
                @change="($event[0] == '1') ? guest.isChecked = true : guest.isChecked = false"
              />
            </div>
            <div class="guestDetail">
              <div class="name">{{ guest.last_name }} {{ guest.first_name }} <small>{{ guest.guest_honor }}</small></div>
              <div class="address">
                <div>{{ guest.postal_code }}</div>
                <div>{{ guest.prefecture }}{{ guest.city }}{{ guest.address }} {{ guest.building }}</div>
                <div>{{ guest.phone }}</div>
              </div>
            </div>
          </li>
        </ul>
        <!-- <p class="size--sm pb-md">※電話番号が不明な場合は自動的にご注文時のご注文者情報にご入力いただいたお電話番号を入力いたします なお ゲスト様がご不在の場合や 住所不明等 配送の際に不備があった場合は配送業者より連絡が入る可能性がございますので予めご了承ください </p> -->
      </div>
    </template>
    <template #footer>
      <footer class="modal-footer" v-if="mode == 'input'">
        <button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
        <button class="btn btn-secondary btn-block" @click="onClickConf()">内容の確認へ</button>
      </footer>
      <footer class="modal-footer" v-else-if="mode == 'inputError'">
        <button class="btn btn-default-outline btn-block" @click="onClickBack()">戻る</button>
        <button class="btn btn-secondary btn-block" @click="onClickConf()">内容の確認へ</button>
      </footer>
      <footer v-else class="modal-footer modal-footer-block">
        <p class="message"><strong>{{ guests.filter(guest => guest.isChecked === true).length }}</strong>件 選択されています</p>
        <div class="btns" style="">
          <button class="btn btn-default-outline btn-block" @click="mode = 'input'">戻る</button>
          <button class="btn btn-secondary btn-block" @click="onClickSave()">ゲスト情報を取り込む</button>
        </div>
      </footer>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();

interface Props {
  guestListId: string;
  guestIds: string[];
}

const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
  close: []
  reload: []
}>()

const { $dayjs } : any = useNuxtApp();

const mode = ref('input')
const isShowSubModal = ref(false)

/**
 * 編集
 */
const input = ref({
  csv: null,
});

const guests = ref([] as any[]);

const isCheckedAll = ref(true);

const rules = computed(() => {
  return {
    csv: {
      required: helpers.withMessage(validationMessage.required('ゲスト情報'), required) 
    },
  };
});

// 全体エラー
const error = ref('')
const errors = ref([] as any)

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { create, errors:createErrors } = useBulkCreateGuest();

const { guestList, refetch:refetchGuestList } = useGetOneGuestList(String(props.guestListId));
const { guestGroups, refetch:refetchGuestGroups } = useGetManyGuestGroup(String(props.guestListId))
const { create:createGuestGroup } = useCreateGuestGroup();
const { guestTags, refetch:refetchGuestTags } = useGetManyGuestTag(String(props.guestListId))
const { create:createGuestTag } = useCreateGuestTag();


// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickConf = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  console.log(v$.value);
  // バリデーション実行
  await v$.value.$validate();
  v$.value.$reset();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }
  if (! input.value.csv) return false;

  const items = csvToArray(input.value.csv);
  guests.value = [];
  errors.value = [];
  for (let i = 0; i < items.length; i++) {
    // csvをパースする
    try {
      const item = items[i];
      // 空はスキップ
      if (item.join('') == '') continue;
      let guest = {} as any;
      guest.isChecked = true;
      guest.guest_list_id = props.guestListId;
      if (item?.[0] == GUEST_TYPE_MASTER.GROOM) {
        guest.guest_type = 'GROOM';
      } else if (item?.[0] == GUEST_TYPE_MASTER.BRIDE) {
        guest.guest_type = 'BRIDE';
      }
      guest.last_name = item?.[1];
      guest.first_name = item?.[2];
      guest.last_name_kana = item?.[3];
      guest.first_name_kana = item?.[4];
      guest.last_name_romaji = item?.[5];
      guest.first_name_romaji = item?.[6];
      guest.guest_honor = item?.[7];
      if (item?.[8] == GENDER_MASTER.ETC) {
          guest.gender = 'ETC';
      } else if (item?.[8] == GENDER_MASTER.MALE) {
          guest.gender = 'MALE';
      } else if (item?.[8] == GENDER_MASTER.FEMALE) {
          guest.gender = 'FEMALE';
      }
      guest.relationship_name = item?.[9];
      guest.relationship = item?.[10];
      guest.guest_title = item?.[11];
      guest.guest_group = item?.[12];
      guest.guest_tags = item?.[13].split(' ');
      guest.allergy = item?.[14];
      if (item?.[15]) {
        guest.birthdate = $dayjs(item?.[15]).format('YYYY-MM-DD');
      }
      guest.postal_code = item?.[16];
      guest.prefecture = item?.[17];
      guest.city = item?.[18];
      guest.address = item?.[19];
      guest.building = item?.[20];
      guest.phone = item?.[21];
      guest.email = item?.[22];
      guest.parent_guest_name = item?.[24].split(' ');
      guest.guest_free_item_values = [];
      for (let n = 0; n <= 4; n++) {
        if (item?.[25+(n*2)] || item?.[26+(n*2)]) {
          guest.guest_free_item_values.push({
            name: item?.[25+(n*2)],
            content: item?.[26+(n*2)]
          });
        }
      }
      if (item?.[35] == GUEST_INVITATION_DELIVERY_MASTER[1]) {
          guest.invitation_delivery = 1;
      }
      guests.value.push(guest);

      if (! guest.last_name) {
        errors.value.push({
          line: i+1,
          message: '姓は必ず入力してください'
        });
      }
      if (! guest.first_name) {
        errors.value.push({
          line: i+1,
          message: '名は必ず入力してください'
        });
      }
    } catch (error) {
      errors.value.push({
        line: i+1,
        message: '形式を確認してください '
      });
    }
  }

  if (! guests.value.length) {
      errors.value.push({
        line: 1,
        message: '形式を確認してください '
      });
  }

  if (! errors.value.length) {
    mode.value = 'conf';
  } else {
    mode.value = 'inputError';
  }
};

const onClickBack = () => {
  mode.value = 'input'; 
  input.value.csv = ''; 
  v$.value.$reset();
}

// 保存ボタンクリック
const onClickCheckAll = (isChecked = true) => {
  for (let i = 0; i < guests.value.length; i++) {
    guests.value[i].isChecked = isChecked;
  }
}

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;

  // let guests = [];
  let input = [] as BulkCreateGuestInput[];
  let inputChildren = [] as BulkCreateGuestInput[];
  for (let i = 0; i < guests.value.length; i++) {
    let guest = JSON.parse(JSON.stringify(guests.value[i]));
    if (guest.isChecked) {
      delete guest.isChecked;

      // guest_group
      if (guest.guest_group) {
        let guestGroup = guestGroups.value.find(guestGroup => guestGroup.name == guest.guest_group)
        if (guestGroup) {
          guest.guest_group_id = guestGroup.id;
        } else {
          await createGuestGroup({
            guest_list_id: props.guestListId,
            name: guest.guest_group
          });
          await refetchGuestGroups();
          guestGroup = guestGroups.value.find(guestGroup => guestGroup.name == guest.guest_group)
          if (guestGroup) guest.guest_group_id = guestGroup.id;
        }
      }
      delete guest.guest_group;

      guest.guest_tag_guests = [];
      for (let n = 0; n < guest.guest_tags.length; n++) {
        const tag = guest.guest_tags[n];
        if (! tag) continue;
        let guestTag = guestTags.value.find(guestTag => guestTag.tag == tag)
        if (guestTag) {
          guest.guest_tag_guests.push({guest_tag_id: guestTag.id});
        } else {
          await createGuestTag({
            guest_list_id: props.guestListId,
            tag: tag
          });
          await refetchGuestTags();
          guestTag = guestTags.value.find(guestTag => guestTag.tag == tag)
          if (guestTag) guest.guest_tag_guests.push({guest_tag_id: guestTag.id});
        }
      }
      delete guest.guest_tags;

      if (guest.parent_guest_name?.[0] && guest.parent_guest_name?.[1]) {
        // 連名ゲストあり
        inputChildren.push(guest);
      } else {
        // 連名ゲストなし
        input.push(guest);
        delete guest.parent_guest_name;
      }
    }
  }

  const isSuccess = await create(input);
  // エラーの場合
  if (! isSuccess) {
    if (createErrors.value) $externalResults.value = createErrors.value;
    if (createErrors.value?.v$?.[0]) error.value = createErrors.value?.v$?.[0];
    isLoading.value = false;
    return false;
  }

  // 子供ゲストのみ最後に設定
  if (inputChildren.length) {
    await refetchGuestList();
    for (let i = 0; i < inputChildren.length; i++) {
      let guest = inputChildren[i];
      if (guestList.value) {
        const parentGuest = guestList.value.guests.find(item => (item.last_name == guest.parent_guest_name[0] && item.first_name == guest.parent_guest_name[1]))
        if (parentGuest) guest.parent_guest_id = parentGuest.id;
      }
      delete guest.parent_guest_name;
      inputChildren[i] = guest;
    }
    const isSuccessChildren = await create(inputChildren);
    // エラーの場合
    if (! isSuccessChildren) {
      if (createErrors.value) $externalResults.value = createErrors.value;
      if (createErrors.value?.v$?.[0]) error.value = createErrors.value?.v$?.[0];
      isLoading.value = false;
      return false;
    }
  }


  // 一覧を再描画
  await emit('reload');
  isLoading.value = false;
  addToastMessage({message: '選択されたゲストを登録しました '});
  // モーダル閉じる
  emit('close');
};
</script>

<style lang="scss">
.inputCheckAll {
  padding-left: 20px;
  label > span {
    font-size: 12px;
  }
}
.inputGuests {
  border-top: 1px solid #D9D9D9;
  border-bottom: 1px solid #D9D9D9;
  margin-bottom: 12px;
  padding-left: 20px;
  .guest {
    font-size: 16px;
    line-height: 1.6;
    display: flex;
    align-items: flex-start;
    width: 100%;
    .guestCheck {
      padding-top: 10px;
      padding-right: 16px;
    }
    .guestDetail {
      width: 100%;
      padding-top: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(33, 33, 33, 0.08);
    }
    small ,
    .address {
      color: var(--00-on-surface-medium-emphasis, rgba(0, 0, 0, 0.60));
      font-size: 12px;
      font-weight: 400;
    }
    &:last-child {
      .guestDetail {
        border-bottom: none;
      }
    }
  }
}

.pb-md {
  padding-bottom: 20px;
}
</style>