<template>
  <Modal class="modalBulkUpdateGuest" @close="$emit('close')" :isShowSubModal="isShowSubModal">
    <template #header>ゲスト情報を一括で編集する</template>
    <template #main>
      <Loading v-if="isLoading" :isShowIcon="true"></Loading>
      <span v-if="error" class="input-error cmn-aligncenter mb-12">{{ error }}</span>
      <p class="mb-30">選択中のゲスト情報を一括で編集できます </p>
      <h3 class="cmn-title size--sm" v-if="events.length">出欠情報</h3>
      <div class="mb-5" v-for="event, i in events" :key="i">
        <InputAttendance
          :title="event.name"
          :value="input?.attendances?.[i]"
          :error="getValidationMessage(v$?.attendances?.[i])"
          :isShowNull="true"
          :isShowNoUpdate="true"
         @change="input.attendances[i] = $event"
        />
      </div>
      <div class="mb-10 mt-20">
        <h3 class="cmn-title size--sm">グループ</h3>
        <InputGuestGroup
          modalTitle="ゲスト情報を一括で編集する"
          size="full"
          :value="String(input?.guest_group_id)"
          :error="getValidationMessage(v$?.guest_group_id)"
          :guestListId="guestListId"
          @change="input.guest_group_id = $event.target.value"
          @showSubModal="isShowSubModal = true"
          @closeSubModal="isShowSubModal = false"
        />
      </div>
      <div class="pb-40">
        <h3 class="cmn-title size--sm">タグ</h3>
        <ul class="list-tags">
          <li v-for="(tag, i) in input.guest_tags" :key="tag?.tag_id">
            <a @click="onClickTag(i)" class="label-tag" :class="{'is-selected': tag?.selected}">{{ tag?.tag }} <i class="material-icons icn-right" v-if="tag?.selected">close</i> </a>
          </li>
        </ul>
        <form @submit.prevent="onSubmitAddTag()">
          <InputText
            title="新しくタグを追加"
            size="full"
            :value="addTag"
            placeholder="御車代あり など"
            :error="errorAddTag"
            @input="addTag = $event.target.value"
          />
          <p class="tag-explain">※入力後に タップまたはEnter で追加</p>
        </form>
      </div>
    </template>
    <template #footer>
      <footer class="modal-footer">
        <button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
        <button class="btn btn-secondary btn-block" @click="onClickSave()">完了</button>
      </footer>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();

interface Props {
  guestListId: string;
  guestIds: string[];
  events: any[];
  guests: any[];
}

const props = withDefaults(defineProps<Props>(), {
  events: () => { return []},
  guests: () => { return []},
});

const emit = defineEmits<{
  close: []
  reload: []
}>()

const isShowSubModal = ref(false)

/**
 * 編集
 */

const addTag = ref('');
const errorAddTag = ref('');

const input = ref({
  attendances: [],
  guest_group_id: 0,
  guest_tags: [] as GuestTagInput[]
});

onMounted(async () => {
  for (let i = 0; i < props.events.length; i++) {
    const event = props.events[i];
    input.value.attendances.push('');
  }

  await refetch();
  for (let i = 0; i < tags.value.length; i++) {
    const tag = tags.value[i];
    input.value.guest_tags.push({
      tag_id: tag.tag_id,
      tag: tag.tag,
      dirty: tag.dirty,
      selected: tag.selected
    });
  }
});

const rules = computed(() => {
  return {
    attendances: {},
    guest_group_id: {},
    guest_tags: {},
  };
});

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// APIから guestLists を読み込み
const { tags, refetch } = useGetManyUpdateGuestTags(props.guestListId, props.guestIds);

// 更新API
const { update, errors } = useBulkUpdateGuest();
const { update:updateGuestTag, errors:updateGuestTagErrors } = useBulkUpdateGuestTag();

// 更新中のLoading
const isLoading = ref(false);

const onSubmitAddTag = () => {
  errorAddTag.value = '';
  if (! addTag.value.trim()) {
    errorAddTag.value = 'タグ名は必須項目です';
    return false;
  }
  input.value.guest_tags.push({
    tag_id: null,
    tag: addTag.value.trim(),
    dirty: true,
    selected: true
  });
  addTag.value = '';
};

const onClickTag = (i:number) => {
  if (! input.value.guest_tags?.[i].tag_id) {
    input.value.guest_tags.splice(i, 1);
  } else {
    input.value.guest_tags[i].selected = !input.value.guest_tags[i].selected;
    input.value.guest_tags[i].dirty = !input.value.guest_tags[i].dirty;
  }
};

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  let guests = [];
  for (let i = 0; i < props.guestIds.length; i++) {
    let item = {
      id: props.guestIds[i]
    } as BulkUpdateGuestInput;
    if (input.value.guest_group_id) {
      item.guest_group_id = input.value.guest_group_id;
    }

    item.guest_event_answers = [];
    const guest = props.guests.find(guest => guest.id === props.guestIds[i]);
    for (let n = 0; n < props.events.length; n++) {
      const event = props.events[n];
      const attendance = input.value.attendances[n];
      let guest_event_attendance = guest?.guest_event_attendances.find((item:any) => item.name == event.name);
      if (attendance === '') continue;
      // 未回答データは id: null, attendance: null
      // if (attendance === null) guest_event_attendance = {};
      item.guest_event_answers.push({
        id: guest_event_attendance?.id,
        name: event.name,
        attendance: attendance
      });
    }
    guests.push(item);
  }

  isLoading.value = true;
  const isSuccess = await update(guests);
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    isLoading.value = false;
    return false;
  }

  const isSuccessTag = await updateGuestTag({
    guest_list_id: props.guestListId, 
    guest_ids: props.guestIds,
    tags: input.value.guest_tags
  });
  // エラーの場合
  if (! isSuccessTag) {
    if (updateGuestTagErrors.value) $externalResults.value = updateGuestTagErrors.value;
    if (updateGuestTagErrors.value?.v$?.[0]) error.value = updateGuestTagErrors.value?.v$?.[0];
    isLoading.value = false;
    return false;
  }

  // 一覧を再描画
  await emit('reload');
  isLoading.value = false;
  addToastMessage({message: '選択されたゲストを編集しました '});
  // モーダル閉じる
  emit('reset');
  emit('close');
};
</script>

<style lang="scss" scoped>
.tag-explain {
  display: inline-block;
  font-size: 12px;
  color: $color-alert;
  margin-top: 2px;
}
.input-error {
  text-align: center;
  margin-top: -10px;
  margin-bottom: 10px;
}
.list-tags {
  margin-bottom: 10px;
  li {
    display: inline-block;
    margin-bottom: 10px;
    margin-right: 5px;
  }
}
a.label-tag {
  border-color: #d9d9d9;
  color: $color-blacktext2;
  background: #fff;
  text-decoration: none;
  font-size: 14px;
  padding: 3px 10px;
  .icn-right {
    vertical-align: middle;
    font-size: 13px;
    line-height: 0;
    margin-left: 3px;
    margin-right: -5px;
    margin-top: -3px;
  }
  &.is-selected {
    border-color: $color-main;
    color: $color-main;
  }
}
</style>

<style lang="scss">
.inputRadio {
  .title {
    margin-bottom: 8px;
  }
  label {
    margin-right: 30px;
  }
}
</style>