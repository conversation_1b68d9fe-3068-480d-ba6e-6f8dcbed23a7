<template>
  <Modal class="modalBulkUpdateGuest" @close="$emit('close')" :isShowSubModal="isShowSubModal">
    <template #header>ゲスト情報を別のリストに複製する</template>
    <template #main>
      <Loading v-if="isLoading"></Loading>
      <span v-if="error" class="input-error cmn-aligncenter mb-12">{{ error }}</span>
      <p class="size--lg mb-30">登録済みのゲスト情報を複製して 別のリストに追加できます </p>
      <div class="mb-20">
        <InputSelect
          title="複製した情報の追加先"
          size="full"
          :options="options"
          :value="String(input?.guest_list_id)"
          :error="getValidationMessage(v$?.guest_list_id)"
          @change="input.guest_list_id = $event.target.value"
        />
      </div>
    </template>
    <template #footer>
      <ShowFooterBarFrow 
        :data="modalPrivateInformationFooterBarFrow"
        class="frowFooterStatic"
        @onClickBtn0="$emit('close')"
        @onClickBtn1="onClickSave()"
      />
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { QueryGuestListsOrderByColumn, SortOrder } from '@/composables/generated';
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();

interface Props {
  guestListId: string;
  guestIds: string[];
}

const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
  close: []
  reload: []
}>()

const isShowSubModal = ref(false)

// APIから guestTag を読み込み
const orderBy = ref({
  column: QueryGuestListsOrderByColumn.CreatedAt,
  order: SortOrder.Asc
} as QueryGuestListsOrderByOrderByClause);

const { guestLists } = useGetManyGuestList([{
  column: QueryGuestListsOrderByColumn.IsDefault,
  order: SortOrder.Desc
}, orderBy.value])
// 敬称リスト
const options = computed(() => {
  const options = guestLists.value.map(guestList => {
    return {value: guestList.id, label: guestList.name};
  });
  return [{value: '', label: 'ゲストリストを選択'}].concat(options).filter(option => option.value != String(props.guestListId));
});

const modalPrivateInformationFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "キャンセル",
          link: false,
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "完了",
          link: false,
        },
      ],
    },
  ],
}

/**
 * 編集
 */
const input = ref({
  guest_list_id: '',
});

const rules = computed(() => {
  return {
    guest_list_id: {
      required: helpers.withMessage(validationMessage.required('ゲストリスト'), required) 
    },
  };
});

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { update, errors } = useCopyGuest();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await update(props.guestIds, input.value.guest_list_id);
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    isLoading.value = false;
    return false;
  }

  isLoading.value = false;
  const selected = options.value.find(option => option.value == input.value.guest_list_id);
  addToastMessage({message: '「'+selected?.label+'」にゲストを複製しました '});
  // モーダル閉じる
  emit('close');
};
</script>