<template>
  <Modal class="modalLogout" size="sm" @close="$emit('close')">
    <template #header>
      ゲスト情報削除
    </template>
    <template #main>
      <Loading v-if="isLoading"></Loading>
      <p v-if="error" class="input-error">{{ error }}</p>
      <p>{{guestIds.length}} 件のゲスト情報を削除してもよろしいですか？ 一度削除したゲスト情報を元に戻すことはできません </p>
    </template>
    <template #footer>
      <a href="javascript:void(0);" @click="$emit('close')">キャンセル</a>
      <a href="javascript:void(0);" class="color-danger" @click="onClickDelete()">削除する</a>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();

const router = useRouter();

export interface Props {
  guestListId: string;
  guestIds: string[];
}

const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
  close: []
  reset: []
  reload: []
}>()

// 全体エラー
const error = ref('')

// 更新API
const { action: deleteAction, errors } = useDeleteGuest();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickDelete = async() => {
  // 全体エラーをリセット
  error.value = '';
  if (! props.guestIds) return false;

  isLoading.value = true;

  const isSuccess = await deleteAction({id: props.guestIds, is_delete_all: false});
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }
  
  isLoading.value = false;
  addToastMessage({message: props.guestIds.length+'件のゲスト情報を削除しました '});
  // モーダル閉じる
  emit('reset');
  emit('reload');
  emit('close');
};
</script>