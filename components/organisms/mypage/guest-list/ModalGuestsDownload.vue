<template>
  <Modal class="modalLogout" size="sm" @close="emits('close')">
    <template #header>
      ゲストリストをダウンロード
    </template>
    <template #main>
      <Loading v-if="isLoading" :isShowIcon="true" />
      「{{props.guestList.name}}」に登録されているゲスト情報をエクセル形式でダウンロードします
      <p v-if="error" class="input-error">{{ error }}</p>
    </template>
    <template #footer>
      <a href="#" @click="emits('close')">キャンセル</a>
      <a href="#" class="color-danger" @click="onClickDownload()">ダウンロード</a>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
interface Props {
  guestList: any;
}
const props = withDefaults(defineProps<Props>(), {});

const emits = defineEmits<{
  (e: 'close'): void;
}>()

const { $dayjs } : any = useNuxtApp();

// 更新中のLoading
const isLoading = ref(false);

// 全体エラー
const error = ref('')

const onClickDownload = async () => {
  const filename = props.guestList.name+'-'+$dayjs(new Date()).format('YYYYMMDD-HHmmss')+'.xlsx';
  isLoading.value = true;
  await downloadByApi('/guest/export', {
    guestListId: props.guestList.id,
  }, filename);
  isLoading.value = false;
  emits('close')
};

</script>