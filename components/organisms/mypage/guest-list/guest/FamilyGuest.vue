<template>
  <div class="boxFamilyGuest">
    <div class="subTitle">
      <!-- <button type="button" class="link-accent size--md mr-10"><i class="icn-left material-symbols-outlined">sorting</i></button> -->
      <h2>連名ゲスト{{index+1}}</h2>
      <span v-if="! parentGuest?.id">
        <span class="label-main ml-10">筆頭者</span>
        <button type="button" class="link-accent size--sm ml-10" @click="showChangeParentGuestModal = true"><i class="icn-left material-icons">cached</i> 変更</button>
      </span>
      <div class="manageGuestList editGuestList"><button data-modalwindow="controlWindow01" @click.prevent="showUpdateModalGuest = true">リスト操作</button></div>
    </div>
    <div class="profileImage acdtrg" :class="{'is-close': isOpenDetail}" @click="isOpenDetail = ! isOpenDetail">
      <div class="img">
        <ImageUUID v-if="guest?.image_url" :uuid="guest?.image_url" />
        <img v-else src="@/assets/images/profile-image.svg" />
      </div>
      <div class="name">
        <strong>{{ guest?.last_name }} {{ guest?.first_name }}</strong>
        <small>{{ guest?.guest_honor }}</small>
        <p>
          {{ guest?.last_name_romaji }} {{ guest?.first_name_romaji }}
          <span v-if="guest?.last_name_kana || guest?.first_name_kana"> / </span>
          {{ guest?.last_name_kana }} {{ guest?.first_name_kana }}
        </p>
      </div>
    </div>
    <div class="profileDetail acdsbj" :class="{'is-close': isOpenDetail}">
      <div class="mb-20">
        <GuestProfileBase
          :guest="guest"
        ></GuestProfileBase>
        <GuestProfileDetail
          :guest="guest"
        ></GuestProfileDetail>
        <GuestProfileMessage
          :guest="guest"
        ></GuestProfileMessage>
      </div>
    </div>
    <div class="cmn-alignright">
      <ControlWindowBtn v-if="! parentGuest?.id" position="bottom-right" mode="sort" style="display: inline-block;">
        <template #button>
          <button type="button" class="link-text size--md"><i class="icn-left material-icons-outlined">cancel</i> リストから削除</button>
        </template>
        <ul>
          <li @click="showDeleteOneModal = true">このゲストのみ削除する</li>
          <li @click="showDeleteModal = true">連名ゲストもまとめて削除する</li>
        </ul>
      </ControlWindowBtn>
      <button v-else type="button" class="link-text size--md" @click="showDeleteOneModal = true"><i class="icn-left material-icons-outlined">cancel</i> リストから削除</button>
      <button v-if="parentGuest?.id" type="button" class="link-accent size--md ml-20" @click="showSetParentGuestModal = true"><i class="icn-left material-icons-outlined">link_off</i> 連名から外す</button>
      <button v-else type="button" class="link-disabled size--md ml-20"><i class="icn-left material-icons-outlined">link_off</i> 連名から外す</button>
    </div>
  </div>
  <ModalUpdateGuest
    v-if="showUpdateModalGuest"
    :guestListId="guest.guest_list.id"
    :guest="guest"
    :parentGuestId="parentGuest?.id"
    @close="showUpdateModalGuest = null"
  />
  <ModalSetParentGuest
    v-if="showSetParentGuestModal"
    :parentGuest="parentGuest"
    :guest="guest"
    @close="showSetParentGuestModal = null"
    @reload="emits('reload')"
  />
  <ModalChangeParentGuest
    v-if="showChangeParentGuestModal"
    :guestListId="guest.guest_list.id"
    :parentGuest="guest"
    :childrenGuests="guest?.children_guests"
    @close="showChangeParentGuestModal = false"
    @reload="emits('reload')"
  />
  <ModalDeleteGuestOne
    v-if="showDeleteOneModal"
    :guestListId="guest.guest_list.id"
    :guest="guest"
    @close="showDeleteOneModal = false"
    :isReload="true"
    @reload="emits('reload')"
  />
  <ModalDeleteGuest
    v-if="showDeleteModal"
    :guestListId="guest.guest_list.id"
    :guest="guest"
    @close="showDeleteModal = false"
    :isReload="true"
    @reload="emits('reload')"
  />
</template>

<script lang="ts" setup>
import ModalSetParentGuest from '@/components/organisms/mypage/guest-list/guest/ModalSetParentGuest.vue'
import ModalChangeParentGuest from '@/components/organisms/mypage/guest-list/guest/ModalChangeParentGuest.vue'
import GuestProfileBase from '@/components/organisms/mypage/guest-list/guest/GuestProfileBase.vue'
import GuestProfileDetail from '@/components/organisms/mypage/guest-list/guest/GuestProfileDetail.vue'
import GuestProfileMessage from '@/components/organisms/mypage/guest-list/guest/GuestProfileMessage.vue'

export interface Props {
  parentGuest: Guest | any;
  guest: Guest | any;
  index?: number;
}

const props = withDefaults(defineProps<Props>(), {
  parentGuest: () => { return {}},
  guest: () => { return {}},
  index: 0
});

const emits = defineEmits<{
  (e: 'reload'): void;
}>();

const showUpdateModalGuest = ref(false)
const showSetParentGuestModal = ref(false)
const showChangeParentGuestModal = ref(false)
const showDeleteOneModal = ref(false)
const showDeleteModal = ref(false)

const isOpenDetail = ref(true)
</script>


<style lang="scss" scoped>
.guest-detail .wrapGuestList .detailWrap .boxFamilyGuest {
  margin-bottom: 12px;
  width: 560px;
  max-width: 100%;
  padding: 16px;
  border-radius: 4px;
  background: $color-mainbackground;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.20);
  .subTitle {
    margin: 0;
    padding: 8px 0 25px;
    h2 {
      color: var(--black, #333);
      line-height: 120%;
      font-size: 16px;
      letter-spacing: 0.32px;
      margin: 0;
      padding: 0;
      display: inline-block;
    }
    .manageGuestList.editGuestList {
      top: 35%;
      right: 6px;
    }
  }
  .profileImage {
    position: relative;
    width: 100%;
    margin-bottom: 15px;
    padding: 0 30px 0 0;
    &.acdtrg::after {
      top: 42.7%;
      right: 5px;
    }
    .name {
      strong {
        font-size: 18px;
      }
      small {
        font-size: 14px;
      }
      p {
        font-size: 12px;
      }
    }
  }
  .profileDetail {
    padding: 0;
  }
}
</style>