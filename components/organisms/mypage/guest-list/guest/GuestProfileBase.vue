<template>
  <ul class="list-info">
    <li><dl>
      <dt>肩書</dt>
      <dd>{{ guest?.guest_title }}</dd>
    </dl></li>
    <li v-for="guest_event_answer in guest.guest_event_answers.filter(item => item.attendance)" :key="guest_event_answer.id"><dl>
      <dt>出欠（{{ guest_event_answer.name }}）</dt>
      <dd>
        <span v-if="guest_event_answer.attendance == GUEST_ATTENDANCE_MASTER.PRESENT"><i class="icn-img icn-attendance-present"></i> {{ guest_event_answer.attendance }}</span>
        <span v-else-if="guest_event_answer.attendance == GUEST_ATTENDANCE_MASTER.ABSENT"><i class="icn-img icn-attendance-absent"></i> {{ guest_event_answer.attendance }}</span>
        <span v-else><i class="icn-img icn-attendance-pending"></i> {{ GUEST_ATTENDANCE_MASTER.PENDING }}</span>
      </dd>
    </dl></li>
    <li><dl>
      <dt>新郎側ゲスト / 新婦側ゲスト</dt>
      <dd>{{ GUEST_TYPE_MASTER?.[guest?.guest_type] }}</dd>
    </dl></li>
    <li><dl>
      <dt>性別</dt>
      <dd>{{ GENDER_MASTER?.[guest?.gender] }}</dd>
    </dl></li>
    <!-- <li><dl>
      <dt>関係性</dt>
      <dd>{{ guest?.relationship_name }}</dd>
    </dl></li> -->
    <li><dl>
      <dt>間柄</dt>
      <dd>{{ guest?.relationship }}</dd>
    </dl></li>
    <li><dl>
      <dt>お誕生日</dt>
      <dd><span v-if="guest?.birthdate">{{ $dayjs(guest.birthdate).format('YYYY/MM/DD') }}</span></dd>
    </dl></li>
    <li><dl>
      <dt>アレルギー等</dt>
      <dd>
        <div v-if="guest.allergies">{{ guest.allergies.join('、') }}</div>
        <div class="preWrap">{{ guest.allergy }}</div>
      </dd>
    </dl></li>
    <li><dl>
      <dt>グループ</dt>
      <dd>{{ guest.guest_group?.name }}</dd>
    </dl></li>
    <li><dl>
      <dt>タグ</dt>
      <dd>
        <span class="label-tag" v-for="guest_tag in guest.guest_tags" :key="guest_tag.id">
          {{ guest_tag.tag }}
        </span>
      </dd>
    </dl></li>
  </ul>
</template>

<script lang="ts" setup>
export interface Props {
  guest: Guest | any;
}

const props = withDefaults(defineProps<Props>(), {
  guest: () => { return {}}
});
</script>
