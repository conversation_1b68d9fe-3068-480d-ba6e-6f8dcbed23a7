<template>
  <ul class="list-info">
    <li><dl>
      <dt>郵便番号</dt>
      <dd>{{ formatZip(guest?.postal_code) }}</dd>
    </dl></li>
    <li><dl>
      <dt>ご住所</dt>
      <dd>
        {{ guest?.prefecture }}{{ guest?.city }}{{ guest?.address }}
        {{ guest?.building }}
    </dd>
    </dl></li>
    <li><dl>
      <dt>電話番号</dt>
      <dd>{{ guest?.phone }}</dd>
    </dl></li>
    <li><dl>
      <dt>メールアドレス</dt>
      <dd>{{ guest?.email }}</dd>
    </dl></li>
    <li><dl>
      <dt>招待状お渡し方法</dt>
      <dd>{{ GUEST_INVITATION_DELIVERY_MASTER?.[guest.invitation_delivery] }}</dd>
    </dl></li>
    <li v-for="guest_free_item_value in guest?.guest_free_item_values" :key="guest_free_item_value.id"><dl>
      <dt>{{ guest_free_item_value.name }}</dt>
      <dd>{{ guest_free_item_value.content }}</dd>
    </dl></li>
  </ul>
</template>

<script lang="ts" setup>
export interface Props {
  guest: Guest | any;
}

const props = withDefaults(defineProps<Props>(), {
  guest: () => { return {}}
});
</script>
