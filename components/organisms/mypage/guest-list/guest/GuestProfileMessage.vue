<template>
  <ul class="list-info">
    <li>
      <dl>
        <dt>メッセージ</dt>
        <dd>
          <div class="guestAnswer_box_row">
            <div class="guestAnswerPreview">
              <div class="guestAnswerPreview_image" v-if="guest.media_uuid">
                <template v-if="guest?.media_type == 'MEDIA_TYPE_MOVIE'">
                  <VideoUUID :uuid="guest?.media_uuid" />
                </template>
                <template v-else>
                  <ImageUUID :uuid="guest?.media_uuid" :type="(guest?.media_type == 'MEDIA_TYPE_MOVIE') ? 'video' : 'image'" />
                </template>
              </div>
              <div v-if="guest?.message" class="guestAnswerPreview_text preWrap">{{ guest?.message }}</div>
            </div>
          </div>
        </dd>
      </dl>
    </li>
  </ul>
</template>

<script lang="ts" setup>
export interface Props {
  guest: Guest | any;
}

const props = withDefaults(defineProps<Props>(), {
  guest: () => { return {}}
});
</script>

<style lang="scss" scoped>
.guestAnswerPreview{
  max-width: 343px;
  width: 100%;
  margin: 10px 0 20px;
  background: #FFF;
  box-shadow: 0 2px 20px rgba(0,0,0,0.14);
  border-radius: 14px;
  overflow: hidden;
  &_image{
    img{
      aspect-ratio: 1 / 1;
      width: 100%;
      object-fit: cover;
    }
  }
  &_text{
    font-size: 16px;
    line-height: 1.5;
    color: #333333;
    padding: 10px 14px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-all;
  }
  &_name{
    font-size: 16px;
    line-height: 1.5;
    color: #333333;
    text-align: right;
    padding: 6px 0 0;
  }
}
</style>
