<template>
  <Modal class="modalChangeParentGuest" @close="emits('close')">
    <template #header>
      筆頭者のゲストを変更する
    </template>
    <template #main>
      <Loading v-if="isLoading" />
      <span v-if="error" class="input-error cmn-aligncenter mb-12">{{ error }}</span>
      <p class="size--lg mb-20">どのゲストを連名の筆頭者にしますか？</p>
      <InputRadio
        name="parentGuestId"
        :value="input.parentGuestId"
        :items="options"
        :error="getValidationMessage(v$?.parentGuestId)"
        @change="input.parentGuestId = $event"
      />
      </template>
    <template #footer>
      <ShowFooterBarFrow 
        :data="modalPrivateInformationFooterBarFrow"
        class="frowFooterStatic"
        @onClickBtn0="emits('close')"
        @onClickBtn1="onClickSave()"
      />
    </template>
</Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
const router = useRouter();

export interface Props {
  guestListId: string;
  parentGuest: Guest;
  childrenGuests: Guest[];
}
const props = withDefaults(defineProps<Props>(), {});

const emits = defineEmits<{
  close: []
  reload: []
}>()

/**
 * 編集
 */
 const input = ref({
  parentGuestId: '',
});

const rules = computed(() => {
  return {
    parentGuestId: {
      required: helpers.withMessage(validationMessage.required('筆頭者のゲスト'), required) 
    },
  };
});

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 敬称リスト
const options = computed(() => {
  return props.childrenGuests.map(guest => {
    let label = guest.last_name;
    if (guest.first_name) label += ' ' + guest.first_name;
    if (guest.guest_honor) label += ' ' + guest.guest_honor;
    return {value: guest.id, label: label};
  });
});

// 更新API
// 連名者のゲスト情報の「筆頭者ID」を変更
const { update, errors } = useBulkUpdateGuest();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  let guests = [] as {
    id: string;
    parent_guest_id: number | null
  }[];
  guests.push({
    id: props.parentGuest.id,
    parent_guest_id: input.value.parentGuestId
  });
  for (let i = 0; i < props.childrenGuests.length; i++) {
    let guest = {
      id: props.childrenGuests[i].id,
      parent_guest_id: input.value.parentGuestId
    }
    if (guest.id == input.value.parentGuestId) guest.parent_guest_id = null;
    guests.push(guest);
  }
  const isSuccess = await update(guests);
  // エラーの場合
  if (! isSuccess) {
    isLoading.value = false;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }
  emits('reload');
  isLoading.value = false;
  // router.push({ path: `/mypage/guest-list/${props.guestListId}/${input.value.parentGuestId}`, 'hash': '#tabCompanion' })
  // モーダル閉じる
  emits('close');
};


const modalPrivateInformationFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "キャンセル",
          link: false,
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "完了",
          link: false,
        },
      ],
    },
  ],
}

</script>

<style lang="scss">
.modalChangeParentGuest .contentsInner {
  span label {
    display: block;
  }
}
</style>