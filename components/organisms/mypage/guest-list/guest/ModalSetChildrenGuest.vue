<template>
  <Modal class="modalSetChildrenGuest" :class="{ 'no-footer': checkedGuestIds.length == 0 }" @close="$emit('close')">
    <template #header>
      登録済みゲストから選ぶ
    </template>
    <template #main>
      <Loading v-if="isLoading" />
      <div v-else>
        <p>{{ parentGuest?.last_name }} {{ parentGuest?.first_name }} {{ parentGuest?.guest_honor }}の連名にするゲストを選んでください </p>
        <ShowGuestList 
          v-if="guestList?.guests.filter(guest => ! guest?.parent_guest?.id && guest?.id !== parentGuest?.id && ! guest?.children_guests.length).length"
          :isModal="true"
          :events="(guestList?.event_list) ? Object.values(guestList.event_list).map(name => ({name: name})) : []"
          :guestListId="guestListId"
          :guests="targetGuests()"
          :checkLimit="props.limit"
          @onChangeGuestCheck="checkedGuestIds = $event" 
          @onReloaded="async () => { await refetch() }"
        />
        <div v-else><p class="size--md">連名に追加できるゲスト情報がありません </p></div>
      </div>
    </template>
    <template #footer>
      <footer class="modal-footer modal-footer-block" v-if="checkedGuestIds.length">
        <p class="message"><strong>{{ checkedGuestIds.length }}</strong>件 選択されています</p>
        <div class="btns">
          <button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
          <button class="btn btn-secondary btn-block" @click="onClickSave()">連名に追加する</button>
        </div>
      </footer>
    </template>
</Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

export interface Props {
  guestListId: string;
  parentGuest: Guest;
  limit: number;
}
const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
  close: []
  reload: []
}>()

const {guestList, refetch, loading} = useGetOneGuestList(String(props.guestListId));
onMounted(async () => {
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
})

// チェックされたユーザID
const checkedGuestIds = ref([] as string[]);

// 全体エラー
const error = ref('')

// 更新API
// 連名者のゲスト情報の「筆頭者ID」を変更
const { update, errors } = useBulkUpdateGuest();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  isLoading.value = true;

  let guests = [];
  for (let i = 0; i < checkedGuestIds.value.length; i++) {
    let guest = {
      id: checkedGuestIds.value[i],
      parent_guest_id: props.parentGuest?.id
    }
    guests.push(guest);
  }

  const isSuccess = await update(guests);
  // エラーの場合
  if (! isSuccess) {
    isLoading.value = false;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }
  await emit('reload');
  isLoading.value = false;
  // モーダル閉じる
  emit('close');
};

// guestList.guests の形式を guests 単体から取得するときの形式に変更
const targetGuests = () => {
  const items = guestList.value?.guests.filter(guest => ! guest?.parent_guest?.id && guest?.id !== props.parentGuest?.id && ! guest?.children_guests.length);
  if (! items) return [];
  let results = [];
  for (let i = 0; i < items.length; i++) {
    let guest = JSON.parse(JSON.stringify(items[i]));
    guest.guest_event_attendances = items[i].guest_event_answers;
    guest.guest_group_name = items[i].guest_group?.name;
    guest.guest_event_tags = items[i].guest_tags;
    guest.web_invitation_id = items[i].web_invitation?.id;
    results.push(guest);
  }
  return results;
}
</script>

<style lang="scss">
.modalWrap.modalSetChildrenGuest.no-footer .modalContainer .contents {
  @include sp {
    height: calc(100dvh - 47px);
  }
}
.modalWrap.modalSetChildrenGuest .modalContainer .contents {
  padding: 25px 60px 0;
  @include sp {
    height: calc(100dvh - 131px);
    padding: 25px 20px 0;
    .searchWrap {
      padding-top: 20px;
    }
    .searchWrap ,
    .showList {
      margin-left: -20px;
      margin-right: -20px;
    }
  }
  .frowFooter.frowFooterFixed { display: none;}
  .addGuestWrap { display: none;}
  .manageSearch {
    padding-left: 0;
    padding-bottom: 20px;
    @include sp {
      padding-left: 10px;
    }
  }
}

</style>