<template>
  <Modal class="modalLogout" size="sm" @close="emits('close')">
    <template #header>
      ゲストを連名から外す
    </template>
    <template #main>
      <Loading v-if="isLoading" />
      {{ guest?.last_name }} {{ guest?.first_name }} {{ guest?.guest_honor }}を
      {{ parentGuest?.last_name }} {{ parentGuest?.first_name }} {{ parentGuest?.guest_honor }}の連名から外します
    </template>
    <template #footer>
      <a href="javascript:void(0);" @click="emits('close')">キャンセル</a>
      <a href="javascript:void(0);" class="color-danger" @click="onClickSave()">連名から外す</a>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

export interface Props {
  parentGuest: Guest;
  guest: Guest;
}

const props = withDefaults(defineProps<Props>(), {});

const emits = defineEmits<{
  (e: 'close'): void;
  (e: 'reload'): void;
}>()

// 全体エラー
const error = ref('')

// 更新API
// 連名者のゲスト情報の「筆頭者ID」を変更
const { update, errors } = useUpdateGuest();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';

  isLoading.value = true;
  let input = {
    id: props.guest.id,
    guest_list_id: props.guest.guest_list.id,
    parent_guest_id: null
  };
  const isSuccess = await update(input, [], []);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }

  emits('reload');
  emits('close');
  // router.push({ path: '/mypage/guest-list/'+props.guest.guest_list?.id+'/'+props.guest.id })
};
</script>