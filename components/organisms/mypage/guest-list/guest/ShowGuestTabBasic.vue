<template>
  <div class="update">
    <dl v-if="guest?.web_invite_reply_datetime">
      <dt>WEB招待状 返信 :</dt>
      <dd v-if="guest?.web_invite_reply_datetime">{{ $dayjs(guest.web_invite_reply_datetime).format('YYYY/MM/DD HH:mm') }}</dd>
    </dl>
    <dl>
      <dt>更新 :</dt>
      <dd v-if="guest?.updated_at">{{ $dayjs(guest.updated_at).format('YYYY/MM/DD HH:mm') }}</dd>
    </dl>
  </div>
  <div class="detailWrap">
    <div class="subTitle">
      <h2>基本情報</h2>
      <div class="btns">
        <a @click="showUpdateModal = true" class="link-accent mr-10"><i class="icn-left material-symbols-outlined">edit</i></a>
        <a @click="showCopyModal = true" class="link-accent"><i class="icn-left material-symbols-outlined">content_copy</i></a>
      </div>
    </div>
    <div class="profileImage">
      <div class="img">
        <ImageUUID v-if="guest?.image_url" :uuid="guest?.image_url" />
        <img v-else src="@/assets/images/profile-image.svg" />
      </div>
      <div class="name">
        <strong>{{ guest?.last_name }} {{ guest?.first_name }}</strong>
        <small>{{ guest?.guest_honor }}</small>
        <p>
          {{ guest?.last_name_romaji }} {{ guest?.first_name_romaji }}
          <span v-if="guest?.last_name_kana || guest?.first_name_kana"> / </span>
          {{ guest?.last_name_kana }} {{ guest?.first_name_kana }}
        </p>
      </div>
    </div>
    <div class="profileDetail">
      <GuestProfileBase
        :guest="guest"
      ></GuestProfileBase>
    </div>
  </div>
  <div class="detailWrap is-borderon">
    <div class="subTitle">
      <h2>詳細情報</h2>
      <div class="btns">
        <a @click="showUpdateModal = true" class="link-accent mr-10"><i class="icn-left material-symbols-outlined">edit</i></a>
      </div>
    </div>
    <div class="profileDetail">
      <GuestProfileDetail
        :guest="guest"
      ></GuestProfileDetail>
    </div>
  </div>
  <div class="detailWrap is-borderon" v-if="guest?.guest_survey_answers.length">
    <div class="subTitle">
      <h2>アンケート項目</h2>
    </div>
    <div class="profileDetail">
      <ul class="list-info">
        <li v-for="guest_survey_answer in guest?.guest_survey_answers" :key="guest_survey_answer.id"><dl>
          <dt>{{ guest_survey_answer.question }}</dt>
          <dd>{{ guest_survey_answer.answer_content }}</dd>
        </dl></li>
      </ul>
    </div>
  </div>
  <div class="detailWrap is-borderon">
    <div class="subTitle">
      <h2>お祝いメッセージ・写真・動画</h2>
    </div>
    <div class="profileDetail">
      <GuestProfileMessage
        :guest="guest"
      ></GuestProfileMessage>
    </div>
  </div>
  <div class="detailWrap is-borderon" v-if="guest.payment_method">
    <div class="subTitle">
      <h2>決済情報</h2>
    </div>
    <div class="profileDetailPayments">
      <ul class="list-info">
        <li><dl>
          <dt>事前決済</dt>
          <dd v-if="guest.payment_method === 'ADVANCE_PAYMENT'">利用</dd>
          <dd v-else>{{ PAYMENT_METHOD_MASTER?.[guest.payment_method] }}</dd>
        </dl></li>
      </ul>
      <div class="table-wrap" v-if="guest.payment_method === 'ADVANCE_PAYMENT'"><table>
        <tbody>
        <tr v-for="(guest_event_answer, index) in guest.guest_event_answers.filter(guest_event_answer => guest_event_answer.payment_amount > 0)" :key="index">
          <th>{{ guest_event_answer.name }}</th>
          <td>{{ setNumberFormat(guest_event_answer.payment_amount) }}円</td>
        </tr>
        <tr v-if="guest.gift_amount">
          <th>お気持ち</th>
          <td>{{ setNumberFormat(guest.gift_amount) }}円</td>
        </tr>
        <tr v-if="guest.system_fee">
          <th>システム利用料</th>
          <td>{{ setNumberFormat(guest.system_fee) }}円</td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
          <th>合計</th>
          <td>{{ setNumberFormat(guest.settlement_amount) }}円</td>
        </tr>
        </tfoot>
      </table></div>
    </div>
  </div>
  <div class="detailWrap">
    <div v-if="guest?.web_invitation?.name" class="cmn-alignright mt-16 mb-32">
      <p class="color-gray">回答したWEB招待状：{{ guest?.web_invitation?.name }}</p>
    </div>
    <div style="float:right">
      <ControlWindowBtn v-if="! guest?.parent_guest?.id && guest?.children_guests.length" position="bottom-right" mode="sort">
        <template #button>
          <button type="button" class="link-text size--md"><i class="icn-left material-icons-outlined">cancel</i> リストから削除</button>
        </template>
        <ul>
          <li @click="showDeleteOneModal = true">このゲストのみ削除する</li>
          <li @click="showDeleteModal = true">連名ゲストもまとめて削除する</li>
        </ul>
      </ControlWindowBtn>
      <button v-else type="button" class="link-text size--md" @click="showDeleteOneModal = true"><i class="icn-left material-icons-outlined">cancel</i> リストから削除</button>
    </div>
  </div>
  <ModalUpdateGuest
    v-if="showUpdateModal"
    :guestListId="guestListId"
    :guest="guest"
    :parentGuestId="guest?.parent_guest?.id"
    @close="showUpdateModal = false"
    @reload="emits('reload')"
  />
  <ModalCopyGuests
    v-if="showCopyModal"
    :guestListId="guestListId"
    :guestIds="[guest.id]"
    @close="showCopyModal = false"
  />
  <ModalDeleteGuestOne
    v-if="showDeleteOneModal"
    :guestListId="guestListId"
    :guest="guest"
    @close="showDeleteOneModal = false"
  />
  <ModalDeleteGuest
    v-if="showDeleteModal"
    :guestListId="guestListId"
    :guest="guest"
    @close="showDeleteModal = false"
  />
</template>

<script lang="ts" setup>
import ModalCopyGuests from '@/components/organisms/mypage/guest-list/ModalCopyGuests.vue'
import GuestProfileBase from '@/components/organisms/mypage/guest-list/guest/GuestProfileBase.vue'
import GuestProfileDetail from '@/components/organisms/mypage/guest-list/guest/GuestProfileDetail.vue'
import GuestProfileMessage from '@/components/organisms/mypage/guest-list/guest/GuestProfileMessage.vue'

export interface Props {
  guestListId: string;
  guest: Guest | any;
}

const props = withDefaults(defineProps<Props>(), {
  guestListId: '',
  guest: () => { return {}}
});

const emits = defineEmits<{
  (e: 'reload'): void;
}>();

const { $dayjs } : any = useNuxtApp()

// 編集モーダル表示フラグ
const showUpdateModal = ref(false)

// 編集モーダル表示フラグ
const showCopyModal = ref(false)

// 削除ボタンクリック
const showDeleteOneModal = ref(false)
const showDeleteModal = ref(false)
</script>

<style lang="scss" scoped>
.profileImage .img {
  width: 88px;
  overflow: hidden;
  border-radius: 50%;
}
.profileDetailPayments {
  .list-info li {
    border-bottom: none;
  }
  .table-wrap {
    border: 1px solid #D9D9D9;
    padding: 7px 12px;
    border-radius: 4px;
    max-width: 400px;
  }
  table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    th, td {
      padding: 5px 0;
      font-size: 14px;
      font-weight: normal;
    }
    td {
      text-align: right;
    }
    tbody tr:last-child {
      th, td {
        padding-bottom: 10px;
      }
    }
    tfoot {
      th, td {
        border-top: 1px solid #D9D9D9;
        padding-top: 10px;
        font-size: 18px;
      }
    }
  }
}
</style>