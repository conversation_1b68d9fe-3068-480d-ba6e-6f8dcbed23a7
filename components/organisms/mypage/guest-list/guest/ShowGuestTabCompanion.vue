<template>
  <Loading v-if="isLoading"></Loading>
  <div v-else>
    <div class="update">
      <dl v-if="guest?.web_invite_reply_datetime">
        <dt>WEB招待状 返信 :</dt>
        <dd v-if="guest?.web_invite_reply_datetime">{{ $dayjs(guest.web_invite_reply_datetime).format('YYYY/MM/DD HH:mm') }}</dd>
      </dl>
      <dl>
        <dt>更新 :</dt>
        <dd v-if="guest?.updated_at">{{ $dayjs(guest.updated_at).format('YYYY/MM/DD HH:mm') }}</dd>
      </dl>
    </div>
    <div class="guestTabCompanion detailWrap">
      <div v-if="(! parentGuest || parentGuest.id === guest.id) && ! parentGuest?.children_guests.length">
        <p class="size--sm mt-40">連名ゲストが設定されていません</p>
      </div>
      <div v-else class="profileImage mb-24">
        <div class="img">
          <ImageUUID v-if="guest?.image_url" :uuid="guest?.image_url" />
          <img v-else src="@/assets/images/profile-image.svg" />
        </div>
        <div class="name">
          <strong>{{ guest?.last_name }} {{ guest?.first_name }}</strong>
          <small>{{ guest?.guest_honor }}</small>
          <span v-if="! guest?.parent_guest?.id && guest?.children_guests.length">
            <span class="label-main ml-10">筆頭者</span>
            <button type="button" class="link-accent size--sm ml-10" @click="showChangeParentGuestModal = true"><i class="icn-left material-icons">cached</i> 変更</button>
          </span>
          <p>
            {{ guest?.last_name_romaji }} {{ guest?.first_name_romaji }}
            <span v-if="guest?.last_name_kana || guest?.first_name_kana"> / </span>
            {{ guest?.last_name_kana }} {{ guest?.first_name_kana }}
          </p>
        </div>
      </div>
      <FamilyGuest
        v-if="parentGuest && parentGuest.id !== guest.id"
        :guest="parentGuest"
        @reload="onClickReload"
      ></FamilyGuest>
      <div v-for="(childrenGuest, index) in parentGuest?.children_guests.filter(childrenGuest => childrenGuest.id !== guest.id)" :key="index">
        <FamilyGuest
          :index="(parentGuest && parentGuest.id !== guest.id) ? index+1 : index"
          :parentGuest="guest"
          :guest="childrenGuest"
          @reload="onClickReload"
        ></FamilyGuest>
      </div>
      <div class="companionControl" v-if="parentGuest?.children_guests.length < 10">
        <ControlWindowBtn position="bottom-right" mode="addbtn">
          <template #button>
            連名ゲストを追加
          </template>
          <ul>
            <li @click.prevent="showSetChildrenGuestModal = true">登録済みゲストから選ぶ</li>
            <li @click.prevent="showCreateGuestModal = true">新たに情報を入力する</li>
          </ul>
        </ControlWindowBtn>
      </div>
      <!-- <div class="companionControl cmn-alignright" style="display:block;" v-else>
        <button type="button" class="btn btn-primary-outline mb-10 btn-block" disabled="disabled"><i class="icn-left material-icons">add</i>連名ゲストを追加</button>
        <p class="size--sm color-gray">連名ゲストを追加できるのは10名までです</p>
      </div> -->
    </div>
    <hr>
    <div class="cmn-aligncenter mt-44 mb-20">
      <NuxtLink :to="'/mypage/guest-list/'+guest?.guest_list?.id" class="link-text"><i class="material-icons icn-left">arrow_back</i> ゲスト一覧へ戻る</NuxtLink>
    </div>
  </div>
  <ModalCreateGuest
    v-if="showCreateGuestModal"
    :guestListId="guest.guest_list.id"
    :parentGuest="parentGuest"
    @close="showCreateGuestModal = false"
    @reload="onClickReload"
  />
  <ModalSetChildrenGuest
    v-if="showSetChildrenGuestModal"
    :guestListId="guest.guest_list.id"
    :parentGuest="parentGuest"
    :limit="10 - parentGuest?.children_guests.length"
    @close="showSetChildrenGuestModal = false"
    @reload="onClickReload"
  />
  <ModalChangeParentGuest
    v-if="showChangeParentGuestModal"
    :guestListId="guest.guest_list.id"
    :parentGuest="parentGuest"
    :childrenGuests="parentGuest?.children_guests"
    @close="showChangeParentGuestModal = false"
    @reload="onClickReload"
  />
</template>

<script lang="ts" setup>
import { useMethodsAccordion } from '@/assets/js/customFunction.js'
import FamilyGuest from '@/components/organisms/mypage/guest-list/guest/FamilyGuest.vue'
import ModalSetChildrenGuest from '@/components/organisms/mypage/guest-list/guest/ModalSetChildrenGuest.vue'
import ModalChangeParentGuest from '@/components/organisms/mypage/guest-list/guest/ModalChangeParentGuest.vue'

import { useCloned } from '@vueuse/core';

const { $dayjs } : any = useNuxtApp()

export interface Props {
  guest: Guest | any;
}

const props = withDefaults(defineProps<Props>(), {
  guest: () => { return {}}
});

const emits = defineEmits<{
  (e: 'reload'): void;
}>();

const showCreateGuestModal = ref(false)
const showSetChildrenGuestModal = ref(false)
const showChangeParentGuestModal = ref(false)

const isLoading = ref(false);

// APIから guest を読み込み
const { guest: parentGuest, refetch } = useGetOneGuest(props?.guest.parent_guest?.id ? props?.guest.parent_guest?.id : props?.guest.id)

onMounted(async () => {
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
})
const onClickReload = async() => {
  isLoading.value = true;
  emits('reload');
  await refetch();
  isLoading.value = false;
};
</script>

<style lang="scss">
.wrapGuestList .guestTabCompanion .companionControl {
  .controlWindowWrap {
    width: 100%;
  }
  .controlWindowWrap[data-mode=addbtn] .controlWindowBtn button  {
    width: 100%;
    display: block;
  }
}
</style>