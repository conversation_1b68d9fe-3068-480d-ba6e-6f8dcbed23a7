<template>
  <div class="update">
    <dl>
      <dt>更新</dt>
      <dd>2022/09/01  15:00</dd>
    </dl>
  </div>
  <div class="detailWrap giftWrap">
    <div class="subTitle">
      <h2>引き出物</h2>
    </div>
    <div class="giftDetail">
      <ShowGiftDetails :data="apiListGift01" />
    </div>
  </div>
  <div class="detailWrap wrappingWrap">
    <div class="subTitle">
      <h2>ラッピング・BOX</h2>
    </div>
    <div class="giftDetail">
      <ShowGiftDetails :data="apiListGift02" />
    </div>
  </div>
  <div class="detailWrap cardWrap">
    <div class="subTitle">
      <h2>ご案内カード</h2>
    </div>
    <div class="giftDetail">
      <ShowGiftDetails :data="apiListGift03" />
    </div>
  </div>
</template>

<script lang="ts" setup>
export interface Props {
  guest: Guest | any;
}

const props = withDefaults(defineProps<Props>(), {
  guest: () => { return {}}
});

const apiListGift01 = {
  param: [
    {
      type: "listguestgift",
      datail: [
        {
          title: '【パターン E02-3-2】3品セット【Favori 限定】Dolce Duo PRIME CATALOG GIFT ボワール 送料無料',
          category: "メインギフト",
          number: "SD-1433-03",
          thumbnail: "/images/sample/thumbnail01.png",
          labels: [
            "数量限定キャンペーン",
          ],
          price: "4510",
          priceStrike: "6600",
          priceUnit: "",
          orderd: "",
        }, 
      ],
    },
    {
      type: "listsubgift",
      datail: [
        {
          title: '【パターン E02-3-2】ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
          category: "引き菓子",
          number: "SD-1433-03",
          thumbnail: "/images/sample/thumbnail02.png",
          labels: [
          ],
          price: "",
          priceStrike: "",
          priceUnit: "",
          orderd: "",
        }, 
        {
          title: '【パターン E02-3-2】ENERGY BOX ゲストにエナジーを',
          category: "縁起物",
          number : "SD-1433-03",
          thumbnail: "/images/sample/thumbnail03.png",
          labels: [
          ],
          price: "",
          priceStrike: "",
          priceUnit: "",
          orderd: "",
        }, 
      ],
    },
  ],
}

const apiListGift02 = {
  param: [
    {
      type: "listgiftwrapping",
      datail: [
        {
          title: 'ラッピング フルール',
          category: "ラッピング",
          number: "",
          thumbnail: "/images/sample/thumbnail02.png",
          labels: [
          ],
          price: "",
          priceStrike: "",
          priceUnit: "",
          orderd: "",
        },
        {
          title: 'フローラル',
          category: "BOXデザイン",
          number: "",
          thumbnail: "/images/sample/thumbnail02.png",
          labels: [
          ],
          price: "",
          priceStrike: "",
          priceUnit: "",
          orderd: "",
        },
      ],
    },
  ],
}

const apiListGift03 = {
  param: [
    {
      type: "listgiftwrapping",
      datail: [
        {
          title: 'コート ネイビー',
          category: "カードデザイン",
          number: "",
          thumbnail: "/images/sample/thumbnail02.png",
          labels: [
          ],
          price: "",
          priceStrike: "",
          priceUnit: "",
          orderd: "",
        },
      ],
    },
  ],
}
</script>
