<template>
  <Modal class="modalMemberEdit" @close="$emit('close')" :isShowSubModal="isShowSubModal">
    <template #header>
      <span v-if="mode == 'input'">会員情報の変更</span>
      <span v-else>変更内容の確認</span>
    </template>
    <template #main>
      <Loading v-if="isLoading"></Loading>
      <span v-if="error" class="input-error cmn-aligncenter mb-12">{{ error }}</span>
      <div class="form-input" v-if="mode == 'input'">
        <InputKanjiFullName 
          :required="true"
          :values="{last_name: input?.input?.last_name, first_name: input?.input?.first_name}"
          :errors="v$?.input"
          :isSubModal="true"
          @showSubModal="isShowSubModal = true" 
          @closeSubModal="isShowSubModal = false" 
          @change="input.input.last_name = $event.last_name; input.input.first_name = $event.first_name"
        />
        <div class="row">
          <InputHiragana
            title="セイ"
            placeholder="フクナガ"
            size="half"
            :value="input?.input?.last_name_kana"
            :error="v$?.input?.last_name_kana?.$errors?.[0]?.$message"
            @update="input.input.last_name_kana = $event"
            />
          <InputHiragana
            title="メイ"
            placeholder="コノミ"
            size="half"
            :value="input?.input?.first_name_kana"
            :error="v$?.input?.first_name_kana?.$errors?.[0]?.$message"
            @update="input.input.first_name_kana = $event"
            />
        </div>
        <div class="row">
          <InputAlphabet
            title="姓（ローマ字）"
            placeholder="Fukunaga"
            size="half"
            format="capitalize"
            :value="input?.input?.last_name_romaji"
            :error="v$?.input?.last_name_romaji?.$errors?.[0]?.$message"
            @update="input.input.last_name_romaji = $event"
            />
          <InputAlphabet
            title="名（ローマ字）"
            placeholder="Konomi"
            size="half"
            format="capitalize"
            :value="input?.input?.first_name_romaji"
            :error="v$?.input?.first_name_romaji?.$errors?.[0]?.$message"
            @update="input.input.first_name_romaji = $event"
            />
        </div>
        <div class="row">
          <InputCalendar
            title="挙式日"
            placeholder="2027/04/01"
            :value="String(input.wedding_info.wedding_date)"
            :error="v$?.wedding_info?.wedding_date?.$errors?.[0]?.$message"
            @change="input.wedding_info.wedding_date = $event"
          />
        </div>
        <div class="row">
          <InputText
            title="挙式会場名"
            placeholder="●●●●●●●ホテル"
            size="block"
            :value="String(input.wedding_info?.wedding_venue)"
            :error="v$?.wedding_info?.wedding_venue?.$errors?.[0]?.$message"
            @input="input.wedding_info.wedding_venue = $event.target.value"
          />
        </div>
        <div class="row">
          <InputSelect
            title="招待人数（目安）"
            size="block"
            :value="input.wedding_info.guest_count ? String(input.wedding_info.guest_count) : ''"
            :options="GUEST_COUNT_OPTIONS"
            :error="v$?.wedding_info?.guest_count?.$errors?.[0]?.$message"
            @input="input.wedding_info.guest_count = parseInt($event.target.value)"
          />
        </div>
        <!-- <div class="row">
          <InputEmail
            :required="true"
            title="メールアドレス"
            size="block"
            :placeholder="'<EMAIL>'"
            :value="input.input.email"
            :error="v$?.input?.email?.$errors?.[0]?.$message"
            @update="input.input.email = $event"
          />
        </div> -->
        <div class="row">
          <InputDate
            title="お誕生日"
            placeholder="1996-7-30"
            :value="String(input.input.birthdate)"
            :error="v$?.input?.birthdate?.$errors?.[0]?.$message"
            @change="input.input.birthdate = $event"
          />
        </div>
      </div>
      <div class="form-input" v-else>
        <ul class="list-info mb-40">
          <li><dl>
            <dt>お名前</dt>
            <dd>{{ input.input.last_name }} {{ input.input.first_name }}</dd>
          </dl></li>
          <li><dl>
            <dt>お名前（カナ）</dt>
            <dd>{{ input.input.last_name_kana }} {{ input.input.first_name_kana }}</dd>
          </dl></li>
          <li><dl>
            <dt>お名前（ローマ字）</dt>
            <dd>{{ input.input.last_name_romaji }} {{ input.input.first_name_romaji }}</dd>
          </dl></li>
          <li><dl>
            <dt>挙式日</dt>
            <dd><span v-if="input.wedding_info?.wedding_date">{{ $dayjs(input.wedding_info?.wedding_date).format('YYYY/MM/DD') }}</span></dd>
          </dl></li>
          <li><dl>
            <dt>挙式会場名</dt>
            <dd>{{ input.wedding_info?.wedding_venue }}</dd>
          </dl></li>
          <li><dl>
            <dt>招待人数（目安）</dt>
            <dd>{{ GUEST_COUNT_OPTIONS.find(item => item.value == input.wedding_info?.guest_count)?.label }}</dd>
          </dl></li>
          <!-- <li><dl>
            <dt>メールアドレス</dt>
            <dd>{{ input.input.email }}</dd>
          </dl></li> -->
          <li><dl>
            <dt>お誕生日</dt>
            <dd><span v-if="input.input.birthdate">{{ $dayjs(input.input.birthdate).format('YYYY/MM/DD') }}</span></dd>
          </dl></li>
        </ul>
      </div>
    </template>
    <template #footer>
      <footer class="modal-footer" v-if="mode == 'input'">
        <button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
        <button class="btn btn-secondary btn-block" @click="onClickConf()">変更内容を確認</button>
      </footer>
      <footer class="modal-footer" v-else>
        <button class="btn btn-default-outline btn-block" @click="mode = 'input'">戻る</button>
        <button class="btn btn-secondary btn-block" @click="onClickSave()">この内容で変更</button>
      </footer>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
.row {
  padding-bottom: 24px;
}
.form-input {
  padding-bottom: 20px;
}
</style>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, email, maxLength, helpers } from '@vuelidate/validators';
import { useCloned } from '@vueuse/core';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();

interface Props {
  member: Member;
}

const props = withDefaults(defineProps<Props>(), {});

const emit = defineEmits<{
  close: []
  reload: []
}>()

const input = ref({
  input: {
    first_name: '',
    last_name: '',
    first_name_kana: '',
    last_name_kana: '',
    first_name_romaji: '',
    last_name_romaji: '',
    // email: '',
    birthdate: '',
  } as UpdateMemberInput,
  wedding_info: {
    reception_date: '',
    wedding_date: '',
    wedding_venue: '',
    guest_count: null,
  } as UpdateWeddingInfoInput
});

const mode = ref('input' as string);

const isLoading = ref(true);

const isShowSubModal = ref(false)

onMounted(async() => {
  input.value.input.id = props.member.id;
  if (props.member.first_name) input.value.input.first_name = props.member.first_name;
  if (props.member.last_name) input.value.input.last_name = props.member.last_name;
  if (props.member.first_name_kana) input.value.input.first_name_kana = props.member.first_name_kana;
  if (props.member.last_name_kana) input.value.input.last_name_kana = props.member.last_name_kana;
  if (props.member.first_name_romaji) input.value.input.first_name_romaji = props.member.first_name_romaji;
  if (props.member.last_name_romaji) input.value.input.last_name_romaji = props.member.last_name_romaji;
  // if (props.member.email) input.value.input.email = String(props.member.email);
  if (props.member.birthdate) input.value.input.birthdate = props.member.birthdate;
  if (props.member.wedding_info?.id) input.value.wedding_info.id = props.member.wedding_info?.id;
  if (props.member.wedding_info?.reception_date) input.value.wedding_info.reception_date = props.member.wedding_info?.reception_date;
  if (props.member.wedding_info?.wedding_date) input.value.wedding_info.wedding_date = props.member.wedding_info?.wedding_date;
  if (props.member.wedding_info?.wedding_venue) input.value.wedding_info.wedding_venue = props.member.wedding_info?.wedding_venue;
  if (props.member.wedding_info?.guest_count) input.value.wedding_info.guest_count = props.member.wedding_info?.guest_count;
  isLoading.value = false;
});

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

const rules = computed(() => {
  return {
    input: {
      // email: { 
      //   required: helpers.withMessage(validationMessage.required('メールアドレス'), required), 
      //   email: helpers.withMessage(validationMessage.required('メールアドレス'), email), 
      // },
      last_name: { 
        required: helpers.withMessage(validationMessage.required('姓'), required),
      },
      first_name: { 
        required: helpers.withMessage(validationMessage.required('名'), required),
      },
      last_name_kana: { 
        // required: helpers.withMessage(validationMessage.required('セイ'), required),
        maxLength: helpers.withMessage(validationMessage.maxLength('セイ', 255), maxLength(255)),
      },
      first_name_kana: { 
        // required: helpers.withMessage(validationMessage.required('メイ'), required),
        maxLength: helpers.withMessage(validationMessage.maxLength('メイ', 255), maxLength(255)),
      },
      last_name_romaji: { 
        // required: helpers.withMessage(validationMessage.required('姓（ローマ字）'), required),
        maxLength: helpers.withMessage(validationMessage.maxLength('姓（ローマ字）', 255), maxLength(255)),
      },
      first_name_romaji: { 
        // required: helpers.withMessage(validationMessage.required('名（ローマ字）'), required),
        maxLength: helpers.withMessage(validationMessage.maxLength('名（ローマ字）', 255), maxLength(255)),
      },
    },
    wedding_info: {
      wedding_date: {},
      wedding_venue: {
        maxLength: helpers.withMessage(validationMessage.maxLength('挙式会場名', 255), maxLength(255)),
      },
      guest_count: {},
    },
  }
});

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { update, errors } = useUpdateMember();

// 保存ボタンクリック
const onClickConf = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  mode.value = 'conf';
}


// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    mode.value = 'input';
    return false;
  }

  isLoading.value = true;
  const isSuccess = await update(input.value.input, input.value.wedding_info);
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0]?.input;
    isLoading.value = false;
    mode.value = 'input';
    return false;
  }

  isLoading.value = false;
  addToastMessage({message: '会員情報を変更しました '});
  // モーダル閉じる
  emit('reload');
  emit('close');
};
</script>