<template>
  <Modal class="modalMemberEdit" size="sm" @close="$emit('close')">
    <template #header>
      退会手続き
    </template>
    <template #main>
      <Loading v-if="isLoading" />
      退会処理を実行します <br>
      よろしいですか？
      <p v-if="error" class="input-error">{{ error }}</p>
    </template>
    <template #footer>
      <a href="#" @click="$emit('close')">キャンセル</a>
      <a href="#" class="color-danger" @click="onClickSave()">退会する</a>
    </template>
  </Modal>
</template>

<style lang="scss" scoped>
.row {
  padding-bottom: 24px;
}
.form-input {
  padding-bottom: 20px;
}
</style>

<script lang="ts" setup>
import { ref } from 'vue';

const router = useRouter();

const emit = defineEmits<{
  close: []
}>()

// 更新API
const { action, errors } = useDeleteMember();
const { logout } = useLogoutMember();


// 更新中のLoading
const isLoading = ref(false);

// 編集モーダル非表示
const onClickSave = async() => {
  isLoading.value = true;
  const isSuccess = await action();
  if (! isSuccess) {
    return false;
  }

  // ログアウトする
  await logout();
  isLoading.value = false;
  router.push({ path: `/login/withdraw/done` })
};
</script>