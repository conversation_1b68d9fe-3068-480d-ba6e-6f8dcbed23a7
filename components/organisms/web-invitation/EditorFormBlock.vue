<template>
  <div class="webInvitation_block">
    <h2 class="webInvitation_block_title">
      <span class="cmn-title">{{ props.title }}</span>
      <div class="webInvitation_block_view" :class="{'is-showText': props.isShowSwitchText}" v-if="props.isShowSwitch">
        <InputSwitch
          :checked="isChecked"
          @update:checked="update($event)"
        ></InputSwitch>
      </div>
    </h2>
    <div class="webInvitation_block_wrap">
      <slot/>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  title?: string,
  isChecked?: boolean,
  isShowSwitch?: boolean,
  isShowSwitchText?: boolean
}>(), {
  isShowSwitch: true,
  isShowSwitchText: false
});

const isChecked = ref(props.isChecked);
watch(() => props, (newVal) => {
  isChecked.value = newVal.isChecked;
}, {
  deep: true
});

const emit = defineEmits(['change']);
const update = (event:any) => {
  isChecked.value = event;
  emit('change', event);
};

</script>

<style lang="scss" scoped>
.webInvitation_block{
  padding: 0 0 0;
  // border-bottom: 8px solid #F4F4F4;
  &:after {
    margin-top: 36px;
    content: " ";
    display: block;
    height: 8px;
    background: #F4F4F4;
    margin-right: calc(50% - 50vw);
    margin-left: calc(50% - 50vw);
    padding-left: calc(50vw - 50%);
    padding-right: calc(50vw - 50%);
  }
  &_title{
    font-size: 16px;
    line-height: 1;
    margin: 0;
    padding: 18px 16px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &_wrap{
    padding: 0 16px;
  }
  &_view{
    &.is-showText{
      .switch{
        position: relative;
        &::before{
          content: '表示';
          position: absolute;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          top: -1.7em;
          left: 0;
          right: 0;
          margin: auto;
          white-space: nowrap;
          font-size: 10px;
          font-weight: 300;
          color: #333;
          text-align: center;
        }
      }
    }
  }
}
</style>