<template>
  <EditorFormBlock
    title="カウントダウン"
    :isChecked="data.visible"
    :isShowSwitchText="true"
    @change="onUpdateView($event)"
  >
    <div v-if="data.visible">
      <p>挙式日（開催日）までのカウントダウンを表示します</p>
    </div>
  </EditorFormBlock>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  data: {
    name: string,
    id: string,
    visible: boolean,
    contents: {
      date: string
    }
  },
  aspect: {},
  imageUploadType: 'user' | 'admin',
  error?: string
}>(), {
  imageUploadType: 'user',
  error: ''
});

const data = ref(props.data);
watch(() => props.data, (newVal) => {
  data.value = newVal;
}, {
  deep: true
});

const emit = defineEmits(['change']);
const onUpdateView = (value:boolean) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.visible = value;
  data.value = dataUpdated;
  emit('change', {key: 'countDown', value: data.value})
};
</script>

<style lang="scss" scoped>
p{
  color: $color-blacktext2;
  margin-bottom: 0;
}
</style>