<template>
  <EditorFormBlock
    title="その他のご案内"
    :isChecked="data.visible"
    @change="onUpdateView($event)"
  >
    <div v-if="data.visible">
      <p>挙式ご列席のお願い、受付や祝辞・乾杯のご依頼などにご利用ください！</p>
      <div class="boxes">
        <div :id="'freeField-' + index" class="box" v-for="(item, index) in data.contents" :key="index">
          <div class="box_sort">
            <a
              class="box_sort_icon box_sort_icon-up"
              :class="{'is-active': index != 0}"
              @click="onReplacement(index, index - 1)"
            ></a>
            <a
              class="box_sort_icon box_sort_icon-down"
              :class="{'is-active': index != data.contents.length - 1}"
              @click="onReplacement(index, index + 1)"
            ></a>
          </div>
          <h2>その他のご案内{{ index + 1 }}</h2>
          <div class="row">
            <MessageEditor
              headingTitle="見出し"
              title="内容"
              :resetId="index == 0 ? 'freeField1' : null"
              :headingValue="item.heading"
              :message="item.message"
              :textAlign="item.textAlign"
              :isShowHeader="true"
              :isShowFilterSettings="false"
              :exampleType="ExampleType1Enum.MessageFree"
              :isValidateNgWord="true"
              @input="onChangeContents(index, 'message', $event)"
              @inputTitle="onChangeContents(index, 'heading', $event)"
              @align="onChangeContents(index, 'textAlign', $event)"
            ></MessageEditor>
          </div>
          <InputCheckSingle
            label="画像or動画を配置する"
            :name="'FreeFieldShowVisual' + index"
            :value="item.isShowVisual"
            :checked="item.isShowVisual"
            @change="onChangeContents(index, 'isShowVisual', $event)"
          />
          <div class="images_area" v-if="item.isShowVisual == true">
            <ImageSelect
              :name="'freeFieldVisualImage' + index"
              select-type="select"
              :qr-id="'freeField-' + index"
              :images="item.images"
              :aspect="null"
              :imageUploadType="props.imageUploadType"
              :loading-items="props.loadingItems"
              :isShowVideoMenu="true"
              @change-images="onChangeContents(index, 'images', $event)"
              @update-loading-items="onLoadImageUpdate($event)"
              @remove-loading-items="onLoadImageRemove($event)"
            ></ImageSelect>
          </div>
          <div class="row cmn-alignright">
            <a class="delete" @click="onDeleteContents(index)">この項目を削除する</a>
          </div>
        </div>
      </div>
      <div class="row cmn-alignright" v-if="data.contents && data.contents.length < 5">
        <ButtonMainColor
          baseColor="reversal"
          class="icn-add"
          :buttonsize="200"
          @click="onCreateContents(data.contents.length)"
        >その他のご案内を追加する</ButtonMainColor>
      </div>
      <p class="exclaim">※その他のご案内は5項目まで設定可能です</p>
      <p class="exclaim">※各項目の右上にある↑↓アイコンで「その他のご案内」の中での表示順を変更できます</p>
    </div>
  </EditorFormBlock>
</template>

<script lang="ts" setup>
import { LoadingItem } from '@/types/LoadingItem';
import { useImageLoading } from '@/composables/useImageLoading';
interface Props {
  data: {
    name: 'フリー項目',
    id: string,
    visible: boolean,
    contents: {
      heading: string,
      textAlign: 'left' | 'center' | 'right',
      message: string,
      isShowVisual: boolean,
      selectVisual: 'images' | 'movie',
      images: string[],
      movie: string[],
    }[]
  },
  aspect?: {},
  imageUploadType: 'user' | 'admin',
  error?: string,
  loadingItems: LoadingItem[]
}
const props = withDefaults(defineProps<Props>(), {
  imageUploadType: 'user',
  error: '',
});

const data = ref(props.data);
watch(() => props.data, (newVal) => {
  data.value = newVal;
}, {
  deep: true
});

// microCMSから例文一覧を取得
const microCms = new MicroCms();
const search = ref({
  orders: 'publishedAt',
  filters: 'content_type[contains]例文デフォルト',
} as {
  orders: string;
  filters: string;
});
const { data: microCmsData, error: microCmsError } = await microCms.fetch('/content', search);

const onCreateContents = (index: number) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  if(index == 0) {
    let title = '';
    let content = '';
    if(microCmsData?.value?.contents){
      let cmsData = microCmsData.value.contents.find((content) => content?.code == 'exapmle_default_freefield_1');
      title = cmsData?.plane_text2;
      content = cmsData?.plane_text;
    }
    dataUpdated.contents.push({
      heading: title ? title : '',
      textAlign: 'center',
      message: content ? content : '',
      isShowVisual: false,
      selectVisual: 'images',
      images: [],
      movie: []
    });
  } else {
    dataUpdated.contents.push({
      heading: '',
      textAlign: 'center',
      message: '',
      isShowVisual: false,
      selectVisual: 'images',
      images: [],
      movie: []
    });
  }
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onDeleteContents = (index) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.splice(index, 1);
  data.value = dataUpdated;
  onUpdate(data.value);
};

const onReplacement = (replaceItem:number, targetItem:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  let dataReplaceItem = JSON.parse(JSON.stringify(data.value.contents[replaceItem]));
  let dataTargetItem = JSON.parse(JSON.stringify(data.value.contents[targetItem]));

  dataUpdated.contents[replaceItem] = dataTargetItem;
  dataUpdated.contents[targetItem] = dataReplaceItem;
  data.value = dataUpdated;
  onUpdate(data.value);
};

const onChangeContents = (index, id:string, value:string) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents[index][id] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};

const emit = defineEmits(['change', 'updateLoadingItems', 'removeLoadingItems']);
const onUpdateView = (value:boolean) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.visible = value;
  data.value = dataUpdated;
  emit('change', {key: 'freeField', value: data.value})
};
const onUpdate = (value: any) => {
  emit('change', {key: 'freeField', value: value})
};

//アップロード中画像
const { onLoadImageUpdate, onLoadImageRemove } = useImageLoading(emit);
</script>

<style lang="scss" scoped>
:deep(textarea){
  min-height: 12.5em;
}
.boxes > .box{
  max-width: 528px;
  width: 100%;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 0 4px rgba(0,0,0,0.25);
  border-radius: 4px;
  position: relative;
  & + .box{
    margin-top: -4px;
  }
}
.row{
  margin-bottom: 4px;
  &.cmn-alignright{
    margin-top: 4px;
    margin-bottom: 0;
  }
}
.layoutEditor{
  margin-bottom: 16px;
}
h2{
  font-size: 14px;
  font-weight: normal;
  margin: 0 0 16px;
  padding: 0 0 15px;
  color: #333333;
  border-bottom: 1px solid #D9D9D9;
}
:deep(a.button--md.button--reversal){
  width: 192px;
}
.exclaim{
  font-size: 13px;
  color: #333333;
  margin-top: 16px;
  margin-bottom: 0;
  padding-left: 1em;
  text-indent: -1em;
  & + .exclaim{
    margin-top: 2px;
  }
}
.delete{
  text-decoration: none;
  position: relative;
  padding: 0 0 0 22px;
  color: $color-alert;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  &::before {
    @include BA;
    left: 0;
    width: 18px;
    height: 18px;
    background-image: url(@/assets/images/icon-delete.svg);
  }
}
.box_sort{
  position: absolute;
  top: 12px;
  right: 16px;
  &_icon{
    display: inline-block;
    width: 26px;
    height: 26px;
    background: $color-grayborder;
    mask-size: cover;
    mask-position: center;
    cursor: pointer;
    pointer-events: none;
    &.is-active {
      background: $color-accent;
      pointer-events: auto;
    }
    &-up{
      mask-image: url(@/assets/images/icon-arrow_circle_up.svg);
    }
    &-down{
      mask-image: url(@/assets/images/icon-arrow_circle_down.svg);
    }
  }
}
.icn-add{
  &::before{
    content: '';
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 4px;
    background-color: currentColor;
    mask-image: url('@/assets/images/icon-plus-gl.svg');
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: cover;
  }
}
</style>