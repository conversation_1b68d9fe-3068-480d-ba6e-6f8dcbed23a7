<template>
  <EditorFormBlock
    title="ギャラリー"
    :isChecked="data.visible"
    @change="onUpdateView($event)"
    >
    <div v-if="data.visible">
      <div class="section">
        <h3 class="cmn-title">写真</h3>
        <p>画像が20枚まで設定可能です </p>
        <ImageSelect
          name="galleryVisualImage"
          select-type="image"
          qr-id="gallery"
          :images="images"
          :max-images="20"
          :aspect="props.aspect.gallery"
          :imageUploadType="props.imageUploadType"
          :loading-items="props.loadingItems"
          @change-images="onChangeImage($event)"
          @update-loading-items="onLoadImageUpdate($event)"
          @remove-loading-items="onLoadImageRemove($event)"
        ></ImageSelect>
      </div>
      <div class="section">
        <h3 class="cmn-title">動画</h3>
        <p>動画は1つだけ設定可能です </p>
        <ImageSelect
          name="galleryVisualVideo"
          select-type="video"
          qr-id="gallery"
          :images="videos"
          :max-images="1"
          :aspect="props.aspect.gallery"
          :imageUploadType="props.imageUploadType"
          :loading-items="props.loadingItems"
          :isShowVideoMenu="true"
          @change-images="onChangeVideo($event)"
          @update-loading-items="onLoadImageUpdate($event)"
          @remove-loading-items="onLoadImageRemove($event)"
        ></ImageSelect>
      </div>
    </div>
  </EditorFormBlock>
</template>

<script lang="ts" setup>
import { LoadingItem } from '@/types/LoadingItem';
import { useImageLoading } from '@/composables/useImageLoading';
const props = withDefaults(defineProps<{
  data?: {
    name: string,
    id: string,
    visible: boolean,
    contents: {
      selectVisual: string,
      groomName: string,
      brideName: string
    }
  },
  aspect: {},
  imageUploadType: 'user' | 'admin',
  error?: string,
  loadingItems: LoadingItem[]
}>(), {
  imageUploadType: 'user',
  error: ''
});
const data = ref(props.data);
watch(() => props.data, (newVal, oldVal) => {
  if (JSON.stringify(newVal) != JSON.stringify(oldVal)) {
    data.value = newVal;
  }
}, {
  deep: true
});

const images = computed(() => {
  if(data.value) {
    return data.value.contents.filter(item => !('srcVideo' in item));
  }
  return []
});

const videos = computed(() => {
  if(data.value) {
    return data.value.contents.filter(item => ('srcVideo' in item));
  }
  return []
});

// コンテンツの変更
const onChangeImage = (item:string) => {
  let array = [...item, ...videos.value];
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents = array;
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onChangeVideo = (item:string) => {
  let array = [...images.value, ...item];
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents = array;
  data.value = dataUpdated;
  onUpdate(data.value);
};

const emit = defineEmits(['select', 'change', 'updateLoadingItems', 'removeLoadingItems']);
const onUpdateView = (value:boolean) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.visible = value;
  data.value = dataUpdated;
  emit('change', {key: 'gallery', value: data.value})
};
const onUpdate = (value: any) => {
  emit('change', {key: 'gallery', value: value})
};
//アップロード中画像
const { onLoadImageUpdate, onLoadImageRemove } = useImageLoading(emit);
</script>

<style lang="scss" scoped>
p{
  color: $color-blacktext2;
  margin-bottom: 16px;
}
a{
  display: block;
  font-size: 12px;
  margin: 12px 0;
}
h3.cmn-title{
  color: #333;
  font-size: 15px;
  font-weight: normal;
  border-bottom: 1px solid #D9D9D9;
  padding: 0 0 14px;
  margin-top: 0;
  margin-bottom: 16px;
}
.section + .section {
  margin-top: 40px;
}
.imageSelect{
  margin: 0;
  &:deep(.link-selectImage){
    margin: 0;
  }
}
</style>