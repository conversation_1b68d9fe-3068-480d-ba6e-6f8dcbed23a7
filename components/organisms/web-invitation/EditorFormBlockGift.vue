<template>
  <EditorFormBlock
    v-if="useRuntimeConfig()?.public?.app?.is_active_prepaid"
    title="会費・ご祝儀の事前支払い"
    :isShowSwitch="false"
    @change="onUpdateView($event)"
  >
    <div>
      <!-- 事前支払い機能の停止アラート ここから -->
      <div v-if="isPrepaymentPausePeriod(system, createdAt)" class="alert_prepaymentPause">
        <img src="@/assets/images/icon-exclamation.svg" alt="">
        <span>事前支払いサービスは一時停止させていただいております。<br>(2月10日12時 以前に作成いただいたWEB招待状では変わらずご利用可能です。)<br>詳しくは<a href="https://favori.wedding/information/buom21258j" target="_blank">こちら</a></span>
      </div>
      <!-- 事前支払い機能の停止アラート ここまで -->
      <p>ゲストが出欠の回答と同じタイミングで ご祝儀や会費をクレジットカードで支払うことができるようになります </p>
      <div class="gift_aboutImage">
        <img src="@/assets/images/aboutGift.png" alt="">
      </div>
      <a href="#" @click.prevent="onOpenModal('normal')" class="cmn-link">詳しくはこちらをご覧ください</a>

      <div class="select_option">
        <div class="label">会費・ご祝儀の事前支払いの利用について</div>
        <div>
          <InputRadio
            name="useGift"
            :items="[
              {
                value: true,
                label: '利用可能にする',
              }
            ]"
            :disabled="isPrepaymentPausePeriod(system, createdAt)"
            :value="data.contents.isUseGift"
            @change="onChangeContents('isUseGift', true)"
          />
        </div>
        <div class="select_option_description">
          <p :class="{'is-disabled': isPrepaymentPausePeriod(system, createdAt)}">（オンラインでの事前支払いか 当日に現金で支払うかは ゲストが自由に選択できます）</p>
          <div v-if="data.contents.isUseGift && data.contents.partySettings && data.contents.partySettings.length != 1">
            <div>
              <InputCheckSingle
                name="useGift"
                label="パーティーごとに設定する"
                :value="data.contents.isUseInVenue"
                :checked="data.contents.isUseInVenue"
                @change="onChangeContents('isUseInVenue', $event)"
              />
            </div>
            <div v-if="data.contents.isUseInVenue" class="party_list">
              <ul>
                <li v-for="(item, index) in data.contents.partySettings" :key="index">
                  <div>{{ item.eventName }}</div>
                  <div class="settings">
                    <div class="setting setting_view">
                      <InputSwitch
                        :checked="item.isUse"
                        @update:checked="onChangeSelectList('isUse', index, $event)"
                      ></InputSwitch>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div>
          <InputRadio
            name="useGift"
            :items="[
              {
                value: false,
                label: '利用しない',
              }
            ]"
            :value="data.contents.isUseGift"
            :disabled="isGuestPaid || isPrepaymentPausePeriod(system, createdAt)"
            @change="onChangeContents('isUseGift', false)"
          />
        </div>
      </div>
      <div class="select_usages" v-if="data.contents.isUseGift">
        <div class="label">システム利用料の負担</div>
        <InputRadio
          name="usageFee"
          :block="true"
          :items="[
            {
              value: 'guest',
              label: 'ゲスト',
            },
            {
              value: 'host',
              label: 'あなた（開催者）',
            },
            {
              value: 'free',
              label: 'ゲストに任せる',
            }
          ]"
          :value="data.contents.usageFee"
          @change="onChangeContents('usageFee', $event)"
        />
        <a href="#" @click.prevent="onOpenModal('scroll')" class="cmn-link">システム利用料についての詳細</a>
      </div>

      <div class="row" v-if="data.contents.isUseGift">
        <InputSelect
          title="主催者への送金予定日"
          size="full"
          :options="[
            {
              label:'開催日1週間前',
              value:'1'
            },
            {
              label:'開催日2週間前',
              value:'2'
            }
          ]"
          :value="data.contents.scheduleDate ? data.contents.scheduleDate : '1'"
          :disabled="isHasPrePaidGuest"
          @change="onChangeContents('scheduleDate', $event.target.value)"
        />
        <p v-if="informationData?.contents?.date" class="exclaim">{{$dayjs(shippingDate).format('YYYY年M月D日')}}に送金予定です <br>※土日祝により前後することがあります </p>
        <p v-else class="exclaim">挙式日が設定されていません </p>
      </div>

      <div v-if="data.contents.isUseGift">
        <MessageEditor
          title="会費・ご祝儀の事前支払いの案内"
          :message="data.contents.message"
          :textAlign="data.contents.textAlign"
          :isShowMessageModal="false"
          :isShowReset="true"
          :isShowFilterSettings="false"
          resetId="payment"
          :isValidateNgWord="true"
          @input="onChangeContents('message', $event)"
          @align="onChangeContents('textAlign', $event)"
        ></MessageEditor>
      </div>
    </div>
    <Modal v-if="isShowAboutGiftModal" class="modalAboutGift" size="full" @close="onCloseModal()">
      <template #header>
        会費・ご祝儀の事前支払いについて
      </template>
      <template #main>
        <div class="modalAboutGift_section">
          <div class="modalAboutGift_wrap">
            <div class="modalAboutGift_image">
              <img src="@/assets/images/aboutGiftDetail.png">
            </div>
            <p>
              会費・ご祝儀の事前支払いを利用すると<br>
              ゲストの出欠の回答と同じタイミングで ご祝儀や会費をクレジットカードでお支払いいただけるシステムです その他にも以下のメリットが沢山！
            </p>
          </div>
        </div>
        <div class="modalAboutGift_section">
          <div class="modalAboutGift_wrap">
            <h3>ゲストへのメリット</h3>
            <div class="modalAboutGift_icon icon-card">新札やご祝儀袋の準備が不要！</div>
            <div class="modalAboutGift_icon icon-heart">当日の受付をスムーズに行える！</div>
            <div class="modalAboutGift_icon icon-gift">ご欠席でもお祝いを簡単に渡せる！</div>
          </div>
        </div>
        <div class="modalAboutGift_section">
          <div class="modalAboutGift_wrap">
            <h3>新郎新婦さまのメリット</h3>
            <div class="modalAboutGift_icon icon-star">事前に会費・ご祝儀を受け取れるので ご結婚式前のお支払いが楽に！</div>
            <div class="modalAboutGift_icon icon-money">ご祝儀の開封や管理の手間が不要！</div>
            <div class="modalAboutGift_icon icon-gift">事前にご祝儀額が分かるので 引き出物などの内容を調整できる！</div>
          </div>
        </div>
        <div class="modalAboutGift_section modalAboutGift_flow">
          <div class="modalAboutGift_wrap">
            <h2>ご利用方法</h2>
            <div class="modalAboutGift_flow_wrap">
              <ol>
                <li>招待状作成時に<br>「会費・ご祝儀の事前支払いを利用する」を選択</li>
                <li>ゲストが出欠の回答時に「事前支払いを利用」を選択</li>
                <li>お預かりした会費・ご祝儀は<br>新郎新婦さまがご指定された口座に送金</li>
              </ol>
              <div><small class="indent1em">※選択いただいた「会費・ご祝儀の送金予定日」に 株式会社テトテからご指定口座へお振込みいたします</small></div>
              <div><small class="indent1em">※実際の口座振込額は 事前支払いの合計金額からシステム利用料と振込手数料を差し引いた金額となります</small></div>
              <div><small class="indent1em">※送金予定日が土日祝日の場合は前後いたします</small></div>
            </div>
            <h2 id="aboutSystemFee">システム利用料について</h2>
            <div class="modalAboutGift_flow_wrap">
              <p>決済金額の3.1%をシステム利用料として頂戴しております<br>
システム利用料のご負担は「ゲスト」 「あなた(主催者)」 「ゲストに任せる」 より設定いただけます</p>
            </div>
            <h2>振込手数料について</h2>
            <div class="modalAboutGift_flow_wrap">
              <p>1回の引き落としにつき660円の振込手数料が発生いたします<br>
                なお 会費・ご祝儀の事前支払いのお振込は招待状毎にお振込みとなります<br>
                お振込み金額を分割したり まとめることはできかねております事ご了承くださいませ</p>
            </div>
            <h2>ゲストがご利用可能なクレジットカード</h2>
            <div class="modalAboutGift_flow_wrap">
              <p>会費・ご祝儀の決済には 以下のクレジットカードがご利用可能です</p>
              <div class="modalAboutGift_image_card">
                <img src="@/assets/images/aboutGiftCard.jpg" alt="">
              </div>
            </div>
            <h2>ご注意事項</h2>
            <div class="modalAboutGift_flow_wrap">
              <p>
                ※ゲストが事前支払いの決済完了後 当日支払いへの変更や決済金額およびシステム利用料の変更・ご返金はできかねます　主催者とゲストで直接ご対応をお願いいたします<br>
                ※振込手数料は1回の振込につき660円発生いたします<br>
                ※振込は招待状毎に行われ 金額の合算は出来かねます<br>
                ※年末年始・GWは金融機関休業のため休業明けの送金となります
              </p>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </EditorFormBlock>
</template>

<script lang="ts" setup>
const { member } = await useGetOneMemberMe();
const { $dayjs } : any = useNuxtApp();
interface Props {
  data?: {
    name: '会費・ご祝儀の事前支払い',
    id: 'gift',
    visible: boolean,
    contents: {
      isUseGift: boolean,
      isUseInVenue: boolean,
      isUseInAfterParty: boolean,
      partySettings: {
        isUse: boolean,
        eventName: string
      }[],
      message: string,
      textAlign: 'left' | 'center' | 'right',
      usageFee: string,
      scheduleDate: string
    }
  },
  informationData: {
    name: 'パーティー情報',
    id: 'information',
    visible: boolean,
    contents: {
      date: string,
      type: string,
      events: {
        isOtherParty: boolean,
        eventName: string,
        plans: {
          isShowPlan: boolean,
          hour: string,
          minute: string
        }[]
        otherPlans: {
          isShowPlan: boolean,
          hour: string,
          minute: string
        }[],
        venue: string,
        zip: string,
        address: string,
        isShowMaps: boolean,
        tel: string,
        url: string,
        feeOption: string,
        feeAmount: string[],
        isShowText: boolean,
        text: string,
        otherVenue: string,
        otherZip: string,
        otherAddress: string,
        isShowOtherMaps: boolean,
        otherTel: string,
        otherUrl: string,
        isShowOtherText: boolean,
        otherText: string
      }[]
    }
  },
  aspect: {},
  imageUploadType: 'user' | 'admin',
  isGuestPaid?: boolean,
  isHasPrePaidGuest?: boolean,
  createdAt?: string,
  error?: string
};
const props = withDefaults(defineProps<Props>(), {
  imageUploadType: 'user',
  isGuestPaid: false,
  isHasPrePaidGuest: false,
  createdAt: '',
  error: ''
});

const { system } = useGetSystem();
const isShowAboutGiftModal = ref(false);
const scrollType = ref(false);
type ModalType = 'normal' | 'scroll'
const onOpenModal = (type:ModalType) => {
  isShowAboutGiftModal.value = true
  scrollType.value = type === 'scroll'

  if (scrollType.value) {
    nextTick(() => {
      const targetElement = document.getElementById('aboutSystemFee')
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: 'instant' })
      }
    })
  }
}

const onCloseModal = () => {
  isShowAboutGiftModal.value = false
  scrollType.value = false
}

const data = ref(props.data);
const informationData = ref(props.informationData);
const isGuestPaid = ref(props.isGuestPaid);
const isHasPrePaidGuest = ref(props.isHasPrePaidGuest);
const createdAt = ref(props.createdAt);
watch(() => props.data, (newData, oldData) => {
  if (JSON.stringify(newData) != JSON.stringify(oldData)) {
    data.value = JSON.parse(JSON.stringify(newData));
  }
}, { deep: true, immediate: true });
watch(() => props.isGuestPaid, (newData) => {
  isGuestPaid.value = newData;
}, { deep: true });
watch(() => props.isHasPrePaidGuest, (newData) => {
  isHasPrePaidGuest.value = newData;
}, { deep: true });
watch(() => props.createdAt, (newData) => {
  createdAt.value = newData;
}, { deep: true });
watch(() => props.informationData, (newInformationData, oldInformationData) => {
  const areObjectsEqual = JSON.stringify(newInformationData) == JSON.stringify(oldInformationData);
  if (areObjectsEqual) return;

  // 更新ロジック
  let array = [];
  const eventType = newInformationData.contents?.type;
  const eventTypes = {
    'ceremony_reception_same_venue': ['挙式', '披露宴'],
    'ceremony_reception_separate_venue': ['挙式', '披露宴'],
    'ceremony': ['挙式'],
    'reception': ['披露宴'],
    '1.5_party': ['1.5次会'],
    '2_party': ['2次会'],
    'other_party': ['パーティー・その他宴席'],
  };

  if (eventTypes[eventType]) {
    array = eventTypes[eventType].map(eventName => ({ isUse: true, eventName }));
  }

  newInformationData.contents?.events.forEach((item, index) => {
    if (index !== 0) {
      array.push({ isUse: true, eventName: item.eventName });
    }
  });

  // dataの更新
  if(data.value.contents.isUseGift !== null){
    data.value.contents.partySettings = array;
    emit('change', { key: 'gift', value: data.value });
  }
  informationData.value = newInformationData;
}, { deep: true, immediate: true });

// 送金予定日の表示
const shippingDate = computed(() => {
  if(informationData?.value?.contents?.date){
    const minusDays = Number(data.value.contents.scheduleDate ? data.value.contents.scheduleDate : 1) * 7;
    const resultDate = new Date(new Date(informationData?.value?.contents?.date).toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));
    resultDate.setDate(resultDate.getDate() - minusDays);
    emit('change-shipping-date', $dayjs(resultDate).format('YYYY-MM-DD'));
    return resultDate.toLocaleDateString();
  }
  emit('change-shipping-date', '');
  return '';
});

// コンテンツの変更
const onChangeContents = (id: string, value: string) => {
  data.value = {
    ...data.value,
    contents: {
      ...data.value.contents,
      [id]: value
    }
  };
  onUpdate(data.value);
};

const onChangeSelectList = (id:string, index:number, value:string) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.partySettings[index][id] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};

// emits
const emit = defineEmits(['change', 'change-shipping-date']);
const onUpdateView = (value:boolean) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.visible = value;
  data.value = dataUpdated;
  emit('change', {key: 'gift', value: data.value})
};
const onUpdate = (value: any) => {
  emit('change', {key: 'gift', value: value})
};
</script>

<style lang="scss" scoped>
p{
  margin-bottom: 8px;
  &.is-disabled{
    opacity: 0.5;
  }
}
:deep(textarea){
  min-height: 23em;
}
.cmn-link{
  display: inline-block;
  text-decoration: underline;
  font-size: 12px;
  font-weight: bold;
  margin: 0;
  &:hover{
    text-decoration: none;
  }
  .select_usages & {
    margin: 4px 0 24px;
  }
}
.exclaim{
  font-size: 13px;
  color: #333333;
  margin-top: 4px;
  margin-bottom: 6px;
}
.label{
  font-size: 12px;
  line-height: 1;
  margin-top: 25px;
  margin-bottom: 16px;
  color: #49454F;
  white-space: nowrap;
  .select_usages &{
    margin-top: 8px;
  }
}
.gift_aboutImage{
  text-align: center;
  margin: 16px 0;
}
.select_usages{
  :deep(label.is-block){
    margin-bottom: 12px;
  }
}
.select_option_description{
  margin-left: 24px;
  margin-top: -6px;
  font-size: 13px;
  color: #333333;
}
.party_list{
  ul{
    margin-left: 24px;
    margin-bottom: 16px;
  }
  li{
    color: $color-blacktext2;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 13px;
    border-bottom: 1px solid $color-grayborder;
    &:first-child{
      .setting{
        position: relative;
        &::before{
          content: '利用';
          position: absolute;
          display: inline-flex;
          justify-content: center;
          align-items: center;
          top: -2em;
          left: 0;
          right: 0;
          margin: auto;
          white-space: nowrap;
          font-size: 10px;
          color: #111;
          text-align: center;
        }
      }
    }
  }
}
:deep(.modalAboutGift){
  &.modalWrap .modalContainer .contents{
    padding: 0;
  }
}
.modalAboutGift{
  &_section{
    &.modalAboutGift_flow{
      padding-top: 0px;
    }
    & + .modalAboutGift_section{
      border-top: 8px solid #F4F4F4;
    }
  }
  &_wrap{
    max-width: 500px;
    width: 100%;
    margin: 0 auto;
    padding: 30px 20px;
  }
  .modalAboutGift_flow_wrap{
    padding: 0 10px;
    p{
      margin-bottom: 16px;
    }
  }
  &_image{
    text-align: center;
    max-width: 342px;
    margin: 0 auto 10px;
    &_card{
      max-width: 240px;
    }
  }
  h2{
    margin: 20px 0 16px;
    padding: 0 0 14px;
    font-size: 15px;
    line-height: 1.2;
    color: #333;
    text-align: left;
    font-weight: normal;
    border-bottom: 1px solid #D9D9D9;
  }
  h3{
    margin: 0 0 20px;
    font-size: 16px;
    line-height: 1.2;
    color: #AD871E;
    text-align: center;
    font-weight: normal;
  }
  ul, ol{
    padding: 0 0 0 1em;
    margin-bottom: 10px;
  }
  li{
    list-style-type: auto;
    color: #333333;
    font-size: 14px;
    line-height: 1.5;
  }
  p{
    color: #333333;
    font-size: 14px;
    line-height: 1.5;
  }
  small{
    display: inline-block;
    font-size: 11px;
    line-height: 1.4;
  }
  &_icon{
    color: #333333;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 4px;
    padding-left: 24px;
    position: relative;
    &::before {
      @include BA;
      top: 0;
      left: 0;
      width: 24px;
      height: 24px;
      transform: none;
    }
    &.icon-card{
      &::before{
        background-image: url(@/assets/images/icon-aboutGift-card.svg);
      }
    }
    &.icon-heart{
      &::before{
        background-image: url(@/assets/images/icon-aboutGift-heart.svg);
      }
    }
    &.icon-star{
      &::before{
        background-image: url(@/assets/images/icon-aboutGift-star.svg);
      }
    }
    &.icon-money{
      &::before{
        background-image: url(@/assets/images/icon-aboutGift-money.svg);
      }
    }
    &.icon-gift{
      &::before{
        background-image: url(@/assets/images/icon-aboutGift-gift.svg);
      }
    }
  }
}
</style>