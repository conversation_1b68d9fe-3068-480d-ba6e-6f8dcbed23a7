<template>
  <EditorFormBlock
    title="出欠フォーム"
    :isChecked="data.visible"
    @change="onUpdateView($event)"
  >
    <div v-if="data.visible">
      <div class="section section-options">
        <h3 class="cmn-title">出欠選択</h3>
        <div class="row" v-if="false">
          <InputCheckSingle
            label="出欠選択エリアを表示しない（案内状としてのみご利用の方）"
            :value="data.contents.attendance.isHideAttendance"
            :checked="data.contents.attendance.isHideAttendance"
            @change="onChangeAttendanceCheck('isHideAttendance', $event)"
          />
        </div>
        <div class="row">
          <InputCheckSingle
            label="保留ボタンを表示しない"
            :value="data.contents.attendance.isHideSkip"
            :checked="data.contents.attendance.isHideSkip"
            @change="onChangeAttendanceCheck('isHideSkip', $event)"
          />
        </div>
        <div class="row" v-if="false">
          <InputCheckSingle
            label="出欠以外の選択肢を追加する"
            :value="data.contents.attendance.isAddFields"
            :checked="data.contents.attendance.isAddFields"
            @change="onChangeAttendanceCheck('isAddFields', $event)"
          />
        </div>
        <div class="fields" v-if="false">
          <div class="label">追加する選択肢</div>
          <div class="field" v-for="(item, index) in data.contents.attendance.fields" :key="index">
            <div class="field_text">
              <InputText
                size="full"
                placeholder="挙式ご列席のお願い"
                :value="item"
                @input="onChangeAttendanceContents(index, $event.target.value)"
              />
            </div>
            <a class="trash" @click="onDeleteAttendanceField(index)"></a>
          </div>
          <a class="cmn-link add_field" @click="onCreateAttendanceField()">回答項目を追加</a>
        </div>
      </div>

      <div class="section">
        <h3 class="cmn-title">ゲスト回答項目</h3>
        <p>ゲストに入力いただく一部の項目は <br>入力必須項目・表示/非表示を変更可能です </p>
        <ul>
          <template v-for="(item, index) in data.contents.selectList" :key="index">
            <li v-if="item.title != '関係性'">
              <div>
                {{ item.title }}
                <a v-if="item.title == '性別' && item.disabled" @click.prevent="isShowGenderConfirmModal = true" href="#" class="cmn-link is-question"><img src="@/assets/images/icon-question-gl.svg" alt=""><span>詳しく見る</span></a>
                <a v-if="item.title == 'メールアドレス'" @click.prevent="isShowMailConfirmModal = true" href="#" class="cmn-link is-question"><img src="@/assets/images/icon-question-gl.svg" alt=""><span>詳しく見る</span></a>
              </div>
              <div class="settings">
                <div class="setting setting_require">
                  <InputCheckSingle
                    :name="'guestAnswer' + index"
                    :value="item.required"
                    :checked="item.required"
                    :disabled="
                      !item.visible ||
                      item.disabled ||
                      item.title == '新郎新婦ゲスト選択' ||
                      item.title == 'お名前' ||
                      item.title == 'メールアドレス'
                    "
                    :style="{display: item.title === '連名入力' ? 'none' : ''}"
                    @change="onChangeSelectListCheck(index, 'required', $event)"
                  />
                </div>
                <div class="setting setting_view">
                  <InputSwitch
                    :checked="item.visible"
                    :disabled="
                      item.disabled ||
                      item.title == '新郎新婦ゲスト選択' ||
                      item.title == 'お名前' ||
                      item.title == 'メールアドレス'
                    "
                    @update:checked="onChangeSelectList(index, 'visible', $event)"
                  ></InputSwitch>
                </div>
              </div>
            </li>
          </template>
        </ul>
      </div>

      <div class="section">
        <h3 class="cmn-title">アンケート項目</h3>
        <div class="boxes">
          <div :id="'questionnaire-' + index" class="box" v-for="(item, index) in data.contents.questionnaire" :key="index">
            <div class="box_sort">
              <a
                class="box_sort_icon box_sort_icon-up"
                :class="{'is-active': index != 0}"
                @click="onReplacement(index, index - 1)"
              ></a>
              <a
                class="box_sort_icon box_sort_icon-down"
                :class="{'is-active': index != data.contents.questionnaire.length - 1}"
                @click="onReplacement(index, index + 1)"
              ></a>
            </div>
            <h4>アンケート項目{{ index + 1 }}</h4>
            <div class="row">
              <InputText
                title="見出し"
                :value="item.heading"
                size="full"
                @input="onChangeQuestionnaireContents(index, 'heading', $event.target.value)"
              />
              <MessageNgWord :value="item.heading"></MessageNgWord>
            </div>
            <div class="row">
              <InputCheckSingle
                label="画像or動画を配置する"
                :value="item.isShowVisual"
                :checked="item.isShowVisual"
                @change="onChangeQuestionnaireContents(index, 'isShowVisual', $event)"
              />
              <div class="images_area" v-if="item.isShowVisual == true">
                <ImageSelect
                  :name="'questionnaireVisualImage' + index"
                  select-type="select"
                  :qr-id="'questionnaire-' + index"
                  :images="item.images"
                  :aspect="null"
                  :imageUploadType="props.imageUploadType"
                  :loading-items="props.loadingItems"
                  :isShowVideoMenu="true"
                  @change-images="onChangeQuestionnaireImageChange(index, 'images', $event)"
                  @update-loading-items="onLoadImageUpdate($event)"
                  @remove-loading-items="onLoadImageRemove($event)"
                ></ImageSelect>
              </div>
            </div>
            <div class="row">
              <MessageEditor
                title="テキスト"
                :message="item.message"
                :isShowHeader="true"
                :isShowMessageModal="false"
                :isShowReset="false"
                :isShowAlgin="false"
                :isShowFilterSettings="false"
                :isValidateNgWord="true"
                @input="onChangeQuestionnaireContents(index, 'message', $event)"
              ></MessageEditor>
            </div>
            <div class="row">
              <InputSelect
                title="回答方法"
                size="full"
                :value="item.method"
                :options="[
                  {
                    label:'テキスト入力フォーム',
                    value:'inputText'
                  },
                  {
                    label:'チェックボックス（複数選択可能）',
                    value:'checkbox'
                  },
                  {
                    label:'ラジオボタン（単一選択）',
                    value:'radio'
                  }
                ]"
                @change="onChangeQuestionnaireContents(index, 'method', $event.target.value)"
              />
            </div>
            <div v-if="item.method == 'checkbox' || item.method == 'radio'">
              <div class="label">回答項目</div>
              <div class="row" v-for="(answer, answerIndex) in item.answer" :key="answerIndex">
                <div class="field">
                  <div class="field_text">
                    <InputText
                      size="full"
                      placeholder="回答項目を追加"
                      :value="answer"
                      @input="onChangeQuestionnaireAnswerContents(answerIndex, index, 'answer', $event.target.value)"
                    />
                  </div>
                  <a class="trash" @click="onDeleteQuestionnaireField(index, answerIndex)"></a>
                </div>
              </div>
              <a class="cmn-link add_field" @click="onCreateQuestionnaireField(index)">回答項目を追加</a>
            </div>
            <div class="row cmn-alignright">
              <a class="delete" @click="onDeleteQuestionnaireContents(index)">この項目を削除する</a>
            </div>
          </div>
        </div>
        <div v-if="data?.contents?.questionnaire && data?.contents?.questionnaire.length >= 1  && data?.contents?.questionnaire.length < 5" class="row cmn-alignright">
          <ButtonMainColor
            baseColor="reversal"
            class="icn-add"
            :buttonsize="200"
            @click="onCreateQuestionnaireContents(data?.contents?.questionnaire.length)"
          >アンケート項目を追加する</ButtonMainColor>
        </div>
        <div v-else-if="data?.contents?.questionnaire && data?.contents?.questionnaire.length == 0" class="row">
          <ButtonMainColor
            baseColor="reversal"
            class="icn-add"
            buttonsize="full"
            @click="onCreateQuestionnaireContents(data?.contents?.questionnaire.length)"
          >オリジナルアンケート項目を作成する</ButtonMainColor>
        </div>
        <p class="exclaim">※アンケートは5項目まで設定可能です</p>
      </div>

      <div id="guestAnswer" class="section">
        <h3 class="cmn-title">回答期限設定</h3>
        <InputRadio
          name="answerLimit"
          :block="true"
          :items="[
            {
              value: 1,
              checked: data.contents.limit.setting == 1 ? true : false,
              label: '開催一ヶ月前に期限を設定' + onGetLimitDate(1),
            },
            {
              value: 2,
              checked: data.contents.limit.setting == 2 ? true : false,
              label: '開催二ヶ月前に期限を設定' + onGetLimitDate(2),
            },
            {
              value: 3,
              checked: data.contents.limit.setting == 3 ? true : false,
              label: '回答期日を入力して設定',
            }
          ]"
          @change="onChangeLimitContents('setting', $event)"
        />
        <div v-if="data.contents.limit.setting == 3" class="limit_date">
          <InputCalendar
            title="回答期日"
            size="full"
            :placeholder="$dayjs(new Date()).format('2027/04/01')"
            :value="data.contents.limit.date"
            :error="props.error?.contents?.limit?.date?.$message"
            @change="onChangeLimitContents('date', $event)"
          />
          <span v-if="isLimitDate" class="input-error">期日には過去日付、または挙式日（開催日）以降の日付は設定できません</span>
        </div>

        <MessageEditor
          title="回答期限下に表示される案内"
          :message="data.contents.limit.message"
          :textAlign="data.contents.limit.textAlign"
          :isShowMessageModal="false"
          :isShowReset="true"
          :isShowFilterSettings="false"
          :isValidateNgWord="true"
          resetId="deadline"
          @input="onChangeLimitContents('message', $event)"
          @align="onChangeLimitContents('textAlign', $event)"
        ></MessageEditor>
      </div>

      <Modal v-if="isShowGenderConfirmModal" class="modalConfirm" size="sm" @close="isShowGenderConfirmModal = false">
        <template #main>
          <div class="wrap">
            <h2 class="confirm_title">性別の選択項目について</h2>
            <p class="confirm_alert">会費・ご祝儀の事前決済ご利用にあたり「会費制（男女別）」を選択された場合は、ゲスト側で性別の選択が必須となります</p>
          </div>
          </template>
        <template #footer>
          <a class="color-accent" @click="isShowGenderConfirmModal = false">閉じる</a>
        </template>
      </Modal>

      <Modal v-if="isShowMailConfirmModal" class="modalConfirm" size="sm" @close="isShowMailConfirmModal = false">
        <template #main>
          <div class="wrap">
            <h2 class="confirm_title">メールアドレスは必須項目です</h2>
            <p class="confirm_alert">
              ゲストに入力いただいたメールアドレス宛に下記のメールなどをお送りします<br><br>
              ・出欠の回答受付メール<br>
              ・パーティー情報が記載された事前案内メール
            </p>
          </div>
          </template>
        <template #footer>
          <a class="color-accent" @click="isShowMailConfirmModal = false">閉じる</a>
        </template>
      </Modal>

    </div>
  </EditorFormBlock>
</template>

<script lang="ts" setup>
import { LoadingItem } from '@/types/LoadingItem';
import { useImageLoading } from '@/composables/useImageLoading';
const { $dayjs } : any = useNuxtApp();
interface Props {
  data: {
    name: '出欠フォーム',
    id: string,
    visible: boolean,
    contents: {
      selectList: {
        title: string,
        id: string,
        disabled: boolean,
        required: boolean,
        visible: boolean,
      }[],
      questionnaire: {
        heading: string,
        textAlign: 'left' | 'center' | 'right',
        message: string,
        method: string,
        isShowVisual: boolean,
        selectVisual: string,
        images: string[],
        movie: string,
        answer: string[]
      }[],
      limit: {
        date: string,
        setting: string,
        textAlign: 'left' | 'center' | 'right',
        message: string,
      },
      attendance: {
        isHideAttendance: boolean,
        isHideSkip: boolean,
        isAddFields: boolean,
        fields: string[]
      }
    }
  },
  informationData?: {},
  aspect: {},
  imageUploadType: 'user' | 'admin',
  error?: string,
  loadingItems: LoadingItem[]
}
const props = withDefaults(defineProps<Props>(), {
  imageUploadType: 'user',
  data: {},
  informationData: {},
  error: ''
});

const isShowGenderConfirmModal = ref(false);
const isShowMailConfirmModal = ref(false);

const data = ref(props.data);
const informationData = ref(props.informationData);
watch(() => props, (newVal) => {
  if(newVal?.data){
    data.value = newVal.data;
  }
  if(newVal?.informationData){
    informationData.value = newVal.informationData;
  }
}, {
  deep: true
});

const onGetLimitDate = (differentMonth:number) => {
  if(informationData?.value?.contents?.date) {
    let date = $dayjs(informationData?.value?.contents?.date).subtract(differentMonth, 'month').format('YYYY/M/D');
    return date ? `（${date}）` : '';
  }else{
    return '';
  }
};

const onReplacement = (replaceItem:number, targetItem:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  let dataReplaceItem = JSON.parse(JSON.stringify(data.value.contents.questionnaire[replaceItem]));
  let dataTargetItem = JSON.parse(JSON.stringify(data.value.contents.questionnaire[targetItem]));

  dataUpdated.contents.questionnaire[replaceItem] = dataTargetItem;
  dataUpdated.contents.questionnaire[targetItem] = dataReplaceItem;

  data.value = dataUpdated;
  onUpdate(data.value);
};

const isLimitDate = computed(() => {
  if(props?.error?.contents?.limit?.date?.$message){ return false }

  let date = new Date(new Date(data?.value?.contents?.limit?.date).toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));
  let comparisonDate = new Date(new Date(informationData?.value?.contents?.date).toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));
  let now = new Date(new Date().toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));

  if (date <= now || date > comparisonDate) return true;
  return false
});

// 画像の変更
const onChangeQuestionnaireImageChange = async (index:number, id:string, item:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.questionnaire[index][id] = item;
  data.value = dataUpdated;
  onUpdate(data.value);
}

const onChangeQuestionnaireRadio = (index:number, $event) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.questionnaire[index]['isShowVisual'] = $event;
  data.value = dataUpdated;
  onUpdate(data.value);
}

const onChangeSelectListCheck = (index:number, id:string, event:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.selectList[index][id] = event;
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onChangeSelectList = (index:number, id:string, event:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.selectList[index][id] = event;
  if(!event){
    dataUpdated.contents.selectList[index]['required'] = false;
  }
  data.value = dataUpdated;
  onUpdate(data.value);
};

const onChangeQuestionnaireContents = (index:number, id:string, value:string) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.questionnaire[index][id] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onChangeQuestionnaireAnswerContents = (answerIndex:number, index:number, id:string, value:string) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.questionnaire[index][id][answerIndex] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};

// microCMSから例文一覧を取得
const microCms = new MicroCms();
const search = ref({
  orders: 'publishedAt',
  filters: 'content_type[contains]例文デフォルト',
} as {
  orders: string;
  filters: string;
});
const { data: microCmsData, error: microCmsError } = await microCms.fetch('/content', search);

const onCreateQuestionnaireContents = (index) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  let message = '';
  let heading = '';
  if(index == 0 && microCmsData?.value?.contents){
    microCmsData.value.contents.forEach(content => {
      // アンケート項目 : exapmle_default_questionnaire_1
      if(content?.code == 'exapmle_default_questionnaire_1'){
        message = content.plane_text;
        heading = content.plane_text2;
      }
    });
  }
  dataUpdated.contents.questionnaire.push({
    title: `アンケート項目${(data.value.contents.questionnaire ? data.value.contents.questionnaire.length : 0) + 1}`,
    heading: heading,
    textAlign: 'left',
    message: message,
    isShowVisual: false,
    selectVisual: 'images',
    images: [],
    movie: '',
    method: 'inputText',
    answer: ['']
  });
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onDeleteQuestionnaireContents = (index:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.questionnaire.splice(index, 1);
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onCreateQuestionnaireField = (index:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.questionnaire[index].answer.push('');
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onDeleteQuestionnaireField = (index:number, answerIndex:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.questionnaire[index].answer.splice(answerIndex, 1);
  data.value = dataUpdated;
  onUpdate(data.value);
};

const onChangeLimitContents = (id:string, value:any) => {
  let changeValue = value;
  if(id == 'date'){
    const parsedDate = new Date(new Date(value).toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));
    changeValue = isNaN(parsedDate.getTime()) ? null : value;
  }
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.limit[id] = changeValue;
  if(id == 'setting' && value != 3 && id == 'setting' && value != '3'){
    dataUpdated.contents.limit.date = null;
  }
  if(id == 'date'){
    data.value.contents.limit.date = dataUpdated.contents.limit.date;
  }else{
    data.value = dataUpdated;
  }
  onUpdate(data.value);
};

const onChangeAttendanceCheck = (id:string, event:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.attendance[id] = event;
  data.value = dataUpdated
  onUpdate(data.value);
};
const onChangeAttendanceContents = (index:number, value:string) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.attendance.fields[index] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onCreateAttendanceField = () => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.attendance.fields.push('');
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onDeleteAttendanceField = (index:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.attendance.fields.splice(index, 1);
  data.value = dataUpdated;
  onUpdate(data.value);
};

const emit = defineEmits(['change', 'updateLoadingItems', 'removeLoadingItems']);
const onUpdateView = (value:boolean) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.visible = value;
  data.value = dataUpdated;
  emit('change', {key: 'guestAnswer', value: data.value})
};
const onUpdate = (value: any) => {
  emit('change', {key: 'guestAnswer', value: value})
};
//アップロード中画像
const { onLoadImageUpdate, onLoadImageRemove } = useImageLoading(emit);
</script>

<style lang="scss" scoped>
.section{
  margin-bottom: 36px;
  &:last-child{
    margin-bottom: 0;
  }
  &.section-options{
    :deep(label){
      margin-bottom: 0;
    }
  }
}
h3.cmn-title{
  color: #333;
  font-size: 15px;
  font-weight: normal;
  border-bottom: 1px solid #D9D9D9;
  padding: 0 0 14px;
  margin: 0 0 16px;
}
p{
  color: $color-blacktext2;
  margin-bottom: 22px;
}
li{
  color: $color-blacktext2;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0px 12px 8px;
  border-bottom: 1px solid $color-grayborder;
  &:first-child{
    .setting{
      position: relative;
      &::before{
        content: '';
        position: absolute;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        top: -2em;
        left: 0;
        right: 0;
        margin: auto;
        white-space: nowrap;
        font-size: 10px;
        color: #111;
        text-align: center;
      }
      &.setting_require::before{
        content: '必須項目';
      }
      &.setting_view::before{
        content: '表示';
      }
    }
  }
  &:deep(label input:checked:disabled + span){
    padding: 0 8px;
  }
}
.inputRadio{
  display: block;
  margin-bottom: 6px;
}
.settings{
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 78px;
  height: 20px;
  position: relative;
  :deep(label) {
    margin-bottom: 0;
  }
}
.setting{
  height: 20px;
}

.boxes > .box{
  max-width: 528px;
  width: 100%;
  margin-bottom: 16px;
  padding: 16px;
  box-shadow: 0 0 4px rgba(0,0,0,0.25);
  border-radius: 4px;
  position: relative;
  & + .box{
    margin-top: -4px;
  }
  h4{
    font-size: 14px;
    font-weight: normal;
    margin: 0 0 16px;
    padding: 0 0 15px;
    color: #333333;
    border-bottom: 1px solid #D9D9D9;
  }
  .row{
    margin-bottom: 0;
    & + .row{
      margin-bottom: 16px;
    }
    &:has(.field){
      margin-bottom: 8px;
    }
    &:has(input[type="checkbox"]){
      margin-top: 16px;
      margin-bottom: 0;
    }
    &.cmn-alignright{
      margin: 16px 0 0;
    }
    :deep(.layoutEditor_textarea textarea){
      min-height: 14.5em;
    }
  }
  .label{
    margin-top: 0;
  }
  :deep(.layoutEditor){
    margin-bottom: 16px;
    .title {
      margin-top: 6px;
    }
  }
}
.box_sort{
  position: absolute;
  top: 12px;
  right: 16px;
  &_icon{
    display: inline-block;
    width: 26px;
    height: 26px;
    background: $color-grayborder;
    mask-size: cover;
    mask-position: center;
    cursor: pointer;
    pointer-events: none;
    &.is-active {
      background: $color-accent;
      pointer-events: auto;
    }
    &-up{
      mask-image: url(@/assets/images/icon-arrow_circle_up.svg);
    }
    &-down{
      mask-image: url(@/assets/images/icon-arrow_circle_down.svg);
    }
  }
}

.cmn-link.add_field{
  display: inline-block;
  margin: 10px 0 0 auto;
  text-align: right;
  position: relative;
  padding-left: 20px;
  font-size: 14px;
  line-height: 100%;
  &::before {
    @include BA;
    left: 3px;
    width: 10px;
    height: 10px;
    background: currentColor;
    mask-image: url(@/assets/images/icon-plus.svg);
    mask-size: contain;
    mask-position: center;
  }
}
.delete{
  text-decoration: none;
  position: relative;
  padding: 0 0 0 22px;
  color: $color-alert;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  &::before {
    @include BA;
    left: 0;
    width: 18px;
    height: 18px;
    background-image: url(@/assets/images/icon-delete.svg);
  }
}
.exclaim{
  font-size: 13px;
  color: #333333;
  margin-top: 16px;
}

.limit_date{
  margin-bottom: 20px;
}

.label{
  font-size: 12px;
  line-height: 1;
  margin-top: 30px;
  margin-bottom: 7px;
  color: #49454F;
  white-space: nowrap;
}
.trash{
  display: inline-block;
  margin: 0 0 0 auto;
  text-align: right;
  position: relative;
  padding-left: 26px;
  font-size: 14px;
  line-height: 100%;
  color: #9C9C9C;
  cursor: pointer;
  &::before {
    @include BA;
    left: 0;
    width: 24px;
    height: 24px;
    background-image: url(@/assets/images/icon-item-trash.svg);
    background-size: contain;
    background-position: center;
  }
}
.fields{
  padding-left: 26px;
  .label{
    margin-top: 4px;
  }
  .field + .field{
    margin-top: 8px;
  }
}
.field{
  display: flex;
  align-items: center;
  &_text{
    width: calc(100% - 40px);
  }
}
.icn-add{
  &::before{
    content: '';
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 4px;
    background-color: currentColor;
    mask-image: url('@/assets/images/icon-plus-gl.svg');
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: cover;
  }
}

.cmn-link.is-question{
  font-size: 10px;
  margin-top: 1px;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: bottom;
  &:hover {
    span {
      text-decoration: none;
    }
  }
  img {
    width: 14px;
    height: 14px;
  }
  span{
    display: inline-block;
    text-decoration: underline;
    margin-left: 4px;
  }
}

.modalConfirm {
  .wrap{
    padding: 16px 0 0;
    color: #333;
    font-size: 18px;
  }
  h2{
    color: #333;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.2;
    margin: 0 0 16px;
  }
  p {
    margin: 0;
  }
  a{
    cursor: pointer;
    font-size: 14px;
  }
}
</style>