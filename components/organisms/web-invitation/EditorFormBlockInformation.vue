<template>
  <EditorFormBlock
    title="パーティー情報"
    :isChecked="data.visible"
    :isShowSwitch="false"
    @change="onUpdateView($event)"
  >
    <div>
      <div class="row">
        <InputCalendar
          title="挙式日（開催日）"
          size="full"
          :placeholder="$dayjs(new Date()).format('2027/04/01')"
          :required="true"
          :value="data.contents.date"
          :error="props.error?.contents?.date?.$message"
          :disabled="props.isGuestAnswer"
          @change="onChangeContents('date', $event)"
        />
      </div>
      <div class="row">
        <InputSelect
          title="主なパーティーの種類"
          size="full"
          :options="[
            {
              label:'挙式・披露宴（会場が同じ）',
              value:'ceremony_reception_same_venue'
            },
            {
              label:'挙式・披露宴（会場が別）',
              value:'ceremony_reception_separate_venue'
            },
            {
              label:'挙式のみ',
              value:'ceremony'
            },
            {
              label:'披露宴のみ',
              value:'reception'
            },
            {
              label:'1.5次会',
              value:'1.5_party'
            },
            {
              label:'2次会',
              value:'2_party'
            },
            {
              label:'パーティー その他宴席',
              value:'other_party'
            }
          ]"
          :value="data.contents.type"
          :disabled="props.isGuestAnswer"
          @change="onChangePartyType($event.target.value)"
        />
      </div>

      <div
        class="group"
        v-for="(event, eventIndex) in data.contents.events" :key="eventIndex"
      >
        <div v-if="data?.contents?.type == 'ceremony_reception_same_venue' && eventIndex  == 0 || data?.contents.type == 'ceremony_reception_separate_venue' && eventIndex  == 0 || data?.contents.type == 'ceremony' && eventIndex  == 0">
          <h3>挙式</h3>
          <div class="input-error" v-if="props.error?.contents?.events?.[`${eventIndex}`]?.eventName?.$message">プリセットのパーティー名または他のパーティー名と重複しています</div>
        </div>
        <div v-else-if="event.isOtherParty">
          <h3>パーティー その他宴席</h3>
        </div>
        <div v-else>
          <h3>{{ event.eventName }}</h3>
          <div class="input-error" v-if="props.error?.contents?.events?.[`${eventIndex}`]?.eventName?.$message">プリセットのパーティー名または他のパーティー名と重複しています</div>
        </div>
        <div v-if="event.isOtherParty" class="row">
          <InputText
            title="パーティー その他宴席の名前"
            :required="true"
            size="full"
            :value="event.eventName"
            :error="props.error?.contents?.events?.[`${eventIndex}`]?.eventName?.$message"
            @input="onChangeEventContents('eventName', eventIndex, $event.target.value)"
          />
        </div>
        <!-- ceremonyの表示 -->
        <div
          class="schedules"
          v-if="event.plans"
        >
          <div class="schedule_header">
            <span class="schedule_label"></span>
            <div>時間</div>
            <div>分</div>
            <div></div>
          </div>
          <div
            class="schedule"
            v-for="(plan, planIndex) in event.plans"
            :key="planIndex"
          >
            <span v-if="data.contents.type == 'reception' && planIndex == 2" class="schedule_label" :disabled="!plan.isShowPlan">お披楽喜</span>
            <span v-else class="schedule_label" :disabled="!plan.isShowPlan">{{ labelData[planIndex] }}</span>
            <InputSelect
              :value="plan.hour"
              :options="hourData"
              :disabled="!plan.isShowPlan"
              @change="onChangePlanContents('hour', eventIndex, planIndex, 'plans', $event.target.value)"
            />
            <InputSelect
              :value="plan.minute"
              :options="minuteData"
              :disabled="!plan.isShowPlan"
              @change="onChangePlanContents('minute', eventIndex, planIndex, 'plans', $event.target.value)"
            />
            <template v-if="planIndex != 1">
              <InputCheckSingle
                class="check"
                :value="plan.isShowPlan"
                :checked="plan.isShowPlan"
                :disabled="planIndex == 1 ? true : false"
                @change="onChangePlanContents('isShowPlan', eventIndex, planIndex, 'plans', $event)"
              />
            </template>
            <template v-else>
              <span class="check_required">必須</span>
            </template>
          </div>
        </div>
        <div
          class="schedules"
          v-if="event.otherVenue || data?.contents.type == 'ceremony_reception_separate_venue' && eventIndex == 0 || data?.contents.type == 'ceremony_reception_same_venue' && eventIndex == 0"
        >
          <div class="row row-m">
            <InputCheckSingle
              :label="getSupplementLabel(event, eventIndex, false, false)"
              :value="event.isShowOtherText"
              :checked="event.isShowOtherText"
              @change="onChangeEventContents('isShowOtherText', eventIndex, $event)"
            />
          </div>
          <transition
            @before-enter="beforeEnter"
            @enter="enter"
            @leave="leave"
          >
            <div v-show="event.isShowOtherText" class="row accordion">
              <MessageEditor
                title="その他の案内"
                placeholder=""
                :message="event.otherText"
                :isShowHeader="false"
                :isShowMessageModal="false"
                :isShowReset="false"
                :isShowAlgin="false"
                :isShowFilterSettings="false"
                @input="onChangeEventContents('otherText', eventIndex, $event)"
              ></MessageEditor>
            </div>
          </transition>
          <div v-show="data?.contents.type != 'ceremony_reception_same_venue' && eventIndex == 0 || eventIndex != 0" class="row accordion">
            <div class="row">
              <InputTextarea
                title="会場名"
                size="full"
                placeholder="●●●●●●●ホテル"
                :value="event.otherVenue"
                @input="onChangeEventContents('otherVenue', eventIndex, $event.target.value)"
              />
            </div>
            <div class="row">
              <InputText
                title="会場名よみがな"
                size="full"
                placeholder="●●●●●●●ホテル"
                :value="event.otherVenue_kana"
                @input="onChangeEventContents('otherVenue_kana', eventIndex, $event.target.value)"
              />
            </div>
            <div class="row">
              <InputZipCode
                title="会場の郵便番号（ハイフンなしで入力）"
                size="full"
                placeholder="1234567"
                :value="event.otherZip"
                :isShowZipLink="false"
                @change="onChangePostalCode('otherAddress', 'otherZip', eventIndex, $event)"
              />
            </div>
            <div class="row row-m">
              <InputTextarea
                title="会場の住所"
                size="full"
                placeholder="東京都中野区上高田3-39-13　山新新井薬師駅前ビル4階"
                :value="event.otherAddress"
                @input="onChangeEventContents('otherAddress', eventIndex, $event.target.value)"
              />
            </div>
            <div class="row">
              <InputTel
                title="会場の電話番号"
                size="full"
                placeholder="00-0000-0000"
                :value="event.otherTel"
                @update="onChangeEventContents('otherTel', eventIndex, $event)"
              />
            </div>
            <div class="row">
              <InputUrl
                title="会場のホームページURL"
                size="full"
                placeholder="https://xxxxxxxxxx.co.jp"
                :value="event.otherUrl"
                @input="onChangeEventContents('otherUrl', eventIndex, $event.target.value)"
              />
            </div>
            <div class="row row-m">
              <InputCheckSingle
                label="Google Mapを表示する"
                :value="event.isShowOtherMaps"
                :checked="event.isShowOtherMaps"
                @change="onChangeEventContents('isShowOtherMaps', eventIndex, $event)"
              />
            </div>
            <div class="row" v-if="event.isShowOtherMaps">
              <GoogleMaps
                :address="event.otherAddress"
              ></GoogleMaps>
              <p class="map_text">※住所によっては正確な位置が表示されない可能性があります 送付前に必ず確認をお願い致します</p>
            </div>
            <div class="row row-m">
              <InputCheckSingle
                label="画像を配置する"
                :value="event.isShowOtherVisual"
                :checked="event.isShowOtherVisual"
                @change="onChangeEventContents('isShowOtherVisual', eventIndex, $event)"
              />
            </div>
            <div :id="'information-other-' + eventIndex" class="row images_area" v-if="event.isShowOtherVisual == true">
              <ImageSelect
                :qr-id="'information-other-' + eventIndex"
                :name="'informationOtherImage' + eventIndex"
                :images="event.otherImages"
                :aspect="props?.aspect?.information ? props.aspect.information : { width: 59, height: 42 }"
                :imageUploadType="props.imageUploadType"
                :loading-items="props.loadingItems"
                @change-images="onChangeEventContents('otherImages', eventIndex, $event)"
                @update-loading-items="onLoadImageUpdate($event)"
                @remove-loading-items="onLoadImageRemove($event)"
              ></ImageSelect>
            </div>
          </div>
        </div>
        <div
          class="schedules"
          v-if="event.otherPlans"
        >
          <h3>披露宴</h3>
          <div class="schedule_header">
            <span class="schedule_label"></span>
            <div>時間</div>
            <div>分</div>
            <div></div>
          </div>
          <div class="schedule" v-for="(plan, otherPlanIndex) in event.otherPlans" :key="otherPlanIndex">
            <span v-if="data.contents.type == 'ceremony_reception_same_venue' && otherPlanIndex == 2 || data.contents.type == 'ceremony_reception_separate_venue' && otherPlanIndex == 2" class="schedule_label" :disabled="!plan.isShowPlan">お披楽喜</span>
            <span v-else class="schedule_label" :disabled="!plan.isShowPlan">{{ labelData[otherPlanIndex] }}</span>
            <InputSelect
              :value="plan.hour"
              :options="hourData"
              :disabled="!plan.isShowPlan"
              @change="onChangePlanContents('hour', eventIndex, otherPlanIndex, 'otherPlans', $event.target.value)"
            />
            <InputSelect
              :value="plan.minute"
              :options="minuteData"
              :disabled="!plan.isShowPlan"
              @change="onChangePlanContents('minute', eventIndex, otherPlanIndex, 'otherPlans', $event.target.value)"
            />
            <template v-if="otherPlanIndex != 1">
              <InputCheckSingle
                class="check"
                :value="plan.isShowPlan"
                :checked="plan.isShowPlan"
                :disabled="otherPlanIndex == 1 ? true : false"
                @change="onChangePlanContents('isShowPlan', eventIndex, otherPlanIndex, 'otherPlans', $event)"
              />
            </template>
            <template v-else>
              <span class="check_required">必須</span>
            </template>
          </div>
        </div>
        <div
          class="schedules"
        >
          <div class="row row-s" v-if="data.contents.type != 'ceremony' || eventIndex != 0">
            <div class="label">会費・ご祝儀設定</div>
            <div>
              <InputRadio
                :name="'ceremonyFee' + eventIndex"
                :items="[
                  {
                    value: 'gift_system',
                    checked: event.feeOption == 'gift_system' ? true : false,
                    label: 'ご祝儀制',
                  }
                ]"
                @change="onChangeEventContents('feeOption', eventIndex, 'gift_system')"
              />
            </div>
            <div>
              <InputRadio
                :name="'ceremonyFee' + eventIndex"
                :items="[
                  {
                    value: 'membership_fee_common',
                    checked: event.feeOption == 'membership_fee_common' ? true : false,
                    label: '会費制（男女共通）',
                  }
                ]"
                @change="onChangeEventContents('feeOption', eventIndex, 'membership_fee_common')"
              />
            </div>
            <div v-if="event.feeOption == 'membership_fee_common'" class="feeBox flex">
              <InputText
                title="男女共通"
                placeholder="10000"
                size="full"
                pattern="number"
                inputmode="numeric"
                :value="event.feeAmount[0]"
                @input="onChangeFeeContents('feeAmount', eventIndex, 0, $event.target.value)"
              />
            </div>
            <div>
              <InputRadio
                :name="'ceremonyFee' + eventIndex"
                :items="[
                  {
                    value: 'membership_fee_separate',
                    checked: event.feeOption == 'membership_fee_separate' ? true : false,
                    label: '会費制（男女別）',
                  }
                ]"
                @change="onChangeEventContents('feeOption', eventIndex, 'membership_fee_separate')"
              />
            </div>
            <div v-if="event.feeOption == 'membership_fee_separate'" class="feeBox flex">
              <div class="column">
                <InputText
                  title="男性"
                  placeholder="10000"
                  size="full"
                  pattern="number"
                  inputmode="numeric"
                  :value="event.feeAmount[0]"
                  @input="onChangeFeeContents('feeAmount', eventIndex, 0, $event.target.value)"
                />
              </div>
              <div class="column">
                <InputText
                  title="女性"
                  placeholder="10000"
                  size="full"
                  pattern="number"
                  inputmode="numeric"
                  :value="event.feeAmount[1]"
                  @input="onChangeFeeContents('feeAmount', eventIndex, 1, $event.target.value)"
                />
              </div>
            </div>
          </div>
          <div class="row row-m">
            <InputCheckSingle
              :label="getSupplementLabel(event, eventIndex, false, true)"
              :value="event.isShowText"
              :checked="event.isShowText"
              @change="onChangeEventContents('isShowText', eventIndex, $event)"
            />
          </div>
          <transition
            @before-enter="beforeEnter"
            @enter="enter"
            @leave="leave"
          >
            <div v-show="event.isShowText" class="row accordion">
              <MessageEditor
                title="その他の案内"
                placeholder=""
                :message="event.text"
                :isShowHeader="false"
                :isShowMessageModal="false"
                :isShowReset="false"
                :isShowAlgin="false"
                :isShowFilterSettings="false"
                @input="onChangeEventContents('text', eventIndex, $event)"
              ></MessageEditor>
            </div>
          </transition>
          <div class="row">
            <InputTextarea
              title="会場名"
              size="full"
              placeholder="●●●●●●●ホテル"
              :value="event.venue"
              @input="onChangeEventContents('venue', eventIndex, $event.target.value)"
            />
          </div>
          <div class="row">
            <InputText
              title="会場名よみがな"
              size="full"
              placeholder="●●●●●●●ホテル"
              :value="event.venue_kana"
              @input="onChangeEventContents('venue_kana', eventIndex, $event.target.value)"
            />
          </div>
          <div class="row">
            <InputZipCode
              title="会場の郵便番号（ハイフンなしで入力）"
              size="full"
              placeholder="1234567"
              :value="event.zip"
              :isShowZipLink="false"
              @change="onChangePostalCode('address', 'zip', eventIndex, $event)"
            />
          </div>
          <div class="row row-m">
            <InputTextarea
              title="会場の住所"
              size="full"
              placeholder="東京都中野区上高田3-39-13　山新新井薬師駅前ビル4階"
              :value="event.address"
              @input="onChangeEventContents('address', eventIndex, $event.target.value)"
            />
          </div>
          <div class="row">
            <InputTel
              title="会場の電話番号"
              size="full"
              placeholder="00-0000-0000"
              :value="event.tel"
              @update="onChangeEventContents('tel', eventIndex, $event)"
            />
          </div>
          <div class="row">
            <InputUrl
              title="会場のホームページURL"
              size="full"
              placeholder="https://xxxxxxxxxx.co.jp"
              :value="event.url"
              @input="onChangeEventContents('url', eventIndex, $event.target.value)"
            />
          </div>
          <div class="row row-m">
            <InputCheckSingle
              :label="getSupplementLabel(event, eventIndex, true, true)"
              :value="event?.isShowAddressText"
              :checked="event?.isShowAddressText"
              @change="onChangeEventContents('isShowAddressText', eventIndex, $event)"
            />
          </div>
          <transition
            @before-enter="beforeEnter"
            @enter="enter"
            @leave="leave"
          >
            <div v-show="event?.isShowAddressText" class="row accordion">
              <MessageEditor
                title="その他の案内"
                placeholder=""
                :message="event?.addressText"
                :isShowHeader="false"
                :isShowMessageModal="false"
                :isShowReset="false"
                :isShowAlgin="false"
                :isShowFilterSettings="false"
                @input="onChangeEventContents('addressText', eventIndex, $event)"
              ></MessageEditor>
            </div>
          </transition>
          <div class="row row-m">
            <InputCheckSingle
              label="Google Mapを表示する"
              :value="event.isShowMaps"
              :checked="event.isShowMaps"
              @change="onChangeEventContents('isShowMaps', eventIndex, $event)"
            />
          </div>
          <div class="row map_wrap" v-if="event.isShowMaps">
            <GoogleMaps
              :address="event.address"
            ></GoogleMaps>
            <p class="map_text">※住所によっては正確な位置が表示されない可能性があります 送付前に必ず確認をお願い致します</p>
          </div>
          <div class="row row-m">
            <InputCheckSingle
              label="画像を配置する"
              :value="event.isShowVisual"
              :checked="event.isShowVisual"
              @change="onChangeEventContents('isShowVisual', eventIndex, $event)"
            />
          </div>
          <div :id="'information-' + eventIndex" class="row images_area" v-if="event.isShowVisual == true">
            <ImageSelect
              :qr-id="'information-' + eventIndex"
              :name="'informationImage' + eventIndex"
              :images="event.images"
              :aspect="null"
              :imageUploadType="props.imageUploadType"
              :loading-items="props.loadingItems"
              @change-images="onChangeEventContents('images', eventIndex, $event)"
              @update-loading-items="onLoadImageUpdate($event)"
              @remove-loading-items="onLoadImageRemove($event)"
            ></ImageSelect>
          </div>
        </div>

        <div v-if="eventIndex  != 0" class="row cmn-alignright">
          <a
            class="delete"
            @click="onDeleteContents(eventIndex)"
            :disabled="props.isGuestAnswer"
          >
            {{event.eventName ? event.eventName : 'パーティー その他宴席'}}を削除する
          </a>
        </div>
      </div>

      <div class="row popup_wrap">
        <ButtonMainColor
          class="popup_open"
          baseColor="reversal"
          :buttonsize="200"
          :disabled="props.isGuestAnswer"
          @click="onToggleMenu(index)"
        >他のパーティーを追加する</ButtonMainColor>
        <transition name="fade">
          <ul v-if="activeMenu === index" class="popup_menu">
            <li><a @click="onCreateContents('1.5_party')">1.5次会</a></li>
            <li><a @click="onCreateContents('2_party')">2次会</a></li>
            <li><a @click="onCreateContents('other_party')">パーティー その他宴席</a></li>
          </ul>
        </transition>
      </div>
    </div>
  </EditorFormBlock>
</template>

<script lang="ts" setup>
const { $dayjs } : any = useNuxtApp();
interface Props {
  data: {
    name: 'パーティー情報',
    id: 'information',
    visible: boolean,
    contents: {
      date: string,
      type: string,
      events: {
        isOtherParty: boolean,
        eventName: string,
        plans: {
          isShowPlan: boolean,
          hour: string,
          minute: string
        }[]
        otherPlans: {
          isShowPlan: boolean,
          hour: string,
          minute: string
        }[],
        venue: string,
        venue_kana: string,
        zip: string,
        address: string,
        addressText: string | null,
        isShowAddressText: boolean,
        isShowMaps: boolean,
        tel: string,
        url: string,
        feeOption: string,
        feeAmount: string[],
        isShowText: boolean,
        text: string,
        otherVenue: string,
        otherVenue_kana: string,
        otherZip: string,
        otherAddress: string,
        otherAddressText: string | null,
        isShowOtherAddressText: boolean,
        isShowOtherMaps: boolean,
        otherTel: string,
        otherUrl: string,
        isShowOtherText: boolean,
        otherText: string,
        isShowVisual: boolean,
        selectVisual: string,
        images: string[],
        isShowOtherVisual: boolean,
        otherSelectVisual: string,
        otherImages: string[],
      }[]
    }
  },
  guestAnswerData: {},
  aspect: {},
  isGuestAnswer: boolean,
  imageUploadType: 'user' | 'admin',
  error?: any,
  loadingItems: LoadingItem[]
};
const props = withDefaults(defineProps<Props>(), {
  isGuestAnswer: false,
  imageUploadType: 'user',
  error: ''
});

const guestAnswerData = ref(props.guestAnswerData);
watch(() => props.guestAnswerData, (newGuestAnswer) => {
  if(newGuestAnswer){
    guestAnswerData.value = newGuestAnswer;
  }
}, { deep: true });

const activeMenu = ref(null);
const onToggleMenu = (index:number) => {
  activeMenu.value = activeMenu.value === index ? null : index;
};
const handleClickOutside = (event:any) => {
  if (!event.target.closest('.popup_open')) {
    activeMenu.value = null;
  }
};
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});

const data = ref(props.data);
watch(() => props, (newVal) => {
  data.value = newVal.data;
}, {
  deep: true
});

// データ定義
const labelData = ref([
  '受　　付',
  '開始時刻',
  '終了予定',
]);
const hourData = ref([
  {
    label:'00',
    value:'0'
  },
  {
    label:'01',
    value:'1'
  },
  {
    label:'02',
    value:'2'
  },
  {
    label:'03',
    value:'3'
  },
  {
    label:'04',
    value:'4'
  },
  {
    label:'05',
    value:'5'
  },
  {
    label:'06',
    value:'6'
  },
  {
    label:'07',
    value:'7'
  },
  {
    label:'08',
    value:'8'
  },
  {
    label:'09',
    value:'9'
  },
  {
    label:'10',
    value:'10'
  },
  {
    label:'11',
    value:'11'
  },
  {
    label:'12',
    value:'12'
  },
  {
    label:'13',
    value:'13'
  },
  {
    label:'14',
    value:'14'
  },
  {
    label:'15',
    value:'15'
  },
  {
    label:'16',
    value:'16'
  },
  {
    label:'17',
    value:'17'
  },
  {
    label:'18',
    value:'18'
  },
  {
    label:'19',
    value:'19'
  },
  {
    label:'20',
    value:'20'
  },
  {
    label:'21',
    value:'21'
  },
  {
    label:'22',
    value:'22'
  },
  {
    label:'23',
    value:'23'
  }
]);
const minuteData = ref([
  {
    label:'00',
    value:'00'
  },
  {
    label:'05',
    value:'05'
  },
  {
    label:'10',
    value:'10'
  },
  {
    label:'15',
    value:'15'
  },
  {
    label:'20',
    value:'20'
  },
  {
    label:'25',
    value:'25'
  },
  {
    label:'30',
    value:'30'
  },
  {
    label:'35',
    value:'35'
  },
  {
    label:'40',
    value:'40'
  },
  {
    label:'45',
    value:'45'
  },
  {
    label:'50',
    value:'50'
  },
  {
    label:'55',
    value:'55'
  }
]);

// 補足事項ラベルの動的生成
const getSupplementLabel = computed(() => {
  return (event: any, index: number, isAddress: boolean = false, isReception: boolean = false) => {
    if (isAddress) {
      return '会場 補足事項を追加する';
    }
    if (event.isOtherParty) {
      return 'パーティー 補足事項を追加する';
    }

    const type = data.value.contents.type;
    const eventName = event.eventName;

    // 挙式・披露宴（会場が同じ）や挙式・披露宴（会場が別）の場合
    if (type === 'ceremony_reception_same_venue' || type === 'ceremony_reception_separate_venue') {
      if (index === 0) {
        if (isReception) {
          return '披露宴 補足事項を追加する';
        } else {
          return '挙式 補足事項を追加する';
        }
      }
    }

    // その他のパターン
    if (eventName === '挙式') {
      return '挙式 補足事項を追加する';
    } else if (eventName === '披露宴') {
      return '披露宴 補足事項を追加する';
    } else if (eventName === '2次会') {
      return '2次会 補足事項を追加する';
    } else if (eventName === '1.5次会') {
      return '1.5次会 補足事項を追加する';
    }

    return '会場 補足事項を追加する';
  };
});

const onCreateContents = (type:string = 'other_party') => {
  let partyData:any = {
    isOtherParty: false,
    eventName: '',
    plans: [
      {
        isShowPlan: true,
        hour: '',
        minute: ''
      },
      {
        isShowPlan: true,
        hour: '',
        minute: ''
      },
      {
        isShowPlan: true,
        hour: '',
        minute: ''
      }
    ],
    venue: '',
    venue_kana: '',
    zip: '',
    address: '',
    addressText: null,
    isShowAddressText: false,
    isShowMaps: false,
    tel: '',
    url: '',
    feeOption: 'gift_system',
    feeAmount: [''],
    isShowText: false,
    text: '',
    isShowVisual: false,
    selectVisual: 'images',
    images: []
  };
  if (type == '1.5_party'){
    partyData.eventName = '1.5次会';
  }else if (type == '2_party'){
    partyData.eventName = '2次会';
  }else if(type == 'other_party'){
    partyData.isOtherParty = true;
  }
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.events.push(partyData);
  data.value = dataUpdated;
  onUpdate(data.value);
};

// コンテンツの変更
const onChangeContents = (id:string, value:string) => {
  let changeValue = value;
  if(id == 'date'){
    const parsedDate = new Date(new Date(value).toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));
    changeValue = isNaN(parsedDate.getTime()) ? null : value;
  }
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents[id] = changeValue;
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onChangePartyType = (value:string) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  let eventData = dataUpdated.contents.events[0];
  let currentType = dataUpdated.contents.type;
  let partyData:any = {};
  let setData = {};

  if(value == 'ceremony_reception_same_venue'){
    partyData = {
      plans: [
        {
          isShowPlan: eventData.plans[0].isShowPlan ? eventData.plans[0].isShowPlan : true,
          hour: eventData.plans[0].hour ? eventData.plans[0].hour : '',
          minute: eventData.plans[0].minute ? eventData.plans[0].minute : ''
        },
        {
          isShowPlan: eventData.plans[1].isShowPlan ? eventData.plans[1].isShowPlan : true,
          hour: eventData.plans[1].hour ? eventData.plans[1].hour : '',
          minute: eventData.plans[1].minute ? eventData.plans[1].minute : ''
        },
        {
          isShowPlan: eventData.plans[2].isShowPlan ? eventData.plans[2].isShowPlan : true,
          hour: eventData.plans[2].hour ? eventData.plans[2].hour : '',
          minute: eventData.plans[2].minute ? eventData.plans[2].minute : ''
        }
      ],
      otherPlans: [
        {
          isShowPlan: eventData?.otherPlans?.[0]?.isShowPlan ? eventData.otherPlans[0].isShowPlan : true,
          hour: eventData?.otherPlans?.[0]?.hour ? eventData.otherPlans[0].hour : '',
          minute: eventData?.otherPlans?.[0]?.minute ? eventData.otherPlans[0].minute : ''
        },
        {
          isShowPlan: eventData?.otherPlans?.[1]?.isShowPlan ? eventData.otherPlans[1].isShowPlan : true,
          hour: eventData?.otherPlans?.[1]?.hour ? eventData.otherPlans[1].hour : '',
          minute: eventData?.otherPlans?.[1]?.minute ? eventData.otherPlans[1].minute : ''
        },
        {
          isShowPlan: eventData?.otherPlans?.[2]?.isShowPlan ? eventData.otherPlans[2].isShowPlan : true,
          hour: eventData?.otherPlans?.[2]?.hour ? eventData.otherPlans[2].hour : '',
          minute: eventData?.otherPlans?.[2]?.minute ? eventData.otherPlans[2].minute : ''
        }
      ],
      venue: eventData.venue ? eventData.venue : '',
      venue_kana: eventData.venue_kana ? eventData.venue_kana : '',
      zip: eventData.zip ? eventData.zip : '',
      address: eventData.address ? eventData.address : '',
      addressText: eventData.addressText !== undefined ? eventData.addressText : null,
      isShowAddressText: eventData.isShowAddressText !== undefined ? eventData.isShowAddressText : false,
      isShowMaps: eventData.isShowMaps ? eventData.isShowMaps : false,
      tel: eventData.tel ? eventData.tel : '',
      url: eventData.url ? eventData.url : '',
      feeOption: eventData.feeOption ? eventData.feeOption : 'gift_system',
      feeAmount: eventData.feeAmount ? eventData.feeAmount : [''],
      isShowText: eventData.isShowText ? eventData.isShowText : false,
      text: eventData.text ? eventData.text : '',
      isShowOtherText: eventData.isShowOtherText ? eventData.isShowOtherText : false,
      otherText: eventData.otherText ? eventData.otherText : '',
      isShowVisual: eventData.isShowVisual ? eventData.isShowVisual : false,
      selectVisual: eventData.selectVisual ? eventData.selectVisual : 'images',
      images: eventData.images ? eventData.images : []
    };
    if(currentType == 'reception'){
      let plansData = JSON.parse(JSON.stringify(eventData.plans));
      partyData.otherPlans = [
        {
          isShowPlan: plansData[0].isShowPlan ? plansData[0].isShowPlan : true,
          hour: plansData[0].hour ? plansData[0].hour : '',
          minute: plansData[0].minute ? plansData[0].minute : ''
        },
        {
          isShowPlan: plansData[1].isShowPlan ? plansData[1].isShowPlan : true,
          hour: plansData[1].hour ? plansData[1].hour : '',
          minute: plansData[1].minute ? plansData[1].minute : ''
        },
        {
          isShowPlan: plansData[2].isShowPlan ? plansData[2].isShowPlan : true,
          hour: plansData[2].hour ? plansData[2].hour : '',
          minute: plansData[2].minute ? plansData[2].minute : ''
        }
      ];
      partyData.plans = [
        {
          isShowPlan: true,
          hour: '',
          minute: ''
        },
        {
          isShowPlan: true,
          hour: '',
          minute:''
        },
        {
          isShowPlan: true,
          hour: '',
          minute: ''
        }
      ];
    }
    if(currentType == 'ceremony'){
      partyData.isShowOtherText = eventData?.isShowText ? eventData.isShowText : false;
      partyData.otherText = eventData?.text ? eventData.text : '';
      partyData.isShowText = false;
      partyData.text = '';
    }
  }else if(value == 'ceremony_reception_separate_venue'){
    partyData = {
      plans: [
        {
          isShowPlan: eventData.plans[0].isShowPlan ? eventData.plans[0].isShowPlan : true,
          hour: eventData.plans[0].hour ? eventData.plans[0].hour : '',
          minute: eventData.plans[0].minute ? eventData.plans[0].minute : ''
        },
        {
          isShowPlan: eventData.plans[1].isShowPlan ? eventData.plans[1].isShowPlan : true,
          hour: eventData.plans[1].hour ? eventData.plans[1].hour : '',
          minute: eventData.plans[1].minute ? eventData.plans[1].minute : ''
        },
        {
          isShowPlan: eventData.plans[2].isShowPlan ? eventData.plans[2].isShowPlan : true,
          hour: eventData.plans[2].hour ? eventData.plans[2].hour : '',
          minute: eventData.plans[2].minute ? eventData.plans[2].minute : ''
        }
      ],
      venue: eventData.venue ? eventData.venue : '',
      venue_kana: eventData.venue_kana ? eventData.venue_kana : '',
      zip: eventData.zip ? eventData.zip : '',
      address: eventData.address ? eventData.address : '',
      isShowMaps: eventData.isShowMaps ? eventData.isShowMaps : false,
      tel: eventData.tel ? eventData.tel : '',
      url: eventData.url ? eventData.url : '',
      feeOption: eventData.feeOption ? eventData.feeOption : 'gift_system',
      feeAmount: eventData.feeAmount ? eventData.feeAmount : [''],
      isShowText: eventData.isShowText ? eventData.isShowText : false,
      text: eventData.text ? eventData.text : '',
      isShowVisual: eventData.isShowVisual ? eventData.isShowVisual : false,
      selectVisual: eventData.selectVisual ? eventData.selectVisual : 'images',
      images: eventData.images ? eventData.images : [],
      otherPlans: [
        {
          isShowPlan: eventData?.otherPlans?.[0]?.isShowPlan ? eventData.otherPlans[0].isShowPlan : true,
          hour: eventData?.otherPlans?.[0]?.hour ? eventData.otherPlans[0].hour : '',
          minute: eventData?.otherPlans?.[0]?.minute ? eventData.otherPlans[0].minute : ''
        },
        {
          isShowPlan: eventData?.otherPlans?.[1]?.isShowPlan ? eventData.otherPlans[1].isShowPlan : true,
          hour: eventData?.otherPlans?.[1]?.hour ? eventData.otherPlans[1].hour : '',
          minute: eventData?.otherPlans?.[1]?.minute ? eventData.otherPlans[1].minute : ''
        },
        {
          isShowPlan: eventData?.otherPlans?.[2]?.isShowPlan ? eventData.otherPlans[2].isShowPlan : true,
          hour: eventData?.otherPlans?.[2]?.hour ? eventData.otherPlans[2].hour : '',
          minute: eventData?.otherPlans?.[2]?.minute ? eventData.otherPlans[2].minute : ''
        }
      ],
      otherVenue: eventData.otherVenue ? eventData.otherVenue : '',
      otherVenue_kana: eventData.otherVenue_kana ? eventData.otherVenue_kana : '',
      otherZip: eventData.otherZip ? eventData.otherZip : '',
      otherAddress: eventData.otherAddress ? eventData.otherAddress : '',
      otherAddressText: eventData.otherAddressText !== undefined ? eventData.otherAddressText : null,
      isShowOtherAddressText: eventData.isShowOtherAddressText !== undefined ? eventData.isShowOtherAddressText : false,
      isShowOtherMaps: eventData.isShowOtherMaps ? eventData.isShowOtherMaps : false,
      otherTel: eventData.otherTel ? eventData.otherTel : '',
      otherUrl: eventData.otherUrl ? eventData.otherUrl : '',
      isShowOtherText: eventData.isShowOtherText ? eventData.isShowOtherText : false,
      otherText: eventData.otherText ? eventData.otherText : '',
      isShowOtherVisual: eventData.isShowOtherVisual ? eventData.isShowOtherVisual : false,
      otherSelectVisual: eventData.otherSelectVisual ? eventData.otherSelectVisual : 'images',
      otherImages: eventData.otherImages ? eventData.otherImages : []
    };
    if(currentType == 'reception'){
      let plansData = JSON.parse(JSON.stringify(eventData.plans));
      partyData.otherPlans = [
        {
          isShowPlan: plansData[0].isShowPlan ? plansData[0].isShowPlan : true,
          hour: plansData[0].hour ? plansData[0].hour : '',
          minute: plansData[0].minute ? plansData[0].minute : ''
        },
        {
          isShowPlan: plansData[1].isShowPlan ? plansData[1].isShowPlan : true,
          hour: plansData[1].hour ? plansData[1].hour : '',
          minute: plansData[1].minute ? plansData[1].minute : ''
        },
        {
          isShowPlan: plansData[2].isShowPlan ? plansData[2].isShowPlan : true,
          hour: plansData[2].hour ? plansData[2].hour : '',
          minute: plansData[2].minute ? plansData[2].minute : ''
        }
      ];
      partyData.plans = [
        {
          isShowPlan: true,
          hour: '',
          minute: ''
        },
        {
          isShowPlan: true,
          hour: '',
          minute:''
        },
        {
          isShowPlan: true,
          hour: '',
          minute: ''
        }
      ];
    }
    if(currentType == 'ceremony'){
      partyData.isShowOtherText = eventData?.isShowText ? eventData.isShowText : false;
      partyData.otherText = eventData?.text ? eventData.text : '';
      partyData.isShowText = false;
      partyData.text = '';
    }
  }else {
    partyData = {
      isOtherParty: eventData.isOtherParty ? eventData.isOtherParty : '',
      eventName: eventData.eventName ? eventData.eventName : '',
      plans: [
        {
          isShowPlan: eventData.plans[0].isShowPlan ? eventData.plans[0].isShowPlan : true,
          hour: eventData.plans[0].hour ? eventData.plans[0].hour : '',
          minute: eventData.plans[0].minute ? eventData.plans[0].minute : ''
        },
        {
          isShowPlan: eventData.plans[1].isShowPlan ? eventData.plans[1].isShowPlan : true,
          hour: eventData.plans[1].hour ? eventData.plans[1].hour : '',
          minute: eventData.plans[1].minute ? eventData.plans[1].minute : ''
        },
        {
          isShowPlan: eventData.plans[2].isShowPlan ? eventData.plans[2].isShowPlan : true,
          hour: eventData.plans[2].hour ? eventData.plans[2].hour : '',
          minute: eventData.plans[2].minute ? eventData.plans[2].minute : ''
        }
      ],
      venue: eventData.venue ? eventData.venue : '',
      venue_kana: eventData.venue_kana ? eventData.venue_kana : '',
      zip: eventData.zip ? eventData.zip : '',
      address: eventData.address ? eventData.address : '',
      isShowMaps: eventData.isShowMaps ? eventData.isShowMaps : false,
      tel: eventData.tel ? eventData.tel : '',
      url: eventData.url ? eventData.url : '',
      feeOption: eventData.feeOption ? eventData.feeOption : 'gift_system',
      feeAmount: eventData.feeAmount ? eventData.feeAmount : [''],
      isShowText: eventData.isShowText ? eventData.isShowText : false,
      text: eventData.text ? eventData.text : '',
      isShowVisual: eventData.isShowVisual ? eventData.isShowVisual : false,
      selectVisual: eventData.selectVisual ? eventData.selectVisual : 'images',
      images: eventData.images ? eventData.images : []
    };
    if(value == 'ceremony'){
      partyData.eventName = '挙式';
      partyData.feeOption = 'gift_system';
      if(currentType == 'ceremony_reception_same_venue' || currentType == 'ceremony_reception_separate_venue'){
        partyData.isShowText = eventData?.isShowOtherText ? eventData.isShowOtherText : false;
        partyData.text = eventData?.otherText ? eventData.otherText : '';
      }
    }else if(value == 'reception'){
      partyData.eventName = '披露宴';
      if(currentType == 'ceremony_reception_same_venue' || currentType == 'ceremony_reception_separate_venue') {
        let otherPlansData = JSON.parse(JSON.stringify(eventData.otherPlans));
        partyData.plans = [
          {
            isShowPlan: otherPlansData[0].isShowPlan ? otherPlansData[0].isShowPlan : true,
            hour: otherPlansData[0].hour ? otherPlansData[0].hour : '',
            minute: otherPlansData[0].minute ? otherPlansData[0].minute : ''
          },
          {
            isShowPlan: otherPlansData[1].isShowPlan ? otherPlansData[1].isShowPlan : true,
            hour: otherPlansData[1].hour ? otherPlansData[1].hour : '',
            minute: otherPlansData[1].minute ? otherPlansData[1].minute : ''
          },
          {
            isShowPlan: otherPlansData[2].isShowPlan ? otherPlansData[2].isShowPlan : true,
            hour: otherPlansData[2].hour ? otherPlansData[2].hour : '',
            minute: otherPlansData[2].minute ? otherPlansData[2].minute : ''
          }
        ];
      }
    }else if (value == '1.5_party'){
      partyData.eventName = '1.5次会';
    }else if (value == '2_party'){
      partyData.eventName = '2次会';
    }
    if(value == 'other_party'){
      partyData.isOtherParty = true;
      partyData.eventName = '';
    }else{
      partyData.isOtherParty = false;
    }
  }

  dataUpdated.contents.type = value;
  dataUpdated.contents.events[0] = partyData;
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onChangeEventContents = (id:string, index:number, value:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.events[index][id] = value;
  if(!dataUpdated.contents.events[index][id]){
    if(id == 'isShowOtherVisual'){
      dataUpdated.contents.events[index].otherSelectVisual = 'images';
      dataUpdated.contents.events[index].otherImages = [];
    }
    if(id == 'isShowVisual'){
      dataUpdated.contents.events[index].selectVisual = 'images';
      dataUpdated.contents.events[index].images = [];
    }
  }
  if(id == 'feeOption'){
    onCheckGuestAnswerGender(dataUpdated);
  }
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onChangeFeeContents = (id:string, index:number, feeIndex:number, value:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.events[index][id][feeIndex] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onChangePlanContents = (id:string, eventIndex:number,  planIndex:number, planValue:any, value:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.events[eventIndex][planValue][planIndex][id] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onDeleteContents = (index:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.events.splice(index, 1);
  data.value = dataUpdated;
  onUpdate(data.value);
  onCheckGuestAnswerGender(dataUpdated);
};

// ゲスト回答フォームの性別の活性・非活性を判定して切り替え
const onCheckGuestAnswerGender = (dataUpdated) => {
  let guestAnswerDataUpdated = JSON.parse(JSON.stringify(guestAnswerData.value));
  let isMembershipFeeSeparate = false;
  dataUpdated.contents.events.forEach(feeOptionItem => {
    if(feeOptionItem?.feeOption == 'membership_fee_separate') {
      isMembershipFeeSeparate = true;
    }
  });
  let guestAnswerDataIndex = guestAnswerDataUpdated.contents.selectList.findIndex(item => item.title === '性別');
  if(isMembershipFeeSeparate){
    guestAnswerDataUpdated.contents.selectList[guestAnswerDataIndex].disabled = true;
    guestAnswerDataUpdated.contents.selectList[guestAnswerDataIndex].required = true;
    guestAnswerDataUpdated.contents.selectList[guestAnswerDataIndex].visible = true;
  }else{
    guestAnswerDataUpdated.contents.selectList[guestAnswerDataIndex].disabled = false;
  }
  guestAnswerData.value = guestAnswerDataUpdated;
  emit('change', {key: 'guestAnswer', value: guestAnswerData.value})
};

// 郵便番号
const onChangePostalCode = (addressId:string, zipId:string, index:number, value:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  let addressData = '';
  if (value?.prefecture) addressData += value.prefecture;
  if (value?.city) addressData += value.city;
  if (value?.address) addressData += value.address;

  // 変更時のみに限定する
  // もとの住所から変わってる場合のみ
  const oldAddressData = data.value.contents.events[index][addressId];
  if(addressData && (!oldAddressData || oldAddressData.trim() === '')) {
    dataUpdated.contents.events[index][addressId] = addressData;
  }
  dataUpdated.contents.events[index][zipId] = value.postal_code;
  data.value = dataUpdated;
  onUpdate(data.value);
};

// emit
const emit = defineEmits(['change']);
const onUpdateView = (value:boolean) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.visible = value;
  data.value = dataUpdated;
  emit('change', {key: 'information', value: data.value})
};
const onUpdate = (value: any) => {
  emit('change', {key: 'information', value: value})
};

//アップロード中画像
const { onLoadImageUpdate, onLoadImageRemove } = useImageLoading(emit);

// アニメーション
const beforeEnter = (el) => {
  el.style.height = '0';
};

const enter = (el) => {
  el.style.height = el.scrollHeight + 'px';
  el.addEventListener('transitionend', () => {
    el.style.height = null;
  }, { once: true });
};

const leave = (el) => {
  el.style.height = el.scrollHeight + 'px';
  setTimeout(() => {
    el.style.height = '0';
  });
};

</script>

<style lang="scss" scoped>
h3{
  font-size: 14px;
  font-weight: normal;
  margin: 0 0 16px;
  padding: 12px 0 15px;
  color: $color-blacktext2;
  border-bottom: 1px solid $color-grayborder;
}
:deep(){
  textarea{
    min-height: 3.5em;
    resize: none;
  }
}
:deep(.zip .help){
  padding-bottom: 0;
}
.schedules{
  margin-bottom: 24px;
}
.schedule{
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  :deep(label){
    margin-bottom: 0;
  }
  :deep(select.is-placeholder){
    color: #333;
  }
  &:last-child{
    margin-bottom: 0;
  }
  &_label{
    font-size: 14px;
    color: #333;
    margin-right: 12px;
    min-width: 60px;
    &[disabled="true"]{
      opacity: 0.4;
    }
  }
  label{
    width: 50%;
    & + label{
      margin-left: 12px;
    }
  }
  &_header{
    display: flex;
    align-items: center;
    font-size: 12px;
    & > div{
      padding: 0 0 4px;
      &:nth-child(1){
        width: calc(100% - 214px);
      }
      &:nth-child(2),
      &:nth-child(3){
        width: calc(50% - 46px);
      }
    }
  }
}
.row{
  width: 100%;
  margin-bottom: 24px;
  &:has(.cmn-alignright){
    margin-bottom: 12px;
  }
  &.row-s{
    margin-bottom: 8px;
  }
  &.row-m{
    margin-bottom: 12px;
  }
}
.group{
  .check{
    margin-left: 12px;
    &_required{
      display: inline-block;
      min-width: 28px;
      color: #C50000;
      font-size: 12px;
      font-weight: bold;
      margin-left: 8px;
      white-space: nowrap;
    }
  }
  .input-error{
    margin-bottom: 20px;
  }
}

.exclaim{
  font-size: 13px;
  color: #333333;
  margin-top: 16px;
}
.delete{
  text-decoration: none;
  position: relative;
  padding: 0 0 0 22px;
  color: $color-alert;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  cursor: pointer;
  &::before {
    @include BA;
    left: 0;
    width: 18px;
    height: 18px;
    background-image: url('@/assets/images/icon-delete.svg');
  }
  &[disabled=true]{
    pointer-events: none;
    opacity: 0.5;
  }
}
.label{
  font-size: 12px;
  line-height: 1;
  margin-top: 24px;
  margin-bottom: 16px;
  color: #49454F;
  white-space: nowrap;
}

.popup {
  &_open{
    cursor: pointer;
    &::before{
      content: '';
      display: inline-block;
      width: 10px;
      height: 10px;
      margin-right: 4px;
      background-color: currentColor;
      mask-image: url('@/assets/images/icon-plus-gl.svg');
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: cover;
    }
  }
  &_menu {
    text-align: left;
    position: absolute;
    right: 0;
    top: 48px;
    margin: 0;
    padding: 0;
    border-radius: 4px;
    background: #fff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    transition: 0.35s ease-in-out;
    z-index: 2;
    li {
      display: block;
      width: 100%;
    }
    a {
      display: block;
      width: 100%;
      padding: 12px 16px;
      line-height: 1;
      font-size: 16px;
      color: #1C1B1F;
      background: #fff;
      cursor: pointer;
      transition: 0.35s ease-in-out;
      &:hover {
        background: #EFF8FF;
      }
    }
  }
  &_wrap{
    position: relative;
    text-align: right;
    margin: 36px 0 0;
  }
}
.feeBox{
  margin-bottom: 10px;
  padding-left: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .column{
    width: 100%;
    & + .column {
      margin-left: 15px;
    }
  }
}
.delete{
  display: inline-block;
  text-decoration: none;
  margin-bottom: 40px;
  position: relative;
  margin-bottom: 20px;
  padding: 0 0 0 22px;
  color: $color-alert;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  cursor: pointer;
  &::before {
    @include BA;
    left: 0;
    width: 18px;
    height: 18px;
    background-image: url(@/assets/images/icon-delete.svg);
  }
}
.map{
  &_wrap{
    margin-top: -12px;
    padding-left: 24px;
  }
  &_text{
    font-size: 12px;
    margin-bottom: 24px;
    line-height: 1.45;
    color: #E65C7A;
    margin-top: 4px;
    padding-left: 1em;
    text-indent: -1em;
  }
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.35s ease-in-out;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.accordion {
  overflow: hidden;
  width: calc(100% + 4px);
  margin-top: -2px;
  margin-left: -2px;
  padding: 2px;
  transition: height 0.25s ease-in-out;
}

.images_area{
  margin-top: -12px;
  padding-left: 24px;
}
</style>