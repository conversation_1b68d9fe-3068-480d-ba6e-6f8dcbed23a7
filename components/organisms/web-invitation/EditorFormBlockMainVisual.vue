<template>
  <EditorFormBlock
    title="メインビジュアル画像or動画"
    :isShowSwitch="false"
    @change="onUpdateView($event)"
  >
    <p v-if="!props.isLockSpecifiedPosition">メインビジュアルには、画像（3枚まで）<br>または動画（1枚）のいずれかを配置できます。</p>
    <p v-else>メインビジュアルには、画像・動画（{{ props.maxMainVisualImages }}枚まで）を<br>自由に組み合わせて配置できます。</p>
    
    <!-- 位置指定なしの場合 -->
    <ImageSelect
      v-if="!props.isLockSpecifiedPosition"
      name="mainVisualImage"
      select-type="select"
      qr-id="mainVisual"
      :images="data.contents.images"
      :aspect="props.aspect.mainVisual"
      :imageUploadType="props.imageUploadType"
      :loading-items="props.loadingItems"
      :is-video-trimming="true"
      @change-images="onClickImageChange('images', $event)"
      @update-loading-items="onLoadImageUpdate($event)"
      @remove-loading-items="onLoadImageRemove($event)"
    ></ImageSelect>
    
    <!-- 位置指定ありの場合 -->
    <ImageSelect
      v-else
      name="mainVisualImage"
      select-type="multiple-video-select"
      qr-id="mainVisual"
      :images="data.contents.images"
      :aspect="getAspectRatioForPosition"
      :imageUploadType="props.imageUploadType"
      :loading-items="props.loadingItems"
      :is-video-trimming="true"
      :max-images="props.maxMainVisualImages"
      @change-images="onClickImageChange('images', $event)"
      @update-loading-items="onLoadImageUpdate($event)"
      @remove-loading-items="onLoadImageRemove($event)"
    ></ImageSelect>
    <div>
      <InputKanji
        title="新郎（新婦）のお名前"
        size="full"
        placeholder="Shunpei / 俊平 など"
        :value="props.data.contents.groomName"
        :error="props.error?.contents?.groomName?.$message"
        @change="onChangeContents('groomName', $event)"
      />
      <InputKanji
        title="新婦（新郎）のお名前"
        size="full"
        placeholder="Konomi / このみ など"
        :value="props.data.contents.brideName"
        :error="props.error?.contents?.brideName?.$message"
        @change="onChangeContents('brideName', $event)"
      />
    </div>
  </EditorFormBlock>
</template>

<script lang="ts" setup>
import { LoadingItem } from '@/types/LoadingItem';
import { useImageLoading } from '@/composables/useImageLoading';
import { useImageCropping } from '@/composables/useImageCropping';
interface Props {
  data: {
    name: 'メインビジュアル',
    id: string,
    visible: boolean,
    contents: {
      selectVisual: string,
      groomName: string,
      brideName: string,
      images?: string[]
    }
  },
  aspect?: {},
  imageUploadType: 'user' | 'admin',
  error?: string,
  loadingItems: LoadingItem[],
  isLockSpecifiedPosition?: boolean,
  maxMainVisualImages?: number,
  getAspectRatioForPosition?: Function
}
const props = withDefaults(defineProps<Props>(), {
  imageUploadType: 'user',
  error: ''
});

const data = ref(props.data);
watch(() => props.data, (newVal) => {
  data.value = newVal;
}, {
  deep: true
});

// Initialize image cropping composable
const { calculateOptimalCropping } = useImageCropping();

// メインビジュアルの全画像に対してトリミング再計算を行う関数
const recalculateAllImagesCropping = async (imageList: any[]) => {
  if (!props.isLockSpecifiedPosition) {
    return imageList;
  }

  try {
    
    let hasChanges = false;
    const updatedImages = JSON.parse(JSON.stringify(imageList));
    
    for (let index = 0; index < updatedImages.length; index++) {
      try {
        const image = updatedImages[index];
        if (image && (image.uuid || image.src)) {
          const currentAspectRatio = getAspectRatioForPosition(index);
          
          
          // 現在の位置に適したトリミング情報を再計算
          const optimalCropping = await calculateOptimalCropping(image, currentAspectRatio);
          
          if (optimalCropping) {
            
            // 最適なトリミング情報を適用
            updatedImages[index].x = Number(optimalCropping.x);
            updatedImages[index].y = Number(optimalCropping.y);
            updatedImages[index].width = Number(optimalCropping.width);
            updatedImages[index].height = Number(optimalCropping.height);
            
            // CropImageコンポーネント用のcropBoxサイズを更新（アスペクト比反映のため）
            updatedImages[index].cropBoxWidth = Number(optimalCropping.width);
            updatedImages[index].cropBoxHeight = Number(optimalCropping.height);
            
            // 回転とスケールファクターは保持（初期化されていない場合のみセット）
            if (updatedImages[index].rotate === undefined) updatedImages[index].rotate = 0;
            if (updatedImages[index].scaleFactor === undefined) updatedImages[index].scaleFactor = 1;
            
            
            hasChanges = true;
          }
        }
      } catch (error) {
        // エラーが発生しても他の画像の処理を続行
        continue;
      }
    }
    
    if (hasChanges) {
      return updatedImages;
    } else {
      return imageList;
    }
  } catch (error) {
    return imageList; // エラー時は元のデータを返す
  }
};

// トリミング完了タイムスタンプを記録する変数
const lastTrimmingTimestamp = ref(0);


// グローバルイベントリスナーを設定
onMounted(() => {
  if (typeof window !== 'undefined') {
    const handleTrimmingCompleted = (event: CustomEvent) => {
      lastTrimmingTimestamp.value = event.detail.timestamp;
    };
    
    window.addEventListener('trimmingCompleted', handleTrimmingCompleted as EventListener);
    
    // クリーンアップ
    onUnmounted(() => {
      window.removeEventListener('trimmingCompleted', handleTrimmingCompleted as EventListener);
    });
  }
});

// トリミング結果かどうかを判定する関数（時間ベース）
const isTrimmingResult = (newImages: any[], currentImages: any[]): boolean => {
  const now = Date.now();
  const timeSinceLastTrimming = now - lastTrimmingTimestamp.value;
  const isWithinTrimmingWindow = timeSinceLastTrimming < 3000; // 3秒以内
  
  // 3秒以内のトリミング完了後の変更であれば、トリミング結果として判定
  if (isWithinTrimmingWindow) {
    return true;
  }
  
  // 従来のロジックも併用（より厳密な判定）
  if (!newImages || !currentImages) {
    return false;
  }
  
  // 新しい画像が追加された場合（配列長が増加）もトリミング結果の可能性
  if (newImages.length > currentImages.length) {
    const newImage = newImages[newImages.length - 1];
    const hasTrimmingData = newImage && (
      typeof newImage.x === 'number' || 
      typeof newImage.y === 'number' || 
      typeof newImage.width === 'number' || 
      typeof newImage.height === 'number'
    );
    if (hasTrimmingData) {
      return true;
    }
  }
  
  // 既存画像のトリミングデータ変更をチェック
  if (newImages.length === currentImages.length) {
    return newImages.some((newImage, index) => {
      const currentImage = currentImages[index];
      if (!newImage || !currentImage || newImage.uuid !== currentImage.uuid) {
        return false;
      }
      
      // トリミング関連のプロパティが変更されているかチェック
      const trimmingProps = ['x', 'y', 'width', 'height', 'cropBoxWidth', 'cropBoxHeight', 'rotate', 'scaleX', 'scaleY', 'scaleFactor'];
      return trimmingProps.some(prop => newImage[prop] !== currentImage[prop]);
    });
  }
  
  return false;
};

// 画像の変更（並び替えやトリミング再計算を含む）
const onClickImageChange = async (id:string, item:any) => {
  // トリミング結果かどうかを判定
  const isFromTrimming = isTrimmingResult(item, data.value.contents[id] || []);
  
  // トリミング結果の場合は再計算をスキップ
  let finalImages = item;
  if (props.isLockSpecifiedPosition && id === 'images' && !isFromTrimming) {
    finalImages = await recalculateAllImagesCropping(item);
  }
  
  let dataUpdated = JSON.parse(JSON.stringify(props.data));
  dataUpdated.contents[id] = finalImages;
  data.value = dataUpdated;
  
  onUpdate(data.value);
}


// 位置ごとのアスペクト比を取得
const getAspectRatioForPosition = (position: number) => {
  if (props.getAspectRatioForPosition && typeof props.getAspectRatioForPosition === 'function') {
    return props.getAspectRatioForPosition(position);
  }
  
  // フォールバック: 直接aspectプロパティから取得
  const aspectValue = props.aspect as any;
  return aspectValue?.mainVisual || { width: 9, height: 16 };
}

// コンテンツの変更
const onChangeContents = (id:string, value:string) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents[id] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};

const emit = defineEmits(['select', 'change', 'updateLoadingItems', 'removeLoadingItems']);
const onUpdateView = (value:boolean) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.visible = value;
  data.value = dataUpdated;
  emit('change', {key: 'mainVisual', value: data.value})
};
const onUpdate = (value: any) => {
  emit('change', {key: 'mainVisual', value: value})
};

//アップロード中画像
const { onLoadImageUpdate, onLoadImageRemove } = useImageLoading(emit);
</script>

<style lang="scss" scoped>
p {
  color: #333333;
  font-size: 13px;
  margin-bottom: 16px;
}
a{
  display: block;
  font-size: 12px;
  margin: 12px 0;
}
.row{
  margin-bottom: 24px;
  &:last-child{
    margin-bottom: 0;
  }
}
:deep(.imageSelect){
  margin-bottom: 24px;
}

</style>