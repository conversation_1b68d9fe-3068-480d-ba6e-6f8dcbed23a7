<template>
  <EditorFormBlock
    title="挨拶・メッセージ"
    :isChecked="data.visible"
    @change="onUpdateView($event)"
  >
    <div v-if="data.visible">
      <MessageEditor
        title="挨拶・メッセージ"
        :message="data.contents.message"
        :textAlign="data.contents.textAlign"
        :exampleType="ExampleType1Enum.EditorGreeting"
        :formUpdated="formUpdated"
        resetId="greeting"
        :isValidateNgWord="true"
        @input="onChangeContents('message', $event)"
        @align="onChangeContents('textAlign', $event)"
      ></MessageEditor>
      <div class="row">
        <InputCheckSingle
          label="画像or動画を配置する"
          :checked="data.contents.isShowVisual"
          @change="onChangeContents('isShowVisual', $event)"
        />
        <div class="images_area" v-if="data.contents.isShowVisual == true">
          <ImageSelect
            name="messageVisualImage"
            select-type="select"
            qr-id="message"
            :images="data.contents.images"
            :aspect="props.aspect.message"
            :imageUploadType="props.imageUploadType"
            :loading-items="props.loadingItems"
            :isShowVideoMenu="true"
            @change-images="onChangeContents('images', $event)"
            @update-loading-items="onLoadImageUpdate($event)"
            @remove-loading-items="onLoadImageRemove($event)"
          ></ImageSelect>
        </div>
      </div>
    </div>
  </EditorFormBlock>
</template>

<script lang="ts" setup>
import { LoadingItem } from '@/types/LoadingItem';
import { useImageLoading } from '@/composables/useImageLoading';
interface Props {
  data: {
    name: '挨拶・メッセージ',
    id: string,
    visible: boolean,
    contents: {
      isShowVisual?: boolean,
      selectVisual?: string,
      images?: string[],
      movie?: string,
      textAlign: 'left' | 'center' | 'right',
      message?: string
    }
  },
  formUpdated?: any,
  aspect?: {},
  imageUploadType: 'user' | 'admin',
  error?: string,
  loadingItems: LoadingItem[]
}
const props = withDefaults(defineProps<Props>(), {
  imageUploadType: 'user',
  formUpdated: [],
  error: '',
});
const data = ref(props.data);
const formUpdated = ref(props.formUpdated);
watch(() => props, (newVal) => {
  data.value = newVal.data;
  formUpdated.value = newVal.formUpdated;
}, {
  deep: true
});


// コンテンツの変更
const onChangeRadio = ($event:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.selectVisual = $event;
  data.value = dataUpdated;
  onUpdate(data.value);
}
const onChangeContents = (id:string, value:string) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents[id] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};

// emit
const emit = defineEmits(['select', 'change', 'updateLoadingItems', 'removeLoadingItems']);
const onUpdateView = (value:boolean) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.visible = value;
  data.value = dataUpdated;
  emit('change', {key: 'message', value: data.value})
};
const onUpdate = (value: any) => {
  emit('change', {key: 'message', value: value})
};
//アップロード中画像
const { onLoadImageUpdate, onLoadImageRemove } = useImageLoading(emit);
</script>

<style lang="scss" scoped>
a{
  display: block;
  font-size: 12px;
  margin: 12px 0;
}
.row{
  margin-top: 24px;
}
.images_area{
  margin-top: 12px;
  margin-bottom: 24px;
}
:deep(textarea){
  min-height: 23em;
}
:deep(.images_area){
  margin-top: 4px;
  margin-bottom: 0;
}
:deep(.layoutEditor_header .title){
  margin-top: 8px;
}
</style>