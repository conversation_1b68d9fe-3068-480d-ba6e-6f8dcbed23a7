<template>
  <EditorFormBlock
    title="新郎・新婦プロフィール"
    :isChecked="data.visible"
    @change="onUpdateView($event)"
  >
    <div v-if="data.visible">
      <div
        :id="'profile-' + index"
        class="profile"
        v-for="(content, index) in data.contents"
        :key="`profile-${index}`"
      >
        <h3 class="cmn-title" v-if="index == 0">新郎（新婦）プロフィール</h3>
        <h3 class="cmn-title" v-else-if="index == 1">新婦（新郎）プロフィール</h3>
        <h3 class="cmn-title" v-else>家族プロフィール{{ index - 1 }}</h3>
        <InputCheckSingle
          label="画像（3枚まで）を配置する"
          :value="content.isShowVisual"
          :checked="content.isShowVisual"
          @change="onChangeContents(index, 'isShowVisual', $event)"
        />
        <div class="images_area" v-if="content.isShowVisual == true">
          <ImageSelect
            :name="'profileVisualImage' + index"
            :qr-id="'profile-' + index"
            :images="content.images"
            :aspect="props.aspect.profile"
            :imageUploadType="props.imageUploadType"
            :loading-items="props.loadingItems"
            @change-images="onClickImageChange(index, 'images', $event)"
            @update-loading-items="onLoadImageUpdate($event)"
            @remove-loading-items="onLoadImageRemove($event)"
          ></ImageSelect>
        </div>

        <div class="row">
          <InputKanji
            title="お名前（Web 招待状にそのまま表示されます）"
            size="full"
            placeholder="岩井 俊平"
            :value="content.name"
            @change="onChangeContents(index, 'name', $event)"
          />
        </div>

        <InputCheckSingle
          label="肩書きを表示する"
          :value="content.isShowRole"
          :checked="content.isShowRole"
          @change="onChangeContents(index, 'isShowRole', $event)"
        />
        <div class="row" v-if="content.isShowRole">
          <template v-if="index < 2">
            <InputText
              title="肩書き（Web 招待状にそのまま表示されます）"
              size="full"
              placeholder="新郎"
              :value="content.role"
              @input="onChangeContents(index, 'role', $event.target.value)"
            />
          </template>
          <template v-else>
            <InputText
              title="肩書き（Web 招待状にそのまま表示されます）"
              size="full"
              placeholder="長男"
              :value="content.role"
              @input="onChangeContents(index, 'role', $event.target.value)"
            />
          </template>
        </div>

        <MessageEditor
          title="メッセージ"
          :message="content.message"
          :textAlign="content.textAlign"
          :exampleType="onSetExampleType(index)"
          :formUpdated="formUpdated"
          :isShowFilterSettings="false"
          resetId="profile"
          :isValidateNgWord="true"
          @input="onChangeContents(index, 'message', $event)"
          @align="onChangeContents(index, 'textAlign', $event)"
        ></MessageEditor>

        <div v-if="index >= 2" class="row cmn-alignright">
          <a class="delete" @click="onClickDeleteProfile(index)">家族プロフィール{{ index - 1 }}を削除する</a>
        </div>
      </div>

      <div class="button_wrap">
        <ButtonMainColor
          baseColor="reversal"
          class="icn-add"
          :buttonsize="200"
          @click="onClickAddProfile"
        >家族プロフィールを追加する</ButtonMainColor>
      </div>
    </div>
  </EditorFormBlock>
</template>

<script lang="ts" setup>
import { LoadingItem } from '@/types/LoadingItem';
import { useImageLoading } from '@/composables/useImageLoading';
interface Props {
  data: {
    name: '新郎・新婦プロフィール',
    id: 'profile',
    visible: boolean,
    contents: {
      isShowVisual: true,
      selectVisual: 'images' | 'movie',
      images: {
        src: string,
        width: string,
        height: string,
        x: string,
        y: string
      }[],
      movie: {
        src: string,
        width: string,
        height: string,
        x: string,
        y: string
      }[],
      name: string,
      isShowRole: boolean,
      role: string,
      textAlign: 'left' | 'center' | 'right',
      message: string,
    }[]
  },
  formUpdated: any,
  aspect?: {},
  imageUploadType: 'user' | 'admin',
  error?: string,
  loadingItems: LoadingItem[]
}
const props = withDefaults(defineProps<Props>(), {
  imageUploadType: 'user',
  formUpdated: [],
  error: ''
});

const data = ref(props.data);
const formUpdated = ref(props.formUpdated);
watch(() => props, (newVal) => {
  data.value = newVal.data;
  formUpdated.value = newVal.formUpdated;
}, {
  deep: true
});

const onSetExampleType = (index: number) => {
  let exampleType = ExampleType1Enum.EditorFamilyProfile;
  if(index == 0){
    exampleType = ExampleType1Enum.EditorGroomProfile;
  }else if(index == 1){
    exampleType = ExampleType1Enum.EditorBrideProfile;
  }
  return exampleType;
};

// プロフィール追加ボタンのクリック
const onClickAddProfile = () => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.push({
    isShowVisual: true,
    selectVisual: 'images',
    images: [],
    movie: [],
    name: '',
    isShowRole: true,
    role: '',
    textAlign: 'left',
    message: ''
  });
  data.value = dataUpdated;
  onUpdate(data.value);
};
const onClickDeleteProfile = (index:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents.splice(index, 1);
  data.value = dataUpdated;
  onUpdate(data.value);
};

// コンテンツの変更
const onClickImageChange = async (index:number, id:string, item:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents[index][id] = item;
  data.value = dataUpdated;
  onUpdate(data.value);
}

const onChangeRadio = (index:number, $event:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents[index][selectVisual] = $event;
  data.value = dataUpdated;
  onUpdate(data.value);
}
const onChangeContents = (index:number, id:string, value:string) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.contents[index][id] = value;
  data.value = dataUpdated;
  onUpdate(data.value);
};

const emit = defineEmits(['select', 'change', 'updateLoadingItems', 'removeLoadingItems']);
const onUpdateView = (value:boolean) => {
  let dataUpdated = JSON.parse(JSON.stringify(data.value));
  dataUpdated.visible = value;
  data.value = dataUpdated;
  emit('change', {key: 'profile', value: data.value})
};
const onUpdate = (value: any) => {
  emit('change', {key: 'profile', value: value})
};
//アップロード中画像
const { onLoadImageUpdate, onLoadImageRemove } = useImageLoading(emit);
</script>

<style lang="scss" scoped>
.profile{
  margin-bottom: 36px;
}
h3.cmn-title{
  color: #333;
  font-size: 15px;
  font-weight: normal;
  border-bottom: 1px solid #D9D9D9;
  padding: 0 0 14px;
  margin-top: 0;
  margin-bottom: 16px;
}
.images_area{
  margin-top: 4px;
  margin-bottom: 0;
}
:deep(textarea){
  min-height: 23em;
}
.row{
  margin-top: 8px;
  margin-bottom: 24px;
  span + &{
    margin-top: 4px;
  }
  &.cmn-alignright{
    margin-top: 16px;
  }
}
.button_wrap{
  text-align: right;
  .icn-add{
    &::before{
      content: '';
      display: inline-block;
      width: 10px;
      height: 10px;
      margin-right: 4px;
      background-color: currentColor;
      mask-image: url('@/assets/images/icon-plus-gl.svg');
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: cover;
    }
  }
}
.delete{
  text-decoration: none;
  position: relative;
  padding: 0 0 0 22px;
  color: $color-alert;
  font-size: 12px;
  line-height: 120%;
  letter-spacing: 0.24px;
  cursor: pointer;
  &::before {
    @include BA;
    left: 0;
    width: 18px;
    height: 18px;
    background-image: url('@/assets/images/icon-delete.svg');
  }
}
</style>