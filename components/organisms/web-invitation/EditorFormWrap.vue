<template>
  <div class="webInvitationEditor">
    <div class="webInvitation_blocks">
      <div class="webInvitation_sort">
        <a @click="webInvitationSort.set(true)">並び替え</a>
      </div>
      <EditorFormBlockMainVisual
        id="mainVisual"
        :data="onFilterBlock('mainVisual')"
        :aspect="imageAspectRatio"
        :imageUploadType="props.imageUploadType"
        :error="errorList?.mainVisual"
        :loading-items="loadingItems"
        :isLockSpecifiedPosition="props.isLockSpecifiedPosition"
        :maxMainVisualImages="props.maxMainVisualImages"
        :getAspectRatioForPosition="props.getAspectRatioForPosition"
        @select="isShowImageSelectModal = true"
        @change="onUpdateBlock"
        @update-loading-items="onLoadImageUpdate($event)"
        @remove-loading-items="onLoadImageRemove($event)"
      ></EditorFormBlockMainVisual>
      <EditorFormBlockCountDown
        id="countDown"
        :data="onFilterBlock('countDown')"
        :aspect="imageAspectRatio"
        :error="errorList?.countDown"
        :imageUploadType="props.imageUploadType"
        @change="onUpdateBlock"
      ></EditorFormBlockCountDown>
      <EditorFormBlockMessage
        id="message"
        :data="onFilterBlock('message')"
        :formUpdated="formUpdated"
        :aspect="imageAspectRatio"
        :imageUploadType="props.imageUploadType"
        :error="errorList?.message"
        :loading-items="loadingItems"
        @change="onUpdateBlock"
        @select="isShowMessageModal = true"
        @update-loading-items="onLoadImageUpdate($event)"
        @remove-loading-items="onLoadImageRemove($event)"
      ></EditorFormBlockMessage>
      <EditorFormBlockProfile
        ref="profile"
        :data="onFilterBlock('profile')"
        :formUpdated="formUpdated"
        :aspect="imageAspectRatio"
        :imageUploadType="props.imageUploadType"
        :error="errorList?.profile"
        :loading-items="loadingItems"
        @change="onUpdateBlock"
        @select="isShowMessageModal = true"
        @update-loading-items="onLoadImageUpdate($event)"
        @remove-loading-items="onLoadImageRemove($event)"
      ></EditorFormBlockProfile>
      <EditorFormBlockGallery
        id="gallery"
        :data="onFilterBlock('gallery')"
        :aspect="imageAspectRatio"
        :imageUploadType="props.imageUploadType"
        :error="errorList?.gallery"
        @change="onUpdateBlock"
        @select="isShowImageSelectModal = true"
        :loading-items="loadingItems"
        @update-loading-items="onLoadImageUpdate($event)"
        @remove-loading-items="onLoadImageRemove($event)"
      ></EditorFormBlockGallery>
      <EditorFormBlockInformation
        id="information"
        :data="onFilterBlock('information')"
        :guestAnswerData="onFilterUpdateBlock('guestAnswer')"
        :aspect="imageAspectRatio"
        :imageUploadType="props.imageUploadType"
        :error="errorList?.information"
        :isGuestAnswer="props.isGuestAnswer"
        :loading-items="loadingItems"
        @change="onUpdateBlock"
      ></EditorFormBlockInformation>
      <EditorFormBlockGift
        id="gift"
        :data="onFilterBlock('gift')"
        :informationData="onFilterUpdateBlock('information')"
        :aspect="imageAspectRatio"
        :imageUploadType="props.imageUploadType"
        :error="errorList?.gift"
        :isGuestPaid="isGuestPaid"
        :isHasPrePaidGuest="isHasPrePaidGuest"
        :createdAt="createdAt"
        @change="onUpdateBlock"
        @change-shipping-date="onChangeShippingDate"
      ></EditorFormBlockGift>
      <EditorFormBlockFreeField
        id="freeField"
        :data="onFilterBlock('freeField')"
        :aspect="imageAspectRatio"
        :imageUploadType="props.imageUploadType"
        :error="errorList?.freeField"
        :loading-items="loadingItems"
        @change="onUpdateBlock"
        @update-loading-items="onLoadImageUpdate($event)"
        @remove-loading-items="onLoadImageRemove($event)"
      ></EditorFormBlockFreeField>
      <EditorFormBlockGuestAnswer
        :data="onFilterBlock('guestAnswer')"
        :informationData="onFilterUpdateBlock('information')"
        :aspect="imageAspectRatio"
        :imageUploadType="props.imageUploadType"
        :error="errorList?.guestAnswer"
        :loading-items="loadingItems"
        @change="onUpdateBlock"
        @update-loading-items="onLoadImageUpdate($event)"
        @remove-loading-items="onLoadImageRemove($event)"
      ></EditorFormBlockGuestAnswer>
    </div>
    <div class="complete">
      <div class="complete_wrap">
        <div class="complete_text">
          <h2>作成お疲れさまでした！</h2>
          <p>My招待状一覧から公開設定をして<br>ゲストに招待状を送りましょう</p>
        </div>
        <div class="complete_image">
          <img src="@/assets/images/webInvitation_complete_image.png" alt="">
        </div>
      </div>
    </div>
    <ModalSortBlock
      v-if="webInvitationSort.isShowSortModal"
      :data="formUpdated.blocks"
      @update="onUpdateSort"
      @close="webInvitationSort.set(false)"
    ></ModalSortBlock>
  </div>
</template>

<script lang="ts" setup>
import { useWebInvitationSortStore } from '@/composables/useWebInvitationSort';
import { LoadingItem } from '@/types/LoadingItem';
interface Props {
  form: any,
  formUpdated: any,
  aspect: any,
  isGuestAnswer: boolean,
  isGuestPaid?: boolean,
  isHasPrePaidGuest?: boolean,
  imageUploadType: 'user' | 'admin',
  createdAt?: string,
  error?: string,
  isLockSpecifiedPosition?: boolean,
  maxMainVisualImages?: number,
  getAspectRatioForPosition?: Function
};
const props = withDefaults(defineProps<Props>(), {
  data: [],
  formUpdated: [],
  aspect: {},
  isGuestAnswer: false,
  isGuestPaid: false,
  isHasPrePaidGuest: false,
  imageUploadType: 'user',
  createdAt: '',
  error: null
});

const onScrollTargetBlock = (target: '') => {
  if (target) {
    let scrollTop = 0;
    if (target) {
      let element = document.getElementById(target);
      let elementTop = element.getBoundingClientRect().top;
      let currentScroll = window.scrollY || document.documentElement.scrollTop;
      scrollTop = elementTop + currentScroll;
    }
    window.scrollTo({top: scrollTop, behavior: "smooth"});
  }
};

defineExpose({
  onScrollTargetBlock
});

const webInvitationSort = useWebInvitationSortStore();
const form = ref(props.form);
const formUpdated = ref(props.formUpdated);
const imageAspectRatio = ref(props.aspect);
const error = ref(props.error);
const isGuestPaid = ref(props.isGuestPaid);
const isHasPrePaidGuest = ref(props.isHasPrePaidGuest);
const createdAt = ref(props.createdAt);
watch(() => props, (newVal) => {
  form.value = newVal.form;
  formUpdated.value = newVal.formUpdated;
  imageAspectRatio.value = newVal.aspect;
  error.value = newVal.error;
  isGuestPaid.value = newVal.isGuestPaid;
  isHasPrePaidGuest.value = newVal.isHasPrePaidGuest;
  createdAt.value = newVal.createdAt;
}, {
  deep: true,
  immediate: true
});
watch(() => props.formUpdated, (newVal) => {
  formUpdated.value = newVal;
}, {
  deep: true,
  immediate: true
});

//アップロード中画像
const loadingItems = ref<LoadingItem[]>([]);

const errorList = computed(() => {
  if (!error.value) {
    return null
  }
  let data = {};
  error.value.forEach((item: any) => {
    let obj = {};
    let current = obj;
    const keys = item.$propertyPath.split('.');
    const objData = JSON.parse(JSON.stringify(formUpdated.value));
    const blockId = objData?.blocks?.[keys[1]]?.id ?? null;

    if (blockId) {
      keys.forEach((key, index) => {
        if (index == 1) {
          current[blockId] = {};
          current = current[blockId];
        } else if (index == keys.length - 1) {
          current[key] = item;
        } else {
          current[key] = {};
          current = current[key];
        }
      });
      combineProperties(data, obj);
    }
  });

  return data?.blocks ? data.blocks : []
});

// オブジェクトの結合
const combineProperties = (target: any, source: any) => {
  Object.keys(source).forEach(key => {
    if (target.hasOwnProperty(key) && typeof target[key] === 'object' && typeof source[key] === 'object') {
      // 両方のプロパティがオブジェクトの場合 再帰的に結合
      combineProperties(target[key], source[key]);
    } else if (!target.hasOwnProperty(key)) {
      // targetにプロパティが存在しない場合 単純にコピー
      target[key] = source[key];
    }
    // targetにプロパティが存在しても 上書きはしない
  });
}

// 特定のブロックidを抽出する
const onFilterBlock = (id: String) => {
  let item = formUpdated?.value?.blocks.filter((element) => {
    return element.id == id;
  });
  if (!item) {
    return null
  }
  return item[0];
}
const onFilterUpdateBlock = (id: String) => {
  let item = formUpdated?.value?.blocks.filter((element) => {
    return element.id == id;
  });
  if (!item) {
    return null
  }
  return item[0];
}

const onUpdateBlock = (target: { key: string, value: string }) => {
  emit('change', target);
}
const onUpdateSort = (target: any) => {
  emit('sort', target);
}
const onChangeShippingDate = (date: any) => {
  emit('change-shipping-date', date);
}

const onLoadImageUpdate = async (item: LoadingItem) => {
  loadingItems.value.push(item);
}

const onLoadImageRemove = async (item: LoadingItem) => {
  if(item.type == 'VIDEO') {
    loadingItems.value = loadingItems.value.filter(i => i.srcVideo != item.srcVideo);
  }else{
    loadingItems.value = loadingItems.value.filter(i => i.src !== item.src);
  }
}

form.value = {
  blocks: [
    {
      name: 'メインビジュアル',
      id: 'mainVisual',
      contents: {
        selectVisual: 'images',
        images: [],
        movie: [],
        groomName: '',
        brideName: ''
      }
    },
    {
      name: 'カウントダウン',
      id: 'countDown',
      visible: true,
      contents: {}
    },
    {
      name: '挨拶・メッセージ',
      id: 'message',
      visible: true,
      contents: {
        isShowVisual: false,
        selectVisual: 'images',
        images: [],
        movie: [],
        textAlign: 'center',
        message: ''
      }
    },
    {
      name: '新郎・新婦プロフィール',
      id: 'profile',
      visible: true,
      contents: [
        {
          isShowVisual: true,
          selectVisual: 'images',
          images: [],
          movie: [],
          name: '',
          isShowRole: true,
          role: '',
          textAlign: 'center',
          message: ''
        },
        {
          isShowVisual: true,
          selectVisual: 'images',
          images: [],
          movie: [],
          name: '',
          isShowRole: true,
          role: '',
          textAlign: 'center',
          message: ''
        }
      ]
    },
    {
      name: '写真ギャラリー',
      id: 'gallery',
      visible: true,
      contents: []
    },
    {
      name: 'パーティー情報',
      id: 'information',
      visible: true,
      contents: {
        date: null,
        type: 'ceremony_reception_same_venue',
        events: [
          {
            plans: [
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              },
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              },
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              }
            ],
            otherPlans: [
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              },
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              },
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              }
            ],
            venue: '',
            venue_kana: '',
            zip: '',
            address: '',
            isShowMaps: false,
            tel: '',
            url: '',
            feeOption: 'gift_system',
            feeAmount: ['', ''],
            isShowVisual: false,
            selectVisual: 'images',
            images: [],
            isShowOtherVisual: false,
            otherSelectVisual: 'images',
            otherImages: []
          }
        ]
      }
    },
    {
      name: '会費・ご祝儀の事前支払い',
      id: 'gift',
      visible: true,
      contents: {
        isUseGift: null,
        isUseInVenue: true,
        partySettings: [],
        isUseInAfterParty: true,
        message: '',
        textAlign: 'center',
        usageFee: 'host',
        scheduleDate: ''
      }
    },
    {
      name: 'フリー項目',
      id: 'freeField',
      visible: true,
      contents: []
    },
    {
      name: '出欠フォーム',
      id: 'guestAnswer',
      visible: true,
      contents: {
        selectList: [
          {
            title: '新郎新婦ゲスト選択',
            id: '',
            disabled: false,
            required: true,
            visible: true,
          },
          {
            title: 'お名前',
            id: '',
            disabled: false,
            required: true,
            visible: true,
          },
          {
            title: 'お名前（ふりがな）',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お名前（ローマ字）',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '関係性',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '間柄',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'プロフィール写真',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '性別',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お誕生日',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '住所',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '電話番号',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'メールアドレス',
            id: '',
            disabled: false,
            required: true,
            visible: true,
          },
          {
            title: 'アレルギー項目の入力',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お祝い画像・動画',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お祝いメッセージ',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '連名入力',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          }
        ],
        questionnaire: [],
        limit: {
          date: '',
          setting: 1,
          textAlign: 'center',
          message: '',
        },
        attendance: {
          isHideAttendance: false,
          isHideSkip: false,
          isAddFields: false,
          fields: []
        }
      }
    }
  ]
}
formUpdated.value = JSON.parse(JSON.stringify(form.value));

// emits
const emit = defineEmits(['change', 'sort', 'change-shipping-date']);
</script>

<style lang="scss" scoped>
.webInvitation {
  &_sort {
    position: absolute;
    top: 20px;
    right: -238px;
    z-index: 1;
    transition: 0.35s ease;
    @include sp {
      display: none;
    }

    a {
      display: inline-block;
      color: $color-whitetext;
      background: $color-main;
      padding: 10px;
      font-size: 14px;
      cursor: pointer;

      @include pc {
        background-color: $color-accent;
        background-image: url('@/assets/images/icon-sort.svg');
        background-repeat: no-repeat;
        background-position: center left 20px;
        padding: 10px 24px 10px 48px;
        border-radius: 24px;
      }
    }
  }

  &_blocks {
    max-width: 560px;
    // width: 100%;
    // box-shadow: 0 0 40px rgba(0, 0, 0, 0.12);
    margin: 0 auto;
    position: relative;
  }
}

.block {
  padding: 36px 16px 20px;

  &_wrap {
    background: $color-lightbackground;
    text-align: center;
    padding: 50px 10px 42px;
  }

  h2 {
    color: #333;
    font-weight: normal;
    font-size: 20px;
    margin: 0 0 14px;
  }

  p {
    color: #333;
    font-size: 13px;
    margin: 0;
  }
}

.complete{
  position: relative;
  &::before,
  &::after{
    content: '';
    background-size: cover;
    position: absolute;
  }
  &::before{
    width: 186px;
    height: 168px;
    background-image: url('@/assets/images/webInvitation_complete_bg_1.png');
    top: 0;
    left: 0;
    @include sp{
      width: 126px;
      height: 174px;
      background-image: url('@/assets/images/webInvitation_complete_bg_sp_1.png');
      left: auto;
      right: 0;
    }
  }
  &::after{
    width: 186px;
    height: 168px;
    background-image: url('@/assets/images/webInvitation_complete_bg_2.png');
    bottom: 0;
    right: 0;
    @include sp{
      width: 127px;
      height: 136px;
      background-image: url('@/assets/images/webInvitation_complete_bg_sp_2.png');
      right: auto;
      left: 0;
    }
  }
  &_wrap{
    padding: 26px 20px 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    @include sp{
      flex-direction: column;
      padding: 44px 20px 28px;
      margin-bottom: 4px;
    }
  }
  &_text{
    font-family: $serif_font;
    @include sp{
      text-align: center;
    }
    h2 {
      color: $color-accent;
      line-height: 1;
      letter-spacing: 0.2em;
      font-weight: normal;
      font-size: 20px;
      margin: 0 0 28px;
      @include sp{
        font-size: 18px;
        margin-bottom: 20px;
      }
    }
    p {
      color: #333;
      font-size: 12px;
      letter-spacing: 0.1em;
      line-height: 1.8;
      margin: 0;
      @include sp{
        line-height: 1.5;
      }
    }
  }
  &_image{
    max-width: 171px;
    @include sp{
      max-width: 144px;
    }
  }
}
</style>