<template>
  <div class="imageSelect">
    <div class="imageSelect_wrap">
      <div v-if="images && images.length">
        <draggable :list="images" :itemKey="item => item.uuid" @change="onChangeList" class="dragArea" handle=".drag-handle" :disabled="props.disabled">
          <template #item="{ element, index }">
            <div
              :class="{ 'is-grayed-out': index >= props.maxImages }"
              class="image-item"
            >
              <UploadedImageBox
                :uuid="element.uuid"
                :src="element.src"
                :srcVideo="element.srcVideo"
                :type="element.type"
                :crop="element"
                :isShowHandle="!props.disabled"
                :isShowVideoMenu="element.type == 'VIDEO' && props.isShowVideoMenu && !props.disabled"
                :isVideoAutoplay="element.isAutoplay"
                :isGrayedOut="index >= props.maxImages"
                @cancel="props.disabled || index >= props.maxImages ? null : onClickImageCancel(index)"
                @change="props.disabled || index >= props.maxImages ? null : onClickImageChange('images', index, element)"
                @change-autoplay="props.disabled || index >= props.maxImages ? null : onChangeVideoAutoplay(index, $event)"
                @change-muted="props.disabled || index >= props.maxImages ? null : onChangeVideoMuted(index, $event)"
                @trim="props.disabled || index >= props.maxImages ? null : onClickImageTrim('images', index, element)"
              ></UploadedImageBox>
            </div>
          </template>
        </draggable>
      </div>
      <a
        class="cmn-link link-selectImage"
        v-if="images.length < props.maxImages && !props.disabled && (props.selectType === 'multiple-video-select' || props.selectType !== 'select' || images.length === 0 || images?.[0]?.type !== 'VIDEO')"
        @click="onAddImage()"
      >
        <span v-if="props.selectType == 'video'">動画を選択する</span>
        <span v-else-if="props.selectType == 'multiple-video-select'">画像または動画を選択する</span>
        <span v-else>{{ isShowMovie ? "画像または動画を選択する" : "画像を選択する" }}</span>
      </a>
      <div v-if="errorText" class="alert">{{errorText}}</div>
    </div>
    <ModalImageSelect
      v-if="isShowImageSelectModal"
      :isShow="isShowImageSelectModal"
      :imageUploadType="props.imageUploadType"
      :loadingItems="loadingItems"
      :validation="validation"
      :images="images"
      :is-lock-specified-position="props.isLockSpecifiedPosition"
      @close="isShowImageSelectModal = false"
      @select-video="onSelectVideo($event)"
      @select-image="onSelectImage($event)"
      @upload="isShowImageSelectModal = false; isShowUploadModal = true"
      @spUpload="onUploadImageSp($event)"
    ></ModalImageSelect>
    <ModalImageUpload
      v-if="isShowUploadModal"
      :qrId="props.qrId"
      :imageUploadType="props.imageUploadType"
      @close="isShowUploadModal = false"
      @upload="onUploadImage($event)"
    ></ModalImageUpload>
    <ModalImageTrimming
      v-if="isShowTrimmingModal"
      @close="isShowTrimmingModal = false"
      :src="selectImage"
      :name="selectImageName"
      :uuid="selectImageUUID"
      :crop="selectImageCrop"
      :aspectWidth="getAspectRatioForIndex(selectIndex).width"
      :aspectHeight="getAspectRatioForIndex(selectIndex).height"
      @crop="onTrimmingImage($event)"
    ></ModalImageTrimming>
    <ModalVideoTrimming
      v-if="isShowVideoTrimmingModal"
      @close="isShowVideoTrimmingModal = false"
      :src="selectImage"
      :src-video="selectVideo"
      :name="selectImageName"
      :uuid="selectImageUUID"
      :crop="selectImageCrop"
      :aspectWidth="getAspectRatioForIndex(selectIndex).width"
      :aspectHeight="getAspectRatioForIndex(selectIndex).height"
      @crop="onTrimmingImage($event)"
    ></ModalVideoTrimming>
  </div>
</template>

<script lang="ts" setup>
import draggable from 'vuedraggable';
import { LoadingItem } from '@/types/LoadingItem';
const props = withDefaults(defineProps<{
  selectType?: 'image' | 'video' | 'select' | 'multiple-video-select',
  name: string,
  maxImages?: number,
  images: {
    uuid: string,
    src: string,
    width: string,
    height: string,
    x: string,
    y: string
  }[],
  aspect?: {
    width: number,
    height: number
  } | Function,
  imageUploadType: 'user' | 'admin',
  loadingItems: LoadingItem[],
  selectIndex?: number,
  qrId?: string,
  isVideoTrimming?: boolean,
  isShowVideoMenu?: boolean,
  disabled?: boolean,
  isLockSpecifiedPosition?: boolean
}>(), {
  selectType: 'image',
  maxImages: 3,
  aspect: {
    width: 16,
    height: 9
  },
  imageUploadType: 'user',
  isVideoTrimming: false,
  isShowVideoMenu: false,
  disabled: false,
  isLockSpecifiedPosition: false
});

const images = ref([...props.images]);
watch(() => props.images, (newVal) => {
  images.value = newVal;
}, {
  deep: true
});
const loadingItems = ref(props.loadingItems);
watch(() => props.loadingItems, (newVal) => {
  loadingItems.value = newVal;
}, {
  deep: true
});

const isShowMovie = computed(() => {
  if(
      props.selectType == 'video' && images.value && images.value.length == 0 ||
      props.selectType == 'select' && images.value && images.value.length == 0 ||
      props.selectType == 'multiple-video-select' && images.value && images.value.length == 0
  ){
    return true
  }
  return false
});

const validation = computed(() => {
  if(props.selectType == 'image') {
    return 'onlyImage'
  }
  if(props.selectType == 'video') {
    return 'onlyVideo'
  }
  if(props.selectType == 'select' && images.value && images.value.length && images.value?.[0]?.type != 'VIDEO') {
    return 'notMixed'
  }
  if(props.selectType == 'multiple-video-select') {
    return 'multipleVideoSelect'
  }
  return ''
});

// データ定義
const isShowImageSelectModal = ref(false);
const isShowUploadModal = ref(false);
const isShowTrimmingModal = ref(false);
const isShowVideoTrimmingModal = ref(false);
const selectImage = ref();
const selectVideo = ref();
const selectType = ref('image');
const selectImageCrop = ref();
const selectImageUUID = ref('');
const selectIndex = ref(0);
const selectImageName = ref();

const emits = defineEmits<{
  close: [],
  crop: [],
  add: [],
  select: [],
  changeRadio: [],
  changeImages: []
}>();

// 位置ごとのアスペクト比を取得
const getAspectRatioForIndex = (index: number) => {
  if (typeof props.aspect === 'function') {
    return props.aspect(index);
  }
  return props.aspect || { width: 16, height: 9 };
};

// 元のサイズの画像をダウンロード
const onLoadOriginalImage = async(uuid: string) => {
  return new Promise((resolve, reject) => {
    (async () => {
      try {
        const { getImages, refetch, loading } = await useGetManyImages2([uuid]);
        const unwatch = watch(loading, (newLoadingValue) => {
          if (!newLoadingValue) {
            unwatch(); // ウォッチャーを停止する
            resolve(getImages?.value?.[0]?.presigned_url);
          }
        });
        await refetch();
      } catch (error) {
        reject(error);
      }
    })();
  });
};

const onChangeList = async(event: any) => {
  if (event.moved) {
    let dataUpdated = JSON.parse(JSON.stringify(images.value));
    const movedElement = dataUpdated.splice(event.moved.oldIndex, 1)[0];
    dataUpdated.splice(event.moved.newIndex, 0, movedElement);
    emits('changeImages', images.value)
  }
};

// アップロード済み画像の操作
const onClickImageCancel = async (index:number) => {
  let dataUpdated = JSON.parse(JSON.stringify(props.images));
  dataUpdated.splice(index, 1);
  images.value = dataUpdated;
  emits('changeImages', images.value);
}
const onClickImageChange = async (id:string, index:number, item:any) => {
  selectIndex.value = index;
  isShowImageSelectModal.value = true;
}
const onClickImageTrim = async (id:string, index:number, item:any) => {
  selectIndex.value = index;
  selectImage.value = item.src;
  selectVideo.value = item.srcVideo;
  selectImageUUID.value = item.uuid;
  selectImageCrop.value = item;
  if(item.type == 'VIDEO') {
    isShowVideoTrimmingModal.value = true;
  } else {
    isShowTrimmingModal.value = true;
  }
  // selectImage.value = await onLoadOriginalImage(item.uuid);
}

// 動画の設定変更
const onChangeVideoAutoplay = async (index:number, value:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(props.images));
  dataUpdated[index].isAutoplay = value;
  dataUpdated[index].isMuted = true;
  images.value = dataUpdated;
  emits('changeImages', images.value);
}
const onChangeVideoMuted = async (index:number, value:any) => {
  let dataUpdated = JSON.parse(JSON.stringify(props.images));
  dataUpdated[index].isMuted = value;
  images.value = dataUpdated;
  emits('changeImages', images.value);
}

const onAddImage = async() => {
  selectIndex.value = images.value.length;
  isShowImageSelectModal.value = true;
};

// 既存(アップロード済み)画像をアップロード
const onSelectImage = async(image) => {
  selectImage.value = image.presigned_url;
  selectImageCrop.value = null;
  selectImageUUID.value = image.uuid;
  selectImage.value = await onLoadOriginalImage(image.uuid);
  isShowImageSelectModal.value = false;
  isShowTrimmingModal.value = true;
};

const onSelectVideo = async(video) => {
  selectImage.value = video.presigned_url;
  selectVideo.value = video?.presigned_url_main;
  selectImageCrop.value = null;
  selectImageUUID.value = video.uuid;
  selectImage.value = await onLoadOriginalImage(video.uuid);
  isShowImageSelectModal.value = false;
  if (props.isVideoTrimming) {
    isShowVideoTrimmingModal.value = true;
  } else {
    onTrimmingImage({
      src: video.presigned_url,
      srcVideo: video.presigned_url_main,
      uuid: video.uuid,
      type: video.type
    });
  }
};

const onUploadImage = async(image) => {
  selectImageName.value = image.fileName;
  selectImage.value = image.fileData;
  selectVideo.value = image.fileData;
  selectType.value = image.fileType;
  selectImageCrop.value = null;
  isShowUploadModal.value = false;
  if (image.fileType == 'VIDEO' && props.isVideoTrimming) {
    isShowVideoTrimmingModal.value = true;
  } else if(image.fileType == 'IMAGE' && props.selectType == 'video'){
    onTrimmingImage({
      src: image.fileData,
      name: image.fileName,
      uuid: '',
      type: image.fileType
    });
  } else if(image.fileType == 'IMAGE') {
    isShowTrimmingModal.value = true;
  } else {
    onTrimmingImage({
      src: null,
      srcVideo: image.fileData,
      name: image.fileName,
      uuid: '',
      type: image.fileType
    });
  }
};

const onUploadImageSp = async(image) => {
  selectImageName.value = image.fileName;
  selectImage.value = image.fileData;
  selectVideo.value = image.fileData;
  selectType.value = image.fileType;
  selectImageUUID.value = "";
  selectImageCrop.value = null;
  isShowImageSelectModal.value = false;
  if (image.fileType == 'VIDEO' && props.isVideoTrimming) {
    isShowVideoTrimmingModal.value = true;
  } else if(image.fileType == 'IMAGE' && props.selectType == 'video'){
    onTrimmingImage({
      src: image.fileData,
      name: image.fileName,
      uuid: '',
      type: image.fileType
    });
  } else if(image.fileType == 'IMAGE'){
    isShowTrimmingModal.value = true;
  } else {
    onTrimmingImage({
      src: null,
      srcVideo: image.fileData,
      name: image.fileName,
      uuid: '',
      type: image.fileType
    });
  }
};

//トリミング後の処理
const errorText = ref('' as string);
let adminSettings = {};
if(props.imageUploadType == 'admin'){
  adminSettings = {clientId: 'admin'}
}
const { mutate, loading } = useBinaryUploadImage2Mutation(adminSettings);
const onTrimmingImage = async(image) => {
  let dataUpdated = JSON.parse(JSON.stringify(images.value));
  let idx = selectIndex.value;

  // 選択できるファイルの種類に応じてセットするかどうかを判定
  if(
    props.selectType == 'image' && image.type != 'VIDEO' ||
    props.selectType == 'video' && image.type == 'VIDEO' ||
    props.isVideoTrimming && image.type == 'VIDEO' ||
    props.selectType == 'select' && images.value && idx == 0 && image.type == 'VIDEO' ||
    props.selectType == 'select' && images.value && image.type != 'VIDEO' ||
    props.selectType == 'multiple-video-select'
  ){
    dataUpdated[idx] = image;
    images.value = dataUpdated;
  }
  isShowTrimmingModal.value = false;
  isShowVideoTrimmingModal.value = false;
  errorText.value = '';

  if(image.uuid == ""){
    let updateImage: LoadingItem = {
      src: image.src,
      srcVideo: image.srcVideo ? image.srcVideo : null,
      type: image.type ? image.type : 'image'
    };
    emits('updateLoadingItems', updateImage);
    emits('changeImages', images.value);
    let file;
    if(image.type == 'VIDEO' && image.srcVideo) {
      file = convertBase64ToFile(image.srcVideo, image.name);
    }else{
      file = convertBase64ToFile(image.src, image.name);
    }

    let imageFileType = FileType.FileTypeMemberMaterial;
    if(props.imageUploadType == 'admin'){
      imageFileType = FileType.FileTypeProductMaster;
    }

    await mutate({
      fileData: file,
      fileType: imageFileType
    }).then(response => {
      if(
        props.selectType == 'image' && image.type != 'VIDEO' && images.value?.[idx] ||
        props.selectType == 'video' && image.type == 'VIDEO' && images.value?.[idx] ||
        props.selectType == 'select' && images.value && idx == 0 && image.type == 'VIDEO' && images.value?.[idx] ||
        props.selectType == 'select' && images.value && image.type != 'VIDEO' && images.value?.[idx] ||
        props.selectType == 'multiple-video-select' && images.value?.[idx]
      ){
        images.value[idx].uuid = response?.data?.binaryUploadImage2?.uuid;
        images.value[idx].src = response?.data?.binaryUploadImage2?.presigned_url;
        if(response?.data?.binaryUploadImage2.presigned_url_main) {
          images.value[idx].srcVideo = response?.data?.binaryUploadImage2.presigned_url_main;
        }
      }
      emits('removeLoadingItems', updateImage);
      // トリミング完了の合図として、グローバルイベントを発火
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('trimmingCompleted', {
          detail: { timestamp: Date.now() }
        }));
      }
      emits('changeImages', images.value);
      errorText.value = '';
    }).catch(error => {
      let errorObject = parseGraphQLErrors(error);
      if (errorObject.file_data && errorObject.file_data.length > 0) {
        errorText.value = errorObject.file_data[0] == 'Failed to fetch' ? 'アップロード中にエラーが発生しました' : errorObject.file_data[0];
      } else if (errorObject.v$ && errorObject.v$.length > 0) {
        errorText.value = errorObject.v$[0] == 'Failed to fetch' ? 'アップロード中にエラーが発生しました' : errorObject.v$[0];
      } else {
        errorText.value = 'アップロード中にエラーが発生しました';
      }
      emits('removeLoadingItems', updateImage);
      if(image.type == 'VIDEO'){
        images.value = [];
      }
      emits('changeImages', images.value);
    });
  }else{
    // トリミング完了の合図として、グローバルイベントを発火
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('trimmingCompleted', {
        detail: { timestamp: Date.now() }
      }));
    }
    emits('changeImages', images.value);
  }
};

const wait = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// ラジオボタンの切り替え
const onChangeRadio = ($event) => {
  emits('changeRadio', $event);
}

// Base64文字列からバイナリデータを取得
const convertBase64ToFile = (src: string, fileName: string): File => {
  // Base64文字列からデータとMIMEタイプを抽出
  const [, dataType, base64Data] = src.match(/^data:(.+);base64,(.+)$/) || [];

  // Base64文字列をデコード
  const binaryString = window.atob(base64Data);
  const bytes = new Uint8Array(binaryString.length);

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // ファイルオブジェクトを作成
  return new File([bytes], fileName, { type: dataType });
};

</script>

<style lang="scss" scoped>
.imageSelect{
  margin: 0 0 8px;
  &_wrap{
    margin-left: 24px;
  }
}
.alert {
  font-size: 14px;
  font-weight: 400;
  color: $color-alert;
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>