<template>
  <div class="layoutEditor">
    <div
      v-if="isShowHeader"
      class="layoutEditor_header"
      :class="{'is-flex': !isShowMessageModal || !isShowReset}"
    >
      <p v-if="props.headingTitle" class="title">{{ props.headingTitle }}</p>
      <p v-else-if="props.title" class="title">{{ props.title }}</p>
      <div class="flex" v-if="isShowMessageModal || isShowReset">
        <div class="button_wrap">
          <ButtonMainColor
            v-if="isShowMessageModal"
            baseColor="reversal"
            @click="isShowModal = true"
          >例文から選んで編集</ButtonMainColor>
        </div>
        <a
          v-if="isShowReset"
          class="reset"
          :class="{'is-active': message !== resetMessage || headingValue !== resetTitle}"
          @click="isShowResetModal = true"
        >初期値に戻す</a>
      </div>
    </div>
    <div v-if="props.headingTitle" class="layoutEditor_title">
      <InputText
        :value="headingValue"
        size="full"
        @input="onChangeTitle($event.target.value)"
      />
      <MessageNgWord v-if="isValidateNgWord" :value="headingValue"></MessageNgWord>
    </div>
    <p v-if="props.headingTitle && props.title" class="title">{{ props.title }}</p>
    <div class="layoutEditor_wrap" :style="{'textAlign': textAlign}">
      <div class="layoutEditor_textarea">
        <InputTextarea
          size="full"
          resize="vertical"
          :maxlength="props.maxLength"
          :placeholder="props.placeholder"
          :value="message"
          @input="onChangeMessage($event.target.value)"
        />
      </div>
      <div class="layoutEditor_footer">
        <div class="align_boxes">
          <a v-if="props.isShowAlgin" class="box is-left" :class="{'is-active': textAlign == 'left'}" @click="onChangeAlign('left')"></a>
          <a v-if="props.isShowAlgin" class="box is-center" :class="{'is-active': textAlign == 'center'}" @click="onChangeAlign('center')"></a>
          <a v-if="props.isShowAlgin" class="box is-right" :class="{'is-active': textAlign == 'right'}" @click="onChangeAlign('right')"></a>
        </div>
        <div class="layoutEditor_count">{{message ? message.length : 0}}/{{props.maxLength}}</div>
      </div>
    </div>
    <MessageNgWord v-if="isValidateNgWord" :value="message"></MessageNgWord>
    <ModalMessageSelect
      v-if="isShowModal"
      :isSidebar="true"
      :exampleType="props.exampleType"
      :formUpdated="formUpdated"
      :isShowFilterSettings="props.isShowFilterSettings"
      @close="isShowModal = false"
      @select="onSelectMessage"
    ></ModalMessageSelect>
    <ModalConfirmWebInvitation
      v-if="isShowResetModal"
      label="初期値に戻す"
      @close="isShowResetModal = false"
      @select="onClickReset()"
    >
      <p class="confirm_alert">入力された内容が初期値に戻りますがよろしいですか？</p>
    </ModalConfirmWebInvitation>
  </div>
</template>
<script lang="ts" setup>
interface Props {
  title?: string,
  headingTitle?: string,
  message?: string,
  headingValue?: string,
  placeholder?: string,
  textAlign?: 'left' | 'center' | 'right',
  isShowHeader?: boolean,
  isShowMessageModal?: boolean,
  isShowReset?: boolean,
  isShowAlgin?: boolean,
  isShowSidebar?: boolean,
  resetId?: 'greeting' | 'profile' | 'payment' | 'deadline' | 'freeField1' | null,
  exampleType?: string,
  formUpdated?: any,
  isShowFilterSettings?: boolean,
  maxLength?: string,
  isValidateNgWord?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  message: '',
  placeholder: '',
  textAlign: 'left',
  isShowHeader: true,
  isShowMessageModal: true,
  isShowReset: true,
  isShowAlgin: true,
  isShowSidebar: true,
  formUpdated: [],
  isShowFilterSettings: true,
  maxLength: '300',
  isValidateNgWord: false
});

const isShowModal = ref(false);
const message = ref(props.message ? props.message : '');
const headingValue = ref(props.headingValue ? props.headingValue : '');
const textAlign = ref(props.textAlign);
const formUpdated = ref(props.formUpdated);

const initialMessage = ref(props.message);
const initialAlign = ref(props.textAlign);
const isInitial = ref(true);

watch(() => props, (newVal) => {
  if(newVal){
    message.value = newVal.message;
    textAlign.value = newVal.textAlign;
    formUpdated.value = newVal.formUpdated;
    if(isInitial.value){
      initialMessage.value = newVal.message;
      initialAlign.value = newVal.textAlign;
      isInitial.value = false;
    }
  }
}, {
  deep: true
});

// 例文モーダルから例文の選択
const onSelectMessage = (value:any) => {
  onChangeMessage(value?.example_text);
  onChangeTitle(value?.title);
  message.value = value?.example_text;
  headingValue.value = value?.title;
  isShowModal.value = false;
};

// microCMSから例文一覧を取得
const microCms = new MicroCms();
const search = ref({
  orders: 'publishedAt',
  filters: 'content_type[contains]例文デフォルト',
} as {
  orders: string;
  filters: string;
});
const { data: microCmsData, error: microCmsError } = await microCms.fetch('/content', search);

// リセットボタンの処理
const resetTitle = ref('');
const resetMessage = ref('');
const onGetResetMessage = () => {
  let contentCode;
  switch (props.resetId) {
    case 'greeting':
      contentCode = 'exapmle_default_greeting_1';
      break;
    case 'profile':
      contentCode = 'exapmle_default_profile_1';
      break;
    case 'payment':
      contentCode = 'exapmle_default_payment_1';
      break;
    case 'deadline':
      contentCode = 'exapmle_default_reply_deadline_1';
      break;
    case 'freeField1':
      contentCode = 'exapmle_default_freefield_1';
      break;
    default:
      break;
  }
  // 例文の追加
  let data = '';
  if(contentCode && microCmsData?.value?.contents){
    let cmsData = microCmsData.value.contents.find((content) => content?.code == contentCode);
    data = cmsData?.plane_text;
    if(cmsData?.plane_text2) {
      resetTitle.value = cmsData?.plane_text2;
    }
  }
  resetMessage.value = data;
};
onGetResetMessage();

// リセット処理
const isShowResetModal = ref(false);
const onClickReset = () => {
  isShowResetModal.value = false;
  message.value = resetMessage.value;
  headingValue.value = resetTitle.value;
  emit('input', message.value)
  emit('inputTitle', headingValue.value)
};

// emit
const emit = defineEmits(['input', 'inputTitle', 'align']);
const onChangeMessage = (value:string) => {
  message.value = value;
  emit('input', value)
};
const onChangeTitle = (value:string) => {
  headingValue.value = value;
  emit('inputTitle', value)
};
const onChangeAlign = (value: string) => {
  textAlign.value = value;
  emit('align', value)
};
</script>

<style lang="scss" scoped>
:deep(.modalConfirm){
  & > .modalContainer > .contents .contentsInner{
    min-height: auto;
  }
  p{
    margin: 0;
  }
  a{
    font-size: 14px;
  }
  .color-accent{
    color: $color-alert !important;
  }
}
:deep(textarea){
  min-height: 6.5em;
  padding: 12px 12px;
  font-size: 14px;
  resize: vertical;
  vertical-align: bottom;
  border: none;
  &:focus{
    outline: none;
  }
}
a{
  display: block;
  font-size: 12px;
  margin: 12px 0;
}
.title{
  font-size: 12px;
  color: #26282C;
  margin: 18px 0 6px;
}
.flex{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.align_boxes{
  a{
    font-size: 12px;
    color: $color-blacktext5;
    margin: 0 2px 10px;
  }
}
.button_wrap{
  max-width: 160px;
  width: 100%;
  margin-bottom: 12px;
  a{
    margin: 0;
  }
}

.reset{
  color: $color-grayborder;
  font-size: 14px;
  display: flex;
  align-items: center;
  line-height: 1;
  margin: 0;
  cursor: pointer;
  pointer-events: none;
  transition: 0.35s ease-in-out;
  white-space: nowrap;
  &::before{
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 2px;
    background-color: currentColor;
    mask-image: url('@/assets/images/icon-reset.svg');
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: 22px;
    vertical-align: middle;
  }
  &.is-active{
    color: $color-main;
    pointer-events: auto;
    &:hover{
      text-decoration: underline;
    }
  }
  &:hover{
    text-decoration: none;
  }
}

.layoutEditor{
  position: relative;
  &_header{
    &.is-flex{
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
  }
  &_wrap{
    color: #333;
    background: #FFF;
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    overflow: hidden;
    &:has(textarea:focus){
      outline: 2px solid #005FCC;
    }
  }
  &_footer{
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #D9D9D9;
    background-color: #fff;
    padding: 6px 12px;
    border-top: 1px solid #D9D9D9;
    min-height: 37px;
    a{
      font-size: 12px;
      color: $color-blacktext5;
      margin: 0 2px;
    }
  }
  &_count{
    font-size: 12px;
    color: #9C9C9C;
  }
  .align_boxes{
    display: flex;
    .box{
      display: block;
      width: 24px;
      height: 24px;
      background: #9C9C9C;
      margin: 0;
      cursor: pointer;
      &.is-active{
        background: #B18A3E;
      }
      &.is-left{
        mask-image: url('@/assets/images/layoutEditor_align_left.svg');
        mask-repeat: no-repeat;
        mask-position: center;
      }
      &.is-center{
        margin-left: 14px;
        mask-image: url('@/assets/images/layoutEditor_align_center.svg');
        mask-repeat: no-repeat;
        mask-position: center;
      }
      &.is-right{
        margin-left: 14px;
        mask-image: url('@/assets/images/layoutEditor_align_right.svg');
        mask-repeat: no-repeat;
        mask-position: center;
      }
    }
  }
}

</style>