<template>
  <div class="modal">
    <div class="modal_background" @click="emits('close')"></div>
    <div class="modal_box">
      <div class="header">
        <h2>ゲストリスト追加</h2>
        <button class="modalClose" @click="emits('close')">
          <img src="@/assets/images/icon-close-b.svg" alt="閉じる" />
        </button>
      </div>

      <div class="contents">
        <div class="main">
          <form class="searchform mb-20" @submit.prevent="onClickSave()">
            <InputText
              title="新規ゲストリスト名"
              placeholder="1.5次会"
              :required="true"
              size="full"
              :value="String(input.name)"
              :error="errors?.input?.name?.[0]"
              @input="input.name = $event.target.value"
              />
          </form>
        </div>
      </div>

      <footer class="modal-footer">
        <button class="btn btn-default-outline btn-block" @click="$emit('close')">キャンセル</button>
        <button class="btn btn-secondary btn-block" @click="onClickSave()">追加する</button>
      </footer>

    </div>
  </div>
</template>

<script lang="ts" setup>
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { CreateGuestListInput } from '@/composables/generated';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();

const props = withDefaults(defineProps<{
  url?: string
}>(), {
  url: ''
});


// 入力項目
const input = ref({
  name: ''
} as CreateGuestListInput)

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return {
    name: {
      required: helpers.withMessage(validationMessage.required('ゲストリスト名'), required)
    },
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

/**
 * 登録
 */
// 登録API
const { create, errors } = useCreateGuestList();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await create(input.value);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }

  // ToastMessage
  addToastMessage({message: 'ゲストリスト「'+input.value.name+'」を追加しました '});
  emits('create')
};

const emits = defineEmits<{
  close: [],
  create: []
}>();

</script>

<style lang="scss" scoped>
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  opacity: 1;
  z-index: 1000;
  transition: opacity 0.35s ease;
  &_background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
  }
  &_box{
    max-width: 640px;
    width: 100%;
    background: #FFF;
    @include sp{
      height: 100vh;
      height: 100dvh;
    }
  }
}
.contents{
  min-height: 320px;
  padding: 20px 16px;
  @include sp{
    min-height: 100vh;
    min-height: 100dvh;
  }
  .main{
    img{
      width: 280px;
      height: 280px;
    }
  }
}
.header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #fff;
  border-bottom: 1px solid #D9D9D9;
  h2{
    color: #333;
    font-size: 18px;
    font-weight: normal;
  }
}
.footer{
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
}
</style>