<template>
  <Modal class="modalConfirm" size="sm" @close="emits('close')">
    <template #main>
      <div class="wrap">
        <slot />
      </div>
      </template>
    <template #footer>
      <a v-if="props.isShowCancel" @click="emits('close')">キャンセル</a>
      <a class="color-accent" @click="emits('select')">{{props.label}}</a>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
export interface Props {
  label?: string;
  isShowCancel?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  label: 'OK',
  isShowCancel: true
});

const emits = defineEmits<{
  (e: 'close'): void;
  (e: 'select'): void;
}>()
</script>

<style lang="scss" scoped>
.wrap{
  padding: 20px 0;
  color: #333;
  font-size: 18px;
}
a{
  cursor: pointer;
}
</style>