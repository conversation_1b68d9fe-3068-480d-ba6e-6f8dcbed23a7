<template>
  <transition name="slide">
    <div v-if="props.isShow" class="modal">
      <div class="modal_background" @click.prevent="emits('close')"></div>
      <div class="modal_box">
        <div class="header is-pc">
          <h2>画像/動画を選択</h2>
          <button class="modalClose" @click="emits('close')">
            <img src="@/assets/images/icon-close-b.svg" alt="閉じる" />
          </button>
        </div>
        <div class="header is-sp">
          <div class="header_item">
            <button class="modalClose" @click="emits('close')">閉じる</button>
          </div>
          <div class="header_center">
            <div
              class="handle resize-handle"
              ref="resizeHandle"
              @mousedown="startResize"
              @touchstart="startResize"
            >
              <span></span>
            </div>
            <h2>画像/動画を選択</h2>
          </div>
          <div class="header_item"></div>
        </div>
        <div v-if="errorText" class="input-error">{{ errorText }}</div>

        <div class="contents">
          <div class="tab_menu">
            <ul>
              <li>
                <a href="#" :class="{'is-active': activeTab == 0}" @click.prevent="activeTab = 0">アップロード画像/動画</a>
                <a href="#" :class="{'is-active': activeTab == 1}" @click.prevent="activeTab = 1">Favoriのおすすめ</a>
              </li>
            </ul>
          </div>
          <div
            class="main resizable-box"
            ref="resizableBox"
            :style="{ 'height': `calc(100dvh - ${height}px)` }"
          >
            <div class="tab" v-if="activeTab == 0">
              <div class="wrap">
                <ul class="image_list">
                  <li class="add_image is-pc" @click="onUploadImage">
                    <a href=""></a>
                  </li>
                  <li class="add_image is-sp">
                    <label for="spImageUpload">
                      <input
                        id="spImageUpload"
                        type="file"
                        accept="image/*,video/*"
                        @change="onClickSpUpload"
                      />
                    </label>
                  </li>
                  <li v-for="(image, index) in loadingItems" :key="index">
                    <span class="loader"></span>
                  </li>
                  <li v-for="(image, index) in imageSrc" :key="index">
                    <span v-if="image.type == 'VIDEO'" class="icn-video"></span>
                    <img :src="image.presigned_url" @click="onSelectImage(image)" alt="">
                    <button class="delete-btn" :disabled="deleteLoading" @click.stop="onDeleteImage(image, index)">
                      <img src="@/assets/images/icon-close.svg" alt="削除" />
                    </button>
                  </li>
                </ul>
              </div>
            </div>
            <div class="tab" v-if="activeTab == 1">
              <div class="wrap">
                <ul class="image_list">
                  <li v-for="(image, index) in materialImages" :key="index">
                    <span v-if="image.type == 'VIDEO'" class="icn-video"></span>
                    <img :src="image.presigned_url" @click="onSelectImage(image)" alt="">
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="preview_loading" v-if="uploadLoading">
        <span class="icn"><i class="material-symbols-outlined">sync</i></span>
        画像をアップロード中...
      </div>
      <ModalConfirmWebInvitation
        v-if="isShowModalConfirm"
        label="閉じる"
        :isShowCancel="false"
        @close="isShowModalConfirm = false"
        @select="isShowModalConfirm = false"
      >
        <h2 class="confirm_title">{{ confirmTitle }}</h2>
        <p class="confirm_alert">{{ confirmText }}</p>
      </ModalConfirmWebInvitation>

      <!-- 削除確認モーダル -->
      <ModalConfirmWebInvitation
        v-if="isShowDeleteConfirm"
        label="削除する"
        :isShowCancel="true"
        @close="isShowDeleteConfirm = false"
        @select="onConfirmDelete"
      >
        <h2 class="confirm_title">画像を削除しますか？</h2>
        <p class="confirm_alert">削除した画像は元に戻すことができません。</p>
      </ModalConfirmWebInvitation>
    </div>
  </transition>
</template>

<script lang="ts" setup>
import { LoadingItem } from '@/types/LoadingItem';
import { timestamp } from '@vueuse/core';
import { json } from 'stream/consumers';
const props = withDefaults(defineProps<{
  imageUploadType: 'user' | 'admin';
  isShow: boolean;
  loadingItems: LoadingItem[];
  validation?: string;
  maxFileSize?: number;
  isLockSpecifiedPosition?: boolean;
  images?: {
    uuid: string,
    src: string,
    width: string,
    height: string,
    x: string,
    y: string
  }[];
}>(), {
  maxFileSize: 50,
  isLockSpecifiedPosition: false
});

const errorText = ref('' as string);
const uploadLoading = ref(false);
const emits = defineEmits<{
  close: [],
  crop: [],
  add: [],
  selectImage: [],
  selectVideo: [],
  upload: [],
  spUpload: [],
  deleteImage: []
}>();
// 型定義
type ImageResponse = {
  __typename?: "ImageResponse" | undefined;
  uuid?: string | null | undefined;
  size?: string | null | undefined;
  presigned_url?: string | null | undefined;
  status?: boolean | null | undefined;
}

// 初期値を持つ ref を作成
const initialMaterialImages: Ref<ImageResponse[]> = ref([
  {
    __typename: "ImageResponse",
    uuid: null,
    size: null,
    presigned_url: null,
    status: null
  }
])
const { materialImages, refetch: refetchMaterialImage, loading } = useGetManyMaterialImage(FileType.FileTypeMaterial);

const activeTab = ref(0);
const imageSrc = ref([]);
let imageRefetch = ref();
let imagesLoading = ref();
const loadingItems = ref(props.loadingItems);
if(props.imageUploadType == 'admin'){
  imageSrc.value = [];
}else if(props.imageUploadType == 'user'){
  const { uploadImages, refetch: refetchUploadImage, loading: uploadImagesLoading } = useGetManyUploadImage();
  imageRefetch.value = refetchUploadImage;
  imagesLoading.value = uploadImagesLoading;
  imageRefetch.value();
  imageSrc.value = uploadImages.value;
  watch(uploadImages, async(newVal) => {
    if(newVal){
      imageSrc.value = newVal;
    }
  }, {
    deep: true,
    immediate: true
  })
}

watch(() => props.loadingItems, async (newVal) => {
    loadingItems.value = newVal;
    setTimeout(() => {
      if(props.imageUploadType == 'user'){
        imageRefetch.value();
      }
    }, 500);
}, {
  deep: true
});

const images = ref(props.images);
watch(() => props.images, (newVal) => {
  images.value = newVal;
}, {
  deep: true
});

const isShowModalConfirm = ref(false);
const confirmTitle = ref('');
const confirmText = ref('');

const onSelectImage = async(image: any) => {
  if(image.type == 'VIDEO') {
    if(props.validation == 'notMixed' && !props.isLockSpecifiedPosition) {
      isShowModalConfirm.value = true;
      confirmTitle.value = '画像が既に選択されています。同時に動画の選択はできません。';
      confirmText.value = '動画を表示したい場合は、選択済みの画像を削除してから動画を選択してください。';
    } else if(props.validation == 'onlyImage') {
      isShowModalConfirm.value = true;
      confirmTitle.value = 'この項目では動画の選択はできません。';
      confirmText.value = '画像を選択してください。';
    } else {
      emits('selectVideo', { presigned_url: image.presigned_url, presigned_url_main: image.presigned_url_main, type: image.type, uuid: image.uuid });
    }
  } else {
    if(images.value && images.value?.[0]?.type == 'VIDEO' && !props.isLockSpecifiedPosition && props.validation != 'multipleVideoSelect') {
      isShowModalConfirm.value = true;
      confirmTitle.value = '動画が既に選択されています。画像の選択はできません。';
      confirmText.value = '画像を表示したい場合は、選択済みの動画を削除してから画像を選択してください。';
    } else if(props.validation == 'onlyVideo') {
      isShowModalConfirm.value = true;
      confirmTitle.value = 'この項目では画像の選択はできません。';
      confirmText.value = '動画を選択してください。';
    } else {
      emits('selectImage', { presigned_url: image.presigned_url, uuid: image.uuid });
    }
  }
};

const onUploadImage = async() => {
  emits('upload');
};

// 削除機能の実装
const { updateImageHidden, errors: deleteErrors } = useUpdateImageHidden();
const deleteLoading = ref(false);
const isShowDeleteConfirm = ref(false);
const imageToDelete = ref(null);

const onDeleteImage = async(image: any, index: number) => {
  if (!image?.uuid) {
    return;
  }
  // 削除対象を保存して確認モーダルを表示
  imageToDelete.value = { image, index };
  isShowDeleteConfirm.value = true;
};

const onConfirmDelete = async() => {
  if (!imageToDelete.value) return;
  const { image, index } = imageToDelete.value;
  deleteLoading.value = true;
  const isSuccess = await updateImageHidden(image.uuid);
  deleteLoading.value = false;

  if (!isSuccess) {
    isShowDeleteConfirm.value = false;
    return;
  }

  // 削除成功時は画像一覧を更新
  if (props.imageUploadType === 'user' && imageRefetch.value) {
    try {
      setTimeout(async () => {
        await imageRefetch.value();
      }, 300);
    } catch (error) {
      console.error('Failed to refresh image list:', error);
    }
  }

  // 親コンポーネントにも削除完了を通知
  emits('deleteImage', {
    uuid: image.uuid,
    index
  });

  // モーダルを閉じる
  isShowDeleteConfirm.value = false;
  imageToDelete.value = null;
};

// SP用の処理
interface PreviewData {
  fileName: string;
  fileData: string;
}
const spPreviewData: Ref<PreviewData> = ref({
  fileName: '',
  fileData: '',
});

const onClickSpUpload = (e: any) => {
  onUploadSp(e.target.files);
};
const onUploadSp = async (inputFiles: any[]) => {
  for (let i = 0; i < inputFiles.length; i++) {
    const file = inputFiles[i];
    let base64, fileType;

    // サイズは64MB以下
    if (file.size / 1024 / 1024 >= props.maxFileSize) {
      errorText.value = 'ファイルは'+String(props.maxFileSize)+'MB以下でアップロードしてください';
      return false;
    }

    if (file.type.startsWith('image/')) {
      // 画像ファイルの場合
      base64 = await convertBase64(file);
      fileType = 'IMAGE'
    } else {
      // 動画ファイルの場合
      try {
        base64 = await convertBase64Video(file);
        fileType = 'VIDEO'
      } catch (error) {
        errorText.value = error.message || '対応していない動画形式です';
        return false;
      }
    }
    if(base64 != false){
      spPreviewData.value.fileName = file.name;
      spPreviewData.value.fileType = fileType;
      spPreviewData.value.fileData = base64;
    }
  }
  imageRefetch.value();
  emits('spUpload', spPreviewData.value);
}

// SPのリサイズ処理
const height = ref(500);
const resizableBox = ref(null);
const resizeHandle = ref(null);

const startResize = (event) => {
  event.preventDefault();
  const onMouseMove = (moveEvent) => {
    const clientY = moveEvent.type === 'touchmove' ? moveEvent.touches[0].clientY : moveEvent.clientY;
    height.value = Math.max(clientY, 50);
  };

  const onMouseUp = () => {
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
    document.removeEventListener('touchmove', onMouseMove);
    document.removeEventListener('touchend', onMouseUp);
  };

  document.addEventListener('mousemove', onMouseMove);
  document.addEventListener('mouseup', onMouseUp);
  document.addEventListener('touchmove', onMouseMove);
  document.addEventListener('touchend', onMouseUp);
};

</script>

<style lang="scss" scoped>
@keyframes circle{
  0%{transform:rotate(0deg)}
  100%{transform:rotate(360deg)}
}
@-webkit-keyframes circle{
  0%{-webkit-transform:rotate(0deg)}
  100%{-webkit-transform:rotate(360deg)}
}
.input-error {
  text-align: center;
}
.preview_loading{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #9C9C9C;
  font-weight: bold;
  font-size: 16px;
  min-height: 388px;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 1);
  z-index: 100;
  .icn{
    margin-bottom: 6px;
  }
  i {
    font-size: 34px;
    animation:circle 1.5s linear infinite;
    -webkit-animation:circle 1.5s linear infinite;
  }
}
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  opacity: 1;
  z-index: 1000;
  transition: opacity 0.35s ease;
  @include sp{
    align-items: flex-end;
  }
  &_background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
  }
  &_box{
    max-width: 640px;
    width: 100%;
    background: $color-mainbackground;
    @include sp{
      border-radius: 20px 20px 0 0;
      box-shadow: 0 -4px 4px rgba(0,0,0,0.08);
    }
  }
  .header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid $color-grayborder;
    position: relative;
    @include sp{
      padding: 0 14px;
      border-bottom: none;
    }
    h2{
      color: #333;
      font-size: 18px;
      font-weight: normal;
      margin: 15px 0;
      @include sp{
        font-size: 12px;
        font-weight: normal;
        margin: 3px 0 0;
        line-height: 2;
      }
    }
    &.is-pc{
      @include sp{
        display: none;
      }
    }
    &.is-sp{
      display: none;
      @include sp{
        display: flex;
      }
      .handle{
        display: block;
        padding: 8px;
        &::before{
          content: '';
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
        }
        span{
          display: block;
          width: 52px;
          height: 4px;
          border-radius: 4px;
          background: #333;
        }
      }
    }
    &_item{
      max-width: 80px;
      width: 100%;
      white-space: nowrap;
      padding: 12px 0 0;
      button{
        font-weight: bold;
        color: #333;
        position: relative;
        z-index: 1;
      }
    }
    &_center{
      text-align: center;
    }
  }
  .main{
    overflow-y: auto;
    max-height: calc(100vh - 200px);
    max-height: calc(100dvh - 200px);
    min-height: 400px;
    overscroll-behavior-y: none;
    @include sp{
      overflow-y: auto;
      max-height: calc(100vh - 92px);
      max-height: calc(100dvh - 92px);
      min-height: 160px;
    }
  }
}

.tab_menu{
  border-bottom: 1px solid $color-grayborder;
  a{
    display: inline-block;
    font-size: 14px;
    line-height: 1;
    padding: 15px 38px;
    color: $color-blackLight;
    text-decoration: none;
    position: relative;
    cursor: pointer;
    @include sp{
      width: 50%;
      padding: 15px;
      text-align: center;
    }
    &::before{
      content: '';
      width: 100%;
      height: 2px;
      position: absolute;
      left: 0;
      right: 0;
      bottom: -2px;
      z-index: 1;
    }
    &.is-active{
      color: #2F587C;
      font-weight: bold;
      &::before{
        background: currentColor;
      }
    }
  }
}

.wrap{
  max-width: 500px;
  margin: 0 auto;
  padding: 24px 20px;
}

.image_list{
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 8px;
  @include sp{
    grid-template-columns: 1fr 1fr 1fr;
  }
  .add_image{
    display: block;
    width: 100%;
    aspect-ratio: 1;
    border: 2px dotted $color-accent;
    position: relative;
    cursor: pointer;
    transition: 0.35s;
    &:hover{
      opacity: 0.5;
    }
    &::before,
    &::after{
      content: '';
      background: $color-accent;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }
    &::before{
      width: 18px;
      height: 2px;
    }
    &::after{
      width: 2px;
      height: 18px;
    }
    input{
      display: none;
    }
    label{
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
    &.is-pc{
      @include sp{
        display: none;
      }
    }
    &.is-sp{
      display: none;
      @include sp{
        display: block;
      }
    }
  }
  li:not(.add_image){
    padding-top: 100%;
    box-sizing: border-box;
    position: relative;
    display: block;
    background: #F4F4F4;
    cursor: pointer;
    transition: 0.35s;
    .icn-video {
      display: block;
      width: 20px;
      height: 20px;
      background-image: url(@/assets/images/icon-movie.svg);
      background-size: cover;
      position: absolute;
      top: 4px;
      left: 4px;
      pointer-events: none;
      z-index: 1;
    }
    .delete-btn {
      position: absolute;
      top: 4px;
      right: 4px;
      width: 20px;
      height: 20px;
      background: #fff;
      border: none;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      cursor: pointer;
      z-index: 2;
      transition: 0.2s;
      &:hover:not(:disabled) {
        opacity: 0.5;
      }
      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }
      img {
        width: 12px;
        height: 12px;
        position: static;
      }
    }
    &:hover:not(:has(.delete-btn:hover)){
      opacity: 0.5;
    }
  }
  img{
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right:0;
    left: 0;
    bottom: 0;
    object-fit: contain;
  }
}

@keyframes slide-in {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-out {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

@include sp{
  .slide-enter-active {
    .modal_background{
      transition: 0.35s;
      opacity: 1;
    }
    .modal_box{
      animation: slide-in 0.5s forwards;
    }
  }

  .slide-leave-active {
    .modal_background{
      transition: 0.35s;
      opacity: 0;
    }
    .modal_box{
      animation: slide-out 0.5s forwards;
    }
  }
}

// .loader-container {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   position: absolute;
//   // top: 50%;
//   // left: 50%;
//   // transform: translate(-50%, -50%);
//   width: 100%;
//   height: 100%;
// }

.loader {
  display: block;
    width: 50%;
    height: 50%;
    position: absolute;
    top: 25%;
    left: 25%;
    // transform: translate(-50%, -50%);
    -o-object-fit: contain;
    object-fit: contain;
  aspect-ratio: 1;
  border-radius: 50%;
  background: 
    radial-gradient(farthest-side,#808080 94%,#0000) top/8px 8px no-repeat,
    conic-gradient(#0000 30%,#808080);
  -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 8px),#000 0);
  animation: l13 1s infinite linear;
}

@keyframes l13 {
  100% {transform: rotate(1turn)}
}

.confirm {
  &_title {
    color: #333;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.5;
    margin: 0 0 16px;
  }
  &_alert {
    color: #FF1B1B;
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
  }
}

.loading-text {
  color: #666;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1000;
}
</style>