<template>
  <div class="modal">
    <div class="modal_background" @click="emits('close');"></div>
    <div class="modal_box">
      <div class="header">
        <h2>トリミング</h2>
        <button class="modalClose" @click="emits('close');">
          <img src="@/assets/images/icon-close-b.svg" alt="閉じる" />
        </button>
      </div>
      <div class="contents">{{ rotateData.value }}
        <div class="img-cropper" v-if="src !== ''">
          <vue-cropper
            ref="cropper"
            :src="props.src"
            :style="{ height: '480px' }"
            :data="trimmingData"
            :view-mode="2"
            :auto-crop-area="1"
            :guides="false"
            :check-cross-origin="false"
            :check-orientation="false"
            :restore="true"
            drag-mode="none"
            :movable="false"
            :scalable="false"
            :zoomable="false"
            :zoomOnTouch="false"
            :zoomOnWheel="false"
            :aspect-ratio="props.aspectWidth && props.aspectHeight ? props.aspectWidth / props.aspectHeight : null"
            @cropmove="onCropMove"
          >
          </vue-cropper>
          <a class="cropper-rotate" @click="onClickRotate()"></a>
        </div>
      </div>

      <div class="footer">
        <div class="footer_wrap">
          <ButtonMainColor
            baseColor="accent"
            size="md"
            @click="onClickTrimming"
          >適用する</ButtonMainColor>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import VueCropper from 'vue-cropperjs';
import 'cropperjs/dist/cropper.css';

const props = withDefaults(defineProps<{
  src: string,
  name?: string,
  uuid: string,
  crop?: {},
  aspectWidth: number,
  aspectHeight: number,
}>(), {
  src: ''
});

const emits = defineEmits<{
  close: [],
  crop: []
}>();

const cropper = ref();
const src = ref();
const rotateData = ref(0);
const trimmingData = ref({
  rotate: 0
});

if(props.crop){
  trimmingData.value = {
    x: props.crop?.x,
    y: props.crop?.y,
    width: props.crop?.width,
    height: props.crop?.height,
    rotate: props.crop?.rotate ?? 0,
    scaleX: props.crop?.scaleX,
    scaleY: props.crop?.scaleY,
  };
  rotateData.value = props.crop?.rotate ?? 0;
}

const onClickTrimming = () => {
  let imageData = cropper.value.getData();
  let cropBoxData = cropper.value.getCropBoxData();
  const originalImageWidth = cropper?.value?.getImageData().naturalWidth;
  const displayedImageWidth = cropper?.value?.getImageData().width;
  const scaleFactor = originalImageWidth / displayedImageWidth;

  let data = {
    src: props.src,
    uuid: props.uuid,
    name: props.name ?? "",
    x: imageData.x,
    y: imageData.y,
    width: imageData.width,
    height: imageData.height,
    rotate: rotateData.value,
    scaleX: imageData.scaleX,
    scaleY: imageData.scaleY,
    cropBoxLeft: cropBoxData.left,
    cropBoxTop: cropBoxData.top,
    cropBoxWidth: cropBoxData.width,
    cropBoxHeight: cropBoxData.height,
    originalWidth: cropper.value.cropper.imageData.naturalWidth,
    originalHeight: cropper.value.cropper.imageData.naturalHeight,
    scaleFactor: scaleFactor
  }
  emits('crop', data);
};

const onCropMove = () => {
  const cropBoxData = cropper.value.getCropBoxData();
  const canvasData = cropper.value.getCanvasData();
  
  // トリミングボックスが画像の範囲を超えないように調整
  if (cropBoxData.left < canvasData.left) {
    cropper.value.setCropBoxData({ left: canvasData.left });
  }
  if (cropBoxData.top < canvasData.top) {
    cropper.value.setCropBoxData({ top: canvasData.top });
  }
  if (cropBoxData.left + cropBoxData.width > canvasData.left + canvasData.width) {
    cropper.value.setCropBoxData({ left: canvasData.left + canvasData.width - cropBoxData.width });
  }
  if (cropBoxData.top + cropBoxData.height > canvasData.top + canvasData.height) {
    cropper.value.setCropBoxData({ top: canvasData.top + canvasData.height - cropBoxData.height });
  }
};

const onClickRotate = () => {
  if (cropper.value) {
    cropper.value.rotate(90);
    rotateData.value += 90;
    if(rotateData.value >= 360){
      rotateData.value = 0;
    }
  }
};
</script>

<style lang="scss" scoped>
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  opacity: 1;
  z-index: 1000;
  transition: opacity 0.35s ease;
  &_background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
  }
  &_box{
    background: $color-mainbackground;
    max-width: 640px;
    width: 100%;
  }
  .header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    h2{
      color: #333;
      font-size: 18px;
      font-weight: normal;
      margin: 15px 0;
      @include sp{
        font-size: 16px;
        font-weight: normal;
      }
    }
  }
  .modalClose{
    display: flex;
    align-items: center;
  }
  .contents{
    overflow: hidden;
    .img-cropper{
      height: 480px;
      position: relative;
      .cropper-rotate{
        display: block;
        width: 44px;
        height: 44px;
        background-image: url(@/assets/images/icon-trimming-rotate.svg);
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        position: absolute;
        right: 12px;
        bottom: 12px;
        cursor: pointer;
        transition: 0.35s;
        opacity: 0.5;
        z-index: 1;
        @include sp{
          right: 0;
          left: 0;
          margin: auto;
        }
      }
    }
    .wrap{
      max-width: 500px;
      width: 100%;
      padding: 36px 20px;
      margin: 0 auto;
      text-align: center;
    }
  }
  .footer{
    padding: 16px 20px 30px;
    text-align: center;
    @include sp{
      padding: 16px 20px;
    }
    a{
      max-width: 400px;
    }
  }
}

</style>