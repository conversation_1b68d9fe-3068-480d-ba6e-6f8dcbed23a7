<template>
  <div>
    <div class="modal">
      <div class="modal_background" @click="emits('close');"></div>
      <div class="modal_box">
        <div class="header">
          <h2>動画/画像をアップロード</h2>
          <button class="modalClose" @click="emits('close');">
            <img src="@/assets/images/icon-close-b.svg" alt="閉じる" />
          </button>
        </div>

        <div class="contents">
          <div class="wrap">
            <InputDropzone3
              :value="upload_image"
              :imageUploadType="props.imageUploadType"
              @change="onChangeFile"
            />
            <!-- <div v-if="props.qrId" class="upload_fromSp">
              <h2 class="upload_text_lg">スマホの写真をアップロード</h2>
              <p class="upload_text_sm">スマホで以下の二次元バーコードを読み込んでください</p>
              <div class="qrcode_wrap">
                <QRCodeVue3
                  :value="url"
                  :cornersSquareOptions="{
                    type: 'square'
                  }"
                  :dotsOptions="{
                    type: 'square'
                  }"
                />
              </div>
            </div> -->
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import QRCodeVue3 from "qrcode-vue3";
const props = withDefaults(defineProps<{
  imageUploadType: 'user' | 'admin',
  qrId?: string
}>(), {
  imageUploadType: 'user'
});

const emits = defineEmits<{
  close: [],
  upload: []
}>();

const url = computed(() => {
  let currentUrl = window.location.href
  if (currentUrl.endsWith('#')) {
    currentUrl = currentUrl.slice(0, -1) // 最後の#を削除
  } else if (currentUrl.includes('#')) {
    currentUrl = currentUrl.split('#')[0] // ハッシュとその後ろを削除
  }
  return `${currentUrl}#${props.qrId}`
})

const upload_image = ref('');
const upload_uuid = ref('');

// ファイル変更
const onChangeFile = async(image) => {
  upload_image.value = image.presigned_url;
  upload_uuid.value = image.uuid;
  emits('upload', image);
};

</script>

<style lang="scss" scoped>
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  opacity: 1;
  z-index: 1000;
  transition: opacity 0.35s ease;
  &_background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
  }
  &_box{
    background: $color-mainbackground;
    max-width: 640px;
    width: 100%;
  }
  .header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid $color-grayborder;
    h2{
      color: #333;
      font-size: 18px;
      font-weight: normal;
      margin: 15px 0;
      @include sp{
        font-size: 16px;
        font-weight: normal;
      }
    }
  }
  .contents{
    overflow: auto;
    max-height: calc(100vh - 200px);
    min-height: 260px;
    .wrap{
      max-width: 500px;
      width: 100%;
      padding: 36px 20px;
      margin: 0 auto;
      text-align: center;
    }
  }
  .footer{
    padding: 16px 20px 30px;
    text-align: center;
    a{
      max-width: 400px;
    }
  }
}

.upload{
  cursor: pointer;
  transition: 0.35s;
  &:hover{
    opacity: 0.5;
  }
  @include sp {
    &:hover{
      opacity: 1;
    }
  }
  &_text{
    &_lg{
      font-size: 20px;
      font-weight: bold;
    }
    &_md{
      font-size: 16px;
    }
    &_sm{
      font-size: 14px;
    }
  }
}

.upload_fromSp{
  text-align: center;
}

.qrcode_wrap{
  max-width: 200px;
  margin: 0 auto;
}

</style>