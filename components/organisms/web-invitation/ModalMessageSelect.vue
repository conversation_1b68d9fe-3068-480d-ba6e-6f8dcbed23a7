<template>
  <div class="modal">
    <div class="modal_background" @click="emits('close')"></div>
    <div class="modal_box">
      <div class="header">
        <h2>例文から選ぶ</h2>
        <button class="modalClose" @click="emits('close')">
          <img src="@/assets/images/icon-close-b.svg" alt="閉じる" />
        </button>
      </div>

      <div v-if="loading" class="contents">
        <Loading></Loading>
      </div>
      <div v-else class="contents" :class="{'is-sidebar': isSidebar && props.isShowFilterSettings || isSidebar && !props.isShowFilterSettings && groupedSentencesArray && groupedSentencesArray.length > 2}">
        <div class="sidebar" v-if="isSidebar && props.isShowFilterSettings || isSidebar && !props.isShowFilterSettings && groupedSentencesArray && groupedSentencesArray.length > 2">
          <div v-if="props.isShowFilterSettings" class="sidebar_section">
            <h3 class="cmn-title">送信月・時候の挨拶</h3>
            <div class="sidebar_block">
              <InputSelect
                title="送信月"
                :options="months"
                :value="selectedDate"
                @change="updateRadioOptions($event.target.value)"
              />
            </div>
            <div class="sidebar_block">
              <InputRadio
                title="時候の挨拶"
                :block="true"
                :items="radioItems"
                :value="radioItems[0].value"
                @change="onClickRadio($event)"
              />
            </div>
          </div>
          <div v-if="groupedSentencesArray && groupedSentencesArray.length > 2" class="sidebar_section">
            <h3 class="cmn-title">例文一覧</h3>
            <div class="sidebar_block">
              <InputSelect
                title="カテゴリーから選ぶ"
                :options="groupedSentencesArray"
                :value="activeTab"
                @change="onChangeCategory($event.target.value)"
              />
            </div>
          </div>
        </div>
        <div class="main">
          <div v-for="(item, type) in groupedSentences" :key="type">
            <div class="tab" v-if="activeTab == type">
              <div class="item" v-for="(message, messageIndex) in item" :key="messageIndex">
                <template v-if="message && message.example_text">
                  <a @click="onClickMessage(message)">
                    <div v-if="message.title" class="title">{{ message.title }}</div>
                    <div class="preWrap">{{ message.example_text }}</div>
                  </a>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
const config = useRuntimeConfig()
const props = withDefaults(defineProps<{
  formUpdated?: any,
  isSidebar?: boolean,
  exampleType?: string,
  isShowFilterSettings?: boolean
}>(), {
  isSidebar: true,
  isShowFilterSettings: true
});

const formUpdated = ref(props.formUpdated);
watch(() => props, (newVal) => {
  formUpdated.value = newVal.formUpdated;
}, {
  deep: true
});

// 現在の年月を取得
const now = dayjs()
// 現在月から2ヶ月前〜12ヶ月先までの月を生成
const months = ref([])
for (let i = -2; i <= 12; i++) {
  const date = now.add(i, 'month')
  months.value.push({
    value: date.format('YYYY-M'),
    label: date.format('YYYY年M月')
  })
}
// 初期値を現在の月に設定
const selectedDate = ref(now.format('YYYY-M'));

const { exampleSentences, loading } = useGetManyExampleSentence(props.exampleType);

// JSONデータを保存するためのref
const jsonExampleSeasonalGreetings = ref(null)
const jsonExampleContentSeason = ref(null)

onMounted(async () => {
  try {
    const response1 = await fetch(config.public.s3PublicUrlExampleSeasonalGreetings as string)
    if (!response1.ok) {
      throw new Error(`HTTP error! status: ${response1.status}`)
    }
    jsonExampleSeasonalGreetings.value = await response1.json()
  } catch (error) {
    console.error('Error fetching JSON data:', error)
  }
  try {
    const response2 = await fetch(config.public.s3PublicUrlExampleContentSeason as string)
    if (!response2.ok) {
      throw new Error(`HTTP error! status: ${response2.status}`)
    }
    jsonExampleContentSeason.value = await response2.json()
  } catch (error) {
    console.error('Error fetching JSON data:', error)
  }
  // 初期表示時に`radio`ボタンのリストを更新
  updateRadioOptions(selectedDate.value)
})

// ラジオボタン
const currentWords = ref([])
const updateRadioOptions = (event:string) => {
  selectedDate.value = event;
  const selectedDateValue = parseInt(selectedDate.value.split('-')[1]) // 月を抽出
  const wordsForselectedDate = jsonExampleSeasonalGreetings.value.filter(item => item.month === selectedDateValue)
  currentWords.value = wordsForselectedDate.map(item => item.word)
}
const radioItems = computed(() => currentWords.value.map((word, index) => ({
  value: word,
  checked: index === 0,
  label: word
})))

const activeRadio = ref('');
const onClickRadio = (event:string) => {
  activeRadio.value = event;
}

// 置き換え関数
const replacePlaceholders = (text, replacementsArray) => {
  let replacedText = text;
  for (const [key, value] of Object.entries(replacementsArray)) {
    const regex = new RegExp(key, 'g');
    replacedText = replacedText.replace(regex, value);
  }
  return replacedText;
};
const groupedSentences = computed(() => {
  if (!exampleSentences.value) return { '全て': [] };
  const groups = { '全て': [] };

  // jsonExampleContentSeasonのデータをマップに変換
  const typeMap = {};
  jsonExampleContentSeason.value.forEach(item => {
    groups[item.name] = [];
    typeMap[item.id] = item.name;
  });

  exampleSentences.value.example_sentences.forEach(sentence => {
    const typeName = typeMap[sentence.example_type2];

    if (!groups[typeName]) {
      groups[typeName] = [];
    }

    const replacedText = replacePlaceholders(sentence.example_text, replacements.value);
    groups[typeName].push({
      ...sentence,
      example_text: replacedText
    });
    groups['全て'].push({
      ...sentence,
      example_text: replacedText
    });
  });

  for (const key in groups) {
    if (key !== '全て' && Array.isArray(groups[key]) && groups[key].length === 0) {
      delete groups[key];
    }
  }

  return groups;
});

const groupedSentencesArray = computed(() => {
  const groupedData = groupedSentences.value;
  return Object.keys(groupedData).map(key => {
    return {
      value: key,
      label: key
    };
  });
});

const activeTab = ref('全て');
const onChangeCategory = (event: string) => {
  activeTab.value = event;
};

// WEB招待状の設定を取得
const onFilterBlock = (id: string) => {
  if(!formUpdated.value || !formUpdated?.value?.blocks) { return null }
  const block = formUpdated.value.blocks.find(block => block.id === id);
  return block ? block : null;
};

// 置き換え用のオブジェクト
const replacements = ref({
  "{{時候の句}}": activeRadio?.value ? activeRadio.value : '',
  "{{選択年}}": selectedDate?.value ? selectedDate.value.split('-')[0] : '',
  "{{選択月}}": selectedDate?.value ? selectedDate.value.split('-')[1] : '',
  "{{新郎姓名}}": "",
  "{{新婦姓名}}": ""
});
watch(selectedDate, (newDate) => {
  replacements.value["{{選択年}}"] = newDate.split('-')[0];
  replacements.value["{{選択月}}"] = newDate.split('-')[1];
}, { immediate: true });
watch(radioItems, (newDate) => {
  onClickRadio(newDate?.[0]?.value ? newDate[0].value : '');
}, { immediate: true });
watch(activeRadio, (newDate) => {
  replacements.value["{{時候の句}}"] = newDate ? newDate : '';
}, { immediate: true });
watch(formUpdated.value, () => {
  if(formUpdated.value || !formUpdated?.value?.blocks){
    replacements.value["{{新郎姓名}}"] = onFilterBlock('profile').contents[0].name ? onFilterBlock('profile').contents[0].name : '';
    replacements.value["{{新婦姓名}}"] = onFilterBlock('profile').contents[1].name ? onFilterBlock('profile').contents[1].name : '';
  }
}, {
  deep: true,
  immediate: true
});

const onClickMessage = (message:string) => {
  emits('select', message);
};

const emits = defineEmits<{
  close: [],
  select: []
}>();

</script>

<style lang="scss" scoped>
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  opacity: 1;
  z-index: 1000;
  transition: opacity 0.35s ease;
  &_background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
  }
  &_box{
    max-width: 760px;
    width: 100%;
    background: $color-mainbackground;
    @include sp{
      height: 100%;
    }
  }
}
.header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid #D9D9D9;
  @include sp{
    min-height: 52px;
  }
  h2{
    color: #333;
    font-size: 18px;
    font-weight: normal;
  }
}
.cmn-title{
  margin-top: 0;
}
.contents{
  height: 626px;
  max-height: calc(100vh - 100px);
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  grid-gap: 0;
  overflow: hidden;
  @include sp{
    overflow: auto;
    height: auto;
    max-height: calc(100vh - 52px);
    max-height: calc(100dvh - 52px);
  }
  &.is-sidebar{
    grid-template-columns: 226px 1fr;
    grid-template-rows: 1fr;
    @include sp{
      display: flex;
      flex-direction: column;
      :deep(label.md){
        max-width: 100%;
      }
      :deep(label.is-block){
        display: inline-block;
      }
    }
  }
}
.main{
  .tab{
    max-height: calc(100vh - 100px);
    height: 626px;
    overflow: auto;
    @include sp{
      max-height: none;
      height: auto;
      overflow: hidden;
    }
  }
}
.sidebar{
  padding: 16px 20px;
  grid-column: 1 / 2;
  grid-row: 1 / 3;
  border-right: 1px solid #D9D9D9;
  overflow: auto;
  @include sp{
    padding-bottom: 0;
    border-right: 0px;
    border-bottom: 1px solid #D9D9D9;
    overflow: hidden;
    min-height: auto;
    flex: 0 0 auto;
  }
  &_block{
    margin-bottom: 24px;
  }
}
.item{
  &:last-child{
    @include sp{
      border-bottom: 1px solid #D9D9D9;
    }
  }
  a{
    display: block;
    color: #444;
    text-decoration: none;
    font-size: 14px;
    line-height: 1.5;
    white-space: break-spaces;
    position: relative;
    padding: 16px 58px 16px 16px;
    transition: 0.35s ease-in-out;
    @include sp{
      padding: 16px 36px 16px 12px;
    }
    &:hover{
      @include pc{
        opacity: 0.8;
      }
      &::before {
        @include pc{
          transform: translate(4px, -50%);
        }
      }
    }
    &::before {
      @include BA;
      right: 16px;
      top: 0;
      bottom: 0;
      margin: auto;
      width: 25px;
      height: 24px;
      background: #333;
      mask-image: url(@/assets/images/icon-chevron_right.svg);
      mask-size: 8px 16px;
      mask-position: center;
      mask-repeat: no-repeat;
      transition: 0.2s ease-in-out;
      @include sp{
        right: 8px;
      }
    }
  }
  & + .item{
    border-top: 1px solid #D9D9D9;
  }
}
.title{
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 6px;
}
</style>