<template>
  <div class="modal">
    <div class="modal_background" @click="emits('close')"></div>
    <div class="modal_box">
      <div class="header">
        <h2>QRコード作成</h2>
        <button class="modalClose" @click="emits('close')">
          <img src="@/assets/images/icon-close-b.svg" alt="閉じる" />
        </button>
      </div>

      <div class="contents">
        <div class="main">
          <div>
            <QRCodeVue3
              :value="url"
              :cornersSquareOptions="{
                type: 'square'
              }"
              :dotsOptions="{
                type: 'square'
              }"
            />
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import QRCodeVue3 from "qrcode-vue3";

const props = withDefaults(defineProps<{
  url?: string
}>(), {
  url: ''
});

const url = ref('');
watch(props, async(newVal) => {
  if(newVal.url){
    url.value = newVal.url;
  }
}, {
  deep: true,
  immediate: true
})

const emits = defineEmits<{
  close: []
}>();

</script>

<style lang="scss" scoped>
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  opacity: 1;
  z-index: 1000;
  transition: opacity 0.35s ease;
  &_background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
  }
  &_box{
    max-width: 640px;
    width: 100%;
    background: #F4F4F4;
    @include sp{
      height: 100vh;
      height: 100dvh;
    }
  }
}
.contents{
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 320px;
  @include sp{
    min-height: 100vh;
    min-height: 100dvh;
  }
  .main{
    img{
      width: 280px;
      height: 280px;
    }
  }
}
.header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #fff;
  border-bottom: 1px solid #D9D9D9;
  h2{
    color: #333;
    font-size: 18px;
    font-weight: normal;
  }
}
</style>