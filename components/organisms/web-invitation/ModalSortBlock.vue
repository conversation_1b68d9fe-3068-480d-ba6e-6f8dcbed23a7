<template>
  <div class="modal">
    <div class="modal_background" @click="emits('close');"></div>
    <div class="modal_box">
      <div class="header">
        <h2>並び替え・非表示設定</h2>
        <button class="modalClose" @click="emits('close');">
          <img src="@/assets/images/icon-close-b.svg" alt="閉じる" />
        </button>
      </div>

      <div class="contents">
        <div class="wrap">
          <draggable :list="listData" item-key="id" tag="ul" class="dragArea" handle=".handle">
            <template #item="{ element, index }">
              <li v-if="!useRuntimeConfig()?.public?.app?.is_active_prepaid && element.id !== 'gift' && element.id !== 'mainVisual' || useRuntimeConfig()?.public?.app?.is_active_prepaid && element.id !== 'gift' && element.id !== 'mainVisual'">
                <div class="handle"></div>
                <div class="title">{{ element.name }}</div>
                <div class="switch">
                  <InputSwitch
                    v-if="element.id !== 'information'"
                    :checked="element.visible"
                    @update:checked="onUpdateSwitch(index, $event)"
                  ></InputSwitch>
                </div>
              </li>
            </template>
          </draggable>
        </div>
      </div>

      <div class="footer">
        <div class="footer_wrap">
          <ButtonMainColor size="md" baseColor="accent" @click="onClickSave();">反映する</ButtonMainColor>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import draggable from 'vuedraggable';
const props = withDefaults(defineProps<{
  data?: {
    name?: string,
    id?: string,
    visible?: boolean
  }[]
}>(), {});

const listData = reactive([...props.data]);

watch(() => props.data, (newVal) => {
  listData.splice(0, listData.length, ...newVal);
}, {
  deep: true,
  immediate: true
});

const emits = defineEmits(['close', 'update']);
const onUpdateSwitch = (index: number, event: any) => {
  let dataUpdated = JSON.parse(JSON.stringify(listData));
  dataUpdated[index].visible = event;
  listData.splice(0, listData.length, ...dataUpdated);
};

const onClickSave = () => {
  emits('update', listData);
  emits('close');
};

</script>

<style lang="scss" scoped>
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  opacity: 1;
  z-index: 1000;
  transition: opacity 0.35s ease;

  &_background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
  }

  &_box {
    max-width: 640px;
    width: 100%;
    background: $color-mainbackground;
    height: 80vh;
    height: 80dvh;
    @include sp {
      height: 100vh;
      height: 100dvh;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    box-shadow: 0 4px 4px rgba(0,0,0,0.08);
    h2{
      font-weight: normal;
      font-size: 16px;
    }
  }

  .contents {
    overflow: auto;
    height: calc(80vh - 130px);
    height: calc(80dvh - 130px);
    overscroll-behavior-y: none;
    @include sp {
      height: calc(100vh - 130px);
      height: calc(100dvh - 130px);
    }
    li{
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      padding: 12px 0 12px 34px;
      border-bottom: 1px solid #F4F4F4;
      position: relative;
      .handle {
        display: block;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        margin: auto;
        width: 26px;
        height: 28px;
        background-image: url(@/assets/images/icon-drag.svg);
        background-repeat: no-repeat;
        background-position: center;
        cursor: grab;
      }
      &:first-child{
        .title + .switch{
          position: relative;
          &::before{
            content: '表示';
            position: absolute;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            top: -2em;
            left: 0;
            right: 0;
            margin: auto;
            white-space: nowrap;
            font-size: 10px;
            color: #111;
            text-align: center;
          }
        }
      }
    }
  }

  .wrap {
    max-width: 460px;
    width: 100%;
    margin: 0 auto;
    padding: 24px 16px;
  }

  .footer {
    padding: 16px 16px 30px;
    text-align: center;

    a {
      max-width: 400px;
    }
  }
}</style>