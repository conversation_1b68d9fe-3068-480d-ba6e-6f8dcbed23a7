<template>
  <div class="modal">
    <div class="modal_background" @click="emits('close')"></div>
    <div class="modal_box">
      <div class="header">
        <button class="modalClose" @click="emits('close')">
        </button>
      </div>

      <div class="navigation">
        <button class="modalNav modalNavPrev" @click="onClickPrevTheme()"></button>
        <button class="modalNav modalNavNext" @click="onClickNextTheme()"></button>
      </div>

      <div class="modal_wrap">
        <transition :name="transitionName">
          <div class="modal_box_wrap" :key="index">
            <div class="contents webInvitationView">
              <template v-if="!loading && form">
                <ViewFormWrap :data="form.blocks" :isSp="true">
                  <div class="blocks">
                    <div class="block" v-for="(block, index) in form.blocks" :key="index">
                      <component
                        :is="onSetComponent(block.id)"
                        :data="block.contents"
                        :informationData="onFilterBlock('information')"
                        :visible="block.visible"
                        :isPreview="true"
                        :webInvitationData="{
                          event_list: ['挙式・披露宴'],
                          editor_settings: form
                        }"
                      ></component>
                    </div>
                  </div>
                </ViewFormWrap>
              </template>
              <div v-else class="contents_loading">
                <Loading />
              </div>
              <slot />
            </div>
            <div class="footer">
              <a href="#" @click.prevent="onClickToggle()" class="footer_toggle" :class="{'is-open': isShowFooter}"></a>
              <transition name="slide-fade">
                <div class="footer_wrap" v-show="isShowFooter">
                  <p class="footer_title">{{ webInvitationData?.name }}</p>
                  <a class="footer_open" @click="isShowDetail = !isShowDetail" :class="{'is-open': isShowDetail}">詳細を読む</a>
                  <transition name="slide-fade">
                    <div class="footer_detail" v-show="isShowDetail">
                      <p>{{ webInvitationData?.product_description }}</p>
                    </div>
                  </transition>
                  <div class="footer_button">
                    <ButtonMainColor
                      v-if="isReSelect"
                      baseColor="accent"
                      size="md"
                      @click="onClickSelect"
                    >このデザインに変更</ButtonMainColor>
                    <ButtonMainColor
                      v-else
                      baseColor="accent"
                      size="md"
                      @click="onClickSelect"
                    >このデザインで始める</ButtonMainColor>
                  </div>
                </div>
              </transition>
            </div>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  index?: number,
  pageSort?: any,
  isReSelect?: boolean
}>(), {
  isReSelect: false
});

const emits = defineEmits<{
  close: [],
  select: []
}>();

const index = ref(props.index ? props.index + 1 : 1);
const pageSort = ref(props.pageSort);
const { webInvitationData, paginatorInfo, refetch, loading } = useGetNextMSpecificationProduct(
  computed(() => pageSort.value),
  computed(() => index.value),
  []
);

const isShowDetail = ref(false);
const form = ref();
const cssCode = ref();

let appendedStyle = null;
watch(() => props.index, (newVal) => {
  if (newVal !== undefined) {
    index.value = newVal + 1;
    refetch();
    if (appendedStyle) {
      appendedStyle.remove();
      appendedStyle = null;
    }
  }
}, {
  deep: true,
  immediate: true
});
watch(() => webInvitationData, (newVal) => {
  if(newVal && newVal?.value && newVal?.value?.m_specification_products){
    form.value = newVal?.value?.m_specification_products?.[0]?.m_web_invitations?.editor_settings_json;
    cssCode.value = newVal?.value?.m_specification_products?.[0]?.m_web_invitations?.css_code;

    if (appendedStyle) {
      appendedStyle.remove();
      appendedStyle = null;
    }

    const style = document.createElement('style');
    style.textContent = cssCode.value;
    appendedStyle = style;
    document.head.appendChild(style);
  }
}, {
  deep: true,
  immediate: true
});

onUnmounted(() => {
  if (appendedStyle) {
    appendedStyle.remove();
  }
});

const onFilterBlock = (id: String) => {
  let item = form?.value?.blocks.filter((element) => {
    return element.id == id;
  });
  if (!item) {
    return null
  }
  return item[0];
}

const state = reactive({
  mainVisual: markRaw(resolveComponent('ViewFormBlockMainVisual') as Component),
  countDown: markRaw(resolveComponent('ViewFormBlockCountDown') as Component),
  message: markRaw(resolveComponent('ViewFormBlockMessage') as Component),
  profile: markRaw(resolveComponent('ViewFormBlockProfile') as Component),
  gallery: markRaw(resolveComponent('ViewFormBlockGallery') as Component),
  information: markRaw(resolveComponent('ViewFormBlockInformation') as Component),
  gift: markRaw(resolveComponent('ViewFormBlockGift') as Component),
  freeField: markRaw(resolveComponent('ViewFormBlockFreeField') as Component),
  guestAnswer: markRaw(resolveComponent('ViewFormBlockGuestAnswer') as Component)
});

const onSetComponent = (id: String) => {
  return state[id];
}

const onClickSelect = async () => {
  emits('select', webInvitationData.value);
}

// 左右に移動
const transitionName = ref('slide-right');
const onClickPrevTheme = async () => {
  transitionName.value = 'slide-right';
  if(index.value <= 1){
    index.value = paginatorInfo.value.total;
  }else{
    index.value--;
  }
}
const onClickNextTheme = async () => {
  transitionName.value = 'slide-left';
  if(index.value >= paginatorInfo.value.total) {
    index.value = 1;
  }else{
    index.value++;
  }
}

const isShowFooter = ref(true);
const onClickToggle = () => {
  if(isShowFooter.value){
    isShowFooter.value = false;
  }else{
    isShowFooter.value = true;
  }
}
</script>

<style lang="scss" scoped>
.modal {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  height: 100dvh;
  z-index: 1000;
  &_background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: -1;
  }
  &_wrap{
    position: relative;
    min-height: 480px;
    max-height: 680px;
    height: calc(100vh - 100px);
    height: calc(100dvh - 100px);
    @include sp {
      height: 100vh;
      height: 100dvh;
      max-height: 100%;
    }
    overflow: hidden;
  }
  &_box{
    max-width: 376px;
    width: 100%;
    background: #F4F4F4;
    position: relative;
    @include sp {
      max-width: 100%;
    }
    &_wrap{
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      overflow: hidden;
    }
  }
  .header{
    position: relative;
    .modalClose{
      background: #FFF;
      width: 44px;
      height: 44px;
      border-radius: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 4px rgba(0,0,0,0.12);
      position: absolute;
      top: 0;
      right: -76px;
      z-index: 100;
      @include sp {
        top: 10px;
        right: 10px;
      }
      &::before{
        content: '';
        display: block;
        width: 14px;
        height: 14px;
        background: $color-main;
        mask-image: url('@/assets/images/icon-close-b.svg');
        mask-repeat: no-repeat;
        mask-position: center;
        mask-size: contain;
      }
    }
  }
  .navigation{
    .modalNav{
      background: #FFF;
      width: 44px;
      height: 44px;
      border-radius: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 4px rgba(0,0,0,0.12);
      z-index: 100;
      position: absolute;
      top: 0;
      bottom: 0;
      margin: auto;
      &::before{
        content: '';
        display: block;
        width: 12px;
        height: 18px;
        background: $color-main;
        mask-image: url('@/assets/images/icon-chevron-right-b.svg');
        mask-repeat: no-repeat;
        mask-position: center left 3px;
      }
      &.modalNavPrev{
        left: -76px;
        @include sp {
          left: 12px;
        }
        &::before{
          transform: rotate(180deg);
        }
      }
      &.modalNavNext{
        right: -76px;
        @include sp {
          right: 12px;
        }
      }
    }
  }
  .contents{
    overflow: hidden;
    min-height: 480px;
    max-height: 680px;
    height: calc(100vh - 100px);
    height: calc(100dvh - 100px);
    padding-bottom: 112px;
    @include sp {
      height: 100vh;
      height: 100dvh;
      max-height: 100%;
    }
    & > img{
      width: 100%;
      max-width: auto;
    }
    &_loading{
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
  }
  .footer{
    background: rgba(255,255,255,0.85);
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    p{
      margin-bottom: 4px;
    }
    &_toggle{
      display: block;
      width: 50px;
      height: 32px;
      background: #FFF;
      border-radius: 6px 0 0 0;
      position: absolute;
      right: 0;
      top: -32px;
      &::after{
        content: '';
        display: block;
        width: 16px;
        height: 16px;
        background: $color-main;
        mask-image: url('@/assets/images/icon-toggle.svg');
        mask-repeat: no-repeat;
        mask-position: center;
        mask-size: contain;
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        margin: auto;
        transition: 0.5s;
        transform: rotate(180deg);
      }
      &.is-open{
        &::after{
          transform: rotate(0deg);
        }
      }
    }
    &_wrap{
      padding: 0 12px;
    }
    &_title{
      padding-top: 12px;
    }
    &_open{
      display: inline-flex;
      align-items: center;
      font-size: 11px;
      color: #B18A3E;
      cursor: pointer;
      &:hover{
        text-decoration: underline;
      }
      &::after{
        content: '';
        display: inline-block;
        width: 20px;
        height: 20px;
        margin-right: 2px;
        background-color: #333;
        mask-image: url('@/assets/images/icon-chevron.svg');
        mask-repeat: no-repeat;
        mask-position: center;
        mask-size: 20px;
        transition: 0.35s ease-in-out;
      }
      &.is-open{
        &::after{
          transform: rotate(180deg);
        }
      }
    }
    &_detail{
      margin-top: 10px;
      p{
        font-size: 11px;
      }
    }
    &_button{
      margin: 12px auto 0;
      padding-bottom: 12px;
    }
  }
}

// 開閉アニメーション
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.5s ease;
  max-height: 200px;
}
.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(20px);
  opacity: 0;
  max-height: 0;
}
.footer_wrap {
  overflow: hidden;
}
.footer_detail {
  overflow: hidden;
}

.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.35s ease-in-out;
}

.slide-left-enter-from {
  transform: translateX(100%);
}

.slide-left-leave-to {
  transform: translateX(-100%);
}

.slide-right-enter-from {
  transform: translateX(-100%);
}

.slide-right-leave-to {
  transform: translateX(100%);
}
</style>