<template>
  <div>
    <div class="imageBox" :class="{'is-disabled': props.uuid == '', 'is-grayed-out': props.isGrayedOut}">
      <span v-if="isShowHandle" class="drag-handle"></span>
      <div class="imageBox_wrap">
        <CropVideo
          v-if="props.type == 'VIDEO' && props?.crop?.width"
          :uuid="uuid"
          :src="src"
          :srcVideo="srcVideo"
          :crop="crop"
          :load-key="loadKey"
        />
        <VideoUUID
          v-else-if="props.type == 'VIDEO'"
          :uuid="props.uuid"
          :src="src"
          :srcVideo="props.srcVideo"
        />
        <div v-else-if="props.uuid == ''" class="loader-container">
          <div class="loader"></div>
        </div>
        <ImageUUID
          v-else-if="props.uuid && ! props?.src"
          :uuid="props.uuid"
        />
        <CropImage
          v-else-if="props.uuid && props?.src"
          :uuid="uuid"
          :src="src"
          :crop="crop"
        ></CropImage>
      </div>
      <div class="imageBox_item imageBox_item-edit popup_open" @click="onToggleMenu(1)">編集</div>
      <transition name="fade">
        <ul v-if="isShowMenu === 1" class="popup_menu">
          <li v-if="props.type != 'VIDEO' || props?.crop?.width"><a @click="onToggleMenu(1); emit('trim')">トリミング</a></li>
          <li><a @click="onToggleMenu(1); emit('change')">{{props.type == 'VIDEO' ? '動画を変更' : '画像を変更'}}</a></li>
          <li><a @click="emit('cancel')">削除</a></li>
        </ul>
      </transition>
    </div>
    <div class="imageBox_videoMenu" v-if="isShowVideoMenu">
      <div class="imageBox_videoMenu_left">
        <InputCheckSingle
          label="動画の音声を再生する"
          :value="!isVideoAutoplay"
          :checked="!isVideoAutoplay"
          @change="onChangeAutoplay($event)"
        />
      </div>
    </div>
    <div v-if="props.isGrayedOut" class="grayed-out-message">
      {{ props.type === 'VIDEO' ? 'この動画は表示されません' : 'この画像は表示されません' }}
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  uuid: string,
  src?: string,
  srcVideo?: string,
  type?: string,
  isShowHandle?: boolean,
  crop?: {
    x: number,
    y: number,
    width: number,
    height: number
  },
  isShowVideoMenu?: boolean,
  isVideoAutoplay?: boolean,
  isGrayedOut?: boolean
}>(), {
  uuid: '',
  src: '',
  isShowHandle: false,
  isShowVideoMenu: false,
  isVideoAutoplay: false,
  isGrayedOut: false,
  showGrayedOutMessage: false
});
const src = ref();
const srcVideo = ref();
const crop = ref();
const uuid = ref();
const isShowHandle = ref(false);
const loadKey = ref(0);
const isShowVideoMenu = ref(props.isShowVideoMenu);
const isVideoAutoplay = ref(props.isVideoAutoplay);
watch(() => props, (newVal, oldVal) => {
  uuid.value = newVal.uuid;
  src.value = newVal.src;
  srcVideo.value = newVal.srcVideo ? newVal.srcVideo : null;
  crop.value = newVal.crop;
  isShowHandle.value = newVal.isShowHandle;
  isShowVideoMenu.value = newVal.isShowVideoMenu;
  isVideoAutoplay.value = newVal.isVideoAutoplay;
  if(newVal.srcVideo != oldVal.srcVideo) {
    loadKey.value++;
  }
}, {
  deep: true,
  immediate: true
});

const onChangeAutoplay = (value:boolean) => {
  isVideoAutoplay.value = !value;
  emit('change-autoplay', isVideoAutoplay.value);
};

const isShowMenu = ref();
const onToggleMenu = (index:number) => {
  isShowMenu.value = isShowMenu.value == index ? null : index;
};
const handleClickOutside = (event:any) => {
  if (!event.target.closest('.popup_open')) {
    isShowMenu.value = null;
  }
};

onMounted(() => {
  if (typeof document !== 'undefined') {
    document.addEventListener('click', handleClickOutside);
  }
});

onBeforeUnmount(() => {
  if (typeof document !== 'undefined') {
    document.removeEventListener('click', handleClickOutside);
  }
});

const emit = defineEmits([
  'change',
  'cancel',
  'trim',
  'change-autoplay',
  'change-muted'
]);

</script>

<style lang="scss" scoped>
.imageBox{
  max-width: 320px;
  width: 100%;
  height: 160px;
  margin: 0 0 12px;
  padding: 8px 40px;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  position: relative;
  text-align: center;
  display: flex;
  align-items: center;
  @include sp{
    max-width: 380px;
  }
  &.is-disabled {
    pointer-events: none;
    opacity: 0.5;
  }
  &.is-grayed-out {
    position: relative;
    :deep(){
      img,
      video {
        filter: grayscale(100%);
      }
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.05);
      pointer-events: none;
      z-index: 1;
    }
  }
  &_wrap{
    position: relative;
    display: flex;
    align-items: stretch;
    min-width: fit-content;
    min-width: -webkit-fill-available;
    width: auto;
    height: 140px;
    max-height: 140px;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    :deep(.crop_image){
      max-height: 140px;
    }
    :deep(.crop_image.crop_video){
      max-height: 100%;
    }
    :deep(video){
      max-height: inherit;
    }
    :deep(){
      & > img {
        max-height: 100%;
      }
    }
  }
  &_item{
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-size: 24px;
    position: absolute;
    cursor: pointer;
    &-edit{
      color: #9C9C9C;
      padding: 4px 8px;
      font-size: 12px;
      width: auto;
      top: 8px;
      right: 4px;
    }
  }
  &_videoMenu{
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 320px;
    width: 100%;
    @include sp{
      max-width: 380px;
    }
    &_left {
      :deep() {
        label {
          margin-bottom: 0;
        }
      }
    }
  }
}

.drag-handle {
  display: block;
  width: 24px;
  height: 32px;
  background-color: #D9D9D9;
  mask-image: url(@/assets/images/icon-drag.svg);
  mask-repeat: no-repeat;
  mask-size: 24px;
  position: absolute;
  top: 8px;
  left: 12px;
  cursor: move;
}

.popup {
  &_menu {
    text-align: left;
    position: absolute;
    right: 10px;
    top: 34px;
    margin: 0;
    padding: 0;
    width: 246px;
    border-radius: 4px;
    background: #fff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    transition: 0.35s ease-in-out;
    z-index: 2;
    li {
      display: block;
      width: 100%;
    }
    a {
      display: block;
      width: 100%;
      padding: 12px 16px;
      line-height: 1;
      font-size: 16px;
      color: #1C1B1F;
      background: #fff;
      cursor: pointer;
      transition: 0.35s ease-in-out;
      &:hover {
        background: #EFF8FF;
      }
    }
  }
  &_wrap{
    position: relative;
    text-align: right;
    margin: 36px 0 0;
  }
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.loader {
  width: 50px;
  aspect-ratio: 1;
  border-radius: 50%;
  background: 
    radial-gradient(farthest-side,#808080 94%,#0000) top/8px 8px no-repeat,
    conic-gradient(#0000 30%,#808080);
  -webkit-mask: radial-gradient(farthest-side,#0000 calc(100% - 8px),#000 0);
  animation: l13 1s infinite linear;
}


.sound{
  color: $color-main;
  font-size: 14px;
  display: flex;
  align-items: center;
  line-height: 1;
  margin: 0;
  cursor: pointer;
  transition: 0.35s ease-in-out color;
  white-space: nowrap;
  text-decoration: none;
  &:hover{
    text-decoration: underline;
  }
  &::before{
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 2px;
    background-color: currentColor;
    mask-image: url('@/assets/images/icon-sound.svg');
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: 22px;
    vertical-align: middle;
  }
  &.is-muted{
    color: $color-main;
    &:hover{
      text-decoration: underline;
    }
    &::before{
      mask-image: url('@/assets/images/icon-sound-muted.svg');
    }
    &.is-disabled{
      color: $color-grayborder;
      pointer-events: none;
      &:hover{
        text-decoration: none;
      }
      &::before{
        mask-image: url('@/assets/images/icon-sound-muted.svg');
      }
    }
  }
  &.is-disabled{
    color: $color-grayborder;
    pointer-events: none;
    &:hover{
      text-decoration: none;
    }
    &::before{
      mask-image: url('@/assets/images/icon-sound-muted.svg');
    }
  }
}

@keyframes l13 {
  100% {transform: rotate(1turn)}
}

.grayed-out-message {
  max-width: 320px;
  font-size: 12px;
  color: #333;
  text-align: right;
  margin: -8px 0 8px;
}
</style>