<template>
  <div class="webInvitation_block" data-animation v-if="isChecked != false">
    <div class="webInvitation_block_wrap">
      <slot/>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = withDefaults(defineProps<{
  isChecked?: boolean
}>(), {
  isChecked: true
});

const isChecked = ref(props.isChecked);
watch(() => props, (newVal) => {
  isChecked.value = newVal.isChecked;
}, {
  deep: true
});

const emit = defineEmits(['change']);
const update = (event) => {
  isChecked.value = event;
  emit('change', event);
};
</script>

<style lang="scss" scoped>
</style>