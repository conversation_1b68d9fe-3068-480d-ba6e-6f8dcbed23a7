<template>
  <ViewFormBlock :isChecked="props.visible">
    <div id="countDown" class="viewBlock countDown" data-animation>
      <div class="viewBlock_wrap countDown_wrap">
        <div class="countDown_box">
          <div v-if="date">
          <div class="countDown_symbol countDown_symbol_1" data-animation></div>
          <h2 class="countDown_title"><span class="lang-ja">結婚式まで</span><span class="lang-en">Count Down</span></h2>
          <div class="countDown_blocks">
            <div class="countDown_symbol countDown_symbol_2" data-animation></div>
            <div class="countDown_block countDown_block_days">
              <span class="countDown_block_value countDown_block_value_large">{{ date.differentDays }}</span>
              <span class="countDown_block_label countDown_block_label_large"><span class="lang-ja">日</span><span class="lang-en">days</span></span>
            </div>
            <div class="countDown_symbol countDown_symbol_3" data-animation></div>
            <div class="countDown_blocks_details">
              <div class="countDown_block countDown_block_hours">
                <span class="countDown_block_value countDown_block_value_small">{{ date.differentHours }}</span>
                <span class="countDown_block_label countDown_block_label_small"><span class="lang-ja">時間</span><span class="lang-en">hours</span></span>
              </div>
              <div class="countDown_block countDown_block_minutes">
                <span class="countDown_block_value countDown_block_value_small">{{ date.differentMinutes }}</span>
                <span class="countDown_block_label countDown_block_label_small"><span class="lang-ja">分</span><span class="lang-en">minutes</span></span>
              </div>
              <div class="countDown_block countDown_block_seconds">
                <span class="countDown_block_value countDown_block_value_small">{{ date.differentSeconds }}</span>
                <span class="countDown_block_label countDown_block_label_small"><span class="lang-ja">秒</span><span class="lang-en">seconds</span></span>
              </div>
              <div class="countDown_symbol countDown_symbol_4" data-animation></div>
            </div>
            <div class="countDown_symbol countDown_symbol_5" data-animation></div>
          </div>

          <div class="countDown_dates">
            <div class="countDown_date">
              {{ $dayjs(props.informationData?.contents?.date).format('YYYY.M.D') }}
            </div>
          </div>
          </div>
        </div>
        <div class="countDown_symbol countDown_symbol_6" data-animation></div>
      </div>
    </div>
  </ViewFormBlock>
</template>

<script lang="ts" setup>
const { $dayjs } : any = useNuxtApp();
const props = withDefaults(defineProps<{
  data?: {},
  informationData?: {},
  visible?: boolean
}>(), {
  visible: true
});

const nowDate = ref(new Date(new Date().toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" })));

const updateDateToJST = (date: Date) => {
  // 日付オブジェクトを日本時間に変換
  return new Date(date.toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' }));
};

const date = computed<string | number>(() => {
  let target = props.informationData?.contents?.date;
  if(!target){ return false }
  if(props.informationData?.contents?.events?.[0]?.plans?.[1]?.hour && props.informationData?.contents?.events?.[0]?.plans?.[1]?.minute){
    target += `T${props.informationData?.contents?.events?.[0]?.plans?.[1]?.hour ? ('00' + props.informationData?.contents?.events?.[0]?.plans?.[1]?.hour).slice(-2): '00'}:${props.informationData?.contents?.events?.[0]?.plans?.[1]?.minute}`;
  }
  const targetDate = updateDateToJST(new Date(target));

  // 年 月 日を取得
  const year = targetDate.getFullYear(); // 年
  const month = targetDate.getMonth() + 1; // 月（0から11までの値を返すため 1を加える）
  const day = targetDate.getDate(); // 日

  // 曜日を取得（0（日曜）から6（土曜）までの値）
  const weekDay = targetDate.getDay();
  const weekDays = ["日", "月", "火", "水", "木", "金", "土"];
  const weekDayString = weekDays[weekDay] + '曜日'; // 曜日を文字列で

  // 現在の日付を日本時間に更新
  const currentTimeInJST = updateDateToJST(nowDate.value);

  const delta = Math.max(0, targetDate - currentTimeInJST);
  return {
    year: year,
    month: month,
    day: day,
    weekDayString: weekDayString,
    differentDays: Math.floor(delta / (1000 * 60 * 60 * 24)),
    differentHours: Math.floor((delta / (1000 * 60 * 60)) % 24),
    differentMinutes: Math.floor((delta / 1000 / 60) % 60),
    differentSeconds: Math.floor((delta / 1000) % 60)
  };
})
setInterval(() => {
  nowDate.value = updateDateToJST(new Date());
}, 1000);

</script>

<style lang="scss" scoped>
.countDown{
  position: relative;
  &_wrap{
    padding: 40px 20px 0;
  }
  &_box{
    margin: 0 auto;
    padding: 56px 10px 76px;
    text-align: center;
  }
  &_title{
    font-size: 20px;
    margin-bottom: 6px;
  }
  &_date{
    font-size: 32px;
  }
  &_blocks{
    &_details{
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 16px;
      margin-bottom: 24px;
    }
  }
  &_block{
    margin: 0 8px;
    &_value{
      line-height: 1;
      &_large{
        font-size: 80px;
      }
      &_small{
        font-size: 32px;
      }
    }
    &_label{
      line-height: 1;
      font-weight: 500;
      &_large{
        font-size: 20px;
        margin-left: 4px;
      }
      &_small{
        font-size: 12px;
        .lang-ja{
          margin-left: 4px;
        }
      }
    }
  }
}
.countDown_date,
.countDown_block_value{
  font-weight: 500;
}
</style>