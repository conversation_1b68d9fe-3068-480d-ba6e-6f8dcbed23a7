<template>
  <ViewFormBlock :isChecked="props.visible">
    <div id="freeField" class="viewBlock freeField information" data-animation v-if="props.data && props.data.length != 0">
      <div class="viewBlock_wrap freeField_wrap information_wrap" data-animation>
        <div class="freeField_box information_box" data-animation>
          <h2 class="viewBlock_title freeField_title information_title" data-animation><span>その他のご案内</span></h2>
          <div
            v-for="(item , index) in props.data"
            :key="index"
            class="freeField_block information_block"
            data-animation
          >
            <h3 class="viewBlock_title_sub freeField_title_sub information_title_sub" data-animation><span>{{ item.heading }}</span></h3>
            <div class="freeField_contents information_contents" :style="{'textAlign': item.textAlign}" data-animation>
              <div v-html="onFormatLink(sanitizeContent(item.message))"></div>
            </div>
            <div v-if="item?.isShowVisual">
              <swiper
                v-if="item?.images && item?.images?.length > 1"
                class="swiper freeField_slides"
                :modules="[Navigation, Pagination, Virtual]"
                :slides-per-view="1"
                :centered-slides="true"
                navigation
                :pagination="{ clickable: true }"
                loop
              >
                <SwiperSlide
                  v-for="(slide, index) in item?.images"
                  class="freeField_slide"
                  :virtualIndex="index"
                  :key="`freeField_slide-${index}`"
                >
                  <CropImageZoomModal
                    :src="slide.src"
                    :crop="slide"
                    :images="item?.images"
                    :index="index"
                  ></CropImageZoomModal>
                </SwiperSlide>
              </swiper>
              <div v-else-if="item?.images && item?.images?.[0]?.src" class="freeField_image" data-animation>
                <div class="freeField_image_wrap">
                  <VideoUUID
                    v-if="item.images[0].srcVideo"
                    :src="item.images[0]?.src"
                    :srcVideo="item.images[0]?.srcVideo"
                    :muted="item.images[0]?.isAutoplay ? item.images[0]?.isAutoplay : true"
                    :browserAutoplay="item.images[0]?.isAutoplay ? item.images[0]?.isAutoplay : false"
                    :controls="item.images[0]?.isAutoplay ? !item.images[0]?.isAutoplay : true"
                  />
                  <CropImageZoomModal
                    v-else
                    :src="item.images[0].src"
                    :crop="item.images[0]"
                  ></CropImageZoomModal>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ViewFormBlock>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Virtual } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const props = withDefaults(defineProps<{
  data?: [],
  visible?: boolean
}>(), {
  visible: true
});

const onFormatLink = (text:string) => {
  if(!text){return null}
  const urlPattern = /(https?:\/\/[^\s]+)/ig;
  return text.replace(urlPattern, '<a href="$1" target="_blank">$1</a>');
};
</script>

<style lang="scss" scoped>
.freeField{
  &_box{
    margin-bottom: 28px;
    & + .freeField_box{
      margin-top: 20px;
    }
  }
  &_contents{
    font-size: 14px;
    line-height: 1.8;
    position: relative;
    padding: 0 20px;
    word-wrap: break-word;
  }
  &_slides{
    margin-top: 20px;
    margin-bottom: 20px;
  }
  &_image{
    &_wrap{
      margin-top: 20px;
      .crop_image{
        min-width: 100%;
      }
    }
  }
}
</style>