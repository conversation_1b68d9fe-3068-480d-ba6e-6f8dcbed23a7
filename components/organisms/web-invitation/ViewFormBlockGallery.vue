<template>
  <ViewFormBlock :isChecked="props.visible">
    <div id="gallery" class="viewBlock gallery" data-animation>
      <div class="viewBlock_wrap gallery_wrap">
        <div class="gallery_box" data-animation>
          <h2 class="viewBlock_title gallery_title"><span class="lang-ja">アルバム</span><span class="lang-en">Album</span></h2>
          <div v-if="images && images.length">
            <swiper
              v-if="images.length >= 4"
              ref="gallerySwiper"
              class="swiper gallery_slides"
              effect="coverflow"
              :modules="[Navigation, Virtual, EffectCoverflow, Thumbs]"
              navigation
              :loop="true"
              :slides-per-view="1.4"
              :centered-slides="true"
              :coverflow-effect="{
                rotate: 0,
                depth: 200,
                stretch: 30,
                modifier: 1,
                scale: 1,
                slideShadows: false,
              }"
              :preloadImages="true"
              :lazy="false"
              :thumbs="{ swiper: thumbnailSwiper }"
              @swiper="setGallerySwiper"
            >
              <SwiperSlide
                class="gallery_slide"
                v-for="(slide, index) in images"
                :virtualIndex="index"
                :key="`gallery_slide-${index}`"
                @click="onClickSlide(index)"
              >
                <CropImage
                  :src="slide.src"
                  :crop="slide"
                ></CropImage>
              </SwiperSlide>
            </swiper>
            <swiper
              v-else
              ref="gallerySwiper"
              class="swiper gallery_slides"
              effect="coverflow"
              :modules="[Navigation, Virtual, EffectCoverflow, Thumbs]"
              navigation
              :loop="false"
              :slides-per-view="1.4"
              :centered-slides="true"
              :coverflow-effect="{
                rotate: 0,
                depth: 200,
                stretch: 30,
                modifier: 1,
                scale: 1,
                slideShadows: false,
              }"
              :preloadImages="true"
              :lazy="false"
              :thumbs="{ swiper: thumbnailSwiper }"
              @swiper="setGallerySwiper"
            >
              <SwiperSlide
                class="gallery_slide"
                v-for="(slide, index) in images"
                :virtualIndex="index"
                :key="`gallery_slide-${index}`"
                @click="onClickSlide(index)"
              >
                <CropImage
                  :src="slide.src"
                  :crop="slide"
                ></CropImage>
              </SwiperSlide>
            </swiper>

            <div class="gallery_thumbnail">
              <swiper
                ref="thumbnailSwiper"
                class="swiper gallery_thumbnail"
                :modules="[Virtual]"
                slides-per-view="auto"
                :loop="false"
                :space-between="10"
                :freeMode="true"
                :watchSlidesProgress="true"
                @swiper="setThumbnailSwiper"
              >
                <SwiperSlide
                  class="gallery_thumbnail_slide"
                  v-for="(slide, index) in images"
                  :virtualIndex="index"
                  :key="`slide-${index}`"
                  @click="onClickThumbnailSlide(index)"
                >
                  <CropImage
                    :src="slide.src"
                    :crop="slide"
                  ></CropImage>
                </SwiperSlide>
              </swiper>
            </div>
          </div>
          <div v-if="videos && videos.length" class="gallery_video">
            <VideoUUID
              v-if="videos[0].srcVideo"
              :src="videos[0]?.src"
              :srcVideo="videos[0]?.srcVideo"
              :muted="videos[0]?.isAutoplay ? true : false"
              :browserAutoplay="videos[0]?.isAutoplay ? videos[0]?.isAutoplay : false"
              :controls="videos[0]?.isAutoplay ? false : true"
            />
          </div>
        </div>
      </div>
    </div>
  </ViewFormBlock>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Virtual, EffectCoverflow, Thumbs } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
import { useWebInvitationSlideModal } from '@/composables/useWebInvitationSlideModal'

const props = withDefaults(defineProps<{
  data?: {},
  visible?: boolean
}>(), {
  visible: true
});

const data = ref(props.data);
watch(() => props.data, (newVal) => {
  data.value = newVal;
}, {
  deep: true
});

const images = computed(() => {
  if(data.value) {
    return data.value.filter(item => !('srcVideo' in item));
  }
  return []
});

const videos = computed(() => {
  if(data.value) {
    return data.value.filter(item => ('srcVideo' in item));
  }
  return []
});
const showSlide = useWebInvitationSlideModal();

const gallerySwiper = ref(null);
const thumbnailSwiper = ref(null);
const setThumbnailSwiper = (swiperInstance) => {
  thumbnailSwiper.value = swiperInstance;
};
const setGallerySwiper = (swiperInstance) => {
  gallerySwiper.value = swiperInstance;
};

const onClickSlide = (index:number) => {
  showSlide.setSlideData(props.data, index);
};

const onClickThumbnailSlide = (index:number) => {
  if(gallerySwiper?.value?.$el?.swiper) {
    setTimeout(() => {
      gallerySwiper.value.$el.swiper.loopDestroy();
      gallerySwiper.value.$el.swiper.loopCreate();
    }, 500)
  }
};

</script>

<style lang="scss" scoped>
.gallery{
  background: #FFF8ED;
  padding: 20px 0;
  .gallery_title{
    span{
      background: #FFF8ED;
    }
  }
  &_slides{
    img{
      width: 100%;
    }
  }
  &_slide{
    will-change: transform;
    backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    cursor: pointer;
  }
  &_thumbnail{
    display: flex;
    justify-content: center;
    align-items: flex-start;
    overflow-x: auto;
    max-width: 100%;
    padding: 0 20px;
    margin: 12px auto;
    position: relative;
    z-index: 1;
    &_slide{
      width: 40px;
      height: 40px;
      margin: 0 4px 8px;
      text-align: center;
      display: flex;
      cursor: pointer;
      img{
        width: 60px;
        height: 40px;
        object-fit: cover;
      }
    }
  }
  &_video {
    max-width: calc(100% - 40px);
    margin: 30px auto 0;
    position: relative;
    z-index: 1;
    :deep() {
      video {
        width: 100%;
        height: 100%;
      }
    }
  }
}

</style>