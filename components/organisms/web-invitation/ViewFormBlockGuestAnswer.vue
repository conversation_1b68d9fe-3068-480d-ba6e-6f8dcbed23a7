<template>
  <ViewFormBlock>
    <div class="block" v-for="(block, i) in webInvitationBlocks.filter(block => block.id == 'guestAnswer')" :key="i">
      <div v-if="block.id == 'guestAnswer' && block?.visible">
        <div id="guestAnswer" class="viewBlock guestAnswer" data-animation ref="windowBox">
          <div class="viewBlock_wrap guestAnswer_wrap" data-animation>
            <header class="guestAnswerHeader guestAnswer_header" data-animation>
              <h2 class="guestAnswer_header_title">
                <span class="lang-ja">返信フォーム</span>
                <span class="lang-ja_invitation" style="display: none;">招待状</span>
                <span class="lang-en">R S V P</span>
                <span class="lang-en_invitation" style="display: none;">Invitations</span>
                <span class="lang-en_attendance" style="display: none;">Attendance form</span>
              </h2>
              <div v-if="$dayjs(answerLimitDate+' 23:59:59').isAfter($dayjs(new Date()))" class="guestAnswer_header_sub_title"><span>御出欠について</span></div>
              <div class="guestAnswer_header_text">
                <template v-if="$dayjs(answerLimitDate+' 23:59:59').isAfter($dayjs(new Date()))">誠に勝手ながら<br><span class="guestAnswer_header_text_limit">{{ $dayjs(answerLimitDate).format('YYYY年M月D日 (ddd)') }}</span>迄に<br>ご回答いただければ幸いに存じます</template>
                <template v-else>ご回答期限が過ぎております</template>
              </div>
              <div
                v-if="block.contents.limit.message && $dayjs(answerLimitDate+' 23:59:59').isAfter($dayjs(new Date()))"
                class="note guestAnswer_header_caution"
                :data-textalign="block.contents.limit.textAlign"
                v-html="onFormatLink(sanitizeContent(block.contents.limit.message))"
              ></div>
              <div v-else-if="!$dayjs(answerLimitDate+' 23:59:59').isAfter($dayjs(new Date()))" class="note guestAnswer_header_caution">※出欠の回答 変更は<br>主催者へ直接ご連絡ください</div>
            </header>

            <template v-if="attendanceItems.length && $dayjs(answerLimitDate+' 23:59:59').isAfter($dayjs(new Date()))">
              <div class="inputAttendance" v-for="(event, n) in events" :key="n" :class="(v$?.guest_event_answers?.[n]?.attendance?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                <div class="inputTitle"><span>{{ event.name }}</span></div>
                <InputRadio
                  :required="true"
                  :value="input?.guest_event_answers?.[n]?.attendance"
                  :items="attendanceItems"
                  :error="v$?.guest_event_answers?.[n]?.attendance?.$errors?.[0]?.$message"
                  @change="setAttendance(event, $event)"
                />
              </div>
            </template>

            <!-- 挙式 -->
            <div v-if="$dayjs(answerLimitDate+' 23:59:59').isAfter($dayjs(new Date()))" class="guestAnswerBox guestAnswer_box" data-animation>
              <div class="guestAnswer_box_wrap">
                <div class="guestAnswer_box_label">
                  <span class="guestAnswer_required">* </span>は必須項目です
                </div>
                <hr>
                <template v-for="(item, n) in block.contents.selectList.filter(item => item.visible === true)" :key="n">
                  <div v-if="item.title === '新郎新婦ゲスト選択'" class="guestAnswer_box_row" :class="(v$?.input?.guest_type?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <div class="guestAnswer_box_label" :data-required="item.required">いずれかをお選びください</div>
                    <div class="inputRadioBtn inputGuestType">
                      <InputGuestType
                        :required="item.required"
                        :value="String(input?.input?.guest_type)"
                        :error="v$?.input?.guest_type?.$errors?.[0]?.$message"
                        @change="input.input.guest_type = $event"
                      />
                    </div>
                  </div>
                  <div v-else-if="item.title === 'お名前'" class="guestAnswer_box_row" :class="(v$?.input?.first_name?.$errors?.[0]?.$message || v$?.input?.last_name?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <div class="guestAnswer_box_label" :data-required="item.required">{{ item.title }}</div>
                    <div>
                      <div class="guestAnswer_box_row_flex" style="margin-bottom: 10px;">
                        <InputText
                          class="guestAnswer_box_input"
                          :required="item.required"
                          placeholder="姓"
                          :value="input?.input?.last_name"
                          maxlength="30"
                          :error="v$?.input?.last_name?.$errors?.[0]?.$message"
                          @input="input.input.last_name = $event.target.value;"
                          @focus="onFocusOldKanjiTarget('inputLastName', 0)"
                          @blur="onBlurOldKanjiTarget"
                          ref="refInputLastName"
                        />
                        <InputText
                          class="guestAnswer_box_input"
                          :required="item.required"
                          placeholder="名"
                          :value="input?.input?.first_name"
                          maxlength="30"
                          :error="v$?.input?.first_name?.$errors?.[0]?.$message"
                          @input="input.input.first_name = $event.target.value;"
                          @focus="onFocusOldKanjiTarget('inputFirstName', 0)"
                          @blur="onBlurOldKanjiTarget"
                          ref="refInputFirstName"
                        />
                      </div>
                      <a :class="{'link-accent link-oldkanji size--md': true, 'is-hide': oldKanjiBtnHide, 'is-disabled': oldKanjiBtnHidden, 'is-lastname': (oldKanjiTarget.indexOf('LastName') === -1) }" @click="showOldKanjiModal = true">
                        <i class="icn-left material-icons icn-lg">search</i>
                        旧字体検索
                      </a>
                    </div>
                  </div>
                  <div v-else-if="item.title === 'お名前（ふりがな）'" class="guestAnswer_box_row" :class="(v$?.input?.last_name_kana?.$errors?.[0]?.$message || v$?.input?.first_name_kana?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <div class="guestAnswer_box_label" :data-required="item.required">{{ item.title }}</div>
                    <div class="row">
                      <InputHiragana
                        class="guestAnswer_box_input"
                        :required="item.required"
                        placeholder="せい"
                        size="half"
                        :value="input?.input?.last_name_kana"
                        maxlength="30"
                        :error="v$?.input?.last_name_kana?.$errors?.[0]?.$message"
                        @update="input.input.last_name_kana = $event"
                      />
                      <InputHiragana
                        class="guestAnswer_box_input"
                        :required="item.required"
                        placeholder="めい"
                        size="half"
                        :value="input?.input?.first_name_kana"
                        maxlength="30"
                        :error="v$?.input?.first_name_kana?.$errors?.[0]?.$message"
                        @update="input.input.first_name_kana = $event"
                        />
                    </div>
                  </div>
                  <div v-else-if="item.title === 'お名前（ローマ字）'" class="guestAnswer_box_row" :class="(v$?.input?.last_name_romaji?.$errors?.[0]?.$message || v$?.input?.first_name_romaji?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <div class="guestAnswer_box_label" :data-required="item.required">{{ item.title }}</div>
                    <div class="row">
                      <InputAlphabet
                        class="guestAnswer_box_input"
                        :required="item.required"
                        placeholder="Sei"
                        size="half"
                        format="capitalize"
                        :value="input?.input?.last_name_romaji"
                        maxlength="30"
                        :error="v$?.input?.last_name_romaji?.$errors?.[0]?.$message"
                        @update="input.input.last_name_romaji = $event"
                        />
                      <InputAlphabet
                        class="guestAnswer_box_input"
                        :required="item.required"
                        placeholder="Mei"
                        size="half"
                        format="capitalize"
                        :value="input?.input?.first_name_romaji"
                        maxlength="30"
                        :error="v$?.input?.first_name_romaji?.$errors?.[0]?.$message"
                        @update="input.input.first_name_romaji = $event"
                        />
                    </div>
                  </div>
                  <div v-else-if="item.title === '性別'" class="guestAnswer_box_row" :class="(v$?.input?.gender?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <div class="inputGender inputRadioBtn guestAnswer_box_input">
                      <InputGender
                        :required="item.required"
                        :title="item.title"
                        :value="String(input?.input?.gender)"
                        :error="v$?.input?.gender?.$errors?.[0]?.$message"
                        @change="input.input.gender = $event"
                      />
                    </div>
                  </div>
                  <!-- <div v-else-if="item.title === '関係性'" class="guestAnswer_box_row" :class="(v$?.input?.relationship_name?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <InputSelect
                      class="guestAnswer_box_input"
                      size="full"
                      :title="item.title"
                      :required="item.required"
                      :options="relationShipNameOptions()"
                      :value="input?.input?.relationship_name"
                      :error="v$?.input?.relationship_name?.$errors?.[0]?.$message"
                      @input="input.input.relationship_name = $event.target.value"
                    />
                  </div> -->
                  <div v-else-if="item.title === '間柄'" class="guestAnswer_box_row" :class="(v$?.input?.relationship?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <InputSelectGroup
                      class="guestAnswer_box_input"
                      :required="item.required"
                      :title="item.title"
                      size="full"
                      :options="relationShipOptions(input?.input?.relationship_name)"
                      :value="input?.input?.relationship"
                      :error="v$?.input?.relationship?.$errors?.[0]?.$message"
                      @input="input.input.relationship = $event.target.value"
                    />
                  </div>
                  <div v-else-if="item.title === 'アレルギー項目の入力'" class="guestAnswer_box_row pb-68" :class="(v$?.input?.allergy?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <InputAllergy
                      addClass="guestAnswer_box_input"
                      :isOnlyPrimary="true"
                      title="アレルギーについて"
                      :required="item.required"
                      :value="{allergies: input?.input?.allergies, allergy: input?.input?.allergy}"
                      :error="v$?.input?.allergy?.$errors?.[0]?.$message"
                      @change="input.input.allergies = $event?.allergies; input.input.allergy = $event?.allergy; "
                    />
                  </div>
                  <div v-else-if="item.title === '誕生日' || item.title === 'お誕生日'" class="guestAnswer_box_row" :class="(v$?.input?.birthdate?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <InputDate
                      class="guestAnswer_box_input guestAnswer_box_date"
                      :required="item.required"
                      title="お誕生日"
                      :placeholder="'1995-07-30'"
                      :value="input?.input?.birthdate"
                      :error="v$?.input?.birthdate?.$errors?.[0]?.$message"
                      @change="input.input.birthdate = $event"
                    />
                  </div>
                  <div v-else-if="item.title === '住所'" class="guestAnswer_box_row" :class="(v$?.input?.postal_code?.$errors?.[0]?.$message || v$?.input?.prefecture?.$errors?.[0]?.$message || v$?.input?.city?.$errors?.[0]?.$message || v$?.input?.address?.$errors?.[0]?.$message || v$?.input?.building?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <div class="guestAnswer_box_label" :data-required="item.required">ご住所</div>
                    <div class="row mb-20">
                      <InputZipCode
                        class="guestAnswer_box_input"
                        title="郵便番号（7桁の半角数字）"
                        placeholder="1234567"
                        :required="item.required"
                        :value="input?.input?.postal_code"
                        :error="v$?.input?.postal_code?.$errors?.[0]?.$message"
                        @change="onChangePostalCode($event)"
                      />
                    </div>
                    <div class="row mb-20">
                      <InputSelect
                        class="guestAnswer_box_input"
                        :required="item.required"
                        title="都道府県"
                        maxlength="50"
                        size="full"
                        :options="prefectureOptions"
                        :value="input?.input?.prefecture"
                        :error="v$?.input?.prefecture?.$errors?.[0]?.$message"
                        @change="input.input.prefecture = $event.target.value"
                      />
                    </div>
                    <div class="row mb-20">
                      <InputText
                        class="guestAnswer_box_input"
                        :required="item.required"
                        title="市区町村"
                        placeholder="新宿区新宿"
                        size="full"
                        maxlength="50"
                        :value="input?.input?.city"
                        :error="v$?.input?.city?.$errors?.[0]?.$message"
                        @input="input.input.city = $event.target.value"
                      />
                    </div>
                    <div class="row mb-20">
                      <InputText
                        class="guestAnswer_box_input"
                        :required="item.required"
                        title="丁目・番地"
                        placeholder="1-36-2"
                        size="full"
                        maxlength="50"
                        :value="input?.input?.address"
                        :error="v$?.input?.address?.$errors?.[0]?.$message"
                        @input="input.input.address = $event.target.value"
                      />
                    </div>
                    <div class="row">
                      <InputText
                        class="guestAnswer_box_input"
                        title="建物名・部屋番号など"
                        placeholder="新宿第七葉山ビル 301"
                        size="full"
                        maxlength="50"
                        :value="input?.input?.building"
                        :error="v$?.input?.building?.$errors?.[0]?.$message"
                        @input="input.input.building = $event.target.value"
                      />
                    </div>
                  </div>
                  <div v-else-if="item.title === '電話番号'" class="guestAnswer_box_row" :class="(v$?.input?.phone?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <InputTel
                      class="guestAnswer_box_input"
                      :required="item.required"
                      title="電話番号（半角数字・ハイフン）"
                      placeholder="090-1234-5678"
                      size="md"
                      maxlength="30"
                      :value="input?.input?.phone"
                      :error="v$?.input?.phone?.$errors?.[0]?.$message"
                      @update="input.input.phone = $event"
                    />
                  </div>
                  <div v-else-if="item.title === 'メールアドレス'" class="guestAnswer_box_row" :class="(v$?.input?.email?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <InputEmail
                      class="guestAnswer_box_input"
                      :required="item.required"
                      title="メールアドレス（半角英数字・記号）"
                      placeholder="<EMAIL>"
                      size="full"
                      maxlength="319"
                      :value="input?.input?.email"
                      :error="v$?.input?.email?.$errors?.[0]?.$message"
                      @update="input.input.email = $event"
                    />
                  </div>
                  <div v-else-if="item.title === 'プロフィール写真'" class="guestAnswer_box_row" :class="(v$?.input?.image?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <div class="inputProfileImage">
                      <div class="guestAnswer_box_label" :data-required="item.required">{{ item.title }}</div>
                      <div class="guestAnswer_box_row_text">よろしければプロフィール写真の添付をお願いいたします</div>
                      <InputUploadBtnGuestV2
                        btnClass="guestAnswer_box_file_button"
                        btnText="アップロードするファイルを選択"
                        :disabled="isPreview"
                        :value="input?.input?.image"
                        :dirName="props.webInvitationData?.member?.id"
                        :error="v$?.input?.image?.$errors?.[0]?.$message"
                        accept="image/*"
                        @change="input.input.image = $event"
                      ></InputUploadBtnGuestV2>
                    </div>
                  </div>
                  <template v-else-if="item.title === '連名入力'">
                    <div class="blockGuests" v-for="(guest, index) in input.guests" :key="index">
                      <div class="blockTitle">お連れ様</div>
                      <template v-for="(item, n) in block.contents.selectList.filter(item => item.visible === true)" :key="n">
                        <div class="guestAnswer_box_row" v-if="item.title === 'お名前'" :class="(v$.guests?.[index]?.last_name?.$errors?.[0]?.$message || v$.guests?.[index]?.first_name?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <div class="guestAnswer_box_label" :data-required="item.required">お連れ様の{{ item.title }}</div>
                          <div class="row">
                            <InputText
                              class="guestAnswer_box_input"
                              :required="item.required"
                              placeholder="姓"
                              size="half"
                              maxlength="30"
                              :value="input?.guests?.[index]?.last_name"
                              :error="v$?.guests?.[index]?.last_name?.$errors?.[0]?.$message"
                              @input="input.guests[index].last_name = $event.target.value; "
                              @focus="onFocusOldKanjiTarget('inputGuestsLastName', index+1)"
                              @blur="onBlurOldKanjiTarget"
                              ref="refInputGuestsLastName"
                            />
                            <InputText
                              class="guestAnswer_box_input"
                              :required="item.required"
                              placeholder="名"
                              size="half"
                              maxlength="30"
                              :value="input?.guests?.[index]?.first_name"
                              :error="v$?.guests?.[index]?.first_name?.$errors?.[0]?.$message"
                              @input="input.guests[index].first_name = $event.target.value; "
                              @focus="onFocusOldKanjiTarget('inputGuestsFirstName', index+1)"
                              @blur="onBlurOldKanjiTarget"
                              ref="refInputGuestsFirstName"
                            />
                            <a :class="{'link-accent link-oldkanji size--md': true, 'is-hide': oldKanjiBtnHide, 'is-disabled': oldKanjiBtnHidden, 'is-lastname': (oldKanjiTarget.indexOf('LastName') === -1) }" @click="showOldKanjiModal = true">
                              <i class="icn-left material-icons icn-lg">search</i>
                              旧字体検索
                            </a>
                          </div>
                        </div>
                        <div class="guestAnswer_box_row" v-else-if="item.title === 'お名前（ふりがな）'" :class="(v$.guests?.[index]?.last_name_kana?.$errors?.[0]?.$message || v$.guests?.[index]?.first_name_kana?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <div class="guestAnswer_box_label" :data-required="item.required">お連れ様の{{ item.title }}</div>
                          <div class="row">
                            <InputHiragana
                              class="guestAnswer_box_input"
                              :required="item.required"
                              placeholder="せい"
                              size="half"
                              maxlength="30"
                              :value="input?.guests?.[index]?.last_name_kana"
                              :error="v$?.guests?.[index]?.last_name_kana?.$errors?.[0]?.$message"
                              @update="input.guests[index].last_name_kana = $event"
                              />
                            <InputHiragana
                              class="guestAnswer_box_input"
                              :required="item.required"
                              placeholder="めい"
                              size="half"
                              maxlength="30"
                              :value="input?.guests?.[index]?.first_name_kana"
                              :error="v$?.guests?.[index]?.first_name_kana?.$errors?.[0]?.$message"
                              @update="input.guests[index].first_name_kana = $event"
                              />
                          </div>
                        </div>
                        <div class="guestAnswer_box_row" v-else-if="item.title === 'お名前（ローマ字）'" :class="(v$.guests?.[index]?.last_name_romaji?.$errors?.[0]?.$message || v$.guests?.[index]?.first_name_romaji?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <div class="guestAnswer_box_label" :data-required="item.required">お連れ様の{{ item.title }}</div>
                          <div class="row">
                            <InputAlphabet
                              class="guestAnswer_box_input"
                              :required="item.required"
                              placeholder="Sei"
                              size="half"
                              maxlength="30"
                              format="capitalize"
                              :value="input?.guests?.[index]?.last_name_romaji"
                              :error="v$?.guests?.[index]?.last_name_romaji?.$errors?.[0]?.$message"
                              @update="input.guests[index].last_name_romaji = $event"
                              />
                            <InputAlphabet
                              class="guestAnswer_box_input"
                              :required="item.required"
                              placeholder="Mei"
                              size="half"
                              maxlength="30"
                              format="capitalize"
                              :value="input?.guests?.[index]?.first_name_romaji"
                              :error="v$?.guests?.[index]?.first_name_romaji?.$errors?.[0]?.$message"
                              @update="input.guests[index].first_name_romaji = $event"
                              />
                          </div>
                        </div>
                        <div class="guestAnswer_box_row" v-else-if="item.title === '性別'" :class="(v$.guests?.[index]?.gender?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <div class="inputGender inputRadioBtn guestAnswer_box_input">
                            <InputGender
                              :required="item.required"
                              :title="item.title"
                              :value="String(input?.guests?.[index]?.gender)"
                              @change="input.guests[index].gender = $event"
                            />
                          </div>
                        </div>
                        <!-- <div v-else-if="item.title === '関係性'" class="guestAnswer_box_row" :class="(v$.guests?.[index]?.relationship_name?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <InputSelect
                            class="guestAnswer_box_input"
                            size="full"
                            :title="item.title"
                            :required="item.required"
                            :options="relationShipNameOptions()"
                            :value="input?.guests?.[index]?.relationship_name"
                            :error="v$.guests?.[index]?.relationship_name?.$errors?.[0]?.$message"
                            @input="input.guests[index].relationship_name = $event.target.value"
                          />
                        </div> -->
                        <div v-else-if="item.title === '間柄'" class="guestAnswer_box_row" :class="(v$.guests?.[index]?.relationship?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <InputSelectGroup
                            class="guestAnswer_box_input"
                            :required="item.required"
                            :title="item.title"
                            size="full"
                            :options="relationShipOptions(input?.guests?.[index]?.relationship_name)"
                            :value="input?.guests?.[index]?.relationship"
                            :error="v$.guests?.[index]?.relationship?.$errors?.[0]?.$message"
                            @input="input.guests[index].relationship = $event.target.value"
                          />
                        </div>
                        <div class="guestAnswer_box_row" v-else-if="item.title === 'アレルギー項目の入力'" :class="(v$.guests?.[index]?.allergy?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <InputAllergy
                            addClass="guestAnswer_box_input"
                            :isOnlyPrimary="true"
                            title="アレルギーについて"
                            :required="item.required"
                            :value="{allergies: input?.guests?.[index]?.allergies, allergy: input?.guests?.[index]?.allergy}"
                            :error="v$?.guests?.[index]?.allergy?.$errors?.[0]?.$message"
                            @change="input.guests[index].allergies = $event?.allergies; input.guests[index].allergy = $event?.allergy; "
                          />
                        </div>
                        <div class="guestAnswer_box_row" v-else-if="item.title === '誕生日' || item.title === 'お誕生日'" :class="(v$.guests?.[index]?.birthdate?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <InputDate
                            class="guestAnswer_box_input guestAnswer_box_date"
                            :required="item.required"
                            title="お誕生日"
                            :value="input?.guests?.[index]?.birthdate"
                            :error="v$?.guests?.[index]?.birthdate?.$errors?.[0]?.$message"
                            @change="input.guests[index].birthdate = $event"
                          />
                        </div>
                        <div class="guestAnswer_box_row" v-else-if="item.title === 'お祝いメッセージ'" :class="(v$.guests?.[index]?.message?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <InputTextarea
                            class="guestAnswer_box_input"
                            :required="item.required"
                            title="お連れ様のお祝いメッセージ"
                            size="full"
                            max-length="300"
                            placeholder="ご自由にご入力ください
（最大300文字まで）"
                            :value="input?.guests?.[index]?.message"
                            :error="v$?.guests?.[index]?.message?.$errors?.[0]?.$message"
                            @input="input.guests[index].message = $event.target.value"
                          />
                        </div>
                        <div class="guestAnswer_box_row" v-else-if="item.title === 'プロフィール写真'" :class="(v$.guests?.[index]?.image?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                          <div class="inputProfileImage">
                            <div class="guestAnswer_box_label" :data-required="item.required">{{ item.title }}</div>
                            <div class="guestAnswer_box_row_text">よろしければプロフィール写真の添付をお願いいたします</div>
                            <InputUploadBtnGuestV2
                              btnClass="guestAnswer_box_file_button"
                              btnText="アップロードするファイルを選択"
                              :disabled="isPreview"
                              :value="input?.guests?.[index]?.image"
                              :dirName="props.webInvitationData?.member?.id"
                              :error="v$.guests?.[index]?.image?.$errors?.[0]?.$message"
                              accept="image/*"
                              @change="input.guests[index].image = $event"
                            ></InputUploadBtnGuestV2>
                          </div>
                        </div>
                      </template>
                      <div class="btn-wrap guestAnswer_box_remove">
                        <a type="button" class="btn is-delete" @click="deleteGuestIndex = index">お連れ様の情報を削除</a>
                      </div>
                    </div>
                    <div class="guestAnswer_box_row">
                      <a v-if="input.guests.length < 20" type="button" class="btn is-add" @click="onClickAddGuest">お連れ様を追加</a>
                    </div>
                  </template>
                  <div v-else-if="item.title === 'お祝い画像' || item.title === 'お祝い画像・動画'" class="guestAnswer_box_row is-noBorder" :class="(v$?.input?.media?.$errors?.[0]?.$message || v$?.input?.media_image?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                    <!-- <div class="guestAnswer_box_label">画像・動画</div> -->
                    <Loading v-if="materialImagesLoading"></Loading>
                    <template v-else>
                      <template v-if="materialImages.length">
                        <div class="guestAnswer_box_label" :data-required="item.required">お祝い画像・動画</div>
                        <InputRadio
                          class="guestAnswer_box_input"
                          name="image"
                          :required="required"
                          :value="input?.input?.is_media_upload"
                          :items="[{value: 0, label: 'お祝い画像一覧から選ぶ'}]"
                          :block="true"
                          @change="input.input.is_media_upload = $event; input.input.media_image = null; input.input.media = null"
                        />
                        <div class="inputImages" v-if="input?.input?.is_media_upload == 0">
                          <InputRadioImages
                            class="guestAnswer_box_input"
                            :items="
                            materialImages.map(image => ({
                              value: image.uuid, 
                              image: image.presigned_url,
                              label: ''
                              // label: '<span class=\'img\'><img src=\''+image.presigned_url+'\'></span><span class=\'txt\'>'+image.name+'</span>'
                            }))"
                            :value="input?.input?.media_image"
                            @change="input.input.media_image = $event"
                          />
                        </div>
                      </template>
                      <div><InputRadio
                        class="guestAnswer_box_input"
                        name="image"
                        :required="required"
                        :value="input?.input?.is_media_upload"
                        :items="[{value: 1, label: 'お祝い画像・動画をアップロード'}]"
                        @change="input.input.is_media_upload = $event; input.input.media_image = null; input.input.media = null"
                      /></div>
                      <InputUploadBtnGuestV2
                        v-if="input?.input?.is_media_upload == 1"
                        btnClass="guestAnswer_box_file_button"
                        btnText="アップロードするファイルを選択"
                        :disabled="isPreview"
                        :value="input?.input?.media"
                        :dirName="props.webInvitationData?.member?.id"
                        accept="image/*, video/*"
                        @change="input.input.media = $event"
                      ></InputUploadBtnGuestV2>
                      <div class="input-error mt-10" v-if="v$?.input?.media?.$errors?.[0]?.$message">{{ v$?.input?.media?.$errors?.[0]?.$message }}</div>
                      <div class="input-error mt-10" v-if="v$?.input?.media_image?.$errors?.[0]?.$message">{{ v$?.input?.media_image?.$errors?.[0]?.$message }}</div>
                    </template>
                  </div>
                  <template v-else-if="item.title === 'お祝いメッセージ'">
                    <div class="guestAnswer_box_row is-noBorder" :class="(v$?.input?.message?.$errors?.[0]?.$message) ? 'refGuestAnswerErrors' : ''">
                      <InputTextarea
                        class="guestAnswer_box_input"
                        :required="item.required"
                        title="メッセージ"
                        size="full"
                        max-length="300"
                        placeholder="ご自由にご入力ください
（最大300文字まで）"
                        :value="input?.input?.message"
                        :error="v$?.input?.message?.$errors?.[0]?.$message"
                        @input="input.input.message = $event.target.value"
                      />
                    </div>
                    <div class="guestAnswer_box_label is-noBorder" v-if="input?.input?.message || (input?.input?.media_image || input?.input?.media?.presigned_url_l)">プレビュー</div>
                    <div class="guestAnswer_box_row is-noBorder">
                      <div class="guestAnswerPreview" v-if="input?.input?.message || (input?.input?.media_image || input?.input?.media?.presigned_url_l)">
                        <div class="guestAnswerPreview_image" v-if="input?.input?.media_image || input?.input?.media?.presigned_url_l">
                          <template v-if="input?.input?.is_media_upload != 1 && input?.input?.media_image">
                            <ImageUUID :uuid="input?.input?.media_image" />
                          </template>
                          <template v-else-if="input?.input?.media?.presigned_url_main">
                            <VideoUUID
                              :uuid="input?.input?.media_image"
                              :src="input?.input?.media?.presigned_url_l"
                              :srcVideo="input?.input?.media?.presigned_url_main"
                              :poster="input?.input?.media?.presigned_url_l"
                            />
                          </template>
                          <template v-else-if="input?.input?.media?.presigned_url_l">
                            <img :src="input?.input?.media?.presigned_url_l" alt="">
                          </template>
                        </div>
                        <div class="guestAnswerPreview_text" v-if="input?.input?.message || input?.input?.last_name || input?.input?.last_name">
                          <span v-if="input?.input?.message">{{ input?.input?.message }}</span>
                          <div class="guestAnswerPreview_name" v-if="input?.input?.last_name || input?.input?.last_name">{{ input?.input?.last_name }}{{ input?.input?.last_name ? ' ' + input?.input?.first_name : input?.input?.first_name }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </template>
                <div class="guestAnswer_box_row" v-for="(item, n) in props?.data?.questionnaire" :key="n">
                  <div class="guestAnswer_box_label">{{ item.heading }}</div>
                  <div v-if="item.isShowVisual">
                    <swiper
                      v-if="item.images && item.images.length > 1"
                      class="swiper questionnaire_slides"
                      :modules="[Navigation, Pagination, Virtual]"
                      :slides-per-view="1"
                      :centered-slides="true"
                      navigation
                      :pagination="{ clickable: true }"
                      data-animation
                    >
                      <SwiperSlide
                        v-for="(slide, index) in item.images"
                        class="questionnaire_slide"
                        :virtualIndex="index"
                        :key="`questionnaire_slide-${index}`"
                      >
                        <CropImageZoomModal
                          :src="slide.src"
                          :crop="slide"
                          :images="item.images"
                          :index="index"
                        ></CropImageZoomModal>
                      </SwiperSlide>
                    </swiper>
                    <VideoUUID
                      v-else-if="item?.images && item?.images.length == 1 && item.images[0].srcVideo"
                      :src="item.images[0]?.src"
                      :srcVideo="item.images[0]?.srcVideo"
                      :muted="item.images[0]?.isAutoplay ? item.images[0]?.isAutoplay : true"
                      :browserAutoplay="item.images[0]?.isAutoplay ? item.images[0]?.isAutoplay : false"
                      :controls="item.images[0]?.isAutoplay ? !item.images[0]?.isAutoplay : true"
                    />
                    <CropImageZoomModal
                      v-else-if="item.images && item.images.length == 1"
                      :src="item.images[0]?.src"
                      :crop="item.images[0]"
                    ></CropImageZoomModal>
                  </div>
                  <p class="guestAnswer_box_row_text" v-html="onFormatLink(sanitizeContent(item.message))" :style="{'textAlign': item.textAlign}"></p>
                  <div v-if="item.method === 'inputText'">
                    <InputText
                      class="guestAnswer_box_input"
                      size="full"
                      :value="input?.guest_survey_answers?.[n]?.answer_content"
                      :error="v$?.guest_survey_answers?.[n]?.answer_content?.$errors?.[0]?.$message"
                      @input="input.guest_survey_answers[n].answer_content = $event.target.value"
                    />
                  </div>
                  <div v-else-if="item.method === 'checkbox'">
                    <InputCheck
                      :required="true"
                      :block="true"
                      :value="input?.guest_survey_answers?.[n]?.answer_content.split(',')"
                      :items="item.answer.map(answer => ({value: answer, label: answer}))"
                      :error="v$?.guest_survey_answers?.[n]?.answer_content?.$errors?.[0]?.$message"
                      @change="input.guest_survey_answers[n].answer_content = $event.join(',').replace(/^,/, '')"
                    />
                  </div>
                  <div v-else-if="item.method === 'radio'">
                    <InputRadio
                      :required="true"
                      :block="true"
                      :value="input?.guest_survey_answers?.[n]?.answer_content"
                      :items="item.answer.map(answer => ({value: answer, label: answer}))"
                      :error="v$?.guest_survey_answers?.[n]?.answer_content?.$errors?.[0]?.$message"
                      @change="input.guest_survey_answers[n].answer_content = $event"
                    />
                  </div>
                </div>
                <div class="mt-50">
                  <button type="button" class="btn is-confirm" :disabled="isPreview" @click="onClickNext">確認画面へ</button>
                  <p v-if="isPreview" class="preview_text mt10">※プレビューのため入力内容の送信はできません</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <Modal size="sm" v-if="deleteGuestIndex !== null" @close="deleteGuestIndex = null">
      <template #header>
        お連れ様の情報を削除
      </template>
      <template #main>
        <p class="mt-15 mb-0">お連れ様の情報を削除してもよろしいですか？</p>
      </template>
      <template #footer>
        <a href="#" @click="deleteGuestIndex = null">キャンセル</a>
        <a href="#" class="color-alert" @click="onClickDeleteGuest(deleteGuestIndex)">削除する</a>
      </template>
    </Modal>
    <ModalOldkanji 
      v-if="showOldKanjiModal" 
      :refTarget="refOldKanjiTarget?.inputRef"
      @close="showOldKanjiModal = false"
      @change="onChangeOldKanji"
    ></ModalOldkanji>
  </ViewFormBlock>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, email, numeric, requiredIf, maxLength, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Virtual } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const { $dayjs } : any = useNuxtApp();

interface Props {
  input?: {
    input: CreateGuestNoValidateInput,
    guests: CreateGuestNoValidateInput[],
    guest_event_answers: CreateGuestEventAnswerNoValidateInput[],
    guest_survey_answers: CreateGuestSurveyAnswerInput[],
  };
  data?: {},
  informationData?: {},
  visible?: boolean,
  isPreview?: boolean;
  webInvitationData: any;
}
const props = withDefaults(defineProps<Props>(), {
  input: () => {
    return {
      input: {},
      guests: [],
      guest_event_answers: [],
      guest_survey_answers: []
    }
  },
  data: {},
  informationData: {},
  visible: {},
  isPreview: false,
  webInvitationData: () => {}
});

const webInvitationBlocks = ref(props?.webInvitationBlocks?.editor_settings?.blocks);

if(props?.webInvitationBlocks && props?.webInvitationBlocks.editor_settings){
  webInvitationBlocks.value = props?.webInvitationBlocks.editor_settings?.blocks;
}else if(props?.webInvitationBlocks && props?.webInvitationBlocks.editor_settings_json){
  webInvitationBlocks.value = props?.webInvitationBlocks.editor_settings_json?.blocks;
}
watch(() => props.webInvitationData, (newVal) => {
  if(newVal && newVal.editor_settings){
    webInvitationBlocks.value = newVal.editor_settings?.blocks;
  }else if(newVal && newVal.editor_settings_json){
    webInvitationBlocks.value = newVal.editor_settings_json?.blocks;
  }
}, {
  deep: true,
  immediate: true
});

const emit = defineEmits<{
  change: []
  next: []
  scrollTo: []
}>()

const onFormatLink = (text:string) => {
  if(!text){return null}
  const urlPattern = /(https?:\/\/[^\s]+)/ig;
  return text.replace(urlPattern, '<a href="$1" target="_blank">$1</a>');
};

const input = ref(props.input);

const deleteGuestIndex = ref(null as any);

const { materialImages, loading:materialImagesLoading } = useGetManyMaterialImage(FileType.FileTypeGuestCelebrationImage);
watch(() => materialImages, () => {
  if (! materialImages.value.length && ! materialImagesLoading.value) {
    input.value.input.is_media_upload = 1;
  }
}, {
  deep: true,
  immediate: true
});

const windowBox = ref();
const refGuestAnswerErrors = ref();

const rules = computed(() => {
  // 基本情報バリデーション
  let rules = {
    input: {
      parent_guest_id: {},
      guest_type: {},
      image: {},
      last_name: {},
      first_name: {},
      last_name_romaji: {},
      first_name_romaji: {},
      last_name_kana: {},
      first_name_kana: {},
      guest_honor: {},
      gender: {},
      relationship_name: {},
      relationship: {},
      birthdate: {
        date: helpers.withMessage(validationMessage.date('お誕生日'), validationDate),
      },
      allergies: {},
      allergy: {},
      guest_list_id: {},
      postal_code: { 
        format: helpers.withMessage(validationMessage.format('郵便番号'), validationPostalCode),
      },
      prefecture: {},
      city: {},
      address: {},
      building: {},
      phone: {},
      email: {
        // required: helpers.withMessage(validationMessage.required('メールアドレス'), required),
        email: helpers.withMessage(validationMessage.email('メールアドレス'), email),
      },
      message: {
        $message: helpers.withMessage(validationMessage.maxLength('お祝いメッセージ', 300), maxLength(300))
      },
      media: {},
      media_image: {},
      is_media_upload: {},
      invitation_delivery: {}
    }
  } as any;

  rules.guests = [];
  for (let i = 0; i < input.value?.guests.length; i++) {
    rules.guests.push({
      parent_guest_id: {},
      image: {},
      last_name: {},
      first_name: {},
      last_name_romaji: {},
      first_name_romaji: {},
      last_name_kana: {},
      first_name_kana: {},
      guest_honor: {},
      gender: {},
      relationship_name: {},
      relationship: {},
      birthdate: {
        date: helpers.withMessage(validationMessage.date('お誕生日'), validationDate),
      },
      allergies: {},
      allergy: {},
      message: {
        $message: helpers.withMessage(validationMessage.maxLength('お祝いメッセージ', 300), maxLength(300))
      }
    });
  }

  rules.guest_event_answers = [];
  for (let i = 0; i < events.value.length; i++) {
    rules.guest_event_answers.push({
      attendance: {
        required: helpers.withMessage(validationMessage.required('出欠'), required)
      }
    });
  }

  let block = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'guestAnswer');

  if(props.webInvitationData && props.webInvitationData.editor_settings){
    block = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'guestAnswer');
  }else if(props.webInvitationData && props.webInvitationData.editor_settings_json){
    block = props.webInvitationData?.editor_settings_json?.blocks.find(block => block.id == 'guestAnswer');;
  }
  for (let i = 0; i < block?.contents?.selectList.length; i++) {
    const item = block?.contents?.selectList[i];
    if (item.visible !== true) continue;
    if (item.required !== true) continue;
    if (item.title === '新郎新婦ゲスト選択') {
      rules.input.guest_type.required = helpers.withMessage(validationMessage.required(item.title), required);
    } else if (item.title === 'お名前') {
      rules.input.last_name.required = helpers.withMessage(validationMessage.required('姓'), required);
      rules.input.last_name.maxLength = helpers.withMessage(validationMessage.maxLength('姓', 30), maxLength(30));
      rules.input.first_name.required = helpers.withMessage(validationMessage.required('名'), required);
      rules.input.first_name.maxLength = helpers.withMessage(validationMessage.maxLength('名', 30), maxLength(30));
      for (let n = 0; n < rules.guests.length; n++) {
        rules.guests[n].last_name.required = helpers.withMessage(validationMessage.required('姓'), required);
        rules.guests[n].last_name.maxLength = helpers.withMessage(validationMessage.maxLength('姓', 30), maxLength(30));
        rules.guests[n].first_name.required = helpers.withMessage(validationMessage.required('名'), required);
        rules.guests[n].first_name.maxLength = helpers.withMessage(validationMessage.maxLength('名', 30), maxLength(30));
      }
    } else if (item.title === 'お名前（ふりがな）') {
      rules.input.last_name_kana.required = helpers.withMessage(validationMessage.required('せい'), required);
      rules.input.last_name_kana.maxLength = helpers.withMessage(validationMessage.maxLength('せい', 30), maxLength(30));
      rules.input.first_name_kana.required = helpers.withMessage(validationMessage.required('めい'), required);
      rules.input.first_name_kana.maxLength = helpers.withMessage(validationMessage.maxLength('めい', 30), maxLength(30));
      for (let n = 0; n < rules.guests.length; n++) {
        rules.guests[n].last_name_kana.required = helpers.withMessage(validationMessage.required('せい'), required);
        rules.guests[n].last_name_kana.maxLength = helpers.withMessage(validationMessage.maxLength('せい', 30), maxLength(30));
        rules.guests[n].first_name_kana.required = helpers.withMessage(validationMessage.required('めい'), required);
        rules.guests[n].first_name_kana.maxLength = helpers.withMessage(validationMessage.maxLength('めい', 30), maxLength(30));
      }
    } else if (item.title === 'お名前（ローマ字）') {
      rules.input.last_name_romaji.required = helpers.withMessage(validationMessage.required('姓（ローマ字）'), required);
      rules.input.last_name_romaji.maxLength = helpers.withMessage(validationMessage.maxLength('姓（ローマ字）', 30), maxLength(30));
      rules.input.first_name_romaji.required = helpers.withMessage(validationMessage.required('名（ローマ字）'), required);
      rules.input.first_name_romaji.maxLength = helpers.withMessage(validationMessage.maxLength('名（ローマ字）', 30), maxLength(30));
      for (let n = 0; n < rules.guests.length; n++) {
        rules.guests[n].last_name_romaji.required = helpers.withMessage(validationMessage.required('姓（ローマ字）'), required);
        rules.guests[n].last_name_romaji.maxLength = helpers.withMessage(validationMessage.maxLength('姓（ローマ字）', 30), maxLength(30));
        rules.guests[n].first_name_romaji.required = helpers.withMessage(validationMessage.required('名（ローマ字）'), required);
        rules.guests[n].first_name_romaji.maxLength = helpers.withMessage(validationMessage.maxLength('名（ローマ字）', 30), maxLength(30));
      }
    } else if (item.title === '性別') {
      rules.input.gender.required = helpers.withMessage(validationMessage.required('性別'), required);
      for (let n = 0; n < rules.guests.length; n++) {
        rules.guests[n].gender.required = helpers.withMessage(validationMessage.required('性別'), required);
      }
    } else if (item.title === '関係性') {
      rules.input.relationship_name.required = helpers.withMessage(validationMessage.required('関係性'), required);
      for (let n = 0; n < rules.guests.length; n++) {
        rules.guests[n].relationship_name.required = helpers.withMessage(validationMessage.required('関係性'), required);
      }
    } else if (item.title === '間柄') {
      rules.input.relationship.required = helpers.withMessage(validationMessage.required('間柄'), required);
      for (let n = 0; n < rules.guests.length; n++) {
        rules.guests[n].relationship.required = helpers.withMessage(validationMessage.required('間柄'), required);
      }
    } else if (item.title === 'アレルギー項目の入力') {
      // rules.input.allergy.required = helpers.withMessage(validationMessage.required('アレルギー項目の入力'), required);
      rules.input.allergy.required = helpers.withMessage(validationMessage.required('アレルギー項目の入力'), requiredIf(input.value?.input?.allergies?.length == 0));
      rules.input.allergy.maxLength = helpers.withMessage(validationMessage.maxLength('アレルギー項目の入力', 250), maxLength(250));
      for (let n = 0; n < rules.guests.length; n++) {
        // rules.guests[n].allergy.required = helpers.withMessage(validationMessage.required('アレルギー項目の入力'), required);
        rules.guests[n].allergy.required = helpers.withMessage(validationMessage.required('アレルギー項目の入力'), requiredIf(input.value?.guests?.[n]?.allergies?.length == 0));
        rules.guests[n].allergy.maxLength = helpers.withMessage(validationMessage.maxLength('アレルギー項目の入力', 250), maxLength(250));
      }
    } else if (item.title === '誕生日' || item.title === 'お誕生日') {
      rules.input.birthdate.required = helpers.withMessage(validationMessage.required('お誕生日'), required);
      for (let n = 0; n < rules.guests.length; n++) {
        rules.guests[n].birthdate.required = helpers.withMessage(validationMessage.required('お誕生日'), required);
      }
    } else if (item.title === '住所') {
      rules.input.postal_code.required = helpers.withMessage(validationMessage.required('郵便番号'), required);
      rules.input.prefecture.required = helpers.withMessage(validationMessage.required('都道府県'), required);
      rules.input.prefecture.maxLength = helpers.withMessage(validationMessage.maxLength('都道府県', 50), maxLength(50));
      rules.input.city.required = helpers.withMessage(validationMessage.required('市区町村'), required);
      rules.input.city.maxLength = helpers.withMessage(validationMessage.maxLength('市区町村', 50), maxLength(50));
      rules.input.address.required = helpers.withMessage(validationMessage.required('丁目・番地'), required);
      rules.input.address.maxLength = helpers.withMessage(validationMessage.maxLength('丁目・番地', 50), maxLength(50));
      // rules.input.building.required = helpers.withMessage(validationMessage.required('建物名・部屋番号など'), required);
    } else if (item.title === '電話番号') {
      rules.input.phone.required = helpers.withMessage(validationMessage.required('電話番号'), required);
    } else if (item.title === 'メールアドレス') {
      rules.input.email.required = helpers.withMessage(validationMessage.required('メールアドレス'), required);
      rules.input.email.email = helpers.withMessage(validationMessage.email('メールアドレス'), email);
      rules.input.email.maxLength = helpers.withMessage(validationMessage.maxLength('メールアドレス', 319), maxLength(319));
    // } else if (item.title === '連名入力') {
      // rules.input.message.required = helpers.withMessage(validationMessage.required('連名入力'), required);
    } else if (item.title === 'プロフィール写真') {
      rules.input.image.required = helpers.withMessage(validationMessage.required('プロフィール写真'), required);
      for (let n = 0; n < rules.guests.length; n++) {
        rules.guests[n].image.required = helpers.withMessage(validationMessage.required('プロフィール写真'), required);
      }
    } else if (item.title === 'お祝いメッセージ') {
      rules.input.message.required = helpers.withMessage(validationMessage.required('お祝いメッセージ'), required);
      for (let n = 0; n < rules.guests.length; n++) {
        rules.guests[n].message.required = helpers.withMessage(validationMessage.required('お祝いメッセージ'), required);
      }
    } else if (item.title === 'お祝い画像') {
      rules.input.media.required = helpers.withMessage(
        validationMessage.required('お祝い画像'),
        requiredIf(() => input.value?.input?.is_media_upload == 1)
      );
      rules.input.media_image.required = helpers.withMessage(
        validationMessage.required('お祝い画像'),
        requiredIf(() => input.value?.input?.is_media_upload == 0)
      );
    } else if (item.title === 'お祝い画像・動画') {
      rules.input.media.required = helpers.withMessage(
        validationMessage.required('お祝い画像・動画'),
        requiredIf(() => input.value?.input?.is_media_upload == 1)
      );
      rules.input.media_image.required = helpers.withMessage(
        validationMessage.required('お祝い画像・動画'),
        requiredIf(() => input.value?.input?.is_media_upload == 0)
      );
    }

  }

  return rules;
});

// 出欠を保存
const setAttendance = (event:any, value:any) => {
  let answer = {
    attendance: value,
    date: event.date,
    name: event.name,
    payment_amount: null
  }
  let index = events.value.findIndex(item => item.name === event.name);
  input.value.guest_event_answers[index] = answer;
}

const onClickAddGuest = () => {
  const guest = {
    image: {},
    last_name: '',
    first_name: '',
    last_name_romaji: '',
    first_name_romaji: '',
    last_name_kana: '',
    first_name_kana: '',
    guest_honor: '',
    gender: '',
    relationship_name: '',
    relationship: '',
    allergies: [],
    allergy: '',
    birthdate: '',
  } as any;
  input.value.guests.push(guest);
}
const onClickDeleteGuest = (index:number) => {
  let newGuests = [];
  for (let i = 0; i < input.value.guests.length; i++) {
    if (i == index) continue;
    const guest = input.value.guests[i];
    newGuests.push(guest);
  }
  input.value.guests = newGuests;

  deleteGuestIndex.value = null
};

// 都道府県リスト
const prefectureOptions = computed(() => {
  let options = [{value: '', label: '選択してください'}];
  for (let i = 0; i < PREFECTURE_MASTER.length; i++) {
    const label = PREFECTURE_MASTER[i];
    options.push({
      value: label,
      label: label,
    });
  }
  return options;
});

const events = computed(() => {
  return getEventsByWebInvitation(props.webInvitationData);
});

const attendanceItems = computed(() => {
  let block = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'guestAnswer');

  if(props.webInvitationData && props.webInvitationData.editor_settings){
    block = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'guestAnswer');
  }else if(props.webInvitationData && props.webInvitationData.editor_settings_json){
    block = props.webInvitationData?.editor_settings_json?.blocks.find(block => block.id == 'guestAnswer');;
  }
  // 出欠選択エリアを表示しない
  // if (block.contents.attendance.isHideAttendance) return [];

  let result = [];
  result.push({value: GUEST_ATTENDANCE_MASTER.PRESENT, label: '<span class=\'txt\'><span class=\'ja\'>ご出席</span><span class=\'en\'>Attend</span></span>'});
  result.push({value: GUEST_ATTENDANCE_MASTER.ABSENT, label: '<span class=\'txt\'><span class=\'ja\'>ご欠席</span><span class=\'en\'>Decline</span></span>'});
  // 保留ボタンを表示しない
  if (! block.contents.attendance.isHideSkip) result.push({value: GUEST_ATTENDANCE_MASTER.PENDING, label: '<span class=\'txt\'><span class=\'ja\'>保&nbsp;留</span><span class=\'en\'>Hold</span></span>'});
  // 出欠以外の選択肢を追加する
  if (block.contents.attendance.isAddFields) {
    for (let i = 0; i < block.contents.attendance.fields.length; i++) {
      const field = block.contents.attendance.fields[i];
      if (! field) continue;
      result.push({value: field, label: '<span class=\'txt\'><span class=\'ja\'>'+field+'</span></span>'});
    }
  }
  return result;
});

const answerLimitDate = computed(() => {
  let informationBlock = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'information');
  if(props.webInvitationData && props.webInvitationData.editor_settings){
    informationBlock = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'information');
  }else if(props.webInvitationData && props.webInvitationData.editor_settings_json){
    informationBlock = props.webInvitationData?.editor_settings_json?.blocks.find(block => block.id == 'information');;
  }

  const date = informationBlock.contents.date;

  let guestAnswerBlock = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'guestAnswer');
  if(props.webInvitationData && props.webInvitationData.editor_settings){
    guestAnswerBlock = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'guestAnswer');
  }else if(props.webInvitationData && props.webInvitationData.editor_settings_json){
    guestAnswerBlock = props.webInvitationData?.editor_settings_json?.blocks.find(block => block.id == 'guestAnswer');;
  }
  // 1: 開催一ヶ月前に期限を設定
  // 2: 開催二ヶ月前に期限を設定
  // 3: 回答期日を入力して設定
  if (guestAnswerBlock.contents.limit.setting == 1) {
    return $dayjs(date).add(-1, "month").format('YYYY-MM-DD');
  } else if (guestAnswerBlock.contents.limit.setting == 2) {
    return $dayjs(date).add(-2, "month").format('YYYY-MM-DD');
  } else if (guestAnswerBlock.contents.limit.setting == 3) {
    return $dayjs(guestAnswerBlock.contents.limit.date).format('YYYY-MM-DD');
  }
  return $dayjs(date).format('YYYY-MM-DD');
});

// 関係性と間柄のAPI
const { relation_ships, refetch } = useGetManyConstant(['CONSTANT_RELATIONSHIP', 'CONSTANT_RELATIONSHIP']);
const relationShipNameOptions = () => {
  let result = [{value: '', label: '関係性を選択してください'}];
  for (const key in RELATIONSHIP_NAME_MASTER) {
    const value = RELATIONSHIP_NAME_MASTER?.[key]
    result.push({value: value, label: value})
  }
  return result;
}
const relationShipOptions = (relationship_name = '') => {
  let relation_ship_data = relation_ships.value.map(category => ({
    groupLabel: category.category,
    groupItems: category.options.map(option => ({
      value: option.name,
      label: option.name
    }))
  }));
  relation_ship_data.unshift({
    value: '',
    label: '間柄を選択してください'
  });
  return relation_ship_data;
}

// 更新API
const { create, errors } = useCreateWebInvitationGuest();
const { mutate } = useGuestBinaryUploadImage2Mutation();

// 更新中のLoading
const isLoading = ref(false);

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 保存ボタンクリック
const onClickNext = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    // refGuestAnswerErrors.value?.[0] ってしたいけど、動的にrefは本番ビルドだと動かないみたい
    // document.getElementsByClassName('refGuestAnswerErrors')
    if (document.getElementsByClassName('refGuestAnswerErrors')?.[0]) {
      emit('scrollTo', document.getElementsByClassName('refGuestAnswerErrors')?.[0].getBoundingClientRect().top)
      return false;
    }
    if (windowBox.value?.[0]) {
      emit('scrollTo', windowBox.value[0].getBoundingClientRect().top)
      return false;
    }
    return false;
  }

  // 各種項目を調整
  const data = getCreateWebInvitationGuestData(input.value, props.webInvitationData, materialImages.value);

  // フリー項目
  isLoading.value = true;
  const isSuccess = await create(false, data.input, data.guests, data.free_item_values, data.guest_event_answers, data.guest_survey_answers);
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    isLoading.value = false;
    // scrollPageTop();
    return false;
  }

  // 一覧を再描画
  // await emit('reload');
  isLoading.value = false;
  window.scrollTo({top: 0, behavior: "smooth"});
  emit('change', input.value);
  emit('next');
};

const onChangePostalCode = (data:any) => {
  input.value.input.postal_code = data.postal_code;
  if (data?.prefecture) input.value.input.prefecture = data.prefecture;
  if (data?.city) input.value.input.city = data.city;
  if (data?.address) input.value.input.address = data.address;
};

const showOldKanjiModal = ref(false);
const oldKanjiTarget = ref('');
const refInputLastName = ref(null);
const refInputFirstName = ref(null);
const refInputGuestsLastName = ref([null]);
const refInputGuestsFirstName = ref([null]);
const refOldKanjiTarget = ref(null);
const oldKanjiBtnHide = ref(true);
const oldKanjiBtnHidden = ref(true);
const onFocusOldKanjiTarget = (key, index) => {
  oldKanjiBtnHide.value = false;
  oldKanjiBtnHidden.value = false;
  oldKanjiTarget.value = key+'.'+String((index-1));
  if (key == 'inputFirstName') {
    refOldKanjiTarget.value = refInputFirstName.value;
  } else if (key == 'inputLastName') {
    refOldKanjiTarget.value = refInputLastName.value;
  } else if (key == 'inputGuestsLastName') {
    refOldKanjiTarget.value = refInputGuestsLastName.value;
  } else if (key == 'inputGuestsFirstName') {
    refOldKanjiTarget.value = refInputGuestsFirstName.value;
  }
  if (refOldKanjiTarget.value?.[index]?.inputRef) {
    refOldKanjiTarget.value = refOldKanjiTarget.value?.[index];
  }
};
const onBlurOldKanjiTarget = () => {
  oldKanjiBtnHide.value = true;
  setTimeout(function(){
    if (oldKanjiBtnHide.value) oldKanjiBtnHidden.value = true;
  }, 500)
};
const onChangeOldKanji = (value) => {
  if (oldKanjiTarget.value.indexOf('inputGuests') !== -1) {
    let index = Number(oldKanjiTarget.value.replace(/[^0-9]/g, ''));
    if (oldKanjiTarget.value.indexOf('LastName') !== -1) {
      input.value.guests[index].last_name = value;
    } else {
      input.value.guests[index].first_name = value;
    }
  } else if (oldKanjiTarget.value.indexOf('inputLastName') !== -1) {
    input.value.input.last_name = value;
  } else if (oldKanjiTarget.value.indexOf('inputFirstName') !== -1) {
    input.value.input.first_name = value;
  }
  showOldKanjiModal.value = false;
}

watch(() => showOldKanjiModal, (newVal) => {
  if (newVal.value) {
    document.body.classList.add('is-open-modal');
  } else {
    document.body.classList.remove('is-open-modal');
  }
}, {
  deep: true,
  immediate: true
});

onMounted(() => {
  // 初期値を「選択なし」に設定
  if (input.value?.input?.is_media_upload === undefined) {
    input.value.input.is_media_upload = null;
  }
});
</script>
<style lang="scss">
@import '@/assets/css/webinvitation/common/style.scss';
.btn.is-confirm{
  &:disabled{
    color: #FFF !important;
    background: #CCC !important;
  }
}
.guestAnswer_box_remove {
  text-align: center;
  margin: 32px 0 24px;
}
.guestAnswer_box_file_button {
  display: block;
  position: relative;
  font-size: 14px;
  line-height: 1;
  background: rgba(0, 0, 0, 0.05);
  padding: 18px 18px 18px 42px;
  margin-top: 10px;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: 0.35s ease-in-out;
  &::before {
    content: '';
    display: block;
    width: 24px;
    height: 24px;
    background-image: url('@/assets/images/icon-plus-form.svg');
    background-size: contain;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 16px;
    margin: auto;
  }
}
.btn.is-delete{
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: #333;
  background: rgba(0, 0, 0, 0.1);
  font-size: 14px;
  position: relative;
  padding: 8px 28px;
  border-radius: 48px;
  transition: 0.35s ease-in-out;
  &::before {
    content: '';
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    background: currentColor;
    mask-image: url('@/assets/images/icon-remove.svg');
    mask-size: contain;
  }
  &:hover{
    background: rgba(0, 0, 0, 0.15);
  }
}
.btn.is-add {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #FFF;
  background: #B18A3E;
  font-size: 14px;
  position: relative;
  margin: 0 20px 92px;
  padding: 8px 15px;
  border-radius: 8px;
  transition: 0.35s ease-in-out;
  &::before {
    content: '';
    display: inline-block;
    width: 28px;
    height: 28px;
    margin-right: 8px;
    background: currentColor;
    mask-image: url('@/assets/images/icon-plus-form.svg');
    mask-size: contain;
  }
}
.webInvitationView .preview_text{
  color: $color-alert;
  font-size: 13px;
  margin-top: 10px;
  padding: 10px 0 0;
  text-align: center;
  white-space: nowrap;
}
.webInvitationView .guestAnswer_box_label.is-noBorder {
  &:has(+.guestAnswer_box_row.is-noBorder .guestAnswerPreview) {
    margin-bottom: 12px;
  }
}
.webInvitationView .guestAnswer_box_row.is-noBorder{
  border-bottom: 0;
  padding-bottom: 0;
  & + .guestAnswer_box_row:not(.is-noBorder) {
    padding-top: 24px;
    border-top: 1px solid var(--Gray, #D9D9D9);
  }
  &:empty {
    margin: 0;
    padding: 0;
  }
  &:has(.guestAnswerPreview) {
    padding-top: 0;
  }
}
.webInvitationView .guestAnswer_box_row.pb-68 {
  & + .guestAnswer_box_row:not(.is-noBorder){
    margin-top: 24px;
  }
}
.inputAttendance{
  -webkit-tap-highlight-color:transparent;
}
.guestAnswerPreview{
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  background: #FFF;
  box-shadow: 0 2px 20px rgba(0,0,0,0.14);
  border-radius: 14px;
  overflow: hidden;
  &_image{
    img{
      aspect-ratio: 1 / 1;
      width: 100%;
      object-fit: cover;
    }
  }
  &_text{
    font-size: 16px;
    line-height: 1.5;
    color: #9C9C9C;
    padding: 10px 14px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-all;
  }
  &_name{
    font-size: 16px;
    line-height: 1.5;
    color: #9C9C9C;
    text-align: right;
    padding: 6px 0 0;
  }
}

.modalWrap {
  font-family: "Noto Sans JP", sans-serif;
  z-index: 5000;
}

.link-oldkanji {
  transition: opacity 0.35s ease-in-out;
  &.is-hide {
    opacity: 0;
    cursor: default;
    &.is-disabled {
      pointer-events: none;
    }
  }
  &.is-lastname {
    margin-left: 50%;
    padding-left: 10px !important;
  }
}

// .link-accent {
//   color:red !important;
// }


// 旧字体モーダル
.modalOldKanji .modalContainer .contents {
  h2 {
    color: #333;
    font-size: 14px;
    font-weight: 700;
  }
  label .title {
    display: none;
  }
  .btn {
    padding: 18px 0;
    border-radius: 50px;
    max-width: 260px;
    margin-left: auto;
    margin-right: auto;
    color: #FFF;
    font-size: 18px;
    font-weight: 700;
    :not(:disabled) {
      background: var(--primary-color);
    }
  }
  .selectKanji li.items {
    border-width: 2px;
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0)
  }
  .selectKanji li.items.is-selected {
    border-color: var(--primary-color);
    background: #fff;
  }
}

body.is-open-modal {
  .webInvitationView_navigation ~ .blocks {
        z-index: 1010;
  }
}
</style>