<template>
  <ViewFormBlock :isChecked="props.visible">
    <div id="information" class="viewBlock information" data-animation>
      <div class="viewBlock_wrap information_wrap" data-animation>
        <div class="information_box" data-animation>
          <h2 class="viewBlock_title information_title"><span class="lang-ja">パーティーのご案内</span><span class="lang-en">Party Information</span></h2>
          <div class="information_date" v-if="!isNaN(date.year) || !isNaN(date.month) || !isNaN(date.day) || date.weekDayString" data-animation>
            <span class="information_date_label">開催日</span>
            <div class="information_date_values">
              <span class="information_date_value" v-if="!isNaN(date.year)">{{ date.year }}</span><span class="information_date_unit" v-if="!isNaN(date.year)">年</span>
              <span class="information_date_value" v-if="!isNaN(date.month)">{{ date.month }}</span><span class="information_date_unit" v-if="!isNaN(date.month)">月</span>
              <span class="information_date_value" v-if="!isNaN(date.day)">{{ date.day }}</span><span class="information_date_unit" v-if="!isNaN(date.day)">日</span>
              <span class="information_date_unit information_date_unit_week" v-if="date.weekDayString">&nbsp;{{ date.weekDayString }}</span>
            </div>
          </div>

          <template
            v-for="(event , eventIndex) in data.events"
            :key="eventIndex"
          >
            <div v-if="event.plans" class="information_block" data-animation>
              <h3
                v-if="data.type == 'ceremony_reception_same_venue' && eventIndex  == 0 || data.type == 'ceremony_reception_separate_venue' && eventIndex  == 0 || data.type == 'ceremony' && eventIndex  == 0"
                class="viewBlock_title_sub information_title_sub"
              ><span>挙式</span></h3>
              <h3
                v-else
                class="viewBlock_title_sub information_title_sub"
              ><span>{{ event.eventName }}</span></h3>
              <div class="information_block_time">
                <div v-if="event.plans[1] && event.plans[1].isShowPlan" class="information_block_time_main">
                  <div class="information_block_time_label">{{ planLabels[1] }}</div>
                  <div class="information_block_time_value">{{ event.plans[1].hour ? event.plans[1].hour : '00' }}:{{ event.plans[1].minute ? event.plans[1].minute : '00' }}</div>
                </div>
                <div class="information_block_time_sub">
                  <div v-if="event.plans[0] && event.plans[0].isShowPlan" class="information_block_time_sub_item">
                    <div class="information_block_time_label information_block_time_label_sub">{{ planLabels[0] }}</div>
                    <div class="information_block_time_value information_block_time_value_sub">{{ event.plans[0].hour ? event.plans[0].hour : '00' }}:{{ event.plans[0].minute ? event.plans[0].minute : '00' }}</div>
                  </div>
                  <div v-if="event.plans[2] && event.plans[2].isShowPlan" class="information_block_time_sub_item">
                    <div v-if="data.type == 'reception'" class="information_block_time_label information_block_time_label_sub">お披楽喜</div>
                    <div v-else class="information_block_time_label information_block_time_label_sub">{{ planLabels[2] }}</div>
                    <div class="information_block_time_value information_block_time_value_sub">{{ event.plans[2].hour ? event.plans[2].hour : '00' }}:{{ event.plans[2].minute ? event.plans[2].minute : '00' }}</div>
                  </div>
                </div>
              </div>
              <div v-if="data.type == 'ceremony_reception_same_venue' && eventIndex != 0 && event.feeOption != 'gift_system' || data.type == 'ceremony_reception_separate_venue' && eventIndex != 0 && event.feeOption != 'gift_system' || data.type != 'ceremony_reception_same_venue' && data.type != 'ceremony_reception_separate_venue' && event.feeOption != 'gift_system'">
                <hr>
                <h4 class="viewBlock_title_small information_title_small"><span>会費</span></h4>
                <div v-if="event.feeOption == 'membership_fee_common'" class="information_block_price">
                  <div v-if="event.feeAmount" class="information_block_price_item">{{ isNaN(event.feeAmount[0]) ? event.feeAmount[0] : Number(event.feeAmount[0]).toLocaleString() }}円</div>
                </div>
                <div v-if="event.feeOption == 'membership_fee_separate'" class="information_block_price">
                  <div v-if="event.feeAmount" class="information_block_price_item">男性 {{ isNaN(event.feeAmount[0]) ? event.feeAmount[0] : Number(event.feeAmount[0]).toLocaleString() }}円</div>
                  <div v-if="event.feeAmount" class="information_block_price_item">女性 {{isNaN(event.feeAmount[1]) ? event.feeAmount[1] : Number(event.feeAmount[1]).toLocaleString() }}円</div>
                </div>
              </div>
              <div v-if="event.isShowOtherText">
                <hr>
                <div class="information_block_freeText" v-html="onFormatLink(sanitizeContent(event.otherText))">
                </div>
              </div>
              <div v-else-if="event.isShowText && data.type != 'ceremony_reception_same_venue' && data.type != 'ceremony_reception_separate_venue' || event.isShowText && eventIndex > 0">
                <hr>
                <div class="information_block_freeText" v-html="onFormatLink(sanitizeContent(event.text))">
                </div>
              </div>
            </div>

            <template
              v-if="data.type != 'ceremony_reception_same_venue'"
            >
              <div v-if="event.otherVenue || event.otherZip || event.otherAddress || event.otherTel || event.otherUrl || event.isShowOtherMaps || event.isShowOtherImages" class="information_block information_block_detail" data-animation>
                <h3 class="viewBlock_title_sub information_title_sub"><span>会場情報</span></h3>
                <div class="information_block_address">
                  <div class="information_block_address_title" v-if="event.otherVenue">{{ event.otherVenue }}</div>
                  <div class="information_block_address_zip" v-if="event.otherZip">〒{{ onFormatZip(event.otherZip) }}</div>
                  <div class="information_block_address_detail" v-if="event.otherAddress">{{ event.otherAddress }}</div>
                  <div class="information_block_address_tel" v-if="event.otherTel">TEL : {{ event.otherTel }}</div>
                  <div class="information_block_address_link" v-if="event.otherUrl">
                    <a class="information_block_address_link_url" :href="event.otherUrl" target="_blank">{{ event.otherUrl }}</a>
                  </div>
                  <div v-if="event.isShowOtherAddressText && event.otherAddressText" class="information_block_address_text">
                    <div v-html="onFormatLink(sanitizeContent(event.otherAddressText))"></div>
                  </div>
                  <div class="information_block_address_maps" v-if="event.isShowOtherMaps">
                    <GoogleMaps
                      :address="event.otherAddress"
                    ></GoogleMaps>
                  </div>
                  <div class="information_block_address_images" v-if="event.isShowOtherVisual">
                    <swiper
                      v-if="event.otherImages && event.otherImages.length > 1"
                      class="swiper address_slides"
                      :modules="[Navigation, Pagination, Virtual]"
                      :slides-per-view="1"
                      :centered-slides="true"
                      navigation
                      :pagination="{ clickable: true }"
                      data-animation
                    >
                      <SwiperSlide
                        v-for="(slide, index) in event.otherImages"
                        class="address_slide"
                        :virtualIndex="index"
                        :key="`address_slide-${index}`"
                      >
                        <CropImageZoomModal
                          :src="slide.src"
                          :crop="slide"
                          :images="event.otherImages"
                          :index="index"
                        ></CropImageZoomModal>
                      </SwiperSlide>
                    </swiper>
                    <CropImageZoomModal
                      v-else-if="event.otherImages && event.otherImages.length == 1"
                      :src="event.otherImages[0]?.src"
                      :crop="event.otherImages[0]"
                    ></CropImageZoomModal>
                  </div>
                </div>
              </div>
            </template>

            <div v-if="event.otherPlans" class="information_block" data-animation>
              <h3 class="viewBlock_title_sub information_title_sub"><span>披露宴</span></h3>
              <div class="information_block_time">
                <div v-if="event.otherPlans[1] && event.otherPlans[1].isShowPlan" class="information_block_time_main">
                  <div class="information_block_time_label">{{ planLabels[1] }}</div>
                  <div class="information_block_time_value">{{ event.otherPlans[1].hour ? event.otherPlans[1].hour : '00' }}:{{ event.otherPlans[1].minute ? event.otherPlans[1].minute : '00' }}</div>
                </div>
                <div class="information_block_time_sub">
                  <div v-if="event.otherPlans[0] && event.otherPlans[0].isShowPlan" class="information_block_time_sub_item">
                    <div class="information_block_time_label information_block_time_label_sub">{{ planLabels[0] }}</div>
                    <div class="information_block_time_value information_block_time_value_sub">{{ event.otherPlans[0].hour ? event.otherPlans[0].hour : '00' }}:{{ event.otherPlans[0].minute ? event.otherPlans[0].minute : '00' }}</div>
                  </div>
                  <div v-if="event.otherPlans[2] && event.otherPlans[2].isShowPlan" class="information_block_time_sub_item">
                    <div v-if="data.type == 'ceremony_reception_same_venue' || data.type == 'ceremony_reception_separate_venue'" class="information_block_time_label information_block_time_label_sub">お披楽喜</div>
                    <div v-else class="information_block_time_label information_block_time_label_sub">{{ planLabels[2] }}</div>
                    <div class="information_block_time_value information_block_time_value_sub">{{ event.otherPlans[2].hour ? event.otherPlans[2].hour : '00' }}:{{ event.otherPlans[2].minute ? event.otherPlans[2].minute : '00' }}</div>
                  </div>
                </div>
              </div>
              <div v-if="event.feeOption != 'gift_system'">
                <hr>
                <h4 class="viewBlock_title_small information_title_small"><span>会費</span></h4>
                <div v-if="event.feeOption == 'membership_fee_common'" class="information_block_price">
                  <div v-if="event.feeAmount" class="information_block_price_item">{{ isNaN(event.feeAmount[0]) ? event.feeAmount[0] : Number(event.feeAmount[0]).toLocaleString() }}円</div>
                </div>
                <div v-if="event.feeOption == 'membership_fee_separate'" class="information_block_price">
                  <div v-if="event.feeAmount" class="information_block_price_item">男性 {{ isNaN(event.feeAmount[0]) ? event.feeAmount[0] : Number(event.feeAmount[0]).toLocaleString() }}円</div>
                  <div v-if="event.feeAmount" class="information_block_price_item">女性 {{ isNaN(event.feeAmount[1]) ? event.feeAmount[1] : Number(event.feeAmount[1]).toLocaleString() }}円</div>
                </div>
              </div>
              <div v-if="event.isShowText">
                <hr>
                <div class="information_block_freeText" v-html="onFormatLink(sanitizeContent(event.text))">
                </div>
              </div>
            </div>

            <div v-if="event.venue || event.zip || event.address || event.tel || event.url || event.isShowMaps || (event.isShowAddressText && event.addressText)" class="information_block information_block_detail" data-animation>
              <h3 class="viewBlock_title_sub information_title_sub"><span>会場情報</span></h3>
              <div class="information_block_address">
                <div class="information_block_address_title" v-if="event.venue">{{ event.venue }}</div>
                <div class="information_block_address_zip" v-if="event.zip">〒{{ onFormatZip(event.zip) }}</div>
                <div class="information_block_address_detail" v-if="event.address">{{ event.address }}</div>
                <div class="information_block_address_tel" v-if="event.tel">TEL : {{ event.tel }}</div>
                <div class="information_block_address_link" v-if="event.url">
                  <a class="information_block_address_link_url" :href="event.url" target="_blank">{{ event.url }}</a>
                </div>
                <div v-if="event.isShowAddressText && event.addressText" class="information_block_address_text">
                  <div v-html="onFormatLink(sanitizeContent(event.addressText))"></div>
                </div>
                <div class="information_block_address_maps" v-if="event.isShowMaps">
                  <GoogleMaps
                    :address="event.address"
                  ></GoogleMaps>
                </div>
                <div class="information_block_address_images" v-if="event.isShowVisual">
                  <swiper
                    v-if="event.images && event.images.length > 1"
                    class="swiper address_slides"
                    :modules="[Navigation, Pagination, Virtual]"
                    :slides-per-view="1"
                    :centered-slides="true"
                    navigation
                    :pagination="{ clickable: true }"
                    data-animation
                  >
                    <SwiperSlide
                      v-for="(slide, index) in event.images"
                      class="address_slide"
                      :virtualIndex="index"
                      :key="`address_slide-${index}`"
                    >
                      <CropImageZoomModal
                        :src="slide.src"
                        :crop="slide"
                        :images="event.images"
                        :index="index"
                      ></CropImageZoomModal>
                    </SwiperSlide>
                  </swiper>
                  <CropImageZoomModal
                    v-else-if="event.images && event.images.length == 1"
                    :src="event.images[0]?.src"
                    :crop="event.images[0]"
                  ></CropImageZoomModal>
                </div>
              </div>
            </div>

          </template>
        </div>
      </div>
    </div>
  </ViewFormBlock>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Virtual } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

interface Props {
  data: {
    name: 'パーティー情報',
    id: 'information',
    visible: boolean,
    contents: {
      date: string | number,
      type: string,
      events: {
        isOtherParty: boolean,
        eventName: string,
        plans: {
          isShowPlan: boolean,
          hour: string,
          minute: string
        }[]
        otherPlans: {
          isShowPlan: boolean,
          hour: string,
          minute: string
        }[],
        venue: string,
        venue_kana: string,
        zip: string,
        address: string,
        addressText: string | null,
        isShowAddressText: boolean,
        isShowMaps: boolean,
        tel: string,
        url: string,
        feeOption: string,
        feeAmount: string[],
        isShowText: boolean,
        text: string,
        otherVenue: string,
        otherVenue_kana: string,
        otherZip: string,
        otherAddress: string,
        otherAddressText: string | null,
        isShowOtherAddressText: boolean,
        isShowOtherMaps: boolean,
        otherTel: string,
        otherUrl: string,
        isShowOtherText: boolean,
        otherText: string
      }[]
    }
  },
  aspect?: {},
  visible?: boolean,
  error?: string
};
const props = withDefaults(defineProps<Props>(), {
  visible: true
});

const onFormatLink = (text:string) => {
  if(!text){return null}
  const urlPattern = /(https?:\/\/[^\s]+)/ig;
  return text.replace(urlPattern, '<a href="$1" target="_blank">$1</a>');
};

const sanitizeContent = (content: string | null) => {
  if (!content) return '';
  return content;
};

const planLabels = ref([
  '受付',
  '開始時刻',
  '終了予定'
])

const date = computed(() => {
  let target = props.data?.date ? props.data.date : '';
  const targetDate = new Date(new Date(target).toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));

  // 年 月 日を取得
  const year = targetDate.getFullYear(); // 年
  const month = targetDate.getMonth() + 1; // 月（0から11までの値を返すため 1を加える）
  const day = targetDate.getDate(); // 日

  // 曜日を取得（0（日曜）から6（土曜）までの値）
  const weekDay = targetDate.getDay();
  const weekDays = ["日", "月", "火", "水", "木", "金", "土"];
  let weekDayString;
  if(weekDays[weekDay]){
    weekDayString = weekDays[weekDay] + '曜日';
  }

  // 時と分を取得
  let hours:string|number = targetDate.getHours(); // 時
  let minutes:string|number = targetDate.getMinutes(); // 分
  if (String(hours).length == 1) {
    hours = '0' + hours;
  }
  if (String(minutes).length == 1) {
    minutes = '0' + minutes;
  }

  return {
    year: year,
    month: month,
    day: day,
    weekDayString: weekDayString,
    hours: hours,
    minutes: minutes
  };
})

const onFormatZip = (value:string) => {
  let start_code = value.substring(0,3);
  let end_code = value.substring(3,7);
  let post_code =  start_code + '-' + end_code;
  return post_code
};
</script>

<style lang="scss" scoped>
.information{
  hr{
    margin: 10px 0;
  }
  .information_title{
    margin-bottom: 30px;
  }
  &_box{
    & + .information_box{
      margin-top: 20px;
    }
  }
  &_date{
    text-align: center;
    margin-bottom: 40px;
    &_label{
      margin-bottom: 4px;
    }
    &_values{
      letter-spacing: 1.4px;
    }
    &_value{
      font-size: 28px;
    }
    &_unit{
      font-size: 16px;
      &_week{
        font-size: 14px;
      }
    }
  }
  &_block{
    padding: 16px 20px;
    margin: 0 auto 12px;
    width: calc(100% - 40px);
    &.information_block_detail{
      margin-bottom: 0;
      & + .information_block{
        margin-top: 40px;
      }
    }
    &_time{
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      line-height: 1;
      padding-bottom: 12px;
      &_label{
        font-size: 14px;
        margin-top: 2px;
        margin-right: 8px;
        &_sub{
          margin-top: 0;
          margin-right: 4px;
        }
      }
      &_value{
        font-size: 28px;
        font-weight: 600;
        &_sub{
          font-size: 16px;
          font-weight: normal;
        }
      }
      &_main{
        display: flex;
        justify-content: center;
        align-items: center;
      }
      &_sub{
        display: flex;
        justify-content: center;
        align-items: center;
        &_item{
          display: flex;
          align-items: center;
          margin-top: 12px;
          & + .information_block_time_sub_item{
            &::before{
              content: '/';
              display: inline-block;
              margin: 0 8px;
            }
          }
        }
      }
    }
    &_price{
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 18px 0;
      &_item{
        & + .information_block_price_item{
          margin-left: 1em;
        }
      }
    }
    &_address{
      text-align: center;
      &_title{
        font-size: 18px;
        margin-bottom: 14px;
        font-weight: 600;
      }
      &_zip{
        font-size: 13px;
        margin-bottom: 8px;
      }
      &_detail{
        font-size: 13px;
        margin-bottom: 10px;
      }
      &_tel{
        font-size: 14px;
        margin-bottom: 12px;
      }
      &_link{
        margin-bottom: 16px;
        &_url{
          display: inline-block;
          font-size: 14px;
          line-height: 1.5;
          text-decoration: underline;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
          &:hover{
            text-decoration: none;
          }
        }
      }
      &_text{
        font-size: 14px;
        margin-bottom: 16px;
      }
      &_maps{
        iframe{
          max-width: 100%;
        }
        & + .information_block_address_images {
          margin-top: 20px;
        }
      }
      &_images {
        .swiper {
          padding-bottom: 20px;
        }
      }
    }
    &_text{
      font-size: 13px;
      max-width: 100%;
      line-height: 1.6;
      a{
        word-wrap: break-word;
        &:hover{
          text-decoration: none;
        }
      }
    }
    &_freeText{
      text-align: center;
      margin-top: 16px;
    }
  }
}

.viewBlock_title_small{
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  margin: 16px 0;
  background-size: auto 8px;
  position: relative;
}
</style>