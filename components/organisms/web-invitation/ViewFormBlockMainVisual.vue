<template>
  <ViewFormBlock :isChecked="props.visible">
    <div id="mainVisual" ref="mainVisualRef" class="viewBlock mainVisual" data-animation>
      <div class="mainVisual_first">
        <div class="mainVisual_first_symbol mainVisual_first_symbol_1"></div>
        <div class="mainVisual_first_symbol mainVisual_first_symbol_2"></div>
        <div class="mainVisual_first_symbol mainVisual_first_symbol_3"></div>
      </div>
      <div class="mainVisual_background" data-animation>
        <template v-if="getValidImages().length > 0">
          <div
            :class="['mainVisual_background_image', { 'mainVisual_background_video': mainImage.srcVideo }]"
            v-for="(mainImage, index) in getValidImages()"
            :key="index"
            :style="{'zoom': zoomValues[index]}"
          >
            <!-- 動画の場合 -->
            <div v-if="mainImage.srcVideo" class="mainVisual_background_video">
              <CropVideo
                v-if="mainImage.srcVideo && mainImage?.width"
                :src="mainImage?.src"
                :srcVideo="mainImage?.srcVideo"
                :crop="mainImage"
                :controls="false"
                :loop="true"
                :muted="true"
                :browserAutoplay="true"
              />
              <VideoUUID
                v-else-if="mainImage.srcVideo"
                :src="mainImage?.src"
                :srcVideo="mainImage?.srcVideo"
                :cover="true"
                :controls="false"
                :loop="true"
                :muted="true"
                :browserAutoplay="true"
              />
            </div>
            <!-- 画像の場合 -->
            <div v-else-if="mainImage.src"
              :style="[
                {'animationDelay': `${(getValidImages().length == 4 || getValidImages().length == 5 ? 2.5 : 3) * index}s`}
              ]"
              class="mainVisual_background_image_item"
              data-animation
              :class="[
                {'slide2': getValidImages().length == 2},
                {'slide3': getValidImages().length == 3},
                {'slide4': getValidImages().length == 4},
                {'slide5': getValidImages().length == 5}
              ]"
            >
              <CropImage
                :src="mainImage.src"
                :crop="mainImage"
                :load-key="loadKey"
              ></CropImage>
            </div>
          </div>
        </template>
        <div v-else class="mainVisual_background_image mainVisual_background_noImage">
        </div>
      </div>
      <div class="viewBlock_wrap mainVisual_wrap" data-animation>
        <div class="mainVisual_box" data-animation>
          <h1 class="mainVisual_title" data-animation>
            <span class="mainVisual_title_symbol mainVisual_title_symbol_start" data-animation></span>
            <span class="mainVisual_title_text" data-animation></span>
            <span class="mainVisual_title_symbol mainVisual_title_symbol_end" data-animation></span>
          </h1>
          <div class="names mainVisual_names">
            <div class="name mainVisual_name mainVisual_name_bride">
              <span>{{ props.data.groomName }}</span>
            </div>
            <div class="name mainVisual_name mainVisual_name_groom">
              <span>{{ props.data.brideName }}</span>
            </div>
          </div>
          <div v-if="props.informationData?.contents?.date" class="date mainVisual_date">
            <span class="mainVisual_date_year">{{ $dayjs(props.informationData?.contents?.date).format('YYYY') }}</span>
            <span class="mainVisual_date_unit mainVisual_date_unit_1">.</span>
            <span class="mainVisual_date_month">{{ $dayjs(props.informationData?.contents?.date).format('M') }}</span>
            <span class="mainVisual_date_month_zero" style="display: none;">{{ $dayjs(props.informationData?.contents?.date).format('MM') }}</span>
            <span class="mainVisual_date_month_en_1" style="display: none;">{{ $dayjs(props.informationData?.contents?.date).locale('en').format('MMM') }}</span>
            <span class="mainVisual_date_month_en_2" style="display: none;">{{ $dayjs(props.informationData?.contents?.date).locale('en').format('MMMM') }}</span>
            <span class="mainVisual_date_unit mainVisual_date_unit_2">.</span>
            <span class="mainVisual_date_day">{{ $dayjs(props.informationData?.contents?.date).format('D') }}</span>
            <span class="mainVisual_date_day_zero" style="display: none;">{{ $dayjs(props.informationData?.contents?.date).format('DD') }}</span>
            <span class="mainVisual_date_unit mainVisual_date_unit_3"></span>
            <span class="mainVisual_date_week_en_1" style="display: none;">{{ $dayjs(props.informationData?.contents?.date).locale('en').format('ddd') }}</span>
            <span class="mainVisual_date_week_en_2" style="display: none;">{{ $dayjs(props.informationData?.contents?.date).locale('en').format('dddd') }}</span>
            <span class="mainVisual_date_week_ja_1" style="display: none;">{{ $dayjs(props.informationData?.contents?.date).format('ddd') }}</span>
            <span class="mainVisual_date_week_ja_2" style="display: none;">{{ $dayjs(props.informationData?.contents?.date).format('dddd') }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="mainVisual_bg" data-animation></div>
  </ViewFormBlock>
</template>

<script lang="ts" setup>
import { useWindowSize } from '@vueuse/core';
const { $dayjs } = useNuxtApp() as any;
const props = withDefaults(defineProps<{
  data?: {},
  informationData?: {},
  visible?: boolean,
  isLockSpecifiedPosition?: boolean,
  maxMainVisualImages?: number,
  getAspectRatioForPosition?: Function
}>(), {
  visible: true,
  isLockSpecifiedPosition: false,
  maxMainVisualImages: 3
});

const zoomValues = ref(['', '', '']);
const mainVisualRef = ref<HTMLElement | null>(null);
const calculateZoom = () => {
  if (mainVisualRef.value) {
    const width = mainVisualRef.value.clientWidth;
    const height = mainVisualRef.value.clientHeight;
    props.data.images.forEach (function(image, index) {
      let heightImageZoom = (height / 680);
      let widthImageZoom = (width / 375);
      if(heightImageZoom > widthImageZoom){
        zoomValues.value[index] = heightImageZoom.toString();
      }else{
        zoomValues.value[index] = widthImageZoom.toString();
      }
    });
  }
};

const loadKey = ref(0)
onMounted(() => {
  calculateZoom();

  nextTick(() => {
    loadKey.value += 1
  });
});

// 位置指定ありの場合の有効な画像を取得
const getValidImages = () => {
  if (!props.isLockSpecifiedPosition) {
    return props.data.images || [];
  }
  
  const images = props.data.images || [];
  const maxImages = props.maxMainVisualImages || 3;
  
  // 最大枚数までの有効な画像のみを返す
  return images.slice(0, maxImages).filter(image => image && (image.src || image.srcVideo));
};

const { width, height } = useWindowSize();
watch([width, height], () => {
  calculateZoom();
});

</script>

<style lang="scss" scoped>
@keyframes slideShow2 {
  0% {
    opacity: 0;
    animation-timing-function: ease-in;
  }
  16.6% {
    opacity: 1;
    animation-timing-function: ease-out;
  }
  50% {
    opacity: 1;
  }
  66.6%,
  100% {
    opacity: 0;
  }
}
@keyframes slideShow3 {
  0% {
    opacity: 0;
    animation-timing-function: ease-in;
  }
  11.11% {
    opacity: 1;
    animation-timing-function: ease-out;
  }
  33.33% {
    opacity: 1;
  }
  44.44%,
  100% {
    opacity: 0;
  }
}
@keyframes slideShow4 {
  0% {
    opacity: 0;
    animation-timing-function: ease-in;
  }
  8.33% {
    opacity: 1;
    animation-timing-function: ease-out;
  }
  25% {
    opacity: 1;
  }
  33.33%,
  100% {
    opacity: 0;
  }
}
@keyframes slideShow5 {
  0% {
    opacity: 0;
    animation-timing-function: ease-in;
  }
  6.66% {
    opacity: 1;
    animation-timing-function: ease-out;
  }
  20% {
    opacity: 1;
  }
  26.66%,
  100% {
    opacity: 0;
  }
}
.mainVisual {
  height: 100vh;
  height: 100svh;
  min-height: 420px;
  position: relative;
  color: #FFF;
  @include pc {
    height: 680px;
  }
  &_background {
    &_image {
      z-index: 0 !important;
      &_item {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        opacity: 0;
        z-index: 0;
        &.slide2 {
          &.is-animated{
            animation: slideShow2 6s linear infinite;
          }
        }
        &.slide3 {
          &.is-animated{
            animation: slideShow3 9s linear infinite;
          }
        }
        &.slide4 {
          &.is-animated{
            animation: slideShow4 10s linear infinite;
          }
        }
        &.slide5 {
          &.is-animated{
            animation: slideShow5 12.5s linear infinite;
          }
        }
      }
      :deep(.crop_image) {
        width: 100% !important;
        height: 100% !important;
        img{
          object-fit: cover;
          min-width: 100%;
          min-height: 100%;
        }
      }
    }
    &_video {
      z-index: 0 !important;
      width: 100%;
      height: 100%;
    }
  }
  &_box {
    padding: 160px 20px;
    text-align: center;
    position: relative;
    z-index: 1;
  }
  &_title {
    font-size: 30px;
    margin: 0 auto 20px;
    writing-mode: vertical-rl;
    line-height: 1.4;
    letter-spacing: 0.4em;
    &_symbol {
      font-weight: bold;
    }
  }
  &_names {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0 36px;
  }
  &_name {
    max-width: 220px;
    width: 100%;
    font-weight: 600;
  }
  &_date {
    margin-top: 20px;
    letter-spacing: 0.2em;
  }
}
</style>