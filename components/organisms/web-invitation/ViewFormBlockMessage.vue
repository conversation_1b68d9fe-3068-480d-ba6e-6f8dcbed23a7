<template>
  <ViewFormBlock :isChecked="props.visible">
    <div id="message" class="viewBlock message" data-animation>
      <div class="message_symbol message_symbol_1" data-animation></div>
      <div class="viewBlock_wrap message_wrap">
        <div class="message_symbol message_symbol_2" data-animation></div>
        <div class="message_box">
          <h2 class="viewBlock_title message_title"><span class="lang-ja">ご挨拶</span><span class="lang-en">Message</span></h2>
          <div class="message_contents" :style="{'textAlign': props.data.textAlign}">
            <div class="message_symbol message_symbol_3" data-animation></div>
            <div class="message_contents_text" v-html="onFormatLink(sanitizeContent(props.data.message))"></div>
            <div class="message_symbol message_symbol_4" data-animation></div>
          </div>
        </div>
        <div class="message_symbol message_symbol_5" data-animation></div>
        <div v-if="props.data.isShowVisual">
          <swiper
            v-if="props.data.images && props.data.images.length > 1"
            class="swiper message_slides"
            :modules="[Navigation, Pagination, Virtual]"
            :slides-per-view="1"
            :centered-slides="true"
            navigation
            :pagination="{ clickable: true }"
            data-animation
          >
            <SwiperSlide
              v-for="(slide, index) in props.data.images"
              class="message_slide"
              :virtualIndex="index"
              :key="`message_slide-${index}`"
            >
              <CropImage
                :src="slide.src"
                :crop="slide"
              ></CropImage>
            </SwiperSlide>
          </swiper>
          <VideoUUID
            v-else-if="props.data?.images && props.data?.images.length == 1 && props.data.images[0].srcVideo"
            :src="props.data.images[0]?.src"
            :srcVideo="props.data.images[0]?.srcVideo"
            :muted="props.data.images[0]?.isAutoplay ? props.data.images[0]?.isAutoplay : true"
            :browserAutoplay="props.data.images[0]?.isAutoplay ? props.data.images[0]?.isAutoplay : false"
            :controls="props.data.images[0]?.isAutoplay ? !props.data.images[0]?.isAutoplay : true"
          />
          <CropImage
            v-else-if="props.data.images && props.data.images.length == 1"
            :src="props.data.images[0]?.src"
            :srcVideo="props.data.images[0]?.srcVideo"
            :crop="props.data.images[0]"
          ></CropImage>
        </div>
      </div>
    </div>
  </ViewFormBlock>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Virtual } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const props = withDefaults(defineProps<{
  data?: {},
  visible?: boolean
}>(), {
  visible: true
});

const onFormatLink = (text:string) => {
  if(!text){return null}
  const urlPattern = /(https?:\/\/[^\s]+)/ig;
  return text.replace(urlPattern, '<a href="$1" target="_blank">$1</a>');
};
</script>

<style lang="scss" scoped>
.message{
  &_contents{
    min-height: 120px;
    font-size: 14px;
    line-height: 1.8;
    position: relative;
    padding-left: 20px;
    padding-right: 20px;
    padding-bottom: 32px;
    &_text{
      position: relative;
      z-index: 1;
    }
  }
  :deep() {
    video {
      width: 100%;
      height: 100%;
    }
  }
}
</style>