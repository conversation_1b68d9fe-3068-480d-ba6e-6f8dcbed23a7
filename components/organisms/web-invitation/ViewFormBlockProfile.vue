<template>
  <ViewFormBlock :isChecked="props.visible">
    <div id="profile" class="viewBlock profile" data-animation>
      <div class="viewBlock_wrap profile_wrap">
        <div class="profile_box">
          <div class="profile_symbol profile_symbol_1" data-animation></div>
          <h2 class="viewBlock_title profile_title"><span class="lang-ja">プロフィール</span><span class="lang-en">Profile</span></h2>
          <div class="profile_items">
            <div
              class="profile_item"
              v-for="(item, index) in props.data"
              :key="index"
              data-animation
            >
              <div v-if="item?.isShowVisual && item.images && item.images.length > 1" class="profile_item_image" data-animation>
                <div class="profile_item_image_wrap">
                  <swiper
                    class="swiper profile_slides"
                    effect="fade"
                    :modules="[Virtual, Autoplay, EffectFade]"
                    :slides-per-view="1"
                    :centered-slides="true"
                    :autoplay="{ delay: 1500 }"
                    :speed="1500"
                    :fade-effect="{ crossFade: true }"
                    loop
                  >
                    <SwiperSlide
                      v-for="(image, imageIndex) in item.images"
                      class="profile_slide"
                      :key="imageIndex"
                    >
                      <CropImage
                        :src="image.src"
                        :crop="image"
                      ></CropImage>
                    </SwiperSlide>
                  </swiper>
                </div>
              </div>
              <div v-else-if="item?.isShowVisual && item.images && item.images.length == 1" class="profile_item_image" data-animation>
                <div class="profile_item_image_wrap">
                  <CropImage
                    :src="item.images[0].src"
                    :crop="item.images[0]"
                  ></CropImage>
                </div>
              </div>
              <div class="profile_item_name" data-animation>
                <div v-if="item.isShowRole" class="profile_item_name_position">{{ item.role }}</div>
                <div class="profile_item_name_main">{{ item.name }}</div>
              </div>
              <div class="profile_item_text" :style="{'textAlign': item.textAlign}" data-animation v-html="onFormatLink(sanitizeContent(item.message))">
              </div>
            </div>
          </div>
          <div class="profile_symbol profile_symbol_5" data-animation></div>
        </div>
        <div class="profile_symbol profile_symbol_6" data-animation></div>
      </div>
    </div>
  </ViewFormBlock>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Virtual, Autoplay, EffectFade } from 'swiper';
import 'swiper/css';
import 'swiper/css/autoplay';
import 'swiper/css/effect-fade';

const props = withDefaults(defineProps<{
  data?: {},
  visible?: boolean
}>(), {
  visible: true
});

const onFormatLink = (text:string) => {
  if(!text){return null}
  const urlPattern = /(https?:\/\/[^\s]+)/ig;
  return text.replace(urlPattern, '<a href="$1" target="_blank">$1</a>');
};
</script>

<style lang="scss" scoped>

.profile{
  &_item{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 27px;
    margin-bottom: 62px;
    &:last-child{
      margin-bottom: 0;
    }
    &_image{
      margin-bottom: 20px;
      position: relative;
      z-index: 1;
      &_wrap{
        max-width: 220px;
        max-height: 220px;
        overflow: hidden;
        border-radius: 50%;
      }
    }
    &_name{
      margin-bottom: 12px;
      position: relative;
      z-index: 2;
      &_position{
        font-size: 14px;
        text-align: center;
        margin-bottom: 4px;
      }
      &_main{
        font-size: 20px;
        font-weight: bold;
        text-align: center;
      }
    }
    &_text{
      color: #222;
      font-size: 14px;
      line-height: 1.8;
      position: relative;
      z-index: 2;
    }
  }
}
</style>