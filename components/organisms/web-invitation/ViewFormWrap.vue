<template>
  <div class="webInvitationView" :class="{'is-sp': props.isSp}">
    <div class="webInvitationView_flex">
      <div class="webInvitationView_flex_wrap">
        <div class="webInvitationView_pc_wrap">
          <div class="webInvitationView_pc_box">
            <div class="webInvitationView_pc_box_name">{{mainVisualData?.contents?.groomName}} <span v-if="mainVisualData?.contents?.brideName && mainVisualData?.contents?.groomName">&</span> {{mainVisualData?.contents?.brideName}}</div>
            <div class="webInvitationView_pc_box_date" v-if="informationData?.contents?.date && informationData?.contents?.date != '--'">{{ $dayjs(informationData.contents.date).format('YYYY.M.D') }}</div>
            <div class="webInvitationView_pc_box_date_detail" v-if="informationData?.contents?.date && informationData?.contents?.date != '--'">
              {{ $dayjs(informationData.contents.date).locale('en').format('dddd') }}
              <span v-if="informationData.contents.events[0]?.plans?.[1]?.hour && informationData.contents.events[0]?.plans?.[1]?.minute">
                {{ $dayjs(`${informationData.contents.date} ${informationData.contents.events[0]?.plans?.[1]?.hour}:${informationData.contents.events[0]?.plans?.[1]?.minute}`).locale('en').format(' hh:mmA') }}
              </span>
            </div>
            <div class="webInvitationView_pc_box_countDown" v-if="informationData?.contents?.date && countDownData?.visible">
              <div class="webInvitationView_pc_box_countDown_blocks_details">
                <div class="webInvitationView_pc_box_countDown_block webInvitationView_pc_box_countDown_block_days">
                  <span class="webInvitationView_pc_box_countDown_block_value">{{ date.differentDays }}</span>
                  <span class="webInvitationView_pc_box_countDown_block_label">days</span>
                </div>
                <div class="webInvitationView_pc_box_countDown_block webInvitationView_pc_box_countDown_block_hours">
                  <span class="webInvitationView_pc_box_countDown_block_value">{{ date.differentHours }}</span>
                  <span class="webInvitationView_pc_box_countDown_block_label">hours</span>
                </div>
                <div class="webInvitationView_pc_box_countDown_block webInvitationView_pc_box_countDown_block_minutes">
                  <span class="webInvitationView_pc_box_countDown_block_value">{{ date.differentMinutes }}</span>
                  <span class="webInvitationView_pc_box_countDown_block_label">minutes</span>
                </div>
                <div class="webInvitationView_pc_box_countDown_block webInvitationView_pc_box_countDown_block_seconds">
                  <span class="webInvitationView_pc_box_countDown_block_value">{{ date.differentSeconds }}</span>
                  <span class="webInvitationView_pc_box_countDown_block_label">seconds</span>
                </div>
              </div>
            </div>
          </div>
          <div class="webInvitationView_pc_box" v-if="guestAnswerData && guestAnswerData.visible">
            <div class="webInvitationView_pc_box_text" v-if="answerLimitDate">お手数ではございますが <br><span class="webInvitationView_pc_box_text_limit">{{ answerLimitDate }}</span>までに<br>ご一報くださいますようお願いします</div>
            <a href="#guestAnswer" class="webInvitationView_pc_box_button">招待状に回答</a>
          </div>
        </div>
        <div class="webInvitationView_sp_wrap" ref="webInvitationView">
          <div class="webInvitationView_navigation" :class="{'is-show': isShowNavigation}">
            <a href="#" class="webInvitationView_navigation_button" @click.prevent="isShowNavigation = !isShowNavigation">
              <div class="line line1"></div>
              <div class="line line2"></div>
              <div class="line line3"></div>
            </a>
            <nav @click="isShowNavigation = false">
              <ul>
                <template v-for="(item, index) in props.data" :key="index">
                  <template v-if="item.visible">
                    <li v-if="item.id == 'message'" :class="`webInvitationView_navigation_item_${item.id}`">
                      <a href="#message">ご挨拶</a>
                    </li>
                    <li v-else-if="item.id == 'profile'" :class="`webInvitationView_navigation_item_${item.id}`">
                      <a href="#profile">新郎新婦</a>
                    </li>
                    <li v-else-if="item.id == 'gallery'" :class="`webInvitationView_navigation_item_${item.id}`">
                      <a href="#gallery">アルバム</a>
                    </li>
                    <li v-else-if="item.id == 'information'" :class="`webInvitationView_navigation_item_${item.id}`">
                      <a href="#information">パーティーのご案内</a>
                    </li>
                    <li v-else-if="item.id == 'freeField'" :class="`webInvitationView_navigation_item_${item.id}`">
                      <a href="#freeField">その他のご案内</a>
                    </li>
                    <li v-else-if="item.id == 'guestAnswer'" :class="`webInvitationView_navigation_item_${item.id}`">
                      <a href="#guestAnswer">御出欠について</a>
                    </li>
                    <li v-else-if="item.id != 'mainVisual' && item.id != 'gift'" :class="`webInvitationView_navigation_item_${item.id}`">
                      <a :href="`#${item.id}`">{{item.name}}</a>
                    </li>
                  </template>
                </template>
              </ul>
            </nav>
          </div>
          <transition>
            <div class="webInvitationView_slideModal" v-if="showSlide.isShowModal">
              <div class="webInvitationView_slideModal_bg" @click="showSlide.close()"></div>
              <div class="webInvitationView_slideModal_wrap">
                <div class="webInvitationView_slideModal_close" @click="showSlide.close()"></div>
                <div class="webInvitationView_slideModal_swiper" v-if="showSlide && showSlide.slideData">
                  <swiper
                    ref="zoomSwiper"
                    class="swiper zoom_slides"
                    :modules="[Navigation]"
                    :loop="true"
                    :slides-per-view="1"
                    :centered-slides="true"
                    :preloadImages="true"
                    :lazy="false"
                    :initialSlide="showSlide.slideIndex"
                    navigation
                  >
                    <SwiperSlide
                      class="zoom_slide"
                      v-for="(slide, index) in showSlide.slideData"
                      :virtualIndex="index"
                      :key="`zoom_slide-${index}`"
                    >
                      <CropImage
                        :src="slide.src"
                        :crop="slide"
                      ></CropImage>
                    </SwiperSlide>
                  </swiper>
                </div>
                <div class="webInvitationView_slideModal_swiper" v-else>
                  <CropImage
                    v-if="showSlide?.cropData"
                    :src="showSlide?.cropData?.src"
                    :crop="showSlide?.cropData"
                  ></CropImage>
                  <img
                    v-else-if="showSlide?.srcData"
                    :src="showSlide?.srcData"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </transition>

          <slot />

        </div>
        <div class="webInvitationView_flex_wrap_scroll"><span>scroll</span></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
const { $dayjs } : any = useNuxtApp();
import { useWebInvitationSlideModal } from '@/composables/useWebInvitationSlideModal'
const props = withDefaults(defineProps<{
  isSp?: boolean,
  data: {}
}>(), {
  isSp: false,
  data: null
});

// スライドモーダルの表示
const showSlide = useWebInvitationSlideModal();

const onFilterBlock = (id: String, newData:any) => {
  if(!newData){return null}
  let item = newData.filter((element) => {
    return element.id == id;
  });
  if (!item) {
    return null
  }
  return item[0];
}
const mainVisualData = ref(onFilterBlock('mainVisual', props.data));
const countDownData = ref(onFilterBlock('countDown', props.data));
const informationData = ref(onFilterBlock('information', props.data));
const guestAnswerData = ref(onFilterBlock('guestAnswer', props.data));
watch(() => props.data, async (newData) => {
  if (newData) {
    mainVisualData.value = onFilterBlock('mainVisual', newData);
    countDownData.value = onFilterBlock('countDown', newData);
    informationData.value = onFilterBlock('information', newData);
    guestAnswerData.value = onFilterBlock('guestAnswer', newData);
  }
}, {
  deep: true,
  immediate: true
});

// 親からも実行可能に
const webInvitationView = ref();
defineExpose({
  webInvitationView
});

const isShowNavigation = ref(false);
onMounted(() => {
  document.getElementsByTagName('html')[0].scrollTop = 0;

  //アニメーション
  let animationTarget = document.querySelectorAll('[data-animation]:not(#mainVisual [data-animation])');

  //要素が交差したときの指示
  const delay = (time:number) => {
    return new Promise(resolve => setTimeout(resolve, time * 1000));
  };
  let callback = (entries: any) => {
    entries.forEach( async(entry: any) => {
      const target = entry.target;
      const targetDelay = target.getAttribute('data-animation-delay');
      if(targetDelay){
        await delay(targetDelay);
      }
      if (entry.isIntersecting) {
        target.classList.add('is-animated');
      }
    });
  }
  const animationOptions = {
    root: null,
    rootMargin: '-10% 0px',
    threshold: 0,
  };
  const observer = new IntersectionObserver(callback, animationOptions);
  animationTarget.forEach((target) => {
    observer.observe(target);
  });

  setTimeout(() => {
    let mainVisualAnimationTarget = document.querySelectorAll('#mainVisual [data-animation]');
    let mainVisual = document.querySelectorAll('#mainVisual');

    mainVisualAnimationTarget.forEach((target) => {
      target.classList.add('is-animated');
    });
    mainVisual.forEach((target) => {
      target.classList.add('is-animated');
    });

  },3000);
});


const nowDate = ref(new Date(new Date().toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" })));

const updateDateToJST = (date: Date) => {
  // 日付オブジェクトを日本時間に変換
  return new Date(date.toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' }));
};

const date = computed<string | number>(() => {
  let target = informationData.value?.contents?.date;
  if(!target){ return false }
  if(informationData.value?.contents?.events?.[0]?.plans?.[1]?.hour && informationData.value?.contents?.events?.[0]?.plans?.[1]?.minute){
    target += ` ${informationData.value?.contents?.events?.[0]?.plans?.[1]?.hour}:${informationData.value?.contents?.events?.[0]?.plans?.[1]?.minute}`;
  }
  const targetDate = updateDateToJST(new Date(target));

  // 年 月 日を取得
  const year = targetDate.getFullYear(); // 年
  const month = targetDate.getMonth() + 1; // 月（0から11までの値を返すため 1を加える）
  const day = targetDate.getDate(); // 日

  // 曜日を取得（0（日曜）から6（土曜）までの値）
  const weekDay = targetDate.getDay();
  const weekDays = ["日", "月", "火", "水", "木", "金", "土"];
  const weekDayString = weekDays[weekDay] + '曜日'; // 曜日を文字列で

  // 現在の日付を日本時間に更新
  const currentTimeInJST = updateDateToJST(nowDate.value);

  const delta = Math.max(0, targetDate - currentTimeInJST);
  return {
    year: year,
    month: month,
    day: day,
    weekDayString: weekDayString,
    differentDays: Math.floor(delta / (1000 * 60 * 60 * 24)),
    differentHours: Math.floor((delta / (1000 * 60 * 60)) % 24),
    differentMinutes: Math.floor((delta / 1000 / 60) % 60),
    differentSeconds: Math.floor((delta / 1000) % 60)
  };
})
setInterval(() => {
  nowDate.value = updateDateToJST(new Date());
}, 1000);

const answerLimitDate = computed(() => {
  const date = informationData.value?.contents?.date;
  // 1: 開催一ヶ月前に期限を設定
  // 2: 開催二ヶ月前に期限を設定
  // 3: 回答期日を入力して設定
  if(!date || date == '--'){return false}
  if (guestAnswerData.value?.contents?.limit?.setting == 1) {
    return $dayjs(date).add(-1, "month").format('YYYY年M月D日 (ddd)');
  } else if (guestAnswerData.value?.contents?.limit.setting == 2) {
    return $dayjs(date).add(-2, "month").format('YYYY年M月D日 (ddd)');
  } else if (guestAnswerData.value?.contents?.limit.setting == 3) {
    return $dayjs(guestAnswerData.value?.contents?.limit.date).format('YYYY年M月D日 (ddd)');
  }
  return $dayjs(date).format('YYYY年M月D日 (ddd)');
});
</script>

<style lang="scss">
@import 'assets/css/webinvitation/common/style.scss';

.webInvitationView {
  overflow-wrap: anywhere;
  a:not([class]) {
    word-break: break-all;
    overflow-wrap: break-word;
  }
  .crop_image {
    min-width: 100%;
    min-height: 100%;
  }
}

.webInvitationView_slideModal{
  display: flex;
  justify-content: center;
  align-items: center;
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  min-height: 680px;
  max-height: 680px;
  height: calc(100vh - 88px);
  height: calc(100dvh - 88px);
  z-index: 100000;
  @include sp {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 100vh;
    height: 100dvh;
    min-height: 480px;
    max-height: 100%;
  }
  &_bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: -1;
  }
  &_wrap {
    max-width: 100%;
    width: 100%;
    position: relative;
    max-height: 580px;
    @include sp {
      max-height: calc(100vh - 100px);
      max-height: calc(100dvh - 100px);
    }
  }
  &_close{
    position: absolute;
    top: -40px;
    right: 5px;
    padding: 10px;
    z-index: 100;
    cursor: pointer;
    &::before{
      content: '';
      display: block;
      width: 14px;
      height: 14px;
      background: #FFF;
      mask-image: url('@/assets/images/icon-close-b.svg');
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: contain;
    }
  }
  &_swiper{
    width: 100%;
    height: 100%;
    .crop_image{
      max-width: 100%;
      max-height: 580px;
      @include sp {
        max-height: calc(100vh - 100px);
        max-height: calc(100dvh - 100px);
      }
    }
  }
}
</style>
