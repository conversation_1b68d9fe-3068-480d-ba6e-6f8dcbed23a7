<template>
  <nav aria-label="Page navigation">
    <ul class="pagination">
      <li class="page-item is-prev" :class="{ disabled: currentPage <= 1 }">
        <a class="page-link" @click="changePage(currentPage - 1)"></a>
      </li>

      <li v-if="showFirstEllipsis" class="page-item">
        <span class="page-link">...</span>
      </li>

      <li class="page-item" v-for="page in pagesToShow" :key="page" :class="{ active: page == currentPage }">
        <a class="page-link" @click="changePage(page)">{{ page }}</a>
      </li>

      <li v-if="showLastEllipsis" class="page-item">
        <span class="page-link">...</span>
      </li>

      <li class="page-item is-next" :class="{ disabled: currentPage >= totalPages }">
        <a class="page-link" @click="changePage(currentPage + 1)"></a>
      </li>
    </ul>
  </nav>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';

const props = withDefaults(defineProps<{
  current: number,
  total: number
}>(), {
  current: 1,
  total: 1
});

const currentPage = ref(props.current);
const totalPages = ref(props.total);

watch(() => props, (newVal) => {
  if(newVal){
    currentPage.value = newVal.current;
    totalPages.value = newVal.total;
  }
}, {
  deep: true,
  immediate: true
});

// 表示するページ番号を計算
const pagesToShow = computed(() => {
  let pages = [];
  let startPage = Math.max(1, currentPage.value - 2);
  let endPage = Math.min(totalPages.value, currentPage.value + 3);

  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }

  return pages;
});

// 最初と最後の省略記号を表示するかどうかを計算
const showFirstEllipsis = computed(() => {
  return currentPage.value > 3;
});

const showLastEllipsis = computed(() => {
  return currentPage.value < totalPages.value - 2;
});

// ページ変更処理
const emit = defineEmits(['change']);
const changePage = (page) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) {
    return;
  }
  currentPage.value = page;
  emit('change', currentPage.value);

};
</script>

<style lang="scss">
.pagination {
  display: flex;
  justify-content: center;
  list-style: none;
  padding: 0;
  margin: 20px auto 30px;
  @include sp {
    margin-bottom: 100px;
  }
}
.page-item {
  margin: 0 3px;
  font-size: 14px;
  &.is-prev .page-link{
    color: inherit;
    cursor: pointer;
    position: relative;
    &::before {
      @include BA;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      width: 14px;
      height: 12px;
      background: currentColor;
      mask-image: url(@/assets/images/icon-chevron_right.svg);
      mask-size: contain;
      mask-position: center;
      mask-repeat: no-repeat;
      transform: rotate(180deg);
    }
  }
  &.is-next .page-link{
    color: inherit;
    cursor: pointer;
    position: relative;
    &::before {
      @include BA;
      right: 0;
      width: 14px;
      height: 12px;
      background: currentColor;
      mask-image: url(@/assets/images/icon-chevron_right.svg);
      mask-size: contain;
      mask-position: center;
      mask-repeat: no-repeat;
    }
  }
}
.page-item.disabled .page-link {
  color: #ccc;
  cursor: not-allowed;
}
.page-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #000;
  text-decoration: none;
  cursor: pointer;
  padding: 4px 8px;
  min-width: 34px;
  min-height: 34px;
  text-align: center;
  border: 1px solid transparent;
}
.page-item.active .page-link {
  font-weight: bold;
  border: 1px solid #333;
  border-radius: 4px;
}
</style>