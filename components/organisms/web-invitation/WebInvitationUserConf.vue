<template>
<div class="pageWebInvitationConf">
<Main
  :partsHidden="paramPartsHidden" 
  >
  <template #main>
    <Loading v-if="isLoading" :fullscreen="true"></Loading>
    <div class="contents-title">
      <Titleh1>
        <a @click="emit('back')" class="backward"><img src="@/assets/images/icon-arrow_backward-b.svg" alt="戻る"></a>
        内容の確認
      </Titleh1>
    </div>
    <div class="contents-body">
      <p class="mb-30 cmn-aligncenter size--lg">ご入力ありがとうございます <br>
      内容をご確認いただき 次へお進みください </p>
      <h2 class="cmn-title">基本情報</h2>
      <ul class="list-info mb-30">
        <template v-for="(item, n) in block.contents.selectList.filter(item => item.visible === true)" :key="n">
          <li v-if="item.title === '新郎新婦ゲスト選択'"><dl>
            <dt>いずれかをお選びください</dt>
            <dd>{{ GUEST_TYPE_MASTER?.[input.input?.guest_type] }}</dd>
          </dl></li>
          <li v-else-if="item.title === 'お名前'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>{{ input.input.last_name }} {{ input.input.first_name }}</dd>
          </dl></li>
          <li v-else-if="item.title === 'お名前（ふりがな）'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>{{ input.input.last_name_kana }} {{ input.input.first_name_kana }}</dd>
          </dl></li>
          <li v-else-if="item.title === 'お名前（ローマ字）'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>{{ input.input.last_name_romaji }} {{ input.input.first_name_romaji }}</dd>
          </dl></li>
          <li v-else-if="item.title === '性別'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>{{ GENDER_MASTER?.[input.input.gender] }}</dd>
          </dl></li>
          <!-- <li v-else-if="item.title === '関係性'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>{{ input.input.relationship_name }}</dd>
          </dl></li> -->
          <li v-else-if="item.title === '間柄'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>{{ input.input.relationship }}</dd>
          </dl></li>
          <li v-else-if="item.title === 'アレルギー項目の入力'"><dl>
            <dt>アレルギー等</dt>
            <dd>
              {{ input.input.allergies.join('、') }}
              <div class="preWrap">{{ input.input.allergy }}</div>
            </dd>
          </dl></li>
          <li v-else-if="item.title === '誕生日' || item.title === 'お誕生日'"><dl>
            <dt>お誕生日</dt>
            <dd>
              <span v-if="input.input.birthdate">{{ $dayjs(input.input.birthdate).format('YYYY/MM/DD') }}</span>
            </dd>
          </dl></li>
          <li v-else-if="item.title === '住所'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>
              <div v-if="input.input.postal_code">〒{{ input.input.postal_code }}</div>
              {{ input.input.prefecture }}
              {{ input.input.city }}
              {{ input.input.address }}
              {{ input.input.building }}
            </dd>
          </dl></li>
          <li v-else-if="item.title === '電話番号'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>{{ input.input.phone }}</dd>
          </dl></li>
          <li v-else-if="item.title === 'メールアドレス'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>{{ input.input.email }}</dd>
          </dl></li>
          <li v-else-if="item.title === 'プロフィール写真'"><dl>
            <dt>{{ item.title }}</dt>
            <dd>
              <div class="profileImage" v-if="input.input.image?.presigned_url_m" style="width: 100px;">
                <div class="img">
                  <img :src="input.input.image?.presigned_url_m" />
                </div>
              </div>
            </dd>
          </dl></li>
          <li v-else-if="isMessageBlock(item)"><dl>
            <dt>
              <div>お祝いメッセージ</div>
            </dt>
            <dd>
              <div class="guestAnswer_box_row">
                <div class="guestAnswerPreview" v-if="input.input.message || (input?.input?.media_image || input?.input?.media?.presigned_url_l)">
                  <div class="guestAnswerPreview_image" v-if="input?.input?.media_image || input?.input?.media?.presigned_url_l">
                    <template v-if="input?.input?.is_media_upload == 1 && input?.input?.media?.presigned_url_main">
                      <VideoUUID
                        :src="input?.input?.media?.presigned_url_l"
                        :srcVideo="input?.input?.media?.presigned_url_main"
                        :poster="input?.input?.media?.presigned_url_l"
                      />
                    </template>
                    <template v-else-if="input?.input?.is_media_upload != 1 && input?.input?.media_image">
                      <ImageUUID :uuid="input?.input?.media_image" />
                    </template>
                    <template v-else-if="input?.input?.media?.presigned_url_l">
                      <img :src="input?.input?.media?.presigned_url_l" alt="">
                    </template>
                  </div>
                  <div class="guestAnswerPreview_text" v-if="input?.input?.message || input?.input?.last_name || input?.input?.last_name">
                    <span v-if="input?.input?.message" class="preWrap">{{ input?.input?.message }}</span>
                    <div class="guestAnswerPreview_name" v-if="input?.input?.last_name || input?.input?.last_name">{{ input?.input?.last_name }}{{ input?.input?.last_name ? ' ' + input?.input?.first_name : input?.input?.first_name }}</div>
                  </div>
                </div>
              </div>
            </dd>
          </dl></li>
        </template>
      </ul>
      <div class="blockGuests" v-for="(guest, index) in input.guests" :key="index">
        <h2 class="cmn-title">お連れ様</h2>
        <ul class="list-info mb-30">
          <template v-for="(item, n) in block.contents.selectList.filter(item => item.visible === true)" :key="n">
            <li v-if="item.title === 'お名前'"><dl>
              <dt>お連れ様の{{ item.title }}</dt>
              <dd>{{ guest.last_name }} {{ guest.first_name }}</dd>
            </dl></li>
            <li v-else-if="item.title === 'お名前（ふりがな）'"><dl>
              <dt>お連れ様の{{ item.title }}</dt>
              <dd>{{ guest.last_name_kana }} {{ guest.first_name_kana }}</dd>
            </dl></li>
            <li v-else-if="item.title === 'お名前（ローマ字）'"><dl>
              <dt>お連れ様の{{ item.title }}</dt>
              <dd>{{ guest.last_name_romaji }} {{ guest.first_name_romaji }}</dd>
            </dl></li>
            <li v-else-if="item.title === '性別'"><dl>
              <dt>{{ item.title }}</dt>
              <dd>{{ GENDER_MASTER?.[guest.gender] }}</dd>
            </dl></li>
            <!-- <li v-else-if="item.title === '関係性'"><dl>
              <dt>{{ item.title }}</dt>
              <dd>{{ guest.relationship_name }}</dd>
            </dl></li> -->
            <li v-else-if="item.title === '間柄'"><dl>
              <dt>{{ item.title }}</dt>
              <dd>{{ guest.relationship }}</dd>
            </dl></li>
            <li v-else-if="item.title === 'アレルギー項目の入力'"><dl>
              <dt>アレルギー等</dt>
              <dd>
                {{ guest.allergies.join('、') }}
                <div class="preWrap">{{ guest.allergy }}</div>
              </dd>
            </dl></li>
            <li v-else-if="item.title === '誕生日' || item.title === 'お誕生日'"><dl>
              <dt>お誕生日</dt>
              <dd>
                <span v-if="guest.birthdate">{{ $dayjs(guest.birthdate).format('YYYY/MM/DD') }}</span>
              </dd>
            </dl></li>
            <li v-else-if="item.title === 'プロフィール写真'"><dl>
              <dt>{{ item.title }}</dt>
              <dd>
                <div class="profileImage" v-if="guest.image?.presigned_url_m" style="width: 100px;">
                  <div class="img">
                    <img :src="guest.image?.presigned_url_m" />
                  </div>
                </div>
              </dd>
            </dl></li>
            <li v-else-if="item.title === 'お祝いメッセージ'"><dl>
              <dt>お連れ様のお祝いメッセージ</dt>
              <dd class="preWrap">{{ guest.message }}</dd>
            </dl></li>
          </template>
        </ul>
      </div>
      <div v-if="input.guest_survey_answers.length">
        <h2 class="cmn-title">自由質問欄</h2>
        <ul class="list-info mb-30">
          <li class="inputItem" v-for="(guest_survey_answer, n) in input.guest_survey_answers" :key="n">
            <dl>
              <dt>{{ guest_survey_answer.question }}</dt>
              <dd>{{ guest_survey_answer.answer_content }}</dd>
            </dl>
          </li>
        </ul>
      </div>
      <h2 class="cmn-title">ご出席・ご欠席・保留について</h2>
      <ul class="list-info mb-30">
        <li class="inputItem" v-for="(guest_event_answer, n) in input.guest_event_answers" :key="n">
          <dl>
            <dd>
              <span style="display:inline-block; min-width:4em;">{{ guest_event_answer.name }}</span>：<span class="ml-10 label-attendance" :data-attendance="guest_event_answer.attendance"><span class="txt">{{ guest_event_answer.attendance }}</span></span>
            </dd>
          </dl>
        </li>
      </ul>

      <button v-if="isFinish" class="btn btn-secondary btn-block mb-10" @click="onClickSubmit" :class="{'is-loading': isLoading}">確定して送信する</button>
      <button v-else class="btn btn-secondary btn-block mb-10" @click="onClickNext">次へ</button>
      <button class="btn btn-default-outline btn-block" @click="onClickBack">戻る</button>
    </div>
  </template>
</Main>
</div>
</template>

<style lang="scss">
.pageWebInvitationConf {
  .l-column1 {
    padding-top: 0;
    .contents-body {
      padding-left: 16px;
      padding-right: 16px;
      padding-top: 32px;
    }
  }
  .is-loading {
    pointer-events: none;
  }
}
.profileImage {
  border-radius: 50px;
  img {
    height: 100px;
    width: 100px;
    object-fit: cover;
  }
}

.guestAnswerPreview{
  max-width: 343px;
  width: 100%;
  margin: 10px 0 20px;
  background: #FFF;
  box-shadow: 0 2px 20px rgba(0,0,0,0.14);
  border-radius: 14px;
  overflow: hidden;
  &_image{
    img{
      aspect-ratio: 1 / 1;
      width: 100%;
      object-fit: cover;
    }
  }
  &_text{
    font-size: 16px;
    line-height: 1.5;
    color: #333333;
    padding: 10px 14px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-all;
  }
  &_name{
    font-size: 16px;
    line-height: 1.5;
    color: #333333;
    text-align: right;
    padding: 6px 0 0;
  }
}
</style>

<script lang="ts" setup>
const { $dayjs } : any = useNuxtApp();

const paramPartsHidden = {
  FixedHeader: 'hidden',
  Menu: 'hidden',
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
}

interface Props {
  input: any;
  webInvitationData: any;
}
const props = withDefaults(defineProps<Props>(), {
  input: () => {},
  webInvitationData: () => {}
});

const emit = defineEmits<{
  change: []
  back: []
  next: []
}>()

const block = computed(() => {
  return props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'guestAnswer');
});

// 事前支払いを利用しない場合 or 事前支払い締め日を過ぎていたら 一気に最終ページへ
const isFinish = computed(() => {
  // 事前支払いを利用しない場合
  const giftBlock = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'gift');
  // console.log(giftBlock.contents.isUseGift);
  if (! giftBlock.contents.isUseGift) return true;

  const informationBlock = props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'information');
  const date = informationBlock.contents.date;

  // ご祝儀・会費の送金予定日
  // 事前支払い締め日（会費ご祝儀の送金予定日の10日前）を過ぎているか
  if (giftBlock.contents.scheduleDate == 2) {
    // 開催日2週間前
    if (new Date(new Date().toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" })) > new Date($dayjs(date).add(-2, "week").add(-7, "day").format('YYYY/M/D 00:00:00'))) return true;
  } else {
    // 開催日1週間前
    if (new Date(new Date().toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" })) > new Date($dayjs(date).add(-1, "week").add(-7, "day").format('YYYY/M/D 00:00:00'))) return true;
  }

  return false;
});

const onClickNext = () => {
  window.scrollTo({top: 0, behavior: "smooth"});
  emit('next');
}
const onClickBack = () => {
  window.scrollTo({top: 0, behavior: "smooth"});
  emit('back');
}

// 更新中のLoading
const isLoading = ref(false);

// 全体エラー
const error = ref('')

// 更新API
const { create, errors } = useCreateWebInvitationGuest();

// 保存ボタンクリック
const router = useRouter();
const onClickSubmit = async() => {
  if(isLoading.value) { return false }
  isLoading.value = true;

  // 各種項目を調整
  const data = getCreateWebInvitationGuestData(props.input, props.webInvitationData);

  const isSuccess = await create(true, data.input, data.guests, data.free_item_values, data.guest_event_answers, data.guest_survey_answers);
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    isLoading.value = false;
    // scrollPageTop();
    return false;
  }

  // 一覧を再描画
  isLoading.value = false;
  router.push({ path: '/wi/'+props.webInvitationData.public_url+'/thankyou'});
};

const isMessageBlock = (item) => {
  const selectList = block.value.contents.selectList.filter(item => item.visible === true);
  const messageBlocks = selectList.filter(item => item.title === 'お祝いメッセージ' || (item.title === 'お祝い画像' || item.title === 'お祝い画像・動画'));
  if (item.title === 'お祝いメッセージ' || (item.title === 'お祝い画像' || item.title === 'お祝い画像・動画')) {
    if (messageBlocks?.[0].title == item.title) return true;
  }
  return false;
}
</script>