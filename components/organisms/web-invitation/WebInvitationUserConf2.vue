<template>
<div class="WebInvitationPayment">
<Main
  :partsHidden="paramPartsHidden" 
  >
  <template #main>
    <div class="contents-title">
      <Titleh1>
        <a @click="emit('back')" class="backward"><img src="@/assets/images/icon-arrow_backward-b.svg" alt="戻る"></a>
        内容の確認2
      </Titleh1>
    </div>
    <div class="contents-body">
      <div class="cmn-aligncenter mb-30">
        <p class="text-danger"><span class="icn material-symbols-outlined">info</span> まだ回答は送信されていません</p>
      </div>
      <div class="box-gradient mb-40">
        <div class="inner" v-if="hasPresent">
          <div class="title color-accent">会費・ご祝儀について</div>
          <div 
            v-if="giftBlock?.contents?.message" 
            class="text preWrap"
            :data-textalign="giftBlock.contents.textAlign" 
          >
            {{ giftBlock.contents.message }}
          </div>
        </div>
        <div class="inner" v-else>
          <div class="title">キャッシュレスご祝儀のご案内</div>
          <div 
            class="text cmn-aligncenter">ご出欠が未定 もしくは欠席の場合でも<br>
            ご祝儀をキャッシュレスでお送りいただけます <br>
            よろしければご利用ください </div>
        </div>
      </div>

      <div class="box-hr"><div class="box-inner">
        <p class="size--sm">WEB決済をご利用いただきますと<br>
        新郎新婦様へ事前にご祝儀がお振込みされます<br>
        パーティー当日 受付の混雑緩和にもなります</p>
      </div></div>
      <InputRadio
        v-if="hasPresent"
        name="inputPaymentWay"
        :items="[
          {value: '1', label: '当日会場へ持参する', checked: (inputPayment.paymentWay == 1)},
          {value: '2', label: 'すでに支払い済み', checked: (inputPayment.paymentWay == 2)},
          {value: '0', label: 'WEBご祝儀・会費を利用する', checked: (inputPayment.paymentWay == 0)}
        ]"
        :block="true"
        @change="inputPayment.paymentWay = Number($event); error = ''"
      />
      <InputRadio
        v-else
        name="inputPaymentWay"
        :items="[{value: '3', label: '保留/欠席の回答と一緒にご祝儀も送る', checked: (inputPayment.paymentWay == 3)}]"
        :block="true"
        @change="inputPayment.paymentWay = Number($event); error = ''"
      />
      <div class="boxPartyFee" v-if="(inputPayment.paymentWay == 0 && hasPresent) || inputPayment.paymentWay == 3">
        <template v-for="(guest_event_answer, n) in input.guest_event_answers" :key="n">
          <div v-if="isUsePaymentForm(guest_event_answer) && getEventName(guest_event_answer)" class="boxPartyFeeItemWrap">
            <h2 class="cmn-title" v-if="hasPresent">{{ getEventName(guest_event_answer) }}</h2>
            <InputCheck
              v-if="getEventByAnswer(guest_event_answer)?.feeOption == 'gift_system' && input.guests.length > 0"
              :value="(input?.guest_event_answers?.[n]?.isNotGuestsPayment) ? ['1'] : []"
              :items="[{value: '1', label: 'お連れ様とまとめてご祝儀を送る'}]"
              @change="($event[0] == '1') ? input.guest_event_answers[n].isNotGuestsPayment = true : input.guest_event_answers[n].isNotGuestsPayment = false"
            />
            <div class="boxPartyFeeItem">
              <div class="itemBody">
                <div class="itemTitle">
                  {{ input.input.last_name }} {{ input.input.first_name }} 様<br>
                </div>
                <div class="itemPrice" v-if="getEventByAnswer(guest_event_answer)?.feeOption === 'membership_fee_common'">
                  <div v-if="getEventByAnswer(guest_event_answer)?.feeAmount?.[0]">会費 : {{ setNumberFormat(Number(getEventByAnswer(guest_event_answer)?.feeAmount?.[0])) }}円</div>
                </div>
                <div class="itemPrice" v-else-if="getEventByAnswer(guest_event_answer)?.feeOption === 'membership_fee_separate'">
                  <div v-if="getEventByAnswer(guest_event_answer)?.feeAmount?.[0]">男性 : {{ setNumberFormat(Number(getEventByAnswer(guest_event_answer)?.feeAmount?.[0])) }}円</div>
                  <div v-if="getEventByAnswer(guest_event_answer)?.feeAmount?.[1]">女性 : {{ setNumberFormat(Number(getEventByAnswer(guest_event_answer)?.feeAmount?.[1])) }}円</div>
                </div>
              </div>
              <div class="itemInput">
                <InputPrice
                  :required="true"
                  :value="String(input?.guest_event_answers?.[n]?.payment_amount)"
                  :unit="'円'"
                  :error="errors?.input?.guest_event_answers?.[n]?.payment_amount"
                  @update="input.guest_event_answers[n].payment_amount = $event"
                />
              </div>
            </div>
            <template v-if="! input?.guest_event_answers?.[n]?.isNotGuestsPayment">
              <div class="boxPartyFeeItem" v-for="(guest, i) in input.guests" :key="i">
                <div class="itemBody">
                  <div class="itemTitle">
                    {{ guest.last_name }} {{ guest.first_name }} 様<br>
                  </div>
                  <div class="itemPrice" v-if="getEventByAnswer(guest_event_answer)?.feeOption === 'membership_fee_common'">
                    <div v-if="getEventByAnswer(guest_event_answer)?.feeAmount?.[0]">会費 : {{ setNumberFormat(Number(getEventByAnswer(guest_event_answer)?.feeAmount?.[0])) }}円</div>
                  </div>
                  <div class="itemPrice" v-else-if="getEventByAnswer(guest_event_answer)?.feeOption === 'membership_fee_separate'">
                    <div v-if="getEventByAnswer(guest_event_answer)?.feeAmount?.[0]">男性 : {{ setNumberFormat(Number(getEventByAnswer(guest_event_answer)?.feeAmount?.[0])) }}円</div>
                    <div v-if="getEventByAnswer(guest_event_answer)?.feeAmount?.[1]">女性 : {{ setNumberFormat(Number(getEventByAnswer(guest_event_answer)?.feeAmount?.[1])) }}円</div>
                  </div>
                </div>
                <div class="itemInput">
                  <InputPrice
                    :required="true"
                    :value="String(input?.guest_event_answers?.[n]?.payment_amounts?.[i])"
                    :unit="'円'"
                    :error="errors?.input?.guest_event_answers?.[n]?.payment_amounts?.[i]"
                    @update="input.guest_event_answers[n].payment_amounts[i] = $event"
                  />
                </div>
              </div>
            </template>
          </div>
        </template> 
        <template v-if="! hasGiftSystem">
          <div class="btnWrap" v-if="! isShowGiftInput && hasPresent">
            <button class="btn btn-primary-outline" @click="isShowGiftInput = true"><i class="material-symbols-outlined icn-left">add</i> お気持ちを追加する</button>
          </div>
          <div class="boxPartyFeeItemWrap" v-else>
            <div class="boxPartyFeeItem">
              <div class="itemBody">
                <div class="itemTitle">
                  お気持ち
                </div>
              </div>
              <div class="itemInput">
                <InputPrice
                  :required="true"
                  :value="String(input?.input?.gift_amount)"
                  :unit="'円'"
                  :error="errors?.input?.gift_amount"
                  @update="input.input.gift_amount = $event"
                />
                <div class="linkWrap" v-if="hasPresent">
                  <a class="link-text-delete" @click="isShowGiftInput = false; input.input.gift_amount = null;">この項目を削除する</a>
                </div>
              </div>
            </div>
          </div>
        </template>
        <div v-if="usageFee == 'free'">
          <p><strong>システム利用料（{{ String(systemFeeRate*100) }}%）負担</strong></p>
          <InputRadio
            :value="(input?.input.is_system_fee) ? '1' : ''"
            :items="[{value: '1', label: '負担する'}, {value: '', label: '負担しない'}]"
            @change="($event == '1') ? input.input.is_system_fee = true : input.input.is_system_fee = false"
          />
          <p class="size--sm">「負担する」を選択すると<br>開催者に代わりシステム利用料がゲストさまのご負担となります </p>
        </div>
        <ul class="list-price mb-30">
          <li v-if="input?.input.is_system_fee"><dl>
            <dt>システム利用料（{{ String(systemFeeRate*100) }}%）</dt>
            <dd><span class="num num-sm">{{ setNumberFormat(systemFee) }}</span><small>円</small></dd>
          </dl></li>
          <li><dl>
            <dt>合計金額</dt>
            <dd><span class="num">{{ setNumberFormat(amount + systemFee) }}</span><small>円</small></dd>
          </dl></li>
        </ul>
        <p v-if="error" class="input-error mb-20" style="margin-top: -20px;">{{ error }}</p>
        <ShowCartInputCard
          ref="refInputCard"
          :isShowBtn="false"
          @update="inputPayment.card = $event"
        />
      </div>
      <InputRadio
        v-if="! hasPresent"
        name="inputPaymentWay"
        :items="[
          {value: '4', label: '保留/欠席の回答のみ送信する', checked: (inputPayment.paymentWay == 4)},
        ]"
        :block="true"
        @change="inputPayment.paymentWay = Number($event); error = ''"
      />
      <p v-if="pageError" class="input-error mb-10">{{ pageError }}</p>
      <p v-else-if="error && inputPayment.paymentWay === null" class="input-error mb-10">{{ error }}</p>
      <button class="btn btn-secondary btn-block mt-40 mb-10" @click="onClickNext()">最終確認へ</button>
      <button class="btn btn-default-outline btn-block" @click="onClickBack()">戻る</button>
    </div>
  </template>
</Main>
</div>
</template>

<style lang="scss">
.WebInvitationPayment .l-column1 {
  padding-top: 0;
  .contents-body {
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 32px;

    .inputRadio label {
      display: block;
    }
    .boxPartyFee {
      .inputRadio label {
        display: inline-block;
      }
    }
  }
}

.itemTitle {
  font-weight: bold;
  font-size: 14px;
  line-height: 1.6;
}
.itemRadio {
  margin: 12px 0 0;
}
.boxPartyFee {
  background: #F4F4F4;
  padding: 16px;
  margin-left: 24px;
  margin-bottom: 20px;
  .boxPartyFeeItemWrap {
    padding-bottom: 12px;
    margin-bottom: 12px;
    border-bottom: 1px solid var(--Gray, #D9D9D9);
  }
  .boxPartyFeeItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    position: relative;
    padding-bottom: 12px;
    padding-top: 12px;
    .itemTitle {
      font-weight: bold;
      font-size: 14px;
      line-height: 1.6;
    }
    .itemPrice {
      margin-top: 14px;
      line-height: 1.6;
    }
    .linkWrap {
      margin-top: 20px;
      text-align: right;
    }
    .itemInput {
      flex-shrink: 0;
    }
  }
  .btnWrap {
    text-align: right;
    padding-bottom: 24px;
    margin-bottom: 24px;
    border-bottom: 1px solid var(--Gray, #D9D9D9);
  }
}
</style>

<script lang="ts" setup>
const paramPartsHidden = {
  FixedHeader: 'hidden',
  Menu: 'hidden',
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
}

interface Props {
  input: any;
  inputPayment: any;
  webInvitationData: any;
  pageError: string;
  isBack: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  input: () => {},
  inputPayment: () => {},
  webInvitationData: () => {},
  pageError: '',
  isBack: false
});

const emit = defineEmits<{
  change: []
  changePayment: []
  back: []
  next: []
  setPageError: []
}>()

const isShowGiftInput = ref(props.input.input.gift_amount ? true : false);

const input = ref(props.input);
const inputPayment = ref(props.inputPayment);

const refInputCard = ref();

const events = computed(() => {
  return getEventsByWebInvitation(props.webInvitationData);
});

const giftBlock = computed(() => {
  return props.webInvitationData?.editor_settings?.blocks.find(block => block.id == 'gift');
});

const systemFeeRate = computed(() => {
  return SYSTEM_FEE_RATE;
});
const systemFee = computed(() => {
  const result = getCreateWebInvitationGuestAmounts(input.value);
  return result.sum.systemFee;
});
const amount = computed(() => {
  const result = getCreateWebInvitationGuestAmounts(input.value);
  return result.sum.amount;
});


// 出席がある場合
const hasPresent = computed(() => {
  if (input.value.guest_event_answers.findIndex(item => item.attendance === GUEST_ATTENDANCE_MASTER.PRESENT) !== -1) return true;
  return false;
});

// ご祝儀制
const hasGiftSystem = computed(() => {
  // ご祝儀制のイベントだけ取得
  const giftEvents = events.value.filter((event:any) => event.feeOption === 'gift_system');
  if (! giftEvents) return false;
  // ご祝儀制のイベントが出席なら true
  for (let i = 0; i < giftEvents.length; i++) {
    const giftEvent = giftEvents[i];
    const answer = input.value.guest_event_answers.find((item:any) => item.name === giftEvent.name);
    if (! answer) continue;
    if (answer.attendance === GUEST_ATTENDANCE_MASTER.PRESENT) return true;
  }
  return false;
});

// システム利用料の負担の表示 
// guest : ゲスト
// host : あなた（開催者）
// free : ゲストに任せる
const usageFee = computed(() => {
  const giftBlock = props.webInvitationData?.editor_settings?.blocks.find((block:any) => block.id == 'gift');
  return giftBlock.contents.usageFee;
});

const isUsePaymentForm = (guest_event_answer = {} as any) => {
  // 出席以外の場合
  if (guest_event_answer.attendance != GUEST_ATTENDANCE_MASTER.PRESENT) return false;
  const giftBlock = props.webInvitationData?.editor_settings?.blocks.find((block:any) => block.id == 'gift');
  if (! giftBlock?.contents?.partySettings) return true;
  let eventName = guest_event_answer.name;
  if (eventName == '挙式・披露宴') eventName = '挙式';
  if (! giftBlock.contents?.isUseInVenue) return true;
  const partySetting = giftBlock.contents.partySettings.find((partySetting:any) => eventName == partySetting.eventName);
  if (partySetting?.isUse) return true
  if (! partySetting) return true;
  // 挙式の事前支払いをOFFにすると披露宴の事前支払いができなくなるみ
  if (guest_event_answer.name == '挙式・披露宴') {
    eventName = "披露宴";
    const partySetting = giftBlock.contents.partySettings.find((partySetting:any) => eventName == partySetting.eventName);
    if (partySetting?.isUse) return true
  }
  return false;
};


onMounted(() => {
  // 戻ってきた場合はスキップ
  if (props.isBack) return true;

  for (let i = 0; i < input.value.guest_event_answers.length; i++) {
    const answer = input.value.guest_event_answers[i];
    if (! isUsePaymentForm(answer)) {
      input.value.guest_event_answers[i].payment_amount = null;
      continue;
    }
    // 挙式 と 披露宴 どっちも会費が設定されてた場合
    if (! getEventName(answer)) {
      input.value.guest_event_answers[i].payment_amount = null;
      continue;
    }

    const event = events.value.find(item => item.name === answer.name);
    input.value.guest_event_answers[i].payment_amount = null;
    // feeOption が gift_system:ご祝儀 / membership_fee_common:会費共通 / membership_fee_separate:会費別
    if (event?.feeOption == 'membership_fee_common') {
      input.value.guest_event_answers[i].payment_amount = Number(event.feeAmount?.[0]);
    } else if (event?.feeOption == 'membership_fee_separate') {
      // 会費制（男女別）の場合：1つ目に男性 2つ目に女性の値
      if (input.value.input?.gender === 'FEMALE') {
        input.value.guest_event_answers[i].payment_amount = Number(event.feeAmount?.[1]);
      } else if (input.value.input?.gender === 'MALE') {
        input.value.guest_event_answers[i].payment_amount = Number(event.feeAmount?.[0]);
      } else {
        // 性別未回答、またはその他の場合は安い方をセット
        let feeAmount = Number(event.feeAmount?.[0]);
        if (feeAmount > Number(event.feeAmount?.[1])) feeAmount = Number(event.feeAmount?.[1]);
        input.value.guest_event_answers[i].payment_amount = feeAmount;
      }
    }

    // 連名ゲストの支払い
    input.value.guest_event_answers[i].isNotGuestsPayment = false;
    input.value.guest_event_answers[i].payment_amounts = [];
    if (event?.feeOption == 'gift_system') {
      input.value.guest_event_answers[i].isNotGuestsPayment = true;
    }

    for (let n = 0; n < input.value.guests.length; n++) {
      input.value.guest_event_answers[i].payment_amounts[n] = null;
      if (event?.feeOption == 'membership_fee_common') {
        input.value.guest_event_answers[i].payment_amounts[n] = Number(event.feeAmount?.[0]);
      } else if (event?.feeOption == 'membership_fee_separate') {
        // 会費制（男女別）の場合：1つ目に男性 2つ目に女性の値
        if (input.value.guests?.[n]?.gender === 'FEMALE') {
          input.value.guest_event_answers[i].payment_amounts[n] = Number(event.feeAmount?.[1]);
        } else if (input.value.guests?.[n]?.gender === 'MALE') {
          input.value.guest_event_answers[i].payment_amounts[n] = Number(event.feeAmount?.[0]);
        } else {
          // 性別未回答、またはその他の場合は安い方をセット
          let feeAmount = Number(event.feeAmount?.[0]);
          if (feeAmount > Number(event.feeAmount?.[1])) feeAmount = Number(event.feeAmount?.[1]);
          input.value.guest_event_answers[i].payment_amounts[n] = feeAmount;
        }
      }
    }

    // undefined => NULL に
    if (typeof input.value.guest_event_answers[i]?.payment_amount === 'undefined') input.value.guest_event_answers[i].payment_amount = null;
  }

  // 初期化
  input.value.input.is_system_fee = 0;
  if (usageFee.value == 'guest') input.value.input.is_system_fee = 1;

  input.value.input.gift_amount = '';
});

const error = ref('')
const errors = ref({})

const onClickNext = async() => {
  inputPayment.value.card = {};
  if (inputPayment.value.paymentWay === null) {
    error.value = '選択してください';
    window.scrollTo({top: 0, behavior: "smooth"});
    return false;
  }

  if (inputPayment.value.paymentWay == 0 || inputPayment.value.paymentWay == 3) {
    error.value = '';

    // 筆頭者に関しては空白は入力エラー
    errors.value = {
      input: {
        gift_amount: '',
        guest_event_answers: [] as any
      }
    };
    for (let i = 0; i < input.value.guest_event_answers.length; i++) {
      const guest_event_answer = input.value.guest_event_answers[i];
      errors.value.input.guest_event_answers[i] = {} as any;
      if (! isUsePaymentForm(guest_event_answer) || ! getEventName(guest_event_answer)) continue;
      if (! guest_event_answer.payment_amount && guest_event_answer.payment_amount != 0) {
        error.value = '筆頭者の金額は必ず入力してください';
        errors.value.input.guest_event_answers[i] = {payment_amount: '筆頭者の金額は必ず入力してください'};
      }
      // 会費制の場合のみ、お連れ様も含む全員の金額欄の入力は必須
      if (getEventByAnswer(guest_event_answer)?.feeOption !== 'gift_system' && guest_event_answer?.payment_amounts) {
        errors.value.input.guest_event_answers[i].payment_amounts = [];
        for (let n = 0; n < guest_event_answer.payment_amounts.length; n++) {
          errors.value.input.guest_event_answers[i].payment_amounts[n] = '';
          if (! guest_event_answer.payment_amounts[n]) {
            errors.value.input.guest_event_answers[i].payment_amounts[n] = '会費は必ず入力してください';
          }
        }
      }
    }
    // if (isShowGiftInput.value && (! input.value.gift_amount && input.value.gift_amount != 0)) {
    //   error.value = '筆頭者の金額は必ず入力してください';
    //   errors.value.input.gift_amount = '筆頭者の金額は必ず入力してください';
    // }
    if (error.value) {
      window.scrollTo({top: 0, behavior: "smooth"});
      return false;
    }
    
    // 0円の場合はNG
    if (! amount.value) {
      error.value = '合計金額を0円以上にしてください';
      window.scrollTo({top: 0, behavior: "smooth"});
      return false;
    }

    // カード番号確定
    await refInputCard.value.onClickSave();
    // inputCard.value
    if (! inputPayment.value.card?.cardToken) {
      window.scrollTo({top: 0, behavior: "smooth"});
      return false;
    }
  }
  // inputPayment.paymentWay
  // 0 : 会費・ご祝儀の事前受付を利用する
  // 1 : 当日会場へ持参する
  // 2 : すでに支払い済み
  // 3 : 欠席の回答と一緒にご祝儀も送る
  // 4 : 欠席の回答のみ送信する
  if (inputPayment.value.paymentWay == 0 || inputPayment.value.paymentWay == 3) {
    input.value.input.payment_method = 'ADVANCE_PAYMENT';
    input.value.input.system_fee = systemFee.value;
    input.value.input.system_fee_rate = systemFeeRate.value;
    input.value.input.total_amount = amount.value;
    input.value.input.settlement_amount = amount.value + systemFee.value;
    input.value.input.card_token = props.inputPayment.card?.cardToken;
  } else if (inputPayment.value.paymentWay == 1) {
    input.value.input.payment_method = 'BRING_ON_THE_DAY';
  } else if (inputPayment.value.paymentWay == 2) {
    input.value.input.payment_method = 'PAID';
  }
  if (input.value.input.payment_method != 'ADVANCE_PAYMENT') {
    for (let i = 0; i < input.value.guest_event_answers.length; i++) {
      input.value.guest_event_answers[i].payment_amount = null;
    }
  }
  window.scrollTo({top: 0, behavior: "smooth"});
  emit('setPageError', '');
  emit('change', input.value);
  emit('changePayment', inputPayment.value);
  emit('next');
}

const onClickBack = () => {
  window.scrollTo({top: 0, behavior: "smooth"});
  emit('back');
}


const getEventByAnswer = (answer:any) => {
  return events.value.find(item => item.name === answer.name);
}

const getEventName = (answer:any) => {
  // 挙式・披露宴（会場が別）の場合、「挙式」と「披露宴」の２行が表示されてしまう。表示上は「挙式・披露宴」と１行だけ表示、保存は「ゲストイベント別回答」の「披露宴」イベントの行に金額をセット
  let eventNames = [];
  for (let i = 0; i < input.value.guest_event_answers.length; i++) {
    const guest_event_answer = input.value.guest_event_answers[i];
    if (! isUsePaymentForm(guest_event_answer)) continue;
    eventNames.push(guest_event_answer.name);
  }
  // 挙式 と 披露宴 がどっちも出てたら、挙式・披露宴 にまとめる
  if (eventNames.indexOf('挙式') !== -1 && eventNames.indexOf('披露宴') !== -1) {
    if (answer.name == '挙式') return '';
    if (answer.name == '披露宴') return '挙式・披露宴';
  }
  return answer.name;
}
</script>

<style lang="scss" scoped>
.box-hr {
  text-align: center;
  margin: 0 auto 50px;
  > .box-inner {
    min-width: 300px;
    display: inline-block;
    border-top: 1px solid rgba(#333, .2);
    border-bottom: 1px solid rgba(#333, .2);
    padding: 15px 0;
  }
  p {
    margin: 0;
  }
}
</style>