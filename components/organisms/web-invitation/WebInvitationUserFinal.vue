<template>
<div class="WebInvitationPayment">
<Main
  :partsHidden="paramPartsHidden" 
  >
  <template #main>
    <Loading v-if="isLoading" :fullscreen="true"></Loading>
    <div class="contents-title">
      <Titleh1>
        <a @click="emit('back')" class="backward"><img src="@/assets/images/icon-arrow_backward-b.svg" alt="戻る"></a>
        最終確認
      </Titleh1>
    </div>
    <div class="contents-body">
      <p class="mb-40">入力内容をご確認いただき <br>
      「確定して送信する」ボタンを押してください </p>
      <h2 class="cmn-title" v-if="hasPresent">会費・ご祝儀について</h2>
      <div style="min-height: calc(100vh - 500px); min-height: calc(100dvh - 500px);">
        <ul class="list-info mb-5">
          <li><dl>
            <dd v-if="inputPayment.paymentWay == 0">WEBご祝儀・会費を利用する</dd>
            <dd v-else-if="inputPayment.paymentWay == 1">当日会場へ持参する</dd>
            <dd v-else-if="inputPayment.paymentWay == 2">すでに支払い済み</dd>
            <dd v-else-if="inputPayment.paymentWay == 3">保留/欠席の回答と一緒にご祝儀も送る</dd>
            <dd v-else-if="inputPayment.paymentWay == 4">保留/欠席の回答のみ送信する</dd>
          </dl></li>
        </ul>
        <ul class="list-price mb-10" v-if="input.input.payment_method == 'ADVANCE_PAYMENT'">
          <template v-for="(guest_event_answer, n) in input.guest_event_answers.filter(item => isUsePaymentForm(item) && getEventName(item))" :key="n">
            <li><dl>
              <dt>
                {{ input.input.last_name }} {{ input.input.first_name }} 様<br>
                {{ getEventName(guest_event_answer) }}
              </dt>
              <dd><span class="num">{{ setNumberFormat(guest_event_answer.payment_amount) }}</span><small>円</small></dd>
            </dl></li>
            <template v-if="! guest_event_answer?.isNotGuestsPayment && guest_event_answer?.payment_amounts">
              <li v-for="(payment_amount, i) in guest_event_answer.payment_amounts.filter(payment_amount => payment_amount !== null)" :key="i"><dl>
                <dt>
                  {{ input.guests?.[i]?.last_name }} {{ input.guests?.[i]?.first_name }} 様<br>
                  {{ getEventName(guest_event_answer) }}
                </dt>
                <dd><span class="num">{{ setNumberFormat(payment_amount) }}</span><small>円</small></dd>
              </dl></li>
            </template>
          </template>
          <li v-if="input.input.gift_amount"><dl>
            <dt>お気持ち</dt>
            <dd><span class="num">{{ setNumberFormat(input.input.gift_amount) }}</span><small>円</small></dd>
          </dl></li>
          <li v-if="input.input.is_system_fee"><dl>
            <dt>システム利用料</dt>
            <dd><span class="num">{{ setNumberFormat(input.input.system_fee) }}</span><small>円</small></dd>
          </dl></li>
          <li><dl>
            <dt>合計金額</dt>
            <dd><span class="num">{{ setNumberFormat(input.input.settlement_amount) }}</span><small>円</small></dd>
          </dl></li>
        </ul>
        <p v-if="error" class="input-error mt-10">{{ error }}</p>
        <div v-if="input.input.payment_method == 'ADVANCE_PAYMENT'">
          <p class="fz-xs">決済方法：クレジットカード<br>請求会社名：株式会社テトテ</p>
        </div>
      </div>
      <button class="btn btn-secondary btn-block mt-40 mb-10" @click="onClickSubmit()" :class="{'is-loading': isLoading}">確定して送信する</button>
      <button class="btn btn-default-outline btn-block" @click="emit('back')">戻る</button>
    </div>
  </template>
</Main>
</div>
</template>

<style lang="scss">
.WebInvitationPayment {
  .l-column1 {
    padding-top: 0;
    .contents-body {
      padding-left: 16px;
      padding-right: 16px;
      padding-top: 32px;
    }
  }
  .is-loading {
    pointer-events: none;
  }
}

</style>

<script lang="ts" setup>
const router = useRouter();
const { $dayjs } = useNuxtApp() as any;

const paramPartsHidden = {
  FixedHeader: 'hidden',
  Menu: 'hidden',
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
}

interface Props {
  input: any;
  inputPayment: any;
  webInvitationData: any;
}
const props = withDefaults(defineProps<Props>(), {
  input: () => {},
  inputPayment: () => {},
  webInvitationData: () => {}
});

const emit = defineEmits<{
  change: []
  back: []
  next: []
  setPageError: []
}>()

// 更新中のLoading
const isLoading = ref(false);
// 出席がある場合
const hasPresent = computed(() => {
  if (props.input.guest_event_answers.findIndex(item => item.attendance === GUEST_ATTENDANCE_MASTER.PRESENT) !== -1) return true;
  return false;
});

// 全体エラー
const error = ref('')

// 更新API
const { create, errors } = useCreateWebInvitationGuest();
const { create:paymentCreate, errors:paymentErrors } = useCreateWebInvitationGuestPayment();


const isUsePaymentForm = (guest_event_answer = {} as any) => {
  // 出席以外の場合
  if (guest_event_answer.attendance != GUEST_ATTENDANCE_MASTER.PRESENT) return false;
  const giftBlock = props.webInvitationData?.editor_settings?.blocks.find((block:any) => block.id == 'gift');
  if (! giftBlock?.contents?.partySettings) return true;
  let eventName = guest_event_answer.name;
  if (eventName == '挙式・披露宴') eventName = '挙式';
  if (! giftBlock.contents?.isUseInVenue) return true;
  const partySetting = giftBlock.contents.partySettings.find((partySetting:any) => eventName == partySetting.eventName);
  if (partySetting?.isUse) return true
  if (! partySetting) return true;
  return false;
};

// 保存ボタンクリック
const onClickSubmit = async() => {
  if(isLoading.value) { return false }
  isLoading.value = true;

  // 各種項目を調整
  const data = getCreateWebInvitationGuestData(props.input, props.webInvitationData);

  // 事前支払いの場合は、3Dセキュア
  if (props.input.input.payment_method == 'ADVANCE_PAYMENT') {
    let payment = {
      card_token: props.input.input.card_token,
      callback_url: window?.location?.origin+'/wi/'+props.webInvitationData.public_url+'/callback'
    }

    const result = await paymentCreate(data.input, data.guests, payment);
    // リダイレクト
    if (result?.redirect_url) {
      // 3Dセキュアが有効な場合
      // sessionStorageに位置保存
      let webInvitationInput = JSON.parse(JSON.stringify(data)) as any;
      webInvitationInput.payment = result;
      sessionStorage.setItem('webInvitationInput', JSON.stringify(webInvitationInput));
      location.href = result?.redirect_url;
    } else if (result?.order_id) {
      // 3Dセキュアが無効なカードの場合、すでに決済されてるので、 order_id を登録
      const payment = {
        access_id: result?.access_id,
        access_pass: result?.access_pass,
        order_id: result?.order_id
      }
      const isSuccess = await create(true, data.input, data.guests, data.free_item_values, data.guest_event_answers, data.guest_survey_answers, payment);
      // エラーの場合
      if (! isSuccess) {
        if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
        isLoading.value = false;
        // scrollPageTop();
        return false;
      }
      isLoading.value = false;
      router.push({ path: '/wi/'+props.webInvitationData.public_url+'/thankyou'});
    } else {
      if (paymentErrors.value?.v$?.[0]) error.value = paymentErrors.value?.v$?.[0];
      isLoading.value = false;
      // scrollPageTop();
      emit('setPageError', error.value);
      emit('back');
      return false;
    }
  } else {
    const isSuccess = await create(true, data.input, data.guests, data.free_item_values, data.guest_event_answers, data.guest_survey_answers);
    // エラーの場合
    if (! isSuccess) {
      if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
      isLoading.value = false;
      // scrollPageTop();
      return false;
    }
    isLoading.value = false;
    router.push({ path: '/wi/'+props.webInvitationData.public_url+'/thankyou'});
  }
};

const getEventName = (answer:any) => {
  // 挙式・披露宴（会場が別）の場合、「挙式」と「披露宴」の２行が表示されてしまう。表示上は「挙式・披露宴」と１行だけ表示、保存は「ゲストイベント別回答」の「披露宴」イベントの行に金額をセット
  let eventNames = [];
  for (let i = 0; i < props.input.guest_event_answers.length; i++) {
    const guest_event_answer = props.input.guest_event_answers[i];
    if (guest_event_answer.name !== '挙式' && guest_event_answer.payment_amount === null) continue;
    eventNames.push(guest_event_answer.name);
  }
  // 挙式 と 披露宴 がどっちも出てたら、挙式・披露宴 にまとめる
  if (eventNames.indexOf('挙式') !== -1 && eventNames.indexOf('披露宴') !== -1) {
    if (answer.name == '挙式') return '';
    if (answer.name == '披露宴') return '挙式・披露宴';
  }
  return answer.name;
}
</script>