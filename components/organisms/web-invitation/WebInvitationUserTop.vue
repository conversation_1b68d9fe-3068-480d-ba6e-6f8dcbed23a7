<template>
  <div class="webInvitationView">
    <Loading v-if="isLoading" :fullscreen="true"></Loading>
    <ViewFormWrap
      v-if="props.webInvitationData?.editor_settings?.blocks || props.webInvitationData?.editor_settings_json?.blocks"
      :data="webInvitationBlocks" ref="viewFormWrap">
      <div class="blocks">
        <div class="block" v-for="(block, i) in templateData?.blocks" :key="i">
          <component
            :is="onSetComponent(block.id)"
            :data="block.contents"
            :informationData="onFilterBlock('information')"
            :visible="block.visible"
            :input="input"
            :isPreview="props.isPreview"
            :webInvitationData="webInvitationData"
            @change="emit('change', $event)"
            @next="emit('next')"
            @scrollTo="scrollTo($event)"
          ></component>
        </div>
      </div>
    </ViewFormWrap>
  </div>
</template>

<script lang="ts" setup>
const router = useRouter();
interface Props {
  input?: any;
  webInvitationData?: any;
  isPreview?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  input: () => {},
  webInvitationData: () => {},
  isPreview: false
});

const emit = defineEmits<{
  change: []
  next: []
}>()

const isLoading = ref(false);
const webInvitationBlocks = ref(props.webInvitationData?.editor_settings?.blocks);
watch(() => props.webInvitationData, (newVal) => {
  if(newVal && newVal.editor_settings){
    webInvitationBlocks.value = newVal?.editor_settings?.blocks;
  }else if(newVal && newVal.editor_settings_json){
    webInvitationBlocks.value = newVal?.editor_settings_json?.blocks;
  }
}, {
  deep: true,
  immediate: true
});

const onFilterBlock = (id: String) => {
  let item;
  if(props.webInvitationData?.editor_settings){
    item = props.webInvitationData?.editor_settings?.blocks.filter((element) => {
      return element.id == id;
    });
  }else if(props.webInvitationData?.editor_settings_json){
    item = props.webInvitationData?.editor_settings_json?.blocks.filter((element) => {
      return element.id == id;
    });
  }
  if (!item) {
    return null
  }
  return item[0];
}

const state = reactive({
  mainVisual: markRaw(resolveComponent('ViewFormBlockMainVisual') as Component),
  countDown: markRaw(resolveComponent('ViewFormBlockCountDown') as Component),
  message: markRaw(resolveComponent('ViewFormBlockMessage') as Component),
  profile: markRaw(resolveComponent('ViewFormBlockProfile') as Component),
  gallery: markRaw(resolveComponent('ViewFormBlockGallery') as Component),
  information: markRaw(resolveComponent('ViewFormBlockInformation') as Component),
  gift: markRaw(resolveComponent('ViewFormBlockGift') as Component),
  freeField: markRaw(resolveComponent('ViewFormBlockFreeField') as Component),
  guestAnswer: markRaw(resolveComponent('ViewFormBlockGuestAnswer') as Component)
});

const onSetComponent = (id: String) => {
  return state[id];
}

onMounted(() => {
  document.getElementsByTagName('html')[0].scrollTop = 0;

  //アニメーション
  let animationTarget = document.querySelectorAll('[data-animation]:not(#mainVisual [data-animation])');
  const animationOptions = {
    root: null,
    rootMargin: '-10% 0px',
    threshold: 0,
  };

  //要素が交差したときの指示
  let callback = (entries: any) => {
    entries.forEach( async(entry: any) => {
      const target = entry.target;
      const targetDelay = target.getAttribute('data-animation-delay');
      if(targetDelay){
        await delay(targetDelay);
      }
      if (entry.isIntersecting) {
        target.classList.add('is-animated');
      }
    });
  }
  const observer = new IntersectionObserver(callback, animationOptions);
  animationTarget.forEach((target) => {
    observer.observe(target);
  });

  const delay = (time:number) => {
    return new Promise(resolve => setTimeout(resolve, time * 1000));
  };

  setTimeout(() => {
    let mainVisualAnimationTarget = document.querySelectorAll('#mainVisual [data-animation]');
    let mainVisual = document.querySelectorAll('#mainVisual');

    mainVisualAnimationTarget.forEach((target) => {
      target.classList.add('is-animated');
    });
    mainVisual.forEach((target) => {
      target.classList.add('is-animated');
    });

  },3000);
});

// 画像の取得
const templateData = ref({});
const getImagesFromUUIDs = async (obj:any) => {
  let updateObj = toRaw(obj);
  let uuids = extractUuids(updateObj);
  const { getImages, refetch } = await useGetManyImages2(uuids);
  await refetch();
  watch(() => getImages, (images, prevImages) => {
    if (images.value) {
      const updatedObjWithImages = setImages(updateObj, images.value);
      templateData.value = updatedObjWithImages;
      return updatedObjWithImages;
    }
  }, {
    deep: true,
    immediate: true
  });
}


let appendedStyle = null;
onUnmounted(() => {
  if (appendedStyle) {
    isLoading.value = true;
    appendedStyle.remove();
  }
});
router.beforeEach((to, from, next) => {
  if (appendedStyle && to?.href == '/products/webinvitation') {
    isLoading.value = true;
    appendedStyle.remove();
  }
  next();
});

watch(() => props.webInvitationData, async(newVal) => {
  if(newVal && newVal.m_web_invitation){
    if (appendedStyle) {
      appendedStyle.remove();
      appendedStyle = null;
    }
    const style = document.createElement('style');
    style.textContent = newVal.m_web_invitation.css_code;
    appendedStyle = style;
    document.head.appendChild(style);
    if(newVal.editor_settings){
      templateData.value = newVal.editor_settings;
      await getImagesFromUUIDs(newVal.editor_settings);
    }
  }else if(newVal && newVal.editor_settings_json){
    if (appendedStyle) {
      appendedStyle.remove();
      appendedStyle = null;
    }
    const style = document.createElement('style');
    style.textContent = newVal.css_code;
    appendedStyle = style;
    document.head.appendChild(style);
    if(newVal.editor_settings_json){
      templateData.value = newVal.editor_settings_json;
      await getImagesFromUUIDs(newVal.editor_settings_json);
    }
  }
}, {
  deep: true,
  immediate: true
});

const viewFormWrap = ref();
const scrollTo = (top: 0) => {
  if (window.matchMedia('(min-width: 768px)').matches) {
    // PCの場合
    if (viewFormWrap.value?.webInvitationView) {
      viewFormWrap.value?.webInvitationView.scrollTo({top: top + viewFormWrap.value?.webInvitationView.scrollTop, behavior: "smooth"});
    }
  } else {
    // SPの場合
    document.getElementsByTagName('html')[0].scrollTop = top + document.getElementsByTagName('html')[0].scrollTop;
  }
}
</script>

<style lang="scss">
@import 'assets/css/webinvitation/common/style.scss';
</style>
