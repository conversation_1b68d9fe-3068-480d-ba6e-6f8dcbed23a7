<template>
  <div>
    <h1>スタッフ</h1>
    <dl v-if="staff">
      <dt>id</dt>
      <dd>{{ staff.id }}</dd>
      <dt>name</dt>
      <dd>{{ staff.name }}</dd>
    </dl>
    <button @click="onUpdate">更新</button>
  </div>
</template>

<script setup lang="ts">
import type { UpdateStaffInput } from '@/composables/generated';


const props = defineProps({
    id: String
  }
)

const { staff } = useGetOneStaff(props.id)
const { update } = useUpdateStaff();

const input = ref({
  name: 'test2'
} as UpdateStaffInput)

const onUpdate = async () => {
  await update({
    id: staff.value.id,
    ...input.value
  })

  // console.log('更新に成功しました')
}

</script>