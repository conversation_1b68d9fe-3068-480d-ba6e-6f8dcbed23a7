<template>
  <div>
    <h1>スタッフ一覧</h1>
    <button @click="onCreate">追加</button>
    <ul>
      <li v-for="staff in staffs" :key="staff.id">
        <NuxtLink :to="`/samples/staffs/${staff.id}`">{{ staff.name }}</NuxtLink>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import type { CreateStaffInput } from '@/composables/generated';


const {staffs, refetch} = useGetManyStaff()

const { create } = useCreateStaff();

const input = ref({
  name: 'test'
} as CreateStaffInput)

const onCreate = async () => {
  await create(input.value)
  await refetch()

  // console.log('作成に成功しました')
}

</script>