<template>
<div :data-scroll="isScrollTop ? 'top' : ''" :class="{ 'is-adminUserLogin':  myGetCookie('isAdminUserLogin')}">
<FixedHeader 
  v-if="! partsHidden.FixedHeader" 
  :title="title" 
  :backlink="backlink" 
  :isBacklinkAction="isBacklinkAction" 
  :partsHiddenSp="{headerTitle: partsHidden.spHeaderTitle, logo: partsHidden.spLogo, backTop: partsHidden.spBackTop}" 
  :isShowSortButton="isShowSortButton"
  @click-backlink="onClickBacklink()"
/>

<slot name="headerafter" />

<Breadcrumbs v-if="breadcrumbs.length" :breadcrumbs="breadcrumbs" :data-sp-hidden="(partsHidden.spBreadcrumbs) ? true : ''" />

<main class="main-contents container" :class="{'is-nobreadcrumbs': ! breadcrumbs.length , 'is-spbreadcrumbsoff': partsHidden.spBreadcrumbs}">
  <MypageDrawerMenu v-if="! partsHidden.Menu && loginCheck?.loginCheck && (route.path.match(/^\/mypage/) && ! route.path.match(/^\/mypage\/webinvitation\/editor\/form/) && ! route.path.match(/^\/mypage\/webinvitation\/create/))" />
  <div class="contents_wrap">
    <slot name="contents-before" />
    <div class="contents" :class="[{'is-full': props.contentSize == 'full'}, {'is-lg': props.contentSize == 'lg'}]">
      <div class="contents-title" v-if="hasSlot('contents-title') && ! partsHidden.h1Title" :data-sp-hidden="partsHidden.spH1Title">
        <Titleh1>
          <slot name="contents-title" />
        </Titleh1>
      </div>
      <div class="contents-title" v-else-if="title && ! partsHidden.h1Title" :data-sp-hidden="partsHidden.spH1Title">
        <Titleh1>
          <NuxtLink v-if="isBacklinkAction" @click="onClickBacklink()" class="backward"><img src="@/assets/images/icon-arrow_backward-b.svg" alt="戻る"></NuxtLink>
          <NuxtLink v-else-if="backlink" :to="backlink" class="backward"><img src="@/assets/images/icon-arrow_backward-b.svg" alt="戻る"></NuxtLink>
          {{title}}
        </Titleh1>
      </div>

      <slot name="column1before" />

      <div class="section-inner" :class="['l-column'+column, addClass]">
        <div v-if="column == '2'" class="l-column2-main">
          <slot name="main" />
        </div>
        <slot v-else name="main" />
        <div v-if="column == '2'" class="l-column2-sub">
          <slot name="sub" />
        </div>
      </div>

      <slot name="column1after" />
      <FixedFooterBottom class="alone" v-if="partsHidden.FixedFooterContentsBottom === 'visible'" />
    </div>
  </div>
</main>
<ShowFooterTop v-if="! partsHidden.ShowFooterTop" />
<a href="#top" class="pagetop" :data-sp-hidden="! (partsHidden.spScrollTop === false && String(partsHidden.spScrollTop) !== 'undefined')"><img src="@/assets/images/icon-pagetop.svg" alt=""></a>
<FixedFooterBottom class="alone" v-if="partsHidden.FixedFooterBottom === 'visible'" />
<FixedFooterBottomReceiver v-if="partsHidden.FixedFooterBottomReceiver === 'visible'" :data-sp-hidden="partsHidden.spFixedFooterBottomReceiver" />
<FixedFooter v-if="! partsHidden.FixedFooter" />


<Transition name="modal-slide">
  <div><slot name="modalWindow" /></div>
</Transition>
<Transition name="modal-slide">
  <ModalLoginRegister
    v-if="isShowModalRegister() || isShowModalLogin()"
  />
</Transition>

<ModalLogout 
  v-if="isShowModalLogout()"
/>

<div class="toast-messages" v-if="toastMessages.length">
  <div class="toast-wrap" v-for="(toastMessage, i) in toastMessages" :key="i">
    <Toast :index="i"></Toast>
  </div>
</div>

<div class="loading-all" v-if="isShowGeneralLoading">
  <Loading :isShowIcon="isShowLoadingIcon"></Loading>
</div>

</div>
</template>

<script lang="ts" setup>
import { onBeforeRouteUpdate } from 'vue-router'
import type { Breadcrumbs } from '@/components/organisms/Breadcrumbs.vue';
import { BreadcrumbsDefault } from '@/components/organisms/Breadcrumbs.vue';
import { useCloned } from '@vueuse/core'
import type { ToastMessage } from '@/composables/useToastMessageState';
import { useToastMessageState } from '@/composables/useToastMessageState';
import { useLoadingState } from '@/composables/useLoading';
import { mySetCookie, myGetCookie, checkMustLoginUrl } from '@/utils/functions';
const { getToastMessages } = useToastMessageState();
const { isShowGeneralLoading, isShowLoadingIcon } = useLoadingState();
const { isShowModalRegister } = useModalRegisterState();
const { isShowModalLogin } = useModalLoginState();
const { isShowModalLogout } = useModalLogoutState();
const { getIsShowMypageMenu, hideDropdownMenu } = useMenuState();

const { loginCheck, refetch: refetchLoginCheck } = useGetLoginCheck();
const { clearAccessToken } = useAccessTokenState();

const slots = useSlots()
const hasSlot = (name:string) => {
  return !!slots[name];
}

export interface Props {
  partsHidden?: {
    FixedHeader?: boolean | 'hidden';
    Menu?: boolean | 'hidden';
    ShowFooterTop?: boolean | 'hidden';
    // 全体のフッター
    FixedFooter?: boolean | 'hidden';
    FixedFooterBottom?: boolean | 'hidden';
    // 少ないフッター
    FixedFooterBottomReceiver?: boolean | 'hidden';
    FixedFooterContentsBottom?: boolean | 'hidden';
    // パンクズ
    spBreadcrumbs?: boolean | 'hidden';
    // h1タイトル
    h1Title?: boolean;
    spHeaderTitle?: boolean;
    spH1Title?: boolean;
    spFixedFooterBottomReceiver?: boolean;

    // スマホのトップへ
    spBackTop?: boolean;
    spScrollTop?: boolean;
  },
  breadcrumbs?: Breadcrumbs;
  addClass?: string;
  title?: string;
  backlink?: string;
  isBacklinkAction?: boolean;
  column?: '1' | '2';
  sub?: '' | 'left';
  isShowSortButton?: boolean;
  // 幅は全幅・広め・基本の3種
  contentSize?: 'full' | 'lg' | '';
}
const props = withDefaults(defineProps<Props>(), {
  partsHidden: () => {
    return {
      FixedHeader: false,
      Menu: false,
      ShowFooterTop: false,
      FixedFooter: false,
      FixedFooterBottom: false,
      FixedFooterBottomReceiver: false,
      FixedFooterContentsBottom: false,
      h1Title: false,
      spBreadcrumbs: false,
      spHeaderTitle: false,
      spH1Title: false,
      spFixedFooterBottomReceiver: false,
      spBackTop: true,
      spScrollTop: true
    }
  },
  breadcrumbs: BreadcrumbsDefault,
  addClass: '',
  title: '',
  backlink: '',
  isBacklinkAction: false,
  column: '1',
  sub: '',
  isShowSortButton: false,
  contentSize: ''
});

const router = useRouter();
const route = useRoute();

const toastMessages = ref([] as ToastMessage[])
onMounted(async () => {
  // 閲覧履歴を保存
  saveUrlHistory();

  // トースト
  toastMessages.value = getToastMessages();

  // マイページメニューは遷移時に閉じる
  hideDropdownMenu();

  window.addEventListener('scroll', handleScroll);

  // ログインチェック
  await refetchLoginCheck();
  if (! loginCheck.value?.loginCheck) {
    clearAccessToken();
    if (checkMustLoginUrl(location.pathname)) {
      mySetCookie(REDIRECT_PATH_COOKIE_KEY, location.pathname);
      router.push({ path: `/login` })
    }
  }
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});

const isScrollTop = ref(true);
const lastScrollY = ref(0);
const handleScroll = () => {
  const scrollY = window.scrollY || window.pageYOffset;
  // console.log(Math.abs(scrollY - lastScrollY.value));
  if (Math.abs(scrollY - lastScrollY.value) < 50) return true;
  if (scrollY < lastScrollY.value) {
    isScrollTop.value = true;
  } else {
    isScrollTop.value = false;
  }
  lastScrollY.value = scrollY;
};

const emit = defineEmits(['click-backlink']);
const onClickBacklink = () => {
  emit('click-backlink');
};
</script>

<style lang="scss" scoped>
.mt-none {
  margin-top: 0 !important;
}
.container {
  display: flex;
  position: relative;
  width: 100%;
  margin-top: 0;
  &.is-nobreadcrumbs {
    margin-top: 0;
  }
  // .menu {
  //   width: 300px;
  //   flex-shrink: 0;
  //   transition: width 0.35s ease;
  //   &.close {
  //     width: 0;
  //   }
  // }
  .contents_wrap {
    flex-grow: 1;
  }
  .contents {
    flex-grow: 1;
  }
}
.l-column1 {
  padding-top: 0;
  padding-bottom: 60px;
  &-nowrap{
    max-width: 100%;
    padding-top: 0;
  }
}
.pagetop {
  position: fixed;
  bottom: 10px;
  left: calc(100% - 50px);
  z-index: 700;
  @include sp {
    // display: none;
    bottom: 10px;
    transition: 0.35s ease;
    [data-scroll="top"] & {
      bottom: 70px;
    }
  }
}

.is-mypageNarrowContents {
  .container {
    .contents-title {
      .section-inner {
        max-width: 1250px;
      }
    }
    .l-column1 {
      padding: 0 24px;
      :deep(.row) {
        max-width: 1250px;
        margin: 24px auto 0;
        & ~ .row {
          margin-top: 39px;
        }
        .row-inner {
          margin-right: auto;
          margin-left: calc((100% - 570px) / 2);
          transition: margin-left 0.35s;
        }
      }
    }
    :deep(.bottomWrap) {
      .bottom-inner {
        max-width: 1050px;
        margin: 0 auto;
      }
    }
    &.is-drawerMenuOpen {
      .l-column1 {
        :deep(.row) {
          .row-inner {
            margin-left: 0;
          }
        }
      }
    }
  }
}
.l-column2 {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  padding: 60px 5px;
  &.is-sub-left {
    flex-direction: row-reverse;
  }
  .l-column2-main {
    width: 650px;
  }
  .l-column2-sub {
    flex-shrink: 0;
    width: 375px;
    padding: 9px 7px;
  }
}

.is-widelayout {
  .l-column2 {
    gap: 0;
    max-width: 1250px;
    min-height: calc(100vh - 187px);
    min-height: calc(100dvh - 187px);
    padding: 0;
    .l-column2-main {
      padding-right: 21px;
      width: 885px;
      padding: 23px 28px 40px 15px;
    }
    .l-column2-sub {
      width: 365px;
      padding: 21px 0 40px;
      border-left: 8px solid $color-lightgray;
      text-align: center;
    }
  }
}

@include sp {
.is-spuioff {
  .header {
    :deep(.ui) {
      display: none;
    }
  }
  .container {
    margin-top: 0;
  }
}

.container.is-spbreadcrumbsoff {
  margin-top: 0;
}

.is-sptitleoff {
  .contents-title {
    display: none;
  }
}

.container {
  // .menu {
  //   display: none;
  // }
  .contents {
    flex-grow: unset;
    width: 100%;
  }
}

.is-mypageNarrowContents {
  .container {
    margin: 0;
    .contents-title {
      .section-inner {
        max-width: 100%;
      }
    }
    .l-column1 {
      padding: 0 16px;
      :deep(.row) {
        max-width: 100%;
        margin-top: 17px;
        & ~ .row {
          margin-top: 39px;
        }
        .row-inner {
          width: 100%;
          margin-left: auto;
          transition: none;
        }
      }
    }
    :deep(.bottomWrap) {
      .bottom-inner {
        max-width: 100%;
      }
    }
    &.is-drawerMenuOpen {
      .l-column1 {
        :deep(.row) {
          .row-inner {
            margin-left: auto;
          }
        }
      }
    }
  }
}

.is-spuibottomfooteroff {
  .container {
    :deep(.bottomWrap) {
      display: none;
    }
  }
}

.l-column2 {
  flex-direction: column;
  .l-column2-main ,
  .l-column2-sub {
    width: 100%;
  }
  &.sub-sp-none {
    .l-column2-sub {
      display: none;
    }
  }
}

.is-widelayout {
  .contents_wrap {
    width: 100%;
  }
  .contents {
    width: 100%;
  }
  .l-column2 {
    max-width: 100%;
    min-height: calc(100vh - 36px);
    min-height: calc(100dvh - 36px);
    .l-column2-main {
      padding-right: 0;
      width: 100%;
      padding: 23px 0 40px;
    }
    .l-column2-sub {
      width: 100%;
      padding: 21px 0 0;
      border-left: 0 solid $color-lightgray;
    }
  }
}
}
</style>
<style lang="scss">
// 後々 scssファイルに移動してください
.toast-messages {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 29px;
  @include sp {
    bottom: 66px;
  }
  z-index: 10000;
  .toast-wrap {
    display: block;
    margin-top: 10px;
    text-align: center;
  }
  .toast {
    position: static;
    transform: none;
    margin-left: auto;
    margin-right: auto;
    display: inline-block !important;
    text-align: left;
    &.close {
      display: none !important;
    }
  }
}

.loading-all{
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  // background: rgba(0,0,0,0.2);
  background: rgba(255,255,255,0.8);
  z-index: 1000;
}

.main-contents {
  display: flex;
  background: $color-lightgray;
  // height: 100%;
  // align-items: flex-start;
  // @include sp {
  //   padding-top: 36px;
  // }
  .mypage-drawer-menu {
    margin-right: 10px;
    width: 249px;
    flex-shrink: 0;
    // height: 100%;
    // border-right: 10px solid $color-lightgray;
    &.close {
      width: 78px;
    }
  }
  .contents_wrap {
    width: 100%;
  }
  .contents {
    max-width: 744px;
    width: 100%;
    margin: 0 auto;
    .section-inner.l-column1 {
      max-width: none;
      background: #fff;
      // margin-top対策の1px
      padding: 1px 0 60px;
      // min-height: 100vh;
      min-height: calc(100vh - 186px);
      body:has(.footerTop) & {
        min-height: 0;
      }
      @include sp {
        min-height: 0;
      }
    }
    &.is-full {
      max-width: none;
    }
    &.is-lg {
      max-width: 1024px;
    }
  }
}
</style>