<template>
  <div class="modalWrap" :class="[classList, 'modal-'+props.size, {'isShowSubModal': props.isShowSubModal}]">
    <div class="modalBg" @click="onClickClose()"></div>

    <div class="modalContainer">
  
      <div v-if="hasSlot('header')" class="header">
        <p><slot name="header" /></p>
        <button v-if="props.size != 'sm' && ! props.isSubModal" class="modalClose" @click.prevent="onClickClose()"><img src="@/assets/images/icon-close-b.svg" alt="閉じる" /></button>
      </div>
  
      <div ref="modalBody" class="contents">
        <div class="contentsInner">
          <slot name="main" />
        </div>
      </div>
  
      <div class="button" v-if="hasSlot('button')">
        <slot name="button" />
      </div>
  
      <div class="footer" v-if="(hasSlot('footer') || hasSlot('cancel') || hasSlot('do'))">
        <ul v-if="(hasSlot('cancel') || hasSlot('do'))">
          <li @click.prevent="onClickClose()"><slot name="cancel" /></li>
          <li class="do"><slot name="do" /></li>
        </ul>
        <slot name="footer" />
      </div>
  
    </div>
  </div>
</template>
  
<script lang="ts" setup>
  export interface Props {
    size: 'full' | 'sm';
    isShowSubModal: boolean;
    isSubModal: boolean;
  }
  
  const props = withDefaults(defineProps<Props>(), {
    size: 'full',
    isShowSubModal: false,
    isSubModal: false,
  });
  
  const emits = defineEmits<{
    close: []
  }>();
  
  // モーダルのClass
  const classList = ref(['is-open', 'closing', 'close'] as string[])
  
  // 閉じるアニメーション
  const onClickClose = () => {
    classList.value = classList.value.filter(item => item !== 'is-open');
    if (props.isSubModal) {
      emits('close');
    } else {
      classList.value.push('closing');
      setTimeout(() => {
        classList.value.push('close');
        emits('close');
      }, 350);
    }
  };
  
  // マウント時に開くアニメーションを追加
  onMounted(() => {
    if (props.isSubModal) {
      classList.value = ['is-open'];
    } else {
      classList.value = classList.value.filter(item => item !== 'close');
      setTimeout(() => {
        classList.value = classList.value.filter(item => item !== 'closing');
      }, 10);
    }
  })
  
  const slots = useSlots()
  const hasSlot = (name:string) => {
    return !!slots[name];
  }

// TOPにスクロール
const modalBody = ref<HTMLElement>();
const scrollTopModal = () => {
  if (modalBody.value) modalBody.value.scrollTo({top: 0, behavior:'smooth' });
};
// 親からも実行可能に
defineExpose({
  scrollTopModal
});
  </script>
  
  <style lang="scss">
  .modalWrap {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    height: 100dvh;
    opacity: 1;
    transition: opacity 0.35s ease;
    z-index: 1000;
    .modalBg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.4);
    }
    &.close {
      display: none;
      z-index: 0;
    }
    &.closing {
      opacity: 0;
      pointer-events: none;
      * {
        pointer-events: none;
      }
    }
    &.isShowSubModal {
      height: 0;
      overflow: hidden;
      @include sp {
        height: auto;
        // 親モーダル
        > .modalContainer {
          > .header {
            height: 0;
            overflow: hidden;
            border: none;
            padding: 0 !important;
          }
          > .contents {
            min-height: 100dvh;
          }
        }
        .contentsInner {
          // height: 0;
          // overflow: hidden;
          .modalWrap .modalContainer .contents {
            padding: 0;
          }
          .modalWrap .modalContainer .contents .contentsInner {
            padding: 18px 3px 5px;
            min-height: calc(100dvh - 110px);
            overflow-y: auto;
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch; 
            overflow-scrolling: touch;
          }
          .footer {
            display: block;
          }
        }
        .footer {
          display: none;
        }
      }
    }
    .modalContainer {
      position: relative;
      width: 640px;
      background-color: $color-mainbackground;
      .header {
        position: relative;
        padding: 14px 40px 14px 20px;
        border-bottom: 1px solid $color-lightgray;
        p {
          font-size: 18px;
          line-height: 120%;
          letter-spacing: 0.04em;
          color: $color-blacktext2;
          margin: 0;
        }
        .modalClose {
          position: absolute;
          top: 12px;
          right: 12px;
        }
        .backword {
          display: inline-block;
          vertical-align: top;
          cursor: pointer;
          i {
            vertical-align: top;
          }
        }
      }
      .contents {
        display: flex;
        justify-content: center;
        max-height: calc(100vh - 150px);
        max-height: calc(100dvh - 150px);
        padding: 30px 90px;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch; 
        overflow-scrolling: touch;
        overscroll-behavior-y: contain;
        .contentsInner {
          width: 100%;
          min-width: 0;
          min-height: 135px;
        }
      }
      .footer {
        background-color: $color-mainbackground;
        :slotted(.frowFooter) {
          position: relative;
        }
      }
    }
    &.modal-sm {
      > .modalContainer {
        border-radius: 4px;
        width: 296px;
        > .header {
          padding: 19px 24px 15px 24px;
          border: none;
          p {
            font-size: 16px;
            letter-spacing: 0;
          }
        }
        > .contents {
          padding: 3px 25px !important;
          .contentsInner {
            min-height: 57px;
            color: $color-blackLight;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0.25px;
            @include sp {
              height: auto;
              min-height: 57px !important;
            }
          }
          :deep(.attention) {
            font-weight: normal;
            color: $color-alert2;
          }
        }
        .button {
          padding: 5px 25px 35px;
          text-align: right;
        }
        > .footer {
          padding: 5px 30px 26px;
          ul {
            display: flex;
            justify-content: flex-end;
            gap: 23px;
            li {
              font-weight: 500;
              font-size: 14px;
              line-height: 16px;
              letter-spacing: 1.25px;
              color: $color-blacktext2;
              cursor: pointer;
              &.do {
                color: $color-accent;
              }
            }
          }
        }
      }
    }
  }
  
  @include sp {
  .modalWrap {
    &.modal-full {
      > .modalBg {
        display: none;
      }
      > .modalContainer {
        width: 100%;
        min-height: 100vh;
        min-height: 100dvh;
        > .header {
          padding: 15px 40px 10px 16px;
          border-bottom-width: 3px;
          p {
            font-size: 16px;
            line-height: 120%;
            letter-spacing: 0.02em;
            color: $color-blacktext2;
          }
          .modalClose {
            right: 15px;
          }
        }
        > .contents {
          max-height: none;
          height: calc(100vh - 110px);
          height: calc(100dvh - 110px);
          padding: 18px 15px 30px;
        }
        > .footer {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
        }
      }
    }
  
    &.modal-sm {
      > .modalContainer {
        > .header {
          border-bottom-width: 3px;
          .modalClose {
            right: 15px;
          }
        }
      }
    }
  }
}

.modal-footer {
  padding: 10px 0 10px;
  background-color: $color-mainbackground;
  box-shadow: 0px -2px 4px rgba(0, 0, 0, 0.06);
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  @include sp {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
  }
  &.modal-footer-block {
    display: block;
    .btns {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
    }
  }
  .btn {
    max-width: 210px;
    margin: 0 10px;
  }
  .message {
    text-align: center;
    font-size: 12px;
    line-height: 14px;
    color: $color-blacktext2;
    strong {
      font-weight: 700;
      font-size: 14px;
      letter-spacing: 0.1em;
    }
  }
}

.modalWrap.modal-sm > .modalContainer {
  > .contents .contentsInner {
    min-height: 0;
    margin-bottom: 0;
    // padding-left: 20px;
    // padding-right: 20px;
    min-height: 80px;
    color: #49454F;
  }
  > .footer {
    text-align: right;
    background: none;
    > * {
      display: inline-block;
    }
    a {
      color: var(--black, #333);
      text-decoration: none;
      padding: 10px;
    }
  }
}
</style>