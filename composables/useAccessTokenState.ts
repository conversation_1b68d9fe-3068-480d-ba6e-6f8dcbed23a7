import { mySetCookie, myGetCookie } from '../utils/functions';

/**
 * アクセストークンの管理
 */
const ACCESS_TOKEN_STATE_KEY = 'accessToken'

export const useAccessTokenState = () => {
  let tmpAccessTokens = {} as any;

  const setAccessToken = (token: string, clientId = 'default') => {
    let tokens = getAccessTokens();
    if (token) {
      tokens[clientId] = token;
    } else {
      if (tokens[clientId]) delete tokens[clientId];
    }
    if (JSON.stringify(tokens) !== '{}') {
      tmpAccessTokens = tokens;
      mySetCookie(ACCESS_TOKEN_STATE_KEY, tokens);
    } else {
      tmpAccessTokens = {};
      mySetCookie(ACCESS_TOKEN_STATE_KEY);
    }
  }

  const clearAccessToken = (clientId = 'default') => {
    setAccessToken('', clientId)
    mySetCookie('isAdminUserLogin', '');
  }

  const getAccessTokens = () => {
    const token = myGetCookie(ACCESS_TOKEN_STATE_KEY);
    if (token) return token;
    return {} as { [key: string]: string | null };
  }

  const getAccessToken = (clientId = 'default') => {
    const tokens = getAccessTokens();
    // 元は if (Object.hasOwnProperty(clientId)) だったが、動かなかったので、調整
    if (tokens?.[clientId]) {
        return tokens[clientId]
    }
    return null;
  }

  const hasAccessToken = (clientId = 'default') => {
    return getAccessToken(clientId) != null
  }

  // トークンをメモリ上に保持
  const setTmpAccessToken = (clientId = 'default') => {
    const token = getAccessToken(clientId);
    tmpAccessTokens[clientId] = token;
  }

  // メモリ上に保持しているトークンとCookieに保存されているトークンを比較
  // https://www.notion.so/987a674a1be74261bc9917e6c6778a54
  const isSameAccessToken = (clientId = 'default') => {
    const tmpToken = tmpAccessTokens?.[clientId]
    const token = getAccessToken(clientId);
    if (! tmpToken && token) {
      setTmpAccessToken(clientId);
    }
    if (tmpToken && token) {
      if (tmpToken != token) return false;
    }
    return true;
  }

  return { hasAccessToken, getAccessToken, setAccessToken, clearAccessToken, setTmpAccessToken, isSameAccessToken }
}