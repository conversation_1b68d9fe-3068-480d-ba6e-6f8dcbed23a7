import { MutationBinaryUploadImageArgs } from "./generated";
import { GraphQLValidationErrors } from "@/utils/graphql";

export const useAdminBinaryUploadImage = () => {
  const { mutate } = useBinaryUploadImageMutation({clientId: 'admin'});

  const errors = ref(null as GraphQLValidationErrors | null);

  const adminUpdate = async (variables: MutationBinaryUploadImageArgs) => {
    try {
      await mutate({
        variables,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    adminUpdate,
    errors
  }
}