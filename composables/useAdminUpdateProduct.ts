import { GraphQLValidationErrors } from "@/utils/graphql";

export const useAdminUpdateProduct = () => {
    const { mutate } = useAdminUpdateProductMutation({clientId: 'admin'});

    const errors = ref(null as GraphQLValidationErrors | null);

    const update = async (product: AdminUpdateProductinput, m_specification_products: AdminUpdateMSpecificationProductInput[], product_tags: AdminCreateProductTagInput[]) => {
        try {
        await mutate({
            product,
            m_specification_products,
            product_tags
        })
        return true
        } catch (e) {
        errors.value = parseGraphQLErrors(e)
        return false
        }
    }

    return {
        update,
        errors,
    }
}