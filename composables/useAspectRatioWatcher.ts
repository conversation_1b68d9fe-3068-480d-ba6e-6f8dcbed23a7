/**
 * 画像アスペクト比の監視と変更検知に関するcomposable
 */

export const useAspectRatioWatcher = () => {
  /**
   * アスペクト比の変更を検知して変更されたブロックのリストを返す
   */
  const detectAspectRatioChanges = (
    newRatio: any, 
    oldRatio: any, 
    blockTypes: string[] = ['mainVisual', 'message', 'profile', 'information', 'gallery', 'freeField', 'access', 'gift']
  ) => {
    const changedBlocks: Array<{type: string, oldAspect: any, newAspect: any}> = [];

    blockTypes.forEach(blockType => {
      const oldAspect = oldRatio?.[blockType];
      const newAspect = newRatio?.[blockType];

      if (
        oldAspect && newAspect &&
        (oldAspect.width !== newAspect.width || oldAspect.height !== newAspect.height)
      ) {
        changedBlocks.push({
          type: blockType,
          oldAspect,
          newAspect
        });
      }
    });

    return changedBlocks;
  };

  /**
   * 位置ごとのトリミング比率を取得する関数
   */
  const getAspectRatioForPosition = (
    position: number,
    isLockSpecifiedPosition: boolean,
    designAspectRatio: any,
    imageAspectRatio: any
  ) => {
    if (isLockSpecifiedPosition) {
      // まずdesignAspectRatioから確認
      if (designAspectRatio?.mainVisual?.specifiedImages?.[position]) {
        return designAspectRatio.mainVisual.specifiedImages[position];
      }
      // フォールバック: imageAspectRatioから確認  
      if (imageAspectRatio?.mainVisual?.specifiedImages?.[position]) {
        return imageAspectRatio.mainVisual.specifiedImages[position];
      }
    }
    
    // 位置指定なしの場合は従来の設定を使用
    return { 
      width: imageAspectRatio?.mainVisual?.width || 9, 
      height: imageAspectRatio?.mainVisual?.height || 16 
    };
  };

  /**
   * 位置指定ありかどうかの判定
   */
  const checkIsLockSpecifiedPosition = (designAspectRatio: any, imageAspectRatio: any) => {
    // まずdesignAspectRatioから確認
    if (designAspectRatio?.mainVisual?.isLockSpecifiedPosition === true) {
      return true;
    }
    // フォールバック: imageAspectRatioから確認
    if (imageAspectRatio?.mainVisual?.isLockSpecifiedPosition === true) {
      return true;
    }
    return false;
  };

  /**
   * MV画像の最大枚数を取得
   */
  const getMaxMainVisualImages = (isLockSpecifiedPosition: boolean, designAspectRatio: any, imageAspectRatio: any) => {
    if (isLockSpecifiedPosition) {
      // まずdesignAspectRatioから確認
      if (designAspectRatio?.mainVisual?.specifiedImages) {
        return designAspectRatio.mainVisual.specifiedImages.length;
      }
      // フォールバック: imageAspectRatioから確認
      if (imageAspectRatio?.mainVisual?.specifiedImages) {
        return imageAspectRatio.mainVisual.specifiedImages.length;
      }
    }
    return 3; // デフォルトは3枚
  };

  return {
    detectAspectRatioChanges,
    getAspectRatioForPosition,
    checkIsLockSpecifiedPosition,
    getMaxMainVisualImages
  };
};