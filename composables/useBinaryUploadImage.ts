import { MutationBinaryUploadImageArgs } from "./generated";
import { GraphQLValidationErrors } from "@/utils/graphql";

export const useBinaryUploadImage = () => {
  const { mutate } = useBinaryUploadImageMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const update = async (variables: MutationBinaryUploadImageArgs) => {
    try {
      await mutate({
        variables,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors
  }
}