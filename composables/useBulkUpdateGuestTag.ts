export const useBulkUpdateGuestTag = () => {
  const { mutate } = useBulkUpdateGuestTagMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const update = async (input: BulkUpdateGuestTagInput) => {
    try {
      await mutate({input})
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}