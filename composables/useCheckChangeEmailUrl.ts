import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useCheckChangeEmailUrl = () => {
  const { mutate } = useCheckChangeEmailUrlMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const action = async (token: string) => {
    try {
      const result = await mutate({token})
      if (result?.data?.checkChangeEmailUrl) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    action,
    errors,
  }
}