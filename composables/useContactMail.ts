import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useContactMail = () => {
    const { mutate } = useContactMailMutation();

    const errors = ref(null as GraphQLValidationErrors | null);

    const sendMail = async (first_name: string, last_name: string, first_name_kana: string, last_name_kana: string, email: string, phone: string, content: string) => {
        try {
        await mutate({
            first_name: first_name,
            last_name: last_name,
            first_name_kana: first_name_kana,
            last_name_kana: last_name_kana,
            email: email,
            phone: phone,
            content: content
        })
        return true
        } catch (e) {
        errors.value = parseGraphQLErrors(e)
        return false
        }
    }

    return {
        sendMail,
        errors,
    }
}