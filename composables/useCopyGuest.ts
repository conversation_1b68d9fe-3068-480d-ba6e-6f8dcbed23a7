export const useCopyGuest = () => {
  const { mutate } = useCopyGuestMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const update = async (guest_ids: string[], guest_list_id: string) => {
    try {
      await mutate({guest_ids, guest_list_id})
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}