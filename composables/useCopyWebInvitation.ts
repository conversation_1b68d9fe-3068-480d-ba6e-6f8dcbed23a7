import { useCopyWebInvitationMutation } from "./generated";

export const useCopyWebInvitation = () => {
  const { mutate } = useCopyWebInvitationMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const copy = async (id: string) => {
    try {
      let copyObj = await mutate({id})
      return copyObj
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    copy,
    errors,
  }
}