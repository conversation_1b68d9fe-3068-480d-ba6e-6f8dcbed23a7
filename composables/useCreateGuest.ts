export const useCreateGuest = () => {
  const { mutate } = useCreateGuestMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const create = async (
    input: CreateGuestInput, 
    guests?: [CreateGuestInput]|[], 
    free_item_values?: [CreateGuestFreeItemValueInput]|[],
    guest_event_answers?: [CreateGuestEventAnswerInput],
    guest_survey_answers?: [CreateGuestSurveyAnswerInput],
  ) => {
    try {
      if (! guests) guests = [];
      if (! free_item_values) free_item_values = [];
      await mutate({
        input,
        guests,
        free_item_values,
        guest_event_answers,
        guest_survey_answers,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}