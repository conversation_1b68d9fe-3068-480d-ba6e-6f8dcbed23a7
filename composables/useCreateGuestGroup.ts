import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useCreateGuestGroup = () => {
  const { mutate } = useCreateGuestGroupMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const create = async (input: GuestGroupInput) => {
    try {
      await mutate({
        input,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}