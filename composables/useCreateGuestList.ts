import type { GraphQLValidationErrors } from "@/utils/graphql";
import { CreateGuestListInput } from "./generated";

export const useCreateGuestList = () => {
  const { mutate } = useCreateGuestListMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const create = async (input: CreateGuestListInput) => {
    try {
      await mutate({
        input,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}