import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useCreateGuestTag = () => {
  const { mutate } = useCreateGuestTagMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const create = async (input: GuestTagInput) => {
    try {
      await mutate({
        input,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}