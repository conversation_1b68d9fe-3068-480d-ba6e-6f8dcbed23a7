import type { GraphQLValidationErrors } from "@/utils/graphql";
const { setAccessToken } = useAccessTokenState();

export const useCreateMember = () => {
  const { mutate } = useCreateMemberMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const create = async (uuid: string) => {
    try {
      const result = await mutate({uuid})
      if (result?.data?.createMember) {
        // token を保存
        setAccessToken(result.data.createMember, 'default');
        return true;
      } else {
        return false;
      }
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}