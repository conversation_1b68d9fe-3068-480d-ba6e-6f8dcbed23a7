import type { GraphQLValidationErrors } from "@/utils/graphql";
import { CreateMemberBankAccountInput } from "./generated";

export const useCreateMemberbankAccount = () => {
  const { mutate } = useCreateMemberBankAccountMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const create = async (input: CreateMemberBankAccountInput) => {
    try {
      await mutate({
        input,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}