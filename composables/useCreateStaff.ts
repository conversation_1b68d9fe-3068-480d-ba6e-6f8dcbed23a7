import { CreateStaffInput } from "./generated";

export const useCreateStaff = () => {
  const { mutate } = useCreateStaffMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const create = async (input: CreateStaffInput) => {
    try {
      await mutate({
        input,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}