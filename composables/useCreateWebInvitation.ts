import { GraphQLValidationErrors } from "@/utils/graphql";
import { MutationCreateWebInvitationArgs } from "./generated";

export const useCreateWebInvitation = () => {
  const { mutate } = useCreateWebInvitationMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const create = async (input: MutationCreateWebInvitationArgs) => {
    try {
      let webInvitation = await mutate(input)
      return webInvitation?.data?.CreateWebInvitation?.id
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}