export const useCreateWebInvitationGuest = () => {
  const { mutate } = useCreateWebInvitationGuestMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const create = async (
    is_save: Boolean,
    input: CreateGuestInput, 
    guests?: [CreateGuestInput]|[], 
    free_item_values?: [CreateGuestFreeItemValueInput]|[],
    guest_event_answers?: [CreateGuestEventAnswerInput],
    guest_survey_answers?: [CreateGuestSurveyAnswerInput],
    payment?: PaymentInput
  ) => {
    try {
      if (! guests) guests = [];
      if (! free_item_values) free_item_values = [];
      await mutate({
        is_save, 
        input,
        guests,
        free_item_values,
        guest_event_answers,
        guest_survey_answers,
        payment
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}