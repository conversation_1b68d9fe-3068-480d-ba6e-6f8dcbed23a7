export const useCreateWebInvitationGuestPayment = () => {
  const { mutate } = useCreateWebInvitationGuestPaymentMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const create = async (
    input: CreateGuestInput, 
    guests: [CreateGuestInput], 
    payment: PaymentInput
  ) => {
    try {
      const result = await mutate({
        input,
        guests,
        payment
      })
      return result?.data?.createWebInvitationGuestPayment
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}