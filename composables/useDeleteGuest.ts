import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useDeleteGuest = () => {
  const { mutate } = useDeleteGuestMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const action = async (input: {id: string | string[]; is_delete_all?: boolean;}) => {
    let data = {
      id: input.id,
      is_delete_all: input?.is_delete_all
    } as {id: string | string[]; is_delete_all: boolean;};
    if (input?.is_delete_all !== false && input?.is_delete_all !== true) data.is_delete_all = false;
    try {
      await mutate(data)
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    action,
    errors,
  }
}