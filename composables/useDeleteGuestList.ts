import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useDeleteGuestList = () => {
  const { mutate } = useDeleteGuestListMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const action = async (input: {id: string}) => {
    try {
      await mutate(input)
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    action,
    errors,
  }
}