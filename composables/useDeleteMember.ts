import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useDeleteMember = () => {
  const { mutate } = useDeleteMemberMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const action = async (id: string) => {
    try {
      await mutate(id)
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    action,
    errors,
  }
}