import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useDeleteWebInvitation = () => {
  const { mutate } = useDeleteWebInvitationMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const action = async (id: string) => {
    try {
      await mutate({deleteWebInvitationId: id})
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    action,
    errors,
  }
}