export const useGetCommon = () => {
  const { result, refetch, loading } = useGetCommonQuery();
  const tagGroup = computed(() => {
    let group;
    if(result.value?.tagGroup){
      group = JSON.parse(JSON.stringify(result.value?.tagGroup));
      if(group.tags){
        group.tags.unshift({
          id: 'all',
          image_url: '/images/category/category_all.png',
          name: 'すべて'
        });
      }
    }else{
      group = [];
    }
    return group;
  });
  return {
    tagGroup,
    refetch,
    loading,
  }
}