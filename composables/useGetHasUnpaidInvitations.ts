import { computed } from 'vue';
import * as VueApolloComposable from '@vue/apollo-composable';
import { GetHasUnpaidInvitationsDocument } from './generated';

export const useGetHasUnpaidInvitations = () => {
  const { result, loading, refetch } = VueApolloComposable.useQuery(
    GetHasUnpaidInvitationsDocument,
    {},
    {
      fetchPolicy: 'no-cache',
      errorPolicy: 'all'
    }
  );
  
  const hasUnpaidInvitations = computed(() => result.value?.hasUnpaidInvitations || false);
  
  return {
    hasUnpaidInvitations,
    loading,
    refetch
  };
}; 