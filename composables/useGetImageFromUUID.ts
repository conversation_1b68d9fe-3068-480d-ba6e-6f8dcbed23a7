import { provideApolloClient } from '@vue/apollo-composable';
import { defineNuxtPlugin } from '~/plugins/apollo';
import { useGetManyImages } from '@/composables/useGetManyImages';

export const getCircularReplacer = () => {
  const seen = new WeakSet();
  return (key, value) => {
    if (typeof value === "object" && value !== null) {
    if (seen.has(value)) {
        return;
    }
    seen.add(value);
    }
    return value;
  }
}

// オブジェクトからUUIDの抽出
export const extractUuids = (obj:any, extractProperty = ['uuid']) => {
  const extract = (item, uuids = []) => {
    if (Array.isArray(item)) {
      item.forEach(i => extract(i, uuids));
    } else if (item && typeof item === 'object') {
      extractProperty.forEach(prop => {
        if (item[prop]) {
          if (Array.isArray(item[prop])) {
            item[prop].forEach(uuid => uuids.push(uuid));
          } else {
            uuids.push(item[prop]);
          }
        }
      });
      Object.values(item).forEach(i => extract(i, uuids));
    }
    return uuids;
  }
  return extract(obj);
}

// 取得したpresigned_urlを元のオブジェクトにセットする
export const setImages = (obj, images, extractProperty = ['uuid'], setProperty = ['src']) => {
  const imageMap = images.reduce((acc, image, index) => {
    if (image && image.uuid) {
      if (image.presigned_url) {
        acc[image.uuid] = { presigned_url: image.presigned_url };
      }
      if (image.presigned_url_main) {
        if (!acc[image.uuid]) {
          acc[image.uuid] = {};
        }
        acc[image.uuid].presigned_url_main = image.presigned_url_main;
      }
    }
    return acc;
  }, {});
  const setImage = (item) => {
    if (Array.isArray(item)) {
      item.forEach(setImage);
    } else if (item && typeof item === 'object') {
      extractProperty.forEach((prop, index) => {
        if (item[prop]) {
          // imageMapにエントリがある場合は新しいURLを設定
          if (imageMap[item[prop]]) {
            if (imageMap[item[prop]].presigned_url) {
              item[setProperty[index]] = imageMap[item[prop]].presigned_url;
            }
            if (imageMap[item[prop]].presigned_url_main) {
              item['srcVideo'] = imageMap[item[prop]].presigned_url_main;
            }
          }
          // imageMapにエントリがない場合は既存のsrcを保持
          // （presigned_urlの取得に失敗した場合や、まだロード中の場合など）
        }
      });
      Object.values(item).forEach(setImage);
    }
  }
  let updateObj = JSON.parse(JSON.stringify(obj));
  setImage(updateObj);
  return updateObj;
}

// 画像の取得（サンプル）
// const getImagesFromUUIDs = async (obj) => {
//   let updateObj = JSON.parse(JSON.stringify(obj, getCircularReplacer()));
//   let uuids = extractUuids(updateObj);
//   const { getImages, refetch, imageLoading } = await useGetManyImages2(uuids);
//   await refetch();
//   watch(() => getImages, (images, prevImages) => {
//     if (images.value) {
//       const updatedObjWithImages = setImages(updateObj, images.value);
//       detailData.value = updatedObjWithImages;
//       form.value = updatedObjWithImages.editor_settings;
//       formUpdated.value = updatedObjWithImages.editor_settings;
//       return updatedObjWithImages;
//     }
//   }, {
//     deep: true,
//     immediate: true
//   });
// }