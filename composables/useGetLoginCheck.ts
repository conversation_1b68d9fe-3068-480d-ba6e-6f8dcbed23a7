export const useGetLoginCheck = () => {
    // SSR地に500エラーがでるので、デフォルト値を指定
    if (process.server) {
        return {
            loginCheck: false,
            refetch: () => {return false},
            loading: false
        }
    } else {
        const { result, refetch, loading } = useGetLoginCheckQuery();
        const loginCheck = computed(() => result.value || [])
        return {
            loginCheck,
            refetch,
            loading
        }
    }


    }