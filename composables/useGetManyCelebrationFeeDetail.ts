export const useGetManyCelebrationFeeDetail = () => {
  // celebrationFeeDetail[0].events[0].guests[0] と celebrationFeeDetail[0].events[1].guests[0] が同じIDを持って違うデータを返すので、キャッシュしないように調整
  const { result, refetch, loading } = useGetManyCelebrationFeeDetailQuery({
    fetchPolicy: 'no-cache'
  });
  const celebrationFeeDetails = computed(() => result.value?.celebrationFeeDetail || [])
  return {
    celebrationFeeDetails,
    refetch,
    loading,
  }
}