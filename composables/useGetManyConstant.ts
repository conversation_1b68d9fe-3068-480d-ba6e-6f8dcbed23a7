import { ConstantEnum } from '@/composables/generated';

export const useGetManyConstant = (constant: [ConstantEnum]) => {
  const { result, refetch, loading } = useGetManyConstantQuery({constant: constant});
  const relation_ship_names = computed(() => result.value?.constants?.relation_ship_names || [])
  const relation_ships = computed(() => result.value?.constants?.relation_ship_list || [])
  return {
    relation_ship_names,
    relation_ships,
    refetch,
    loading,
  }
}