import { QueryGuestListsOrderByColumn, SortOrder } from '@/composables/generated';

export const useGetManyGuest = (data: {
    guest_list_id: number;
    is_update_member_confirm_type: boolean;
    guest_event_attendances?: GuestEventAttendanceQueryInput[];
    guest_name?: string;
    web_invitation_id?: number;
    guest_type?: GuestTypeEnum;
    guest_group_ids?: number[];
    guest_tag_ids?: number[];
    orderBy?: QueryGuestsOrderByRelationOrderByClause[];
  }) => {

  // if (! data.orderBy) {
  //   data.orderBy = [{
  //     column: QueryGuestsOrderByColumn.CreatedAt,
  //     order: SortOrder.Asc
  //   }];
  // }
  const { result, refetch, loading } = useGetManyGuestQuery(data);
  const guests = computed(() => result.value?.guests || [])
  return {
    guests,
    refetch,
    loading,
  }
}