import { QueryGuestListsOrderByColumn, SortOrder } from '@/composables/generated';

export const useGetManyGuestList = (orderBy?: QueryGuestListsOrderByOrderByClause[]) => {

  if (! orderBy) {
    orderBy = [{
      column: QueryGuestListsOrderByColumn.IsDefault,
      order: SortOrder.Desc
    },{
      column: QueryGuestListsOrderByColumn.Name,
      order: SortOrder.Asc
    }];
  }
  const { result, refetch, loading } = useGetManyGuestListQuery({orderBy: orderBy});
  const guestLists = computed(() => result.value?.guestLists || [])
  return {
    guestLists,
    refetch,
    loading,
  }
}