export const useGetManyImages = async(uuids: string | string[]) => {
    if(!uuids || uuids.length == 0){
      return {
        getImages: [],
        refetch: function() {},
        loading: false
      }
    }else {
      const { result, refetch, loading } = await useGetManyImagesQuery({uuids: uuids});
      const getImages = computed(() => result?.value?.images)
      return {
        getImages,
        refetch,
        loading,
      }
    }
  }