export const useGetManyImages2 = async(uuids: string | string[]) => {
    if(!uuids || uuids.length == 0){
      return {
        getImages: [],
        refetch: function() {},
        loading: false
      }
    }else {
      const { result, refetch, loading } = await useGetManyImages2Query({uuids: uuids});
      const getImages = computed(() => result?.value?.images2)
      return {
        getImages,
        refetch,
        loading,
      }
    }
  }