export const useGetManyMSpecificationProducts = (orderBy: [QueryMSpecificationProductsOrderByOrderByClause], first: number, page: number, productTagIds: string[]) => {
  const { result, refetch, loading } = useGetManyMSpecificationProductQuery({orderBy: orderBy, first: first, page: page, productTagIds: productTagIds});
  const data = computed(() => result.value?.productWebInvitations?.data || [])
  const tagGroup = computed(() => {
    let group;
    if(result.value?.tagGroup){
      group = JSON.parse(JSON.stringify(result.value?.tagGroup));
      if(group.tags){
        group.tags.unshift({
          id: 'all',
          image_url: '/images/category/category_all.png',
          name: 'すべて'
        });
      }
    }else{
      group = [];
    }
    return group;
  });
  const paginatorInfo = computed(() => result.value?.productWebInvitations?.paginatorInfo || [])
  return {
    data,
    tagGroup,
    paginatorInfo,
    refetch,
    loading,
  }
}