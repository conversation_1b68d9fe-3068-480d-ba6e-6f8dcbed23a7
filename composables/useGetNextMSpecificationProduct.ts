export const useGetNextMSpecificationProduct = (orderBy: [QueryMSpecificationProductsOrderByOrderByClause], page: number, productTagIds: string[]) => {
  const { result, refetch, loading } = useGetNextMSpecificationProductQuery({orderBy: orderBy, first: 1, page: page, productTagIds: productTagIds});
  const webInvitationData = computed(() => result.value?.productWebInvitations?.data?.[0] || [])
  const paginatorInfo = computed(() => result.value?.productWebInvitations?.paginatorInfo || [])
  return {
    webInvitationData,
    paginatorInfo,
    refetch,
    loading,
  }
}