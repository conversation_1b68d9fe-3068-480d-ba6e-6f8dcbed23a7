export const useGetOneMemberMe = () => {
  const { result, refetch, loading } = useGetOneMemberMeQuery();
  const member = computed(() => result.value)
  const webInvitationsCnt = computed(() => {
    if (! result.value?.webInvitations) return 0;
    return result.value?.webInvitations.length;
  })
  const newGuestCnt = computed(() => {
    let cnt = 0;
    if (! result.value?.guestLists) return cnt;
    for (let i = 0; i < result.value?.guestLists.length; i++) {
      cnt += result.value?.guestLists[i]?.guests.filter(guest => guest.member_confirm_type == 'New').length
    }
    return cnt;
  })
  return {
    member,
    webInvitationsCnt,
    newGuestCnt,
    refetch,
    loading,
  }
}