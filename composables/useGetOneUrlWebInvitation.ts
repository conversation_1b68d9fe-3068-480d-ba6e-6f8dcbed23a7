export const useGetOneUrlWebInvitation = (public_url: string) => {
  // SSR地に500エラーがでるので、デフォルト値を指定
  if (process.server) {
    return {
      webInvitationData: false,
      refetch: () => {return false},
      loading: false
    }
  } else {
    const { result, refetch, loading } = useGetOneUrlWebInvitationQuery({
      public_url: public_url
    });
    const webInvitationData = computed(() => result?.value?.urlWebInvitation || null)
    return {
      webInvitationData,
      refetch,
      loading,
    }
  }
}