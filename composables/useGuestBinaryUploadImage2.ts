import { MutationGuestBinaryUploadImage2Args } from "./generated";
import { GraphQLValidationErrors } from "@/utils/graphql";

export const useGuestBinaryUploadImage2 = () => {
  const { mutate } = useGuestBinaryUploadImage2Mutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const update = async (variables: MutationGuestBinaryUploadImage2Args) => {
    try {
      await mutate({
        variables,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors
  }
}