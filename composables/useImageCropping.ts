/**
 * 画像のトリミング処理に関するcomposable
 */

// 隠しCropperを使ってトリミングデータを取得するクラス
class HiddenCropper {
  container: HTMLElement;

  constructor() {
    this.container = this.createHiddenContainer();
  }

  createHiddenContainer(): HTMLElement {
    const container = document.createElement('div');
    container.style.cssText = `
      position: absolute;
      left: -9999px;
      width: 500px;
      height: 500px;
      visibility: hidden;
    `;
    document.body.appendChild(container);
    return container;
  }

  async getCroppingData(imageUrl: string, aspectRatio: { width: number, height: number }): Promise<{ x: number, y: number, width: number, height: number }> {
    return new Promise((resolve, reject) => {
      const img = document.createElement('img');
      img.onload = () => {
        try {
          // Cropperライブラリの取得を試行
          const CropperClass = (window as any).Cropper || (window as any).cropper || (globalThis as any).Cropper;
          if (!CropperClass) {
            throw new Error('Cropper library not found');
          }

          const cropper = new CropperClass(img, {
            aspectRatio: aspectRatio.width / aspectRatio.height,
            autoCrop: true,
            autoCropArea: 1,
            modal: false,
            guides: false,
            center: false,
            highlight: false,
            background: false,
            cropBoxMovable: false,
            cropBoxResizable: false,
            zoomable: false,
            ready: () => {
              try {
                const data = cropper.getData();
                cropper.destroy();
                this.container.removeChild(img);
                resolve({
                  x: Math.round(data.x),
                  y: Math.round(data.y),
                  width: Math.round(data.width),
                  height: Math.round(data.height)
                });
              } catch (error) {
                cropper.destroy();
                this.container.removeChild(img);
                reject(error);
              }
            }
          });
          this.container.appendChild(img);
        } catch (error) {
          reject(error);
        }
      };
      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };
      img.src = imageUrl;
    });
  }

  destroy() {
    if (this.container && this.container.parentNode) {
      document.body.removeChild(this.container);
    }
  }
}

export const useImageCropping = () => {
  /**
   * 画像とアスペクト比に基づいて最適なクロッピング情報を計算する関数
   */
  const calculateOptimalCropping = async (image: any, aspectRatio: { width: number, height: number }) => {
    if (!aspectRatio?.width || !aspectRatio?.height) {
      return null;
    }

    // 画像のソースURLを取得（srcがnullの場合はuuidから取得を試行）
    let imageUrl = image.src;
    if (!imageUrl && image.uuid) {
      // UUIDがある場合は、一般的な画像URLパターンを推測
      // 実際のプロジェクトでは適切な画像URL取得ロジックに置き換える
    }

    // Cropperライブラリが利用可能で、画像のソースURLが存在する場合、隠しCropperを使用
    const CropperClass = (window as any).Cropper || (window as any).cropper || (globalThis as any).Cropper;
    if (imageUrl && !imageUrl.startsWith('data:') && CropperClass) {
      try {
        const hiddenCropper = new HiddenCropper();
        const croppingData = await hiddenCropper.getCroppingData(imageUrl, aspectRatio);
        hiddenCropper.destroy();
        return croppingData;
      } catch (error) {
        // Hidden Cropper failed, falling back to manual calculation
      }
    }

    // フォールバック: 手動計算
    const originalWidth = image.originalWidth || image.width || 1920;
    const originalHeight = image.originalHeight || image.height || 1080;

    const targetAspect = aspectRatio.width / aspectRatio.height;
    const imageAspect = originalWidth / originalHeight;

    let cropWidth, cropHeight, cropX, cropY;

    if (imageAspect > targetAspect) {
      // 画像が目標アスペクト比より横長 → 高さ基準でクロップ（中央揃え）
      cropHeight = originalHeight;
      cropWidth = cropHeight * targetAspect;
      cropX = (originalWidth - cropWidth) / 2;
      cropY = 0;
    } else {
      // 画像が目標アスペクト比より縦長 → 幅基準でクロップ（中央揃え）
      cropWidth = originalWidth;
      cropHeight = cropWidth / targetAspect;
      cropX = 0;
      cropY = (originalHeight - cropHeight) / 2;
    }

    return {
      x: Math.round(cropX),
      y: Math.round(cropY),
      width: Math.round(cropWidth),
      height: Math.round(cropHeight)
    };
  };

  /**
   * 縦横比が変更されたブロックのトリミングをリセットする関数
   */
  const resetCroppingForChangedBlocks = async (
    changedBlocks: Array<{ type: string, oldAspect: any, newAspect: any }>,
    formBlocks: any[]
  ) => {
    if (!formBlocks) return { hasChanges: false, updatedBlocks: formBlocks };

    let hasChanges = false;
    const updatedBlocks = JSON.parse(JSON.stringify(formBlocks));

    for (const { type, newAspect } of changedBlocks) {
      // ブロックIDのマッピング（blockTypeからブロックIDへの変換）
      let blockId = type;
      if (type === 'mainVisual') blockId = 'mainVisual';
      else if (type === 'message') blockId = 'message';
      else if (type === 'profile') blockId = 'profile';
      else if (type === 'information') blockId = 'information';
      else if (type === 'gallery') blockId = 'gallery';
      else if (type === 'freeField') blockId = 'freeField';
      else if (type === 'access') blockId = 'access';
      else if (type === 'gift') blockId = 'gift';

      const block = updatedBlocks.find((b: any) => b.id === blockId);

      // プロフィールブロックは配列構造のため特別処理
      if (blockId === 'profile' && block?.contents && Array.isArray(block.contents)) {

        for (let profileIndex = 0; profileIndex < block.contents.length; profileIndex++) {
          const profileItem = block.contents[profileIndex];
          if (profileItem?.images && Array.isArray(profileItem.images)) {
            for (let index = 0; index < profileItem.images.length; index++) {
              const image = profileItem.images[index];
              if (image && (image.uuid || image.src)) {
                const optimalCropping = await calculateOptimalCropping(image, newAspect);
                if (optimalCropping) {
                  image.x = optimalCropping.x;
                  image.y = optimalCropping.y;
                  image.width = optimalCropping.width;
                  image.height = optimalCropping.height;

                  // CropImageコンポーネント用のcropBoxサイズを更新（アスペクト比反映のため）
                  image.cropBoxWidth = optimalCropping.width;
                  image.cropBoxHeight = optimalCropping.height;

                  if (image.rotate === undefined) image.rotate = 0;
                  if (image.scaleFactor === undefined) image.scaleFactor = 1;
                  hasChanges = true;
                }
              }
            }
          }
        }
        continue;
      }

      // 情報ブロックは特別な構造のため個別処理
      if (blockId === 'information' && block?.contents?.events && Array.isArray(block.contents.events)) {

        for (let eventIndex = 0; eventIndex < block.contents.events.length; eventIndex++) {
          const event = block.contents.events[eventIndex];

          // images配列がある場合
          if (event?.images && Array.isArray(event.images)) {
            for (let index = 0; index < event.images.length; index++) {
              const image = event.images[index];
              if (image && (image.uuid || image.src)) {
                const optimalCropping = await calculateOptimalCropping(image, newAspect);
                if (optimalCropping) {
                  image.x = optimalCropping.x;
                  image.y = optimalCropping.y;
                  image.width = optimalCropping.width;
                  image.height = optimalCropping.height;

                  // CropImageコンポーネント用のcropBoxサイズを更新（アスペクト比反映のため）
                  image.cropBoxWidth = optimalCropping.width;
                  image.cropBoxHeight = optimalCropping.height;

                  if (image.rotate === undefined) image.rotate = 0;
                  if (image.scaleFactor === undefined) image.scaleFactor = 1;
                  hasChanges = true;
                }
              }
            }
          }

          // otherImages配列がある場合
          if (event?.otherImages && Array.isArray(event.otherImages)) {
            for (let index = 0; index < event.otherImages.length; index++) {
              const image = event.otherImages[index];
              if (image && (image.uuid || image.src)) {
                const optimalCropping = await calculateOptimalCropping(image, newAspect);
                if (optimalCropping) {
                  image.x = optimalCropping.x;
                  image.y = optimalCropping.y;
                  image.width = optimalCropping.width;
                  image.height = optimalCropping.height;

                  // CropImageコンポーネント用のcropBoxサイズを更新（アスペクト比反映のため）
                  image.cropBoxWidth = optimalCropping.width;
                  image.cropBoxHeight = optimalCropping.height;

                  if (image.rotate === undefined) image.rotate = 0;
                  if (image.scaleFactor === undefined) image.scaleFactor = 1;
                  hasChanges = true;
                }
              }
            }
          }
        }
        continue;
      }

      // 通常のブロック（mainVisual、message、gallery、freeField等）
      if (!block?.contents?.images) continue;


      for (let index = 0; index < block.contents.images.length; index++) {
        const image = block.contents.images[index];
        if (image && (image.uuid || image.src)) {
          // 新しいアスペクト比で最適なクロッピング情報を計算
          const optimalCropping = await calculateOptimalCropping(image, newAspect);

          if (optimalCropping) {

            // 計算された最適な値を適用
            image.x = optimalCropping.x;
            image.y = optimalCropping.y;
            image.width = optimalCropping.width;
            image.height = optimalCropping.height;

            // CropImageコンポーネント用のcropBoxサイズを更新（アスペクト比反映のため）
            image.cropBoxWidth = optimalCropping.width;
            image.cropBoxHeight = optimalCropping.height;

            // 回転とスケールファクターは保持（初期化されていない場合のみセット）
            if (image.rotate === undefined) image.rotate = 0;
            if (image.scaleFactor === undefined) image.scaleFactor = 1;

            hasChanges = true;
          }
        }
      }
    }


    return { hasChanges, updatedBlocks };
  };

  /**
   * 画像読み込み後のトリミング比率再計算（遅延実行）
   */
  const recalculateAfterImageLoad = async (
    changedBlocks: Array<{ type: string, oldAspect: any, newAspect: any }>,
    formBlocks: any[],
    detailData: any = null,
    delay: number = 1000
  ) => {
    // 指定された時間待機してから再計算
    await new Promise(resolve => setTimeout(resolve, delay));

    if (!formBlocks) return { hasChanges: false, updatedBlocks: formBlocks };

    let hasChanges = false;
    const updatedBlocks = JSON.parse(JSON.stringify(formBlocks));

    // detailDataから現在のsrc情報を取得してupdatedBlocksに反映
    if (detailData?.editor_settings?.blocks) {
      const sourceBlocks = detailData.editor_settings.blocks;
      for (let i = 0; i < updatedBlocks.length; i++) {
        const sourceBlock = sourceBlocks.find((b: any) => b.id === updatedBlocks[i].id);
        if (sourceBlock) {
          // src情報を保持する関数
          const preserveSrcInfo = (target: any, source: any) => {
            if (Array.isArray(target) && Array.isArray(source)) {
              target.forEach((item, index) => {
                if (source[index]) {
                  preserveSrcInfo(item, source[index]);
                }
              });
            } else if (target && source && typeof target === 'object' && typeof source === 'object') {
              // srcVideoのみ保持（srcは上書きしない）
              if (source.srcVideo && target.uuid === source.uuid) {
                target.srcVideo = source.srcVideo;
              }
              // 子オブジェクトも再帰的に処理
              Object.keys(target).forEach(key => {
                if (source[key]) {
                  preserveSrcInfo(target[key], source[key]);
                }
              });
            }
          };
          preserveSrcInfo(updatedBlocks[i], sourceBlock);
        }
      }
    }

    for (const { type, newAspect } of changedBlocks) {
      // ブロックIDのマッピング
      let blockId = type;
      if (type === 'mainVisual') blockId = 'mainVisual';
      else if (type === 'message') blockId = 'message';
      else if (type === 'profile') blockId = 'profile';
      else if (type === 'information') blockId = 'information';
      else if (type === 'gallery') blockId = 'gallery';
      else if (type === 'freeField') blockId = 'freeField';
      else if (type === 'access') blockId = 'access';
      else if (type === 'gift') blockId = 'gift';

      const block = updatedBlocks.find((b: any) => b.id === blockId);

      // プロフィールブロックは配列構造のため特別処理
      if (blockId === 'profile' && block?.contents && Array.isArray(block.contents)) {
        for (let profileIndex = 0; profileIndex < block.contents.length; profileIndex++) {
          const profileItem = block.contents[profileIndex];
          if (profileItem?.images && Array.isArray(profileItem.images)) {
            for (let index = 0; index < profileItem.images.length; index++) {
              const image = profileItem.images[index];
              if (image && (image.uuid || image.src)) {
                const optimalCropping = await calculateOptimalCropping(image, newAspect);
                if (optimalCropping) {
                  image.x = optimalCropping.x;
                  image.y = optimalCropping.y;
                  image.width = optimalCropping.width;
                  image.height = optimalCropping.height;
                  image.cropBoxWidth = optimalCropping.width;
                  image.cropBoxHeight = optimalCropping.height;
                  if (image.rotate === undefined) image.rotate = 0;
                  if (image.scaleFactor === undefined) image.scaleFactor = 1;
                  hasChanges = true;
                }
              }
            }
          }
        }
        continue;
      }

      // 情報ブロック特別処理
      if (blockId === 'information' && block?.contents?.events && Array.isArray(block.contents.events)) {
        for (let eventIndex = 0; eventIndex < block.contents.events.length; eventIndex++) {
          const event = block.contents.events[eventIndex];

          // images配列
          if (event?.images && Array.isArray(event.images)) {
            for (let index = 0; index < event.images.length; index++) {
              const image = event.images[index];
              if (image && (image.uuid || image.src)) {
                const optimalCropping = await calculateOptimalCropping(image, newAspect);
                if (optimalCropping) {
                  image.x = optimalCropping.x;
                  image.y = optimalCropping.y;
                  image.width = optimalCropping.width;
                  image.height = optimalCropping.height;
                  image.cropBoxWidth = optimalCropping.width;
                  image.cropBoxHeight = optimalCropping.height;
                  if (image.rotate === undefined) image.rotate = 0;
                  if (image.scaleFactor === undefined) image.scaleFactor = 1;
                  hasChanges = true;
                }
              }
            }
          }

          // otherImages配列
          if (event?.otherImages && Array.isArray(event.otherImages)) {
            for (let index = 0; index < event.otherImages.length; index++) {
              const image = event.otherImages[index];
              if (image && (image.uuid || image.src)) {
                const optimalCropping = await calculateOptimalCropping(image, newAspect);
                if (optimalCropping) {
                  image.x = optimalCropping.x;
                  image.y = optimalCropping.y;
                  image.width = optimalCropping.width;
                  image.height = optimalCropping.height;
                  image.cropBoxWidth = optimalCropping.width;
                  image.cropBoxHeight = optimalCropping.height;
                  if (image.rotate === undefined) image.rotate = 0;
                  if (image.scaleFactor === undefined) image.scaleFactor = 1;
                  hasChanges = true;
                }
              }
            }
          }
        }
        continue;
      }

      // 通常のブロック（mainVisual、message、gallery、freeField等）
      if (!block?.contents?.images) continue;

      for (let index = 0; index < block.contents.images.length; index++) {
        const image = block.contents.images[index];
        if (image && (image.uuid || image.src)) {
          const optimalCropping = await calculateOptimalCropping(image, newAspect);

          if (optimalCropping) {
            image.x = optimalCropping.x;
            image.y = optimalCropping.y;
            image.width = optimalCropping.width;
            image.height = optimalCropping.height;
            image.cropBoxWidth = optimalCropping.width;
            image.cropBoxHeight = optimalCropping.height;
            if (image.rotate === undefined) image.rotate = 0;
            if (image.scaleFactor === undefined) image.scaleFactor = 1;
            hasChanges = true;
          }
        }
      }
    }
    return { hasChanges, updatedBlocks };
  };

  return {
    calculateOptimalCropping,
    resetCroppingForChangedBlocks,
    recalculateAfterImageLoad,
    HiddenCropper
  };
};