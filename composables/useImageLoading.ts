import { LoadingItem } from '@/types/LoadingItem';

export function useImageLoading(emit: any) {
	const onLoadImageUpdate = async (item: LoadingItem) => {
			emit('updateLoadingItems', JSON.parse(JSON.stringify(item)));
	};

	const onLoadImageRemove = async (item: LoadingItem) => {
			emit('removeLoadingItems', JSON.parse(JSON.stringify(item)));
	};

	return {
			onLoadImageUpdate,
			onLoadImageRemove
	};
}