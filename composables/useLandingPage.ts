import { ref, computed } from 'vue';
import { useRuntimeConfig } from '#app';
import { MicroCms, type MicroCmsLandingPage } from '@/utils/microCms';

export const useLandingPage = () => {
  const microCms = new MicroCms();
  const lpData = ref<MicroCmsLandingPage | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 環境を判定する関数
  const getEnvironment = (apiEndpoint: string): string => {
    switch (apiEndpoint) {
      case 'production':
        return '本番サーバー';
      case 'staging':
        return 'ステージングサーバー';
      default:
        return '開発サーバー';
    }
  };

  // LPデータを取得する関数（パスベース）
  const fetchLandingPage = async (contentPath: string): Promise<MicroCmsLandingPage | null> => {
    loading.value = true;
    error.value = null;

    try {
      const config = useRuntimeConfig();
      const environment = getEnvironment(config.public.serverEnvironment);

      const { data } = await microCms.fetch('/landing-page', {
        filters: `public[contains]${environment}[and]view[contains]LPページ[and]path[equals]${contentPath}`,
      });

      if (data.value?.contents?.length > 0) {
        lpData.value = data.value.contents[0] as MicroCmsLandingPage;
        return lpData.value;
      } else {
        error.value = 'LP not found';
        return null;
      }
    } catch (err) {
      console.error('LP fetch error:', err);
      error.value = 'Failed to fetch LP data';
      return null;
    } finally {
      loading.value = false;
    }
  };

  // HTML指定があるかどうか（body_htmlを優先）
  const hasHtmlContent = computed(() => {
    return !!(lpData.value?.contents?.[0]?.body_html);
  });

  // リッチエディタコンテンツがあるかどうか
  const hasRichContent = computed(() => {
    return !!(lpData.value?.contents?.[0]?.body && !lpData.value?.contents?.[0]?.body_html);
  });

  // 表示コンテンツを取得（body_htmlを優先、なければbody）
  const getDisplayContent = computed(() => {
    if (!lpData.value?.contents?.[0]) return '';
    return lpData.value.contents[0].body_html || lpData.value.contents[0].body || '';
  });

  // PC用コンテンツを取得
  const getPcContent = computed(() => {
    if (!lpData.value?.contents?.[0]) return '';
    const content = lpData.value.contents[0];
    return content.body_html || content.body_pc || content.body || '';
  });

  // SP用コンテンツを取得
  const getSpContent = computed(() => {
    if (!lpData.value?.contents?.[0]) return '';
    const content = lpData.value.contents[0];
    return content.body_html || content.body || '';
  });

  // body_pcコンテンツがあるかどうか
  const hasPcContent = computed(() => {
    return !!(lpData.value?.contents?.[0]?.body_pc);
  });

  // SEO情報を取得
  const getSeoData = computed(() => {
    if (!lpData.value) return null;

    const seoData = lpData.value.seo?.[0];

    return {
      title: seoData?.seo_title || lpData.value.title || '',
      description: seoData?.seo_desctiption || seoData?.seo_description || '',
      ogImage: seoData?.seo_eyecatch?.url || '',
      seoIndex: seoData?.seo_index
    };
  });

  // カスタムスタイル（CSS）を取得
  const getCustomCss = computed(() => {
    if (!lpData.value) return '';
    return lpData.value.custom_css || '';
  });

  // カスタムスクリプト（JS）を取得
  const getCustomJs = computed(() => {
    if (!lpData.value) return '';
    return lpData.value.custom_js || '';
  });

  // ショートコード用データを取得
  const getShortcodeData = computed(() => {
    if (!lpData.value?.contents?.[0]) return {};

    const content = lpData.value.contents[0];
    return {
      couponCodeNumber: content.couponCodeNumber || '',
      couponCodeTextColor: content.couponCodeTextColor || '#D7A241',
      couponCodeBtnBgColor: content.couponCodeBtnBgColor || '#FF8F79',
      couponCodeBtnColor: content.couponCodeBtnColor || '#FFF',
      copyBoxText: content.copyBoxText || ''
    };
  });

  return {
    // データ
    lpData: readonly(lpData),
    loading: readonly(loading),
    error: readonly(error),

    // メソッド
    fetchLandingPage,

    // 計算プロパティ
    hasHtmlContent,
    hasRichContent,
    hasPcContent,
    getDisplayContent,
    getPcContent,
    getSpContent,
    getSeoData,
    getCustomCss,
    getCustomJs,
    getShortcodeData
  };
};