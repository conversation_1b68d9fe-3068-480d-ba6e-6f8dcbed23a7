import { ref } from 'vue';

export const useLoadingState = () => {
  const isShowGeneralLoading = useState('isShowGeneralLoading', () => (false));
  const isShowLoadingIcon = useState('isShowIcon', () => (false));

  // 全体ローディングを表示する関数
  function showLoading(isShowIcon = false) {
    isShowGeneralLoading.value = true;
    isShowLoadingIcon.value = isShowIcon;
  }
  // 全体ローディングを非表示する関数
  function hideLoading() {
    isShowGeneralLoading.value = false;
    isShowLoadingIcon.value = false;
  }

  // 全体ローディングの状態を取得する関数
  function getLoadingStatus() {
    return isShowGeneralLoading.value;
  }

  return {
    isShowGeneralLoading,
    showLoading,
    hideLoading,
    getLoadingStatus,
    isShowLoadingIcon
  };
}