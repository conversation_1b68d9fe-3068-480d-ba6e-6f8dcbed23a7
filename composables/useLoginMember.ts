import type { GraphQLValidationErrors } from "@/utils/graphql";
import { mySetCookie } from '@/utils/functions';
const { setAccessToken } = useAccessTokenState();

export const useLoginMember = () => {
  const { mutate } = useLoginMemberMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const login = async (email: string, password: string) => {
    try {
      const result = await mutate({
        email: email,
        password: password
      })
      if (result?.data?.loginMember) {
        // token を保存
        setAccessToken(result.data.loginMember, 'default');
        // なりすましログインフラグ削除
        mySetCookie('isAdminUserLogin', '');
        return true;
      } else {
        return false;
      }
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    login,
    errors,
  }
}