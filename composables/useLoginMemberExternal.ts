import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useLoginMemberExternal = () => {
  const { mutate } = useLoginMemberMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const login = async (email: string, password: string) => {
    try {
      const result = await mutate({
        email: email,
        password: password
      })
      if (result?.data?.loginMember) {
        return result.data.loginMember;
      } else {
        return false;
      }
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    login,
    errors,
  }
}