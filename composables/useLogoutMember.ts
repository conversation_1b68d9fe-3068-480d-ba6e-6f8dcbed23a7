import type { GraphQLValidationErrors } from "@/utils/graphql";
const { clearAccessToken } = useAccessTokenState();

export const useLogoutMember = () => {
  const { mutate } = useLogoutMemberMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const logout = async () => {
    try {
      const result = await mutate();
      clearAccessToken();
      return true;
    } catch (e) {
      clearAccessToken();
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    logout,
    errors,
  }
}