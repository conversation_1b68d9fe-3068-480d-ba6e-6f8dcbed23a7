import { ref } from 'vue';

const isShowDropdownMenu = ref(false);
const isShowMypageMenu = ref(true);

export const useMenuState = () => {
  function showDropdownMenu() {
    isShowDropdownMenu.value = true;
  }
  function hideDropdownMenu() {
    isShowDropdownMenu.value = false;
  }
  function getIsShowDropdownMenu() {
    return isShowDropdownMenu.value;
  }

  function toggleMypageMenu() {
    isShowMypageMenu.value = ! isShowMypageMenu.value;
  }
  function showMypageMenu() {
    isShowMypageMenu.value = true;
  }
  function hideMypageMenu() {
    isShowMypageMenu.value = false;
  }
  function getIsShowMypageMenu() {
    return isShowMypageMenu.value;
  }


  return {
    showDropdownMenu,
    hideDropdownMenu,
    getIsShowDropdownMenu,
    toggleMypageMenu,
    showMypageMenu,
    hideMypageMenu,
    getIsShowMypageMenu
  };
}