const config = useRuntimeConfig();
const uri = config.public.microCms.clients.default.uri;
const apiKey = config.public.microCms.clients.default.apiKey;

export const useMicroCms = (
  path: string,
  params: any,
) => {
  // { result, refetch, loading }
  const response = useFetch(uri + path, {
    headers: {
      'X-API-KEY': apiKey
    },
    params: params
  });
  const data = response.data;
  const errors = response.error;
  const refetch = response.refresh;
  return {
    data,
    refetch,
    errors,
  }
}