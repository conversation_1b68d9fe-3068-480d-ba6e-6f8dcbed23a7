import { ref } from 'vue';
import { useCookies } from '@vueuse/integrations/useCookies'
const REDIRECT_PATH_COOKIE_KEY = 'redirectUrl';

const showModal = ref(false);
const errors = ref({} as any)
const cookies = useCookies([REDIRECT_PATH_COOKIE_KEY]);
const IS_EXTERNAL_LOGIN_STATE_KEY = 'isExternalLogin'

export const useModalLoginState = () => {
  function isShowModalLogin() {
    return showModal.value;
  }
  // redirectUrl にURLを指定すると、ログイン後にそのURLにリダイレクトする
  function showModalLogin(redirectUrl = '') {
    if (redirectUrl) {
      cookies.set(REDIRECT_PATH_COOKIE_KEY, redirectUrl, {path: '/'})
    }
    const { hideModalRegister } = useModalRegisterState();
    hideModalRegister();
    showModal.value = true;
  }
  function hideModalLogin() {
    showModal.value = false;
  }

  function setLoginErrors(errorMessages = {}) {
    errors.value = errorMessages;
  }
  function getLoginErrors() {
    return errors.value;
  }
  return {
    isShowModalLogin,
    showModalLogin,
    hideModalLogin,

    setLoginErrors,
    getLoginErrors
  };
}