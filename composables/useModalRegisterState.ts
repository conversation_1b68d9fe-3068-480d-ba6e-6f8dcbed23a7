import { ref } from 'vue';
import { useCookies } from '@vueuse/integrations/useCookies'

const showModal = ref(false);
const errors = ref({} as any)
const modalStep = ref('');
const IS_SNS_REGISTER_STATE_KEY = 'snsRegister'

export const useModalRegisterState = () => {
  function isShowModalRegister() {
    return showModal.value;
  }
  function showModalRegister(step = '') {
    const { hideModalLogin } = useModalLoginState();
    hideModalLogin();
    showModal.value = true;
    if (typeof step == 'string') {
      modalStep.value = step;
    } else {
      modalStep.value = '';
    }
  }
  function hideModalRegister() {
    showModal.value = false;
  }
  function getModalStep() {
    return modalStep.value;
  }

  // SNSログイン or SNS会員登録の判定
  function isSnsRegister() {
    const cookies = useCookies([IS_SNS_REGISTER_STATE_KEY])
    return cookies.get(IS_SNS_REGISTER_STATE_KEY);
  }
  function setSnsRegister() {
    const cookies = useCookies([IS_SNS_REGISTER_STATE_KEY])
    cookies.set(IS_SNS_REGISTER_STATE_KEY, true, {path: '/'})
  }
  function setSnsLogin() {
    const cookies = useCookies([IS_SNS_REGISTER_STATE_KEY])
    cookies.set(IS_SNS_REGISTER_STATE_KEY, null, {path: '/', maxAge: 0})
  }

  function setRegisterErrors(errorMessages = {}) {
    errors.value = errorMessages;
  }
  function getRegisterErrors() {
    return errors.value;
  }
  return {
    isShowModalRegister,
    showModalRegister,
    hideModalRegister,
    getModalStep,

    isSnsRegister,
    setSnsRegister,
    setSnsLogin,

    setRegisterErrors,
    getRegisterErrors
  };
}