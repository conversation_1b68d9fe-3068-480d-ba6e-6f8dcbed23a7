export const useNgWordsStore = async () => {
  const ngWords = useState('ngWords', () => null);
  const isLoading = useState('isNgWordsLoading', () => false);
  const config = useRuntimeConfig();

  // 初回だけロード
  if (! ngWords.value && ! isLoading.value) {
    isLoading.value = true;
    const { data } = await useFetch(config.public.s3PublicUrlNgWord as string, { server: false });
    isLoading.value = false;
    ngWords.value = data.value;
  }

  return {ngWords, isLoading};
};