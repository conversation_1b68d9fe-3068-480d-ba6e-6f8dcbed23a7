import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useResetPassword = () => {
  const { mutate } = useResetPasswordMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const update = async (token: string, password: string) => {
    try {
      const result = await mutate({token: token, password: password})
      if (result?.data?.resetPassword) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}