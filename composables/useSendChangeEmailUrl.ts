import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useSendChangeEmailUrl = () => {
  const { mutate } = useSendChangeEmailUrlMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const action = async (email: string, password: string) => {
    try {
      const result = await mutate({email, password})
      if (result?.data?.sendChangeEmailUrl) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    action,
    errors,
  }
}