import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useSendResetPasswordUrl = () => {
  const { mutate } = useSendResetPasswordUrlMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const action = async (email: string) => {
    try {
      const result = await mutate({email})
      if (result?.data?.sendResetPasswordUrl) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    action,
    errors,
  }
}