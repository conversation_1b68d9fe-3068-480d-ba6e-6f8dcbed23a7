import type { GraphQLValidationErrors } from "@/utils/graphql";
import { MemberInput } from "./generated";

export const useTmpCreateMember = () => {
  const { mutate } = useTmpCreateMemberMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const create = async (input: MemberInput, member_regist_questionnaire: MemberRegistQuestionnaireInput[], wedding_info: WeddingInfoInput) => {
    try {
      await mutate({
        input,
        member_regist_questionnaire,
        wedding_info
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    create,
    errors,
  }
}