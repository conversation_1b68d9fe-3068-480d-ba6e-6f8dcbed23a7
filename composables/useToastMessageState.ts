import { ref } from 'vue';

// 他コンポーネントで流用 breadcrumbs の型
export type ToastMessage = { message: string; theme?: '' | 'success', shown: boolean; };

const toastMessages = ref([] as ToastMessage[]);

export const useToastMessageState = () => {
  // Toastメッセージを追加する関数
  function addToastMessage(toast: ToastMessage = { message: '', theme: '', shown: false }): void {
    toast.shown = false;
    toastMessages.value.push(toast);
  }

  // Toastメッセージを取得する関数
  function getToastMessages() {
    return toastMessages.value;
  }

  // Toastメッセージを取得する関数
  function getToastMessage(index:number) {
    return toastMessages.value?.[index];
  }

  // Toastメッセージを削除する関数
  function removeToastMessage(index:number) {
    toastMessages.value.splice(index, 1);
  }

  // Toastメッセージを削除する関数
  function shownToastMessage(index:number, timer:number) {
    if (! toastMessages.value?.[index]?.shown) {
      toastMessages.value[index].shown = true;
    }
    setTimeout(() => {
      removeToastMessage(index);
    }, timer);
  }

  return {
    addToastMessage,
    getToastMessages,
    getToastMessage,
    removeToastMessage,
    shownToastMessage
  };
}