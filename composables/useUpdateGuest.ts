export const useUpdateGuest = () => {
  const { mutate } = useUpdateGuestMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const update = async (
    input: UpdateGuestInput, 
    guests?: [UpdateGuestInput]|[], 
    free_item_values?: [UpdateGuestFreeItemValueInput]|[],
    guest_event_answers?: [UpdateGuestEventAnswerInput],
    guest_survey_answers?: [UpdateGuestSurveyAnswerInput],
  ) => {
    try {
      if (! guests) guests = [];
      if (! free_item_values) free_item_values = [];
      await mutate({
        input,
        guests,
        free_item_values,
        guest_event_answers,
        guest_survey_answers,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}