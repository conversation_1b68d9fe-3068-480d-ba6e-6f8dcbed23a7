import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useUpdateGuestGroup = () => {
  const { mutate } = useUpdateGuestGroupMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const update = async (input: GuestGroupInput) => {
    try {
      await mutate({
        input,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}