import { UpdateGuestListInput } from "./generated";
import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useUpdateGuestList = () => {
  const { mutate } = useUpdateGuestListMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const update = async (input: UpdateGuestListInput) => {
    try {
      await mutate({
        input,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}