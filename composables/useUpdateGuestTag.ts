import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useUpdateGuestTag = () => {
  const { mutate } = useUpdateGuestTagMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const update = async (input: GuestTagInput) => {
    try {
      await mutate({
        input,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}