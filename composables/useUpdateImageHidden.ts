export const useUpdateImageHidden = () => {
  const { mutate } = useUpdateImageHiddenMutation();
  const errors = ref(null as GraphQLValidationErrors | null);

  const updateImageHidden = async (uuid: string) => {
    try {
      await mutate({ uuid });
      return true;
    } catch (e) {
      errors.value = parseGraphQLErrors(e);
      return false;
    }
  };

  return {
    updateImageHidden,
    errors,
  };
};