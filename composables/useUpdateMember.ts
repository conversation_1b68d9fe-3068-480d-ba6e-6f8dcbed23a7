export const useUpdateMember = () => {
  const { mutate } = useUpdateMemberMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const update = async (
    input: UpdateMemberInput, 
    wedding_info?: UpdateWeddingInfoInput 
  ) => {
    try {
      await mutate({input, wedding_info})
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}