export const useUpdateMemberPassword = () => {
  const { mutate } = useUpdateMemberPasswordMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const update = async (
    password: string, 
    new_password: string, 
  ) => {
    try {
      await mutate({password, new_password})
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}