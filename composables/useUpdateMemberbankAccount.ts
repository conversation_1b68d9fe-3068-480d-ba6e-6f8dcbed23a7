import { UpdateMemberBankAccountInput } from "./generated";
import type { GraphQLValidationErrors } from "@/utils/graphql";

export const useUpdateMemberbankAccount = () => {
  const { mutate } = useUpdateMemberBankAccountMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const update = async (input: UpdateMemberBankAccountInput) => {
    try {
      await mutate({
        input,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}