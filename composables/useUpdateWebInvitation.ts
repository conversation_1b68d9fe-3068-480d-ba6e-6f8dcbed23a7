import { GraphQLValidationErrors } from "@/utils/graphql";
import { MutationUpdateWebInvitationArgs } from "./generated";

export const useUpdateWebInvitation = () => {
  const { mutate } = useUpdateWebInvitationMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const update = async (input: MutationUpdateWebInvitationArgs) => {
    try {
      await mutate(input)
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}