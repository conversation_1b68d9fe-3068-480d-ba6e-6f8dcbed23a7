import { MutationUploadImageArgs } from "./generated";
import { GraphQLValidationErrors } from "@/utils/graphql";

export const useUploadImage = () => {
  const { mutate } = useUploadImageMutation();

  const errors = ref(null as GraphQLValidationErrors | null);

  const update = async (variables: MutationUploadImageArgs) => {
    try {
      await mutate({
        variables,
      })
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors
  }
}