import type { GraphQLValidationErrors } from "@/utils/graphql";
import type { CreateFamilyProfileInput } from "./generated";

export const useUpsertFamilyProfile = () => {
  const { mutate } = useUpsertFamilyProfileMutation();
  const errors = ref(null as GraphQLValidationErrors | null);
  const update = async (
    is_save: boolean,
    input: CreateFamilyProfileInput,
  ) => {
    try {
      await mutate({is_save, input})
      return true
    } catch (e) {
      errors.value = parseGraphQLErrors(e)
      return false
    }
  }

  return {
    update,
    errors,
  }
}