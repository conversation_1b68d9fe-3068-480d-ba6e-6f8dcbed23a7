import { defineStore } from "pinia";

export const useWebInvitationSlideModal = defineStore('slideModalStore', {
  state: () => ({
    cropData: null,
    srcData: null,
    slideData: null,
    slideIndex: 0
  }),
  actions: {
    set(value:any) {
      this.cropData = value;
    },
    setUrl(value:any) {
      this.srcData = value;
    },
    setSlideData(value:any, index: number) {
      this.slideData = value;
      this.slideIndex = index;
    },
    close() {
      this.cropData = null;
      this.srcData = null;
      this.slideData = null;
      this.slideIndex = 0;
    }
  },
  getters: {
    isShowModal: (state) => {
      if(state.cropData == null && state.srcData == null && state.slideData == null){
        return false
      }else{
        return true
      }
    }
  }
})