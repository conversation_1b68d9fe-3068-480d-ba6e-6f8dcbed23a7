# Getting Start

## バックエンドの起動

## ローカル環境の起動

### .env の作成

.env.examle を参考に.env を作る

```
cp .env.example .env
```

docker-compose でバックエンドを起動している場合は、以下の環境変数を追加する

```.env
API_ENDPOINT=http://localhost/graphql
```

### 依存モジュールのインストール

yarn で依存関係をインストールする

```
yarn install
```

### ローカルの起動

以下のコマンドローカル環境を起動する
yarn dev

```
基本的に`http://localhost:3000`で表示できると思うが、ポートが埋まっている場合は、起動時のコンソール表示に表示されているURLにアクセスすること
```

#　開発の流れ

## API のよびだし

API を利用してデータの取得などを行う手順を説明します。

### 1. GraphQL を追加

API 呼び出しの際には、事前に graphql を記載する必要があります。

GraphQL ファイルは、`graphql`に保存されています。

**Query**
graphql/query/xxxx.graphql に格納されます。
データを取得する場合に使用します。

```
query GetManyStaff($name: String) {
  staffs(name: $name) {
    id
    name
  }
}
```

**Mutation**
graphql/mutation/xxxx.graphql に格納します。
データを登録・更新する際に使用します。

```
mutation CreateStaff(input: $input) {
    id
    name
    teams {
      id
      name
    }
}
```

**Fragment**
graphql/mutation/fragment/xxxFragment.graphql に格納します。
データの型を事前に定義できます。

```
fragment StaffFragment on Staff {
  id
  name
  deleted_at
  created_at
  updated_at
}
```

Fragment を利用することで以下のような書き方ができます。

```
query GetManyStaff($name: String) {
  staffs(name: $name) {
    ...StaffFragment
  }
}
```

```
query GetOneStaff($id: ID!) {
  staffs(id: $id) {
    ...StaffFragment
    teams {
      ...TeamFragment
    }
  }
}
```

```
mutation CreateStaff(input: $input) {
  id
    name
    teams {
      id
      name
    }
}
```

### 2. コードの生成

以下のコマンドで、`composables/generated.ts`にコードが生成されます。

上の例では主に以下の型が生成されます。

- StaffType
- useGetManyStaffQuery
- useGetOneStaffQuery
- useCreateStaffMutation
- CreateStaffInput

### 3. データを取得する

以下の様なコードでデータを取得できます。

```
const name = ref(null as string | null)
const { result } = useGetManyStaffQuery({
  name,
})
const staffs = computed(() => result.data?.staffs)
```

`result`に通信の結果が返りますが、実行直後は空になります。
また、上の例では、name の値が変わり次第、API への通信が動きます。

[他の返り値](https://v4.apollo.vuejs.org/api/use-query.html#parameters)
useXxxxxQuery には、以下のような返り値もあります。

- loading
- error
- variables
- refetch
- onResult
- onError

この内 onReuslt は、使用をおすすめしません。

apollo には、キャッシュ機構があるのですが、onResult は、キャッシュから呼ばれた際には、動かないです。
例えば以下のコードは、1 回目の onReuslt は、動きますが、２回めは、動きません。

```
// FetchPolicyがno-cache以外の場合
const { onReuslt } = useGetManyStaffQuery()
onReuslt(() => {
  console.log('ネットワーク通信になるので表示されます。')
})

const {onReuslt: onResult2} = useGetManyStaffQuery()
onReuslt2(() => {
  console.log('キャッシュのロードになるので表示されません。')
})

```

### 3. データを作成・更新・削除する

```
const input = reatctive({} asCreateStaffInput )
const errorMessage　= ref([])
const { mutate } = useCreateStaffMutation()

const onClick = async () => {
  try {
    const response = await mutate(
      input,
    )
    const newStaff = response.data?.createStaff;
    console.log(`新しいスタッフ(${newStaff.name})が登録されました。`)
  } catch(e) {
    // バリデーションエラーのメッセージを抽出
    errorMessage.value = parseGraphQLErromr(e)
  }
}

```
