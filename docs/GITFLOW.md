# Gitflow について

このプロジェクトでは、Gitflow を使用しています。

## Gitflow とは

以下の資料を参考にしてください。
[Git-flow をざっと整理してみた](https://dev.classmethod.jp/articles/introduce-git-flow/)
[GitHub Flow とは](https://qiita.com/tatane616/items/aec00cdc1b659761cf88)
[Gitflow(Github)](https://github.com/nvie/gitflow)

## 本 Project においてのフロー

Github Floww と Git Flow を混ぜたような Flow を取ります。

### 機能開発までのフロー

1. 開発リポジトリの develop ブランチをローカルに pullします
2. develop ブランチから、作業ブランチを切ります
   - ブランチの名前は feature/xxx
   - xxx 部分は、対応する backlog がある場合は、できるだけ課題キーを付けること
3. ローカルで開発、コミットして Fork したブランチへ Push します
4. develop リポジトリへ プルリクエストを出し、フィードバックを受けながら開発を進めます
   1. レビューワーに xxxx を入れること
   2. Assignee に自分の入れること
   3. わかりやすいタイトルをつけること
   4. 内容に簡単にどういうことをしたのか記載すること
5. レビュワーに承認されたら、最後のレビュー担当者が、develp リポジトリへマージします
6. デプロイ 🎉
   1. TODO: develop にマージされてら自動的にデプロイされます。

### リリース時のフロー

1. develop ブランチから main ブランチにプルリクエスト作成する
   - TODO: 命名規則(MainRelease_YYYY-MM-DD)など
2. レビューなどする(要検討)
3. プルリクエストを main にマージ
4. リリース前環境に自動デプロイ
   - 非開発メンバーが確認します。
5. main から producito ブランチにプルリクエスト作成
6. プルリクエストをマージ
7. 本番環境に自動デプロイされる

## Git flow コマンドのインストール

コマンドを利用するのが便利ですし、チェックもしてくれるので安全です。

[Git Flow コマンドのインストール](https://github.com/nvie/gitflow/wiki/Installation

Max OSX

```
brew install git-flow
```

特に問題なければ、以下のコマンドで初期化可能です。

```
git flow init -d
```

## faeture ブランチの切り方

```
git checkout develop
git pull
git flow feature xxxxxx
```

xxxx は、ブランチの作成ルールに従ってください。

## Feature ブランチに push するコマンド

```
git push --set-upstream origin feature/xxxx
```
