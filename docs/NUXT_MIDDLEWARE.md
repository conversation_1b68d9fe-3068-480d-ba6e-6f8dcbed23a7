# Middleware について

ページ遷移毎に処理を挟む場合に使います。
middleware ディレクトリに ts ファイルを作成することで自動的に使用可能になります。

```
export default defineNuxtRouteMiddleware((to, from) => {
  if (to.params.id === '1') {
    return abortNavigation()
  }
  // In a real app you would probably not redirect every route to `/`
  // however it is important to check `to.path` before redirecting or you
  // might get an infinite redirect loop
  if (to.path !== '/') {
    return navigateTo('/')
  }
})
```

## Global

すべてのページに middleware を組み込みたい場合。global というサフィックスをつけることで自動的に適用されます。

```
middleware/xxxx.global.ts
```

## 通常

global がつかない場合は、pages 側で呼び出しの設定をする必要があります。

```
<script setup lang="ts">
definePageMeta({
  middleware: [
    function (to, from) {
      // Custom inline middleware
    },
    'auth',
  ],
});
</script>
```

# Links

- [Nuxt3 Middleware Directory](https://nuxt.com/docs/guide/directory-structure/middleware)
