<template>
<Main
  contentSize="full"
>
  <template #main>
  <article class="article">
    <div class="contener-xs">
      <h2 class="cmn-title">{{ props?.error?.statusCode }}</h2>
      <div v-if="props?.error?.statusCode == 404">
        <h3 class="title">ページが見つかりませんでした</h3>
        <p>アクセス先が間違いない場合<br>移動もしくは削除された可能性があります</p>
      </div>
      <div v-else>
        <h3 class="title">問題が発生しました</h3>
        <p>一時的にアクセスできない状況です<br>しばらく時間を置いてから再度お試しください</p>
      </div>
      <NuxtLink to="/" class="link-text mb-40"><i class="material-icons icn-left">arrow_back</i> トップへ戻る</NuxtLink>
    </div>
  </article>
  </template>
</Main>
</template>

<script lang="ts" setup>
interface Props {
  error: Error;
}
const props = defineProps<Props>();
</script>

<style lang="scss" scoped>
.article {
  padding-top: 60px;
  text-align: center;
  @include sp {
    padding-top: 40px;
  }
  .cmn-title {
    font-size: 90px;
    line-height: 1;
    margin-bottom: 10px;
    margin-top: 0;
    @include sp {
      font-size: 60px;
      margin-bottom: 30px;
    }
  }
  .title {
    font-size: 24px;
    font-weight: 400;
    margin-bottom: 20px;
    @include sp {
      font-size: 20px;
    }
  }
  p {
    font-size: 14px;
    margin-bottom: 40px;
    @include sp {
      font-size: 12px;
    }
}
}
</style>