{"parserOptions": {"parser": "babel-es<PERSON>", "sourceType": "module"}, "extends": ["eslint:recommended", "plugin:vue/recommended", "plugin:prettier/recommended", "prettier/vue"], "plugins": ["vue"], "env": {"jest": true, "browser": true, "es6": true, "node": true}, "globals": {}, "rules": {"camelcase": [2, {"properties": "always"}], "quotes": [2, "single", {"avoidEscape": true}], "eqeqeq": [2, "always", {"null": "ignore"}], "prefer-const": 2, "vue/component-name-in-template-casing": "off", "vue/no-v-html": "off"}}