fragment GuestFragment on Guest {
  id
  email
  first_name
  last_name
  first_name_kana
  last_name_kana
  first_name_romaji
  last_name_romaji
  guest_type
  allergies
  allergy
  relationship_name
  relationship
  birthdate
  gender
  guest_honor
  postal_code
  prefecture
  city
  address
  building
  phone
  guest_title
  invitation_delivery
  message
  media_uuid
  media_type
  image_url
  web_invite_reply_datetime
  member_confirm_type
  payment_method
  gift_amount
  is_system_fee
  system_fee
  system_fee_rate
  total_amount
  settlement_amount
  created_at
  updated_at
  deleted_at
  guest_survey_answers {
    id
    question
    answer_content
  }
  guest_group {
    id
    name
  }
  guest_tags {
    id
    tag
    created_at
    updated_at
    deleted_at
  }
  guest_list {
    id
    name
    deleted_at
    created_at
    updated_at
  }
  guest_event_answers {
    id
    name
    date
    payment_amount
    attendance
    created_at
    updated_at
    deleted_at
  }
  guest_event_attendances:guest_event_attendance  {
    id
    name
    attendance
  }
  web_invitation {
    id
    name
    public_url
    password
    is_password
    block_settings
    editor_settings
    event_list
  }
  guest_free_item_values {
    id
    name
    content
    created_at
    updated_at
    deleted_at
  }
  parent_guest {
    id
    email
    first_name
    last_name
    first_name_kana
    last_name_kana
    first_name_romaji
    last_name_romaji
    guest_type
    allergies
    allergy
    relationship_name
    relationship
    birthdate
    gender
    guest_honor
    postal_code
    prefecture
    city
    address
    building
    phone
    guest_title
    invitation_delivery
    message
    media_uuid
    media_type
    image_url
    web_invite_reply_datetime
    member_confirm_type
    payment_method
    gift_amount
    is_system_fee
    system_fee
    system_fee_rate
    total_amount
    settlement_amount
    created_at
    updated_at
    deleted_at
    guest_event_answers {
      id
      name
      attendance
      payment_amount
    }
    guest_survey_answers {
      id
      question
      answer_content
    }
    guest_group {
      id
      name
    }
    guest_tags {
      id
      tag
      created_at
      updated_at
      deleted_at
    }
    guest_list {
      id
      name
      deleted_at
      created_at
      updated_at
    }
    guest_free_item_values {
      id
      name
      content
      created_at
      updated_at
      deleted_at
    }
  }
  children_guests {
    id
    email
    first_name
    last_name
    first_name_kana
    last_name_kana
    first_name_romaji
    last_name_romaji
    guest_type
    allergies
    allergy
    relationship_name
    relationship
    birthdate
    gender
    guest_honor
    postal_code
    prefecture
    city
    address
    building
    phone
    guest_title
    invitation_delivery
    message
    media_uuid
    media_type
    image_url
    web_invite_reply_datetime
    member_confirm_type
    payment_method
    gift_amount
    is_system_fee
    system_fee
    system_fee_rate
    total_amount
    settlement_amount
    created_at
    updated_at
    deleted_at
    guest_event_answers {
      id
      name
      attendance
      payment_amount
    }
    guest_survey_answers {
      id
      question
      answer_content
    }
    guest_group {
      id
      name
    }
    guest_tags {
      id
      tag
      created_at
      updated_at
      deleted_at
    }
    guest_list {
      id
      name
      deleted_at
      created_at
      updated_at
    }
    guest_free_item_values {
      id
      name
      content
      created_at
      updated_at
      deleted_at
    }
  }
}
