fragment GuestListFragment on GuestList {
  id
  name
  latest_guest_updated_at
  member {
    id
  }
  event_list
  web_invitations {
    id
    name
    public_url
    password
    is_password
    block_settings
    editor_settings
    event_list
  }
  guests {
    id
    email
    first_name
    last_name
    first_name_kana
    last_name_kana
    first_name_romaji
    last_name_romaji
    guest_type
    address
    allergies
    allergy
    relationship_name
    relationship
    guest_event_answers {
      name
      attendance
      payment_amount
    }
    birthdate
    building
    city
    gender
    guest_honor
    guest_tags {
      id
      tag
      created_at
      updated_at
      deleted_at
    }
    guest_group {
      id
      name
    }
    guest_title
    invitation_delivery
    message
    phone
    postal_code
    prefecture
    image_url
    web_invite_reply_datetime
    member_confirm_type
    payment_method
    gift_amount
    is_system_fee
    system_fee
    system_fee_rate
    total_amount
    settlement_amount
    parent_guest {
      id
    }
    children_guests {
      id
    }
    guest_event_answers {
      id
      name
      date
      payment_amount
      attendance
      created_at
      updated_at
      deleted_at
    }
    web_invitation {
      id
      name
      public_url
      password
      is_password
      block_settings
      editor_settings
    }
    created_at
    updated_at
    deleted_at
  }
  deleted_at
  created_at
  updated_at
}
