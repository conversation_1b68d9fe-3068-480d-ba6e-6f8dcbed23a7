fragment MWebInvitationTemplateFragment on MWebInvitationTemplate {
    id
    name
    description
    image
    web_invitation_template_blocks {
        block_name
        vue_component_name
        display_order
    }
    m_web_invitations {
        id
        css_code
        editor_settings_json
        image_aspect_settings_json
        m_web_invitation_template {
            id
        }
        web_invitation_design_images {
            id
            file_name
            file_path
        }
    }
}