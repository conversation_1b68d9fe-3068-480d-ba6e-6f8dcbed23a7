fragment MemberFragment on Member {
  id
  email
  last_name
  first_name
  last_name_kana
  first_name_kana
  last_name_romaji
  first_name_romaji
  birthdate
  sns_login_id
  alternate_member_number
  is_use_password
  isAccountAlertRegist
  wedding_info {
    id
    wedding_date
    wedding_venue
    guest_count
  }
  member_bank_account {
    id
		account_name
		account_number
		account_type
		bank_code
		bank_name
		branch_code
		branch_name
    phone
  }
  family_profiles {
    id
    last_name
    first_name
    order
    last_name_kana
    first_name_kana
    last_name_romaji
    first_name_romaji
    birth_date
    type
    deleted_at
    created_at
    updated_at
  }
  deleted_at
  created_at
  updated_at
}
