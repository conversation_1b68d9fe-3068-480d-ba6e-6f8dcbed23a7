fragment ProductFragment on Product {
  id
  name
  shipping_cost
  estimated_delivery_days
  is_sample_request_allowed
  is_use_component
  is_sales_period_specified
  sales_period_start
  sales_period_end
  is_reservation_period_specified
  reservation_period_start
  reservation_period_end
  product_inventory
  admin_notes
  product_description
  is_editor
  is_specification
  is_variation_specification
  product_type_code
  option_product_price_difference
  is_unpublished
  meta_title
  meta_description
  meta_canonical
  meta_keywords
  deleted_at
  created_at
  updated_at
  tags {
    id
    name
  }
  m_specification_products {
    id
    product_images {
      id
      uuid
      image_comment
    }
    m_web_invitations {
      id
      css_code
      image_aspect_settings_json
      editor_settings_json
      m_web_invitation_template {
        id
      }
      m_web_invitation_visual_block {
        id
      }
      web_invitation_design_images {
        id
        file_name
        file_path
      }
    }
  }
}
