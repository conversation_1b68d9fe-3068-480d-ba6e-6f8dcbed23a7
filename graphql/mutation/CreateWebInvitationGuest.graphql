mutation CreateWebInvitationGuest(
  $is_save: Boolean!, 
  $input: CreateGuestNoValidateInput!, 
  $guests: [CreateGuestNoValidateInput!]!, 
  $free_item_values: [CreateGuestFreeItemValueNoValidateInput!],
  $guest_event_answers: [CreateGuestEventAnswerNoValidateInput!],
  $guest_survey_answers: [CreateGuestSurveyAnswerInput!],
  $payment: PaymentInput, 
  ) {
  createWebInvitationGuest(
    is_save: $is_save, 
    input: $input,
    guest_event_answers: $guest_event_answers,
    guest_survey_answers: $guest_survey_answers,
    free_item_values: $free_item_values,
    guests: $guests, 
    payment: $payment
  ) {
    ...GuestFragment
  }
}
