mutation UpdateGuest(
  $input: UpdateGuestInput!, 
  $guests: [UpdateGuestInput!]!, 
  $free_item_values: [CreateGuestFreeItemValueInput!],
  $guest_event_answers: [CreateGuestEventAnswerInput!],
  $guest_survey_answers: [CreateGuestSurveyAnswerInput!],
  ) {
  updateGuest(
    input: $input,
    guest_event_answers: $guest_event_answers,
    guest_survey_answers: $guest_survey_answers,
    free_item_values: $free_item_values,
    guests: $guests, 
  ) {
    ...GuestFragment
  }
}