query GetManyGuest(
  $guest_list_id: String!
  $guest_event_attendances: [GuestEventAttendanceQueryInput!]
  $guest_name: String
  $web_invitation_id: String
  $guest_type: GuestTypeEnum
  $guest_group_ids: [String!]
  $guest_tag_ids: [String!]
  $orderBy: [QueryGuestsOrderByRelationOrderByClause!]
  $is_update_member_confirm_type: Boolean!
  ) {
  guests(
    guest_list_id: $guest_list_id
    guest_event_attendances: $guest_event_attendances
    guest_name: $guest_name
    web_invitation_id: $web_invitation_id
    guest_type: $guest_type
    guest_group_ids: $guest_group_ids
    guest_tag_ids: $guest_tag_ids
    orderBy: $orderBy
    is_update_member_confirm_type: $is_update_member_confirm_type
  ) {
    id
    parent_guest_id
    email
    first_name
    last_name
    first_name_kana
    last_name_kana
    first_name_romaji
    last_name_romaji
    guest_honor
    image_url
    guest_type
    allergies
    allergy
    guest_group_name
    web_invitation_id
    web_invite_reply_datetime
    member_confirm_type
    payment_method
    gift_amount
    is_system_fee
    system_fee
    system_fee_rate
    total_amount
    settlement_amount
    media_type
    media_uuid
    message
    created_at
    updated_at
    deleted_at
    guest_event_tags {
      id
      tag
    }
    guest_event_attendances {
      id
      name
      attendance
    }
  }
}
