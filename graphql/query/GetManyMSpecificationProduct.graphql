query GetManyMSpecificationProduct($orderBy: [QueryProductWebInvitationsOrderByOrderByClause!],$first: Int!, $page: Int, $productTagIds: [ID!]) {
  productWebInvitations(orderBy: $orderBy, first: $first, page: $page, product_tag_ids: $productTagIds) {
    data {
      id
      name
      product_description
      product_type_code
      tags {
        id
        name
      }
      m_specification_products {
        id
        product_images {
          id
          uuid
          image_comment
        }
        m_web_invitations {
          id
        }
      }
    }
    paginatorInfo {
      ...PaginatorFragment
    }
  }
  tagGroup(id: "9be15451-6be9-4249-b278-336b63cb302a"){
    id
    name
    tags {
      id
      image_url
      name
    }
  }
}