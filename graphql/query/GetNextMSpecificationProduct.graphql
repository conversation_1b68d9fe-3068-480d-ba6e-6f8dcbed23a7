query GetNextMSpecificationProduct($orderBy: [QueryProductWebInvitationsOrderByOrderByClause!],$first: Int!, $page: Int, $productTagIds: [ID!]) {
  productWebInvitations(orderBy: $orderBy, first: $first, page: $page, product_tag_ids: $productTagIds) {
    data {
      id
      name
      product_description
      product_type_code
      m_specification_products {
        id
        m_web_invitations {
          id
          css_code
          editor_settings_json
          image_aspect_settings_json
          m_web_invitation_template {
            id
          }
          m_web_invitation_visual_block {
            id
          }
          web_invitation_design_images {
            id
            file_name
            file_path
          }
        }
        product_images {
          id
          uuid
          image_comment
        }
      }
    }
    paginatorInfo {
      ...PaginatorFragment
    }
  }
}