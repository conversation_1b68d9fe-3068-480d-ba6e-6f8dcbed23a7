query GetOneWebInvitation($webInvitationId: ID!) {
  webInvitation(id: $webInvitationId) {
    id
    name
    is_public
    public_url
    is_password
    password
    block_settings
    editor_settings
    event_list
    has_pre_paid_guest
    scheduled_transfer_date
    created_at
    m_web_invitation {
      id
      css_code
      image_aspect_settings_json
      web_invitation_design_images {
        file_name
        file_path
        id
      }
      m_specification_product {
        product_images {
          id
          uuid
        }
        product {
          name
        }
      }
    }
    guest_list {
      id
      name
    }
    guests {
      id
      guest_type
      guest_event_answers {
        name
        attendance
        payment_amount
      }
      guest_title
      invitation_delivery
      message
      phone
      postal_code
      prefecture
      image_url
      web_invite_reply_datetime
      member_confirm_type
      payment_method
      gift_amount
      is_system_fee
      system_fee
      system_fee_rate
      total_amount
      settlement_amount
      guest_event_answers {
        id
        name
        date
        payment_amount
        attendance
        created_at
        updated_at
        deleted_at
      }
    }
  }
}
