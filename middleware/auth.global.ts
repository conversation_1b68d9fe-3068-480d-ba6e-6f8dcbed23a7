import { mySetCookie, myGetCookie, checkMustLoginUrl } from '../utils/functions';

export default defineNuxtRouteMiddleware(async (to, from) => {
  const { hasAccessToken, setAccessToken, getAccessToken } = await useAccessTokenState();

  // URLに adminToken があった場合は token を保存する
  if (to?.query?.adminToken) {
    setAccessToken(to?.query?.adminToken as string, 'admin');
    // かつ URLに userToken があった場合は token を保存する
    if (to?.query?.userToken) {
      setAccessToken(to?.query?.userToken as string, 'default');
      mySetCookie('isAdminUserLogin', '1');
      return navigateTo(removeQueryParam(removeQueryParam(to.fullPath, 'adminToken'), 'userToken'));
    }
    return navigateTo(removeQueryParam(to.fullPath, 'adminToken'));
  }

  // admin でログインしてるか判断
  if (! hasAccessToken('admin') && /^\/admin/.test(to.fullPath)) {
    return navigateTo('/');
  }

  // member でログインしてるか判断
  if (! hasAccessToken('default')) {
    // 以下に閲覧不可の条件を記載する
    if (checkMustLoginUrl(to.fullPath)) {
      // ログイン後に遷移するURLを保存
      mySetCookie(REDIRECT_PATH_COOKIE_KEY, to.fullPath);
      return navigateTo('/login')
    }
  } else {
    // ログイン後に遷移するURLがあれば遷移
    const url = myGetCookie(REDIRECT_PATH_COOKIE_KEY);
    if (url) {
      // ログイン後に遷移するURLを削除
      mySetCookie(REDIRECT_PATH_COOKIE_KEY, '');
      return navigateTo(url);
    }

    // ログイン時状態なら、ログイン画面を出さない
    if (/^\/login/.test(to.fullPath) && ! (/^\/login\/logout/.test(to.fullPath) || /externalLogin/.test(to.fullPath))) {
      return navigateTo('/mypage');
    }
  }
})