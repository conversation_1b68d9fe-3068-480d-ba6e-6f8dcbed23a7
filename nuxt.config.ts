import vue from "@vitejs/plugin-vue";

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  // ルートごとに設定します
  routeRules: {
    "/": { ssr: true, prerender: false },
    "/sitemap": { ssr: true, prerender: false },
    "/support": { ssr: true, prerender: false },
    "/guide": { ssr: true, prerender: false },
    "/about": { ssr: true, prerender: false },
    "/question": { ssr: true, prerender: false },
    "/question/**": { ssr: true, prerender: false },
    "/contact": { ssr: true, prerender: false },
    "/privacy": { ssr: true, prerender: false },
    "/terms": { ssr: true, prerender: false },
    // "/products/webinvitation": { ssr: true, prerender: false },
    // ↓ noindex が ssr じゃないと入らないので ssr にしてます
    "/wi/**": { ssr: true, prerender: false },
    "/lp/**": {
      ssr: true,
      prerender: true,
      headers: {
        'X-LP-Page': 'true'
      },
      // LPページでは共通のSEO設定を無効化
      experimentalNoScripts: false
    },
    "/**": { ssr: false, prerender: false },
  },
  css: [
    '@/assets/styles/common.scss',
    '@/assets/styles/animation.scss'
  ],

  modules: [
    '@pinia/nuxt',
    'nuxt-gtag'
  ],

  gtag: {
    id: 'G-8Z4XKVS9YT' // 測定ID
  },

  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width,initial-scale=1.0,maximum-scale=1.0',
      htmlAttrs: {
        lang: 'ja',
        prefix: 'og: http://ogp.me/ns#'
      },
      title: 'WEB招待状 「ふたりの人生をつなぐ。」Favori（ファヴォリ）',
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&family=Noto+Serif+JP:wght@400;500;700&Cormorant:wght@400;500;700&Roboto:wght@400;700&family=Cinzel:wght@400..900&display=swap' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css?family=Lato%7CLato%3A300' },
        // ↓ app.vue で 動的読み込みに変更
        // { rel: 'stylesheet', href: 'https://fonts.googleapis.com/icon?family=Material+Icons%7CMaterial+Icons+Outlined%7CMaterial+Symbols+Outlined:opsz,wght,FILL,GRAD@24,200,0,0' }
      ],
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1,maximum-scale=1.0' },
      ],
      script: [
        {
          hid: 'gtm-script',
          innerHTML: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0], j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','${process.env.GTM_ID}');`,
          type: 'text/javascript',
          charset: 'utf-8'
        }
      ],
      __dangerouslyDisableSanitizersByTagID: {
        'gtm-script': ['innerHTML'],
      }
    },
    render: {
      csp: {
        addMeta: true,
        policy: {
          'script-src': ["'self'", 'https://www.googletagmanager.com'],
          'img-src': ["'self'", 'https://www.googletagmanager.com'],
          'style-src': ["'self'", "'unsafe-inline'"]
        },
        unsafeInline: true
      }
    }
  },
  runtimeConfig: {
    // ここに書くとサーバ側でのみ使用できます。
    //apiSecret: '123',
    public: {
      // ここに書くとクライアント側でも使用できます。
      gtmId: process.env.GTM_ID || 'GTM-WQPWFC3S',
      apiEndpoint: process.env.API_ENDPOINT || 'http://localhost/graphql',
      serverEnvironment: process.env.SERVER_ENVIRONMENT || 'develop',
      s3PublicUrlAllergy: process.env.S3_PUBLIC_URL_ALLERGY,
      s3PublicUrlEventNameList: process.env.S3_PUBLIC_URL_EVENT_NAME_LIST,
      s3PublicUrlExampleContentSeason: process.env.S3_PUBLIC_URL_EXAMPLE_CONTENT_SEASON,
      s3PublicUrlExampleSeasonalGreetings: process.env.S3_PUBLIC_URL_EXAMPLE_SEASONAL_GREETINGS_SEASON,
      s3PublicUrlNgWord: process.env.S3_PUBLIC_URL_NG_WORD,
      apollo: {
        //https://apollo.nuxtjs.org/getting-started/quick-start
        clients: {
          default: {
            uri: process.env.API_ENDPOINT || 'http://localhost/graphql',
          },
          admin: {
            uri: process.env.API_ENDPOINT || 'http://localhost/graphql',
          },
        },
      },
      microCms: {
        //https://apollo.nuxtjs.org/getting-started/quick-start
        clients: {
          default: {
            uri: process.env.MICROCMS_SERVICE_DOMAIN,
            apiKey: process.env.MICROCMS_API_KEY,
          }
        },
      },
      instagram: {
        access_token: process.env.INSTAGRAM_API_ACCESS_TOKEN,
        account_id: process.env.INSTAGRAM_API_ACCOUNT_ID,
      },
      rakuten: {
        payvault_url: process.env.RAKUTEN_PAYMENT_PAYVAULT_URL || 'https://stg-static-content.payment.global.rakuten.com/pv/payvault/V7/7.5/payvault.js',
        service_id: process.env.RAKUTEN_PAYMENT_SERVICE_ID || 'stg-all-webportal'
      },
      gmo: {
        mptoken_url: process.env.GMO_PG_MPTOKEN_URL || 'https://stg.static.mul-pay.jp/payment/js/mp-token.js',
        shop_id: process.env.GMO_PG_SHOP_ID || 'tshop00024104'
      },
      app: {
        // 事前支払いの有効 or 無効
        is_active_prepaid: (process.env?.IS_ACTIVE_PREPAID === 'false') ? false : true,
        accept_external_login_domein: process.env.ACCEPT_EXTERNAL_LOGIN_DOMEIN || 'https://www.favori-cloud.com',
        oldchar_api_endpoint: process.env.OLDCHAR_API_ENDPOINT || 'https://dev8695.favori.wedding/biz-glyph/base/oldchar',
        withdraw_backlink_id: process.env.WITHDRAW_BACKLINK_ID || 'jib7es3ad752',
      },
      bankCode: {
        apiKey: process.env.BANK_CODE_JP
      },
    },
  },
  buildModules: [
    '@nuxtjs/style-resources'
  ],
  vite: {
    css: {
      preprocessorOptions: {
        scss: {

          additionalData:
            '@import "@/assets/styles/_mixin.scss";\
                        @import "@/assets/styles/_theme.scss";'
        },
      },
    },
  },
  components: [
    { path: '~/components/atoms', prefix: '' },
    { path: '~/components/molecules', prefix: '' },
    { path: '~/components/organisms', prefix: '' },
    { path: '~/components/organisms/web-invitation', prefix: '' },
    { path: '~/components/templates', prefix: '' },
    '~/components'
  ],

  nitro: {
    rollupConfig: {
      // @ts-ignore
      plugins: [vue()],
    },
  },

  // 初期ローディングを非表示に
  loading: false,
  // nuxt-spa-loading を消す
  spaLoadingTemplate: false
})
