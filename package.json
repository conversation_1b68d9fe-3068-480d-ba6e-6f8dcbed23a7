{"name": "nuxt-app", "private": true, "scripts": {"build": "dotenv -e .env.develop nuxt build", "build:staging": "dotenv -e .env.staging nuxt build", "build:production": "dotenv -e .env.production nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "storybook": "storybook dev -p 6006", "postinstall": "nuxt prepare", "build-storybook": "storybook build", "chromatic": "npx chromatic --project-token=chpt_e98d88adcd61bd3 --exit-once-uploaded", "lint-js": "eslint --ext .js,.vue .", "lint-js:fix": "eslint --ext .js,.vue . --fix", "codegen": "graphql-codegen --require dotenv/config --config codegen.ts"}, "devDependencies": {"@babel/preset-env": "^7.21.5", "@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/client-preset": "^4.1.0", "@graphql-codegen/introspection": "^4.0.0", "@graphql-codegen/typescript": "^4.0.1", "@graphql-codegen/typescript-operations": "^4.0.1", "@graphql-codegen/typescript-vue-apollo": "^3.3.7", "@nuxtjs/dotenv": "^1.4.1", "@nuxtjs/gtm": "^2.4.0", "@nuxtjs/style-resources": "^1.2.1", "@storybook/addon-essentials": "^7.0.0", "@storybook/vue3": "^7.0.0", "@storybook/vue3-vite": "^7.0.0", "@types/apollo-upload-client": "^18.0.0", "@types/node": "^18", "@types/sanitize-html": "^2.13.0", "@vue/compiler-sfc": "^3.3.4", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "babel-eslint": "^10.1.0", "chromatic": "^6.17.4", "dotenv-cli": "^7.4.0", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.14.1", "nuxt": "^3.5.0", "nuxt-gtag": "1.0.0", "nuxt-sass-resources-loader": "^2.0.5", "prettier": "^2.8.8", "react": "^18.2.0", "react-dom": "^18.2.0", "sanitize-html": "^2.14.0", "sass": "^1.62.1", "sass-resources-loader": "^2.2.5", "storybook": "^7.0.0", "storybook-addon-nuxt": "^1.3.0", "style-loader": "^3.3.3", "ts-loader": "^9.4.2", "vue-cropper": "^0.6.4", "vue-loader": "^17.0.1"}, "version": "", "readme": "ERROR: No README data found!", "_id": "nuxt-app@", "dependencies": {"@apollo/client": "^3.7.17", "@nuxtjs/google-gtag": "^1.0.4", "@pinia/nuxt": "^0.4.11", "@vue/apollo-composable": "^4.0.0-beta.8", "@vueform/multiselect": "^2.6.6", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.3", "@vuepic/vue-datepicker": "^8.2.0", "@vueuse/core": "^10.3.0", "@vueuse/integrations": "^10.3.0", "@vueuse/router": "^10.4.1", "apollo-upload-client": "^18.0.1", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "dayjs": "^1.11.9", "graphql": "^16.7.1", "hls.js": "^1.6.5", "pinia": "^2.1.6", "qrcode-vue3": "^1.7.1", "swiper": "^9.4.1", "universal-cookie": "^4.0.4", "vue-cropperjs": "^5.0.0", "vuedraggable": "^4.1.0", "yubinbango-core2": "^0.6.3"}}