<template>
  <div class="webInvitation">
    <Main
      :partsHidden="paramPartsHidden"
      :breadcrumbs="breadcrumbs"
      title="編集・招待状A">
      <template #main>
        <div class="webInvitation_selectedTheme">
          <div class="webInvitation_selectedTheme_wrap">
            <div class="webInvitation_selectedTheme_image">
              <div v-if="detailData?.product_images?.[0]?.src">
                <img :src="detailData?.product_images?.[0]?.src">
              </div>
            </div>
            <div class="webInvitation_selectedTheme_text">
              <div>{{ templateData?.name }}</div>
            </div>
          </div>
        </div>
        <EditorFormWrap
          ref="editorWrap"
          :form="form"
          :formUpdated="formUpdated"
          :aspect="imageAspectRatio"
          :isGuestAnswer="onCheckGuestAnswer"
          :isGuestPaid="onCheckGuestPaid"
          :isHasPrePaidGuest="hasPrePaidGuest"
          imageUploadType="admin"
          :error="error"
          :isLockSpecifiedPosition="isLockSpecifiedPosition"
          :maxMainVisualImages="maxMainVisualImages"
          :getAspectRatioForPosition="getAspectRatioForPosition"
          @sort="onUpdateSort"
          @change="onUpdateBlock"
          @change-shipping-date="onChangeShippingDate"
        ></EditorFormWrap>

        <ShowFooterBarFrow :data="modalFooterBarFrow" @onClickBtn0="onClickPreview()" @onClickBtn1="onClickSave()" />
      </template>

    </Main>
  </div>
</template>

<script lang="ts" setup>
import { required, email, helpers, minLength, maxLength, sameAs } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import { GraphQLValidationErrors } from "@/utils/graphql";
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();
import { useLoadingState } from '@/composables/useLoading';
const { showLoading, hideLoading } = useLoadingState();
const router = useRouter();
const route = useRoute();

// 画像の取得
import { extractUuids, setImages } from '@/composables/useGetImageFromUUID';

interface Props {
  errors: GraphQLValidationErrors | null;
}
const form = ref({});
const formUpdated = ref({});
const guests = ref();
const isShowSaveToast = ref(false);

// 画像の取得
const getImagesFromUUIDs = async (obj) => {
  let updateObj = toRaw(obj);
  let uuids = extractUuids(updateObj);
  const { getImages, refetch, imageLoading } = await useGetManyImages2(uuids);
  await refetch();
  watch(() => getImages, (images, prevImages) => {
    if (images.value) {
      const updatedObjWithImages = setImages(updateObj, images.value);
      detailData.value = updatedObjWithImages;
      if(updatedObjWithImages?.m_specification_products?.[0]?.m_web_invitations?.editor_settings_json){
        form.value = updatedObjWithImages?.m_specification_products?.[0]?.m_web_invitations?.editor_settings_json;
        formUpdated.value = updatedObjWithImages?.m_specification_products?.[0]?.m_web_invitations?.editor_settings_json;
      }
      return updatedObjWithImages;
    }
  }, {
    deep: true,
    immediate: true
  });
}

// 送金予定日の変更
const shippingDate = ref('');
const onChangeShippingDate = async (date: any) => {
  shippingDate.value = date;
}

// クエリパラメーターがある場合
let queryId = route?.params.id;
let getData: any;
if (queryId) {
  const { item } = useAdminProductWebInvitation(route?.params.id);
  getData = await item;
}
const templateData = ref({});
const input = ref({
  product: {
    id: queryId
  } as AdminUpdateProductinput,
  m_specification_products: [] as AdminUpdateMSpecificationProductInput[],
  product_tags: [] as AdminCreateProductTagInput[],
});

// 子要素のrefを受け取る
const editorWrap = ref(null);

// 更新API
const { create, errors:createErrors } = useCreateWebInvitation();
const { update, errors: updateErrors } = useAdminUpdateProduct();

// 保存ボタンクリック
const wholeError = ref();
const isSave = ref(false);
const isShowModalDateConfirm = ref(false);
const onClickSave = async () => {
  if (isSave.value){ return false }
  // 全体エラーをリセット
  error.value = '';

  // サーバサイドメッセージをリセット
  v$.value.$reset();
  v$.value.$clearExternalResults();

  await v$.value.$validate();

  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  showLoading();
  isSave.value = true;

  input.value.product.id = templateData.value.id;
  input.value.m_specification_products = [
    {
      id: templateData.value?.m_specification_products?.[0]?.id,
      m_web_invitation: {
        id: templateData.value?.m_specification_products?.[0]?.m_web_invitations?.id,
        editor_settings_json: formUpdated.value
      }
    }
  ];
  if(templateData?.value?.tags){
    templateData.value?.tags.forEach(tagItem => {
      input.value.product_tags.push({ tag_id: tagItem.id });
    });
  }

  const isSuccess = await update(
    input.value.product,
    input.value.m_specification_products,
    input.value.product_tags
  );

  hideLoading();
  window.close();
};

// プレビューボタンのクリック
const onClickPreview = async () => {
  // リンクの作成
  const previewLink = router.resolve({
    path: `/admin/webinvitations/preview/${route?.params.id}`
  });
  // セッションストレージにformデータを保存
  sessionStorage.setItem('webInvitationAdminFormData', JSON.stringify(formUpdated.value));
  window.open(previewLink.href, '_blank');
}

// #modalWindow01へのデータ
const modalFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "プレビュー",
          link: false,
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "保存する",
          link: false,
        },
      ],
    },
  ],
}

// ブロック設定
const sortUpdated = ref([]);
const sort = ref({
  sort: [
    {
      name: 'カウントダウン',
      id: 'countDown',
      visible: true
    },
    {
      name: '挨拶・メッセージ',
      id: 'message',
      visible: true
    },
    {
      name: '新郎・新婦プロフィール',
      id: 'profile',
      visible: true
    },
    {
      name: '写真ギャラリー',
      id: 'gallery',
      visible: true
    },
    {
      name: 'パーティー情報',
      id: 'information',
      visible: true
    },
    {
      name: '会費・ご祝儀の事前支払い',
      id: 'gift',
      visible: true
    },
    {
      name: 'フリー項目',
      id: 'freeField',
      visible: true
    },
    {
      name: '出欠フォーム',
      id: 'guestAnswer',
      visible: true
    }
  ]
});

//フォーム設定
const imageAspectRatio = ref({
  mainVisual: {
    width: 9,
    height: 16
  },
  message: {
    width: 16,
    height: 9
  },
  profile: {
    width: 1,
    height: 1
  },
  gallery: {
    width: 1,
    height: 1
  }
});

// デザイン設定から画像の縦横比情報を取得（管理画面用）
const designAspectRatio = computed(() => {
  const designSettings = detailData.value?.design_settings;
  if (designSettings) {
    try {
      const settings = typeof designSettings === 'string' ? JSON.parse(designSettings) : designSettings;
      return settings;
    } catch (error) {
      console.error('デザイン設定の解析に失敗しました:', error);
      return null;
    }
  }
  return null;
});

// 位置指定ありかどうかの判定
const isLockSpecifiedPosition = computed(() => {
  return designAspectRatio.value?.mainVisual?.isLockSpecifiedPosition === true;
});

// MV画像の最大枚数を取得
const maxMainVisualImages = computed(() => {
  if (isLockSpecifiedPosition.value && designAspectRatio.value?.mainVisual?.specifiedImages) {
    return designAspectRatio.value.mainVisual.specifiedImages.length;
  }
  return 3; // デフォルトは3枚
});

// 位置ごとのトリミング比率を取得
const getAspectRatioForPosition = (position: number) => {
  if (isLockSpecifiedPosition.value && designAspectRatio.value?.mainVisual?.specifiedImages?.[position]) {
    return designAspectRatio.value.mainVisual.specifiedImages[position];
  }
  // 位置指定なしの場合は従来の設定を使用
  return imageAspectRatio.value.mainVisual;
};

// if (!queryId) {
  form.value = {
    blocks: [
      {
        name: 'メインビジュアル',
        id: 'mainVisual',
        contents: {
          selectVisual: 'images',
          images: [],
          movie: [],
          groomName: '',
          brideName: ''
        }
      },
      {
        name: 'カウントダウン',
        id: 'countDown',
        visible: true,
        contents: {}
      },
      {
        name: '挨拶・メッセージ',
        id: 'message',
        visible: true,
        contents: {
          isShowVisual: false,
          selectVisual: 'images',
          images: [],
          movie: [],
          textAlign: 'center',
          message: ''
        }
      },
      {
        name: '新郎・新婦プロフィール',
        id: 'profile',
        visible: true,
        contents: [
          {
            isShowVisual: true,
            selectVisual: 'images',
            images: [],
            movie: [],
            name: '',
            isShowRole: true,
            role: '',
            textAlign: 'center',
            message: ''
          },
          {
            isShowVisual: true,
            selectVisual: 'images',
            images: [],
            movie: [],
            name: '',
            isShowRole: true,
            role: '',
            textAlign: 'center',
            message: ''
          }
        ]
      },
      {
        name: '写真ギャラリー',
        id: 'gallery',
        visible: true,
        contents: []
      },
      {
        name: 'パーティー情報',
        id: 'information',
        visible: true,
        contents: {
          date: null,
          type: 'ceremony_reception_same_venue',
          events: [
            {
              plans: [
                {
                  isShowPlan: true,
                  hour: '',
                  minute: ''
                },
                {
                  isShowPlan: true,
                  hour: '',
                  minute: ''
                },
                {
                  isShowPlan: true,
                  hour: '',
                  minute: ''
                }
              ],
              otherPlans: [
                {
                  isShowPlan: true,
                  hour: '',
                  minute: ''
                },
                {
                  isShowPlan: true,
                  hour: '',
                  minute: ''
                },
                {
                  isShowPlan: true,
                  hour: '',
                  minute: ''
                }
              ],
              venue: '',
              venue_kana: '',
              zip: '',
              address: '',
              isShowMaps: false,
              tel: '',
              url: '',
              feeOption: 'gift_system',
              feeAmount: ['', '']
            }
          ]
        }
      },
      {
        name: '会費・ご祝儀の事前支払い',
        id: 'gift',
        visible: true,
        contents: {
          isUseGift: null,
          isUseInVenue: true,
          partySettings: [],
          isUseInAfterParty: true,
          message: '',
          textAlign: 'center',
          usageFee: 'host',
          scheduleDate: ''
        }
      },
      {
        name: 'フリー項目',
        id: 'freeField',
        visible: true,
        contents: []
      },
      {
        name: '出欠フォーム',
        id: 'guestAnswer',
        visible: true,
        contents: {
          selectList: [
            {
              title: '新郎新婦ゲスト選択',
              id: '',
              disabled: false,
              required: true,
              visible: true,
            },
            {
              title: 'お名前',
              id: '',
              disabled: false,
              required: true,
              visible: true,
            },
            {
              title: 'お名前（ふりがな）',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: 'お名前（ローマ字）',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: '関係性',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: '間柄',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: 'プロフィール写真',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: '性別',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: 'お誕生日',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: '住所',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: '電話番号',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: 'メールアドレス',
              id: '',
              disabled: false,
              required: true,
              visible: true,
            },
            {
              title: 'アレルギー項目の入力',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: 'お祝い画像・動画',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: 'お祝いメッセージ',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            },
            {
              title: '連名入力',
              id: '',
              disabled: false,
              required: false,
              visible: true,
            }
          ],
          questionnaire: [],
          limit: {
            date: '',
            setting: 1,
            textAlign: 'center',
            message: '',
          },
          attendance: {
            isHideAttendance: false,
            isHideSkip: false,
            isAddFields: false,
            fields: []
          }
        }
      }
    ]
  }
// }
formUpdated.value = JSON.parse(JSON.stringify(form.value));
sortUpdated.value = JSON.parse(JSON.stringify(sort.value));

interface DetailData {
  m_web_invitation?: {
    web_invitation_design_images?:{
      upload_file_name?: string | null
    }[]
  }
}
const detailData = ref<DetailData>({
  m_web_invitation: {
    web_invitation_design_images: [
      {
        upload_file_name: null
      }
    ]
  }
});
const hasPrePaidGuest = ref();
watch(getData, async (newVal) => {
  if (newVal){
    templateData.value = newVal;
  }
  if (newVal && newVal?.m_specification_products?.[0]?.m_web_invitations) {
    detailData.value = newVal;

    let editorSettings = newVal?.m_specification_products?.[0]?.m_web_invitations?.editor_settings_json;
    if (editorSettings) {
      form.value = newVal?.m_specification_products?.[0]?.m_web_invitations?.editor_settings_json;
      formUpdated.value = newVal?.m_specification_products?.[0]?.m_web_invitations?.editor_settings_json;
    }
    if (newVal?.m_specification_products?.[0]?.m_web_invitations?.image_aspect_settings_json) {
      imageAspectRatio.value = newVal?.m_specification_products?.[0]?.m_web_invitations?.image_aspect_settings_json;
    }
    if (newVal?.guests) {
      guests.value = newVal.guests;
    }
    if (newVal?.has_pre_paid_guest) {
      hasPrePaidGuest.value = newVal.has_pre_paid_guest;
    }
    await getImagesFromUUIDs(newVal);
  }
}, {
  deep: true,
  immediate: true
})

const onCheckGuestAnswer = computed(() => {
  if(!guests || !guests.value || guests.value.length <= 0) { return false };
  return true;
});
const onCheckGuestPaid = computed(() => {
  if(!guests || !guests.value || guests.value.length <= 0) { return false };
  guests.value.forEach((guest) => {
    if(guest.payment_method == 'ADVANCE_PAYMENT'){
      return true;
    }
  });
  return false;
});

const onUpdateBlock = (target: { key: string, value: string }) => {
  let formItem = JSON.parse(JSON.stringify(formUpdated.value));
  let blockIndex = formUpdated.value?.blocks.findIndex((element) => {
    return element.id == target.key;
  });
  formItem.blocks[blockIndex] = target.value;

  formUpdated.value = formItem;
}
const onUpdateSort = (target: any) => {
  if (target.length) {
    let formItem = JSON.parse(JSON.stringify(formUpdated.value));
    formItem.blocks = target;
    formUpdated.value = formItem;
  }
}

const onFilterUpdateBlock = (id: string) => {
  const block = formUpdated.value.blocks.find(block => block.id === id);
  return block ? block : null;
};

// 入力項目
const error = ref('');
const $externalResults = ref({} as GraphQLValidationErrors);
// バリデーションルール
const rules = computed(() => {
  let rules = {
    blocks: {
      $each: {
        $each: (block:any) => {
          return null
        }
      }
    }
  };
  return rules;
});
const v$ = useVuelidate(rules, formUpdated, { $externalResults });

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "ゲストリスト",
    link: '/mypage/guest-list',
  },
  {
    title: "ゲストリストを追加"
  }
];

const paramPartsHidden = {
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
}
</script>

<style lang="scss" scoped>
:deep(.contents-title) {
  display: none;
}
:deep(.breadcrumbs) {
  display: none;
}
:deep(.container) {
  margin-top: 0;
}
@include pc {
  :deep(.container) {
    .l-column1{
      padding-top: 0;
      max-width: 100%;
    }
  }
}
.webInvitation {
  &_selectedTheme {
    background: $color-lightgray;
    position: relative;
    z-index: 1;

    // @include sp {
    //   margin-top: -60px;
    // }

    &_wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 340px;
      margin: 0 auto;
      padding: 20px 20px 12px;
    }

    &_image {
      background: #ccc;
      display: block;
      width: 124px;
      aspect-ratio: 75 / 132;
      border: 1px solid #D9D9D9;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 124px;
        aspect-ratio: 75 / 133;
        object-fit: cover;
      }
    }
  }
}

:deep(.frowFooter) {
  @include sp {
    position: fixed;
    z-index: 800;
  }
}
</style>