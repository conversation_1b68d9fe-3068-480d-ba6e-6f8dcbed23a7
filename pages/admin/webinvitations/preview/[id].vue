<template>
  <div class="webInvitationView" v-if="form">
    <Loading v-if="isLoading"></Loading>
    <ViewFormWrap v-else :data="form.blocks">
      <div class="blocks">
        <div class="block" v-for="(block, index) in form.blocks" :key="index">
          <component
            :is="onSetComponent(block.id)"
            :data="block.contents"
            :informationData="onFilterBlock('information')"
            :visible="block.visible"
            :isPreview="true"
            :webInvitationData="{
              event_list: ['挙式・披露宴'],
              editor_settings: form
            }"
            :isLockSpecifiedPosition="isLockSpecifiedPosition"
            :maxMainVisualImages="maxMainVisualImages"
            :getAspectRatioForPosition="getAspectRatioForPosition"
          ></component>
        </div>
      </div>
    </ViewFormWrap>
  </div>
</template>

<script lang="ts" setup>
import { markRaw } from 'vue'
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import { CreateGuestListInput } from 'composables/generated';
import { GraphQLValidationErrors } from "@/utils/graphql";

const router = useRouter();
const route = useRoute();
const form = ref();
const isLoading = ref(true);

// デザイン設定から画像の縦横比情報を取得（管理画面プレビュー用）
const designAspectRatio = ref(null);
const isLockSpecifiedPosition = computed(() => {
  return designAspectRatio.value?.mainVisual?.isLockSpecifiedPosition === true;
});
const maxMainVisualImages = computed(() => {
  if (isLockSpecifiedPosition.value && designAspectRatio.value?.mainVisual?.specifiedImages) {
    return designAspectRatio.value.mainVisual.specifiedImages.length;
  }
  return 3;
});
const getAspectRatioForPosition = (position: number) => {
  if (isLockSpecifiedPosition.value && designAspectRatio.value?.mainVisual?.specifiedImages?.[position]) {
    return designAspectRatio.value.mainVisual.specifiedImages[position];
  }
  return { width: 9, height: 16 };
};

onMounted(() => {
  const previewData = sessionStorage.getItem('webInvitationAdminFormData');
  form.value = JSON.parse(previewData);
});

let queryId = route?.params.id;
let getData: any;
if (queryId) {
  const { item } = useAdminProductWebInvitation(route?.params.id);
  getData = await item;
}

watch(() => getData, (newVal) => {
  if(newVal.value){
    const style = document.createElement('style');
    style.textContent = newVal.value?.m_specification_products?.[0]?.m_web_invitations?.css_code;
    document.head.appendChild(style);
    
    // デザイン設定を取得
    const designSettings = newVal.value?.design_settings;
    if (designSettings) {
      try {
        designAspectRatio.value = typeof designSettings === 'string' ? JSON.parse(designSettings) : designSettings;
      } catch (error) {
        console.error('デザイン設定の解析に失敗しました:', error);
        designAspectRatio.value = null;
      }
    }
    
    isLoading.value = false;
  }
}, {
  deep: true,
  immediate: true
});

const onFilterBlock = (id: String) => {
  let item = form?.value?.blocks.filter((element) => {
    return element.id == id;
  });
  if (!item) {
    return null
  }
  return item[0];
}

const state = reactive({
  mainVisual: markRaw(resolveComponent('ViewFormBlockMainVisual') as Component),
  countDown: markRaw(resolveComponent('ViewFormBlockCountDown') as Component),
  message: markRaw(resolveComponent('ViewFormBlockMessage') as Component),
  profile: markRaw(resolveComponent('ViewFormBlockProfile') as Component),
  gallery: markRaw(resolveComponent('ViewFormBlockGallery') as Component),
  information: markRaw(resolveComponent('ViewFormBlockInformation') as Component),
  gift: markRaw(resolveComponent('ViewFormBlockGift') as Component),
  freeField: markRaw(resolveComponent('ViewFormBlockFreeField') as Component),
  guestAnswer: markRaw(resolveComponent('ViewFormBlockGuestAnswer') as Component)
});

const onSetComponent = (id: String) => {
  return state[id];
}
</script>

<style lang="scss">
@import 'assets/css/webinvitation/common/style.scss';
</style>
