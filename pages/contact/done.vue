<template>
    <Main
			:breadcrumbs="breadcrumbs"
			:partsHidden="paramPartsHidden" 
			title="お問い合わせ"
    >
        <template #main>
            <div class="wrapAddGuestList">
                <div class="form-input">
                    <div class="conf-info-content">送信完了しました </div>

                    <div class="ft-16 comp-input sp_cmn-aligncenter">このたびは お問い合わせ頂き<br class="sp_only">誠にありがとうございます </div>
                    <div class="ft-16 comp-input sp_cmn-aligncenter">ご記入頂いたメールアドレスへ <br class="sp_only">自動返信の確認メールをお送りしております </div>

                    <div class="ft-14 mt-30 mb-30 comp-input">しばらく経ってもメールが届かない場合は 入力頂いたメールアドレスが間違っているか 迷惑メールフォルダに振り分けられている可能性がございます <br class="sp_only">また ドメイン指定をされている場合は 「@favori.wedding」からのメールが受信できるようあらかじめ設定をお願いいたします </div>
                    <div class="cmn-aligncenter mb-30">
                        <NuxtLink to="/" class="link-text"><i class="material-icons icn-left">arrow_back</i> トップへ戻る</NuxtLink>
                    </div>
                </div>
            </div>
        </template>
    </Main>
</template>
    
<script lang="ts" setup>
import { ref } from 'vue';
import { required, email, maxLength, helpers, sameAs, requiredUnless } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";

// レイアウト設定
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "お問い合わせ",
  },
];
const paramPartsHidden = {
  spHeaderTitle: true,
}
</script>
    
<style lang="scss" scoped>
    .searchform .row {
        margin-bottom: 50px;
    }
		:deep(.container) {
			.l-column1 {
				padding-top: 30px;
				padding-bottom: 30px;
			}
		}
    .content {
        text-align: center;
				font-size: 14px;
    }

    .wrapAddGuestList {
        padding: 60px 20px 0;
        max-width: 640px;
        margin: 0 auto;
        line-height: 1.5;
        @media (max-width: 1100px) {
            // margin-right: 10px;
            // margin-left: 10px;
        }
        @include sp {
            padding-top: 20px;
        }
    }
		.btn-input {
			background: #2f587c;
			color: #fff;
		}
    .conf-info {
			margin-bottom: 20px;
			color: #b18a3e;
			font-size: 18px;
    }
		.conf-info-content {
			text-align: center;
			margin-bottom: 20px;
      color: #b18a3e;
      font-size: 24px;
		}
    .ft-24 {
        font-size: 24px;
    }
    .ft-16 {
        font-size: 16px;
    }
    .ft-14 {
        font-size: 14px;
    }
    a.close {
        text-decoration: none;
    }
    .text-center {
        text-align: center;
    }
    .btn.btn-block {
        margin: 0 auto;
        max-width: 740px;
    }
		.row-close {
			margin-top: 50px;
			@media(max-width: 680px) {
				margin-top: 30px;
			}
		}
		.comp-input {
			text-align: center;
			@media(max-width: 680px) {
				text-align: left;
			}
		}
</style>