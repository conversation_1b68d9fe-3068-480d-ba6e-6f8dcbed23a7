<template>
    <Main
			:breadcrumbs="breadcrumbs"
			:partsHidden="paramPartsHidden" 
			title="お問い合わせ"
    >
        <template #main>
            <Loading v-if="isLoading" :fullscreen="true"></Loading>
            <div class="wrapAddGuestList">
                <form class="searchform"  v-if="mode == 'input'">
                    <div class="content">商品に関するご質問 ご相談は </div>
                    <div class="content mb-20">以下の必要事項をご記入の上 お問い合わせください </div>
                    <div class="row">
                        <InputText
                            title="姓"
                            :required="true"
                            placeholder="福永"
                            size="half"
                            :value="input.input.last_name"
                            :error="v$?.input?.last_name?.$errors?.[0]?.$message"
                            @input="input.input.last_name = $event.target.value"
                        />
                        <InputText
                            title="名"
                            :required="true"
                            placeholder="このみ"
                            size="half"
                            :value="input.input.first_name"
                            :error="v$?.input?.first_name?.$errors?.[0]?.$message"
                            @input="input.input.first_name = $event.target.value"
                        />
                    </div>
                    <div class="row">
                        <InputHiragana
                            title="せい"
                            :required="true"
                            placeholder="ふくなが"
                            size="half"
                            :value="input.input.last_name_kana"
                            :error="v$?.input?.last_name_kana?.$errors?.[0]?.$message"
                            @update="input.input.last_name_kana = $event"
                        />
                        <InputHiragana
                            title="めい"
                            :required="true"
                            placeholder="このみ"
                            size="half"
                            :value="input.input.first_name_kana"
                            :error="v$?.input?.first_name_kana?.$errors?.[0]?.$message"
                            @update="input.input.first_name_kana = $event"
                        />
                    </div>
                    <div class="row">
                        <InputEmail
                            :required="true"
                            title="メールアドレス"
                            size="full"
                            :placeholder="'<EMAIL>'"
                            :value="input.input.email"
                            :error="v$?.input?.email?.$errors?.[0]?.$message"
                            @update="input.input.email = $event"
                        />
                    </div>
                    <div class="row">
                        <InputEmail
                            :required="true"
                            title="メールアドレス（確認用）"
                            size="full"
                            :placeholder="'<EMAIL>'"
                            :value="input.input.confirm_email"
                            :error="v$?.input?.confirm_email?.$errors?.[0]?.$message"
                            @update="input.input.confirm_email = $event"
                        />
                    </div>
                    <div class="row">
                        <InputTel
                            title="電話番号"
                            placeholder="09012345678"
                            size="full"
                            :value="input?.input?.phone"
                            :error="v$?.input?.phone?.$errors?.[0]?.$message"
                            @update="input.input.phone = $event"
                        />
                    </div>
                    <div class="row">
                        <InputTextarea
                            :required="true"
                            :value="input.input.content"
                            title="お問い合わせ内容"
                            size="full"
                            placeholder="具体的にご記載ください "
                            :error="v$?.input?.content?.$errors?.[0]?.$message"
                            @input="input.input.content = $event.target.value"
                        />
                    </div>
                    <div class="row">
                        <InputCheckSingleRequired
                            label=""
                            :value="(input.input.policy) ? '1' : ''"
                            :checked="input.input.policy"
                            :error="v$?.input?.policy?.$errors?.[0]?.$message"
                            @change="($event == '1') ? input.input.policy = true : input.input.policy = null"
                        ><a class="cmn-link" href="/privacy" target="_blank">プライバシーポリシー</a>に同意する</InputCheckSingleRequired>
                    </div>
                    <div class="row text-center">
                        <button type="button" class="btn btn-secondary btn-block mt-16" @click="onClickConf()">入力内容を確認</button>
                    </div>
                </form>
                <div class="form-input" v-else-if="mode == 'conf'">
                    <div class="content">入力いただきました内容をご確認の上 ご送信ください </div>
                    <div class="conf-info mt-20">入力情報</div>
                    <ul class="list-info mb-40">
                        <li><dl>
                            <dt>お名前</dt>
                            <dd>{{ input.input.last_name }} {{ input.input.first_name }} 様</dd>
                        </dl></li>
                        <li><dl>
                            <dt>お名前（かな）</dt>
                            <dd>{{ input.input.last_name_kana }} {{ input.input.first_name_kana }}</dd>
                        </dl></li>
                        <li><dl>
                            <dt>メールアドレス</dt>
                            <dd>{{ input.input.email }}</dd>
                        </dl></li>
                        <li><dl>
                            <dt>電話番号</dt>
                            <dd>{{ input.input.phone }}</dd>
                        </dl></li>
                        <li><dl>
                            <dt>お問い合わせ内容</dt>
                            <dd class="preWrap">{{ input.input.content }}</dd>
                        </dl></li>
                    </ul>
                    <div class="row">
                        <button type="button" class="btn btn-secondary btn-block mt-16" @click="onClickSave()">送信する</button>
                    </div>
                    <div class="cmn-aligncenter link-text mt-30 mb-10" @click="onBack"><i class="material-icons icn-left">arrow_back</i>入力画面に戻る</div>
                </div>
            </div>
        </template>
    </Main>
</template>
    
<script lang="ts" setup>
import { ref } from 'vue';
import { required, email, maxLength, helpers, sameAs, requiredUnless } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";

const metaTitle = 'お問い合わせ｜WEB招待状 Favori（ファヴォリ）';
const metaDescription = 'WEB招待状 Favori（ファヴォリ）のお問い合わせフォームです。';
useHead({
  title: metaTitle,
  meta: [
    { hid: 'description', name: 'description', content: metaDescription },
    { hid: 'og:title', property: 'og:title', content: metaTitle },
    { hid: 'og:description', property: 'og:description', content: metaDescription },
    { hid: 'twitter:title', property: 'twitter:title', content: metaTitle },
    { hid: 'twitter:description', property: 'twitter:description', content: metaDescription },
  ],
})

const router = useRouter();

// レイアウト設定
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "お問い合わせ",
  },
];
const paramPartsHidden = {
  spHeaderTitle: true
}

interface InputData {
    first_name?: string;
    last_name?: string;
    first_name_kana?: string;
    last_name_kana?: string;
    email?: string;
    confirm_email?: string;
    phone?: string;
    content?: string;
    policy?: boolean | null;
}

const input = ref({
    input: {
        first_name: '',
        last_name: '',
        first_name_kana: '',
        last_name_kana: '',
        email: '',
        confirm_email: '',
        phone: '',
        content: '',
        policy: null,
    } as InputData,
});

const { $dayjs } : any = useNuxtApp();
const isLoading = ref(true);
const mode = ref('input' as string);

onMounted(async() => {
    scrollToTop();
    const { loginCheck, refetch: refetchLoginCheck } = useGetLoginCheck();
    const { member, refetch } = useGetOneMemberMe();
    await refetchLoginCheck();
    if(loginCheck.value?.loginCheck){
        await refetch();
        if (member.value?.memberMe?.first_name) input.value.input.first_name = member.value?.memberMe?.first_name;
        if (member.value?.memberMe?.last_name) input.value.input.last_name = member.value?.memberMe?.last_name;
        if (member.value?.memberMe?.first_name_kana) input.value.input.first_name_kana = member.value?.memberMe?.first_name_kana;
        if (member.value?.memberMe?.last_name_kana) input.value.input.last_name_kana = member.value?.memberMe?.last_name_kana;
        if (member.value?.memberMe?.email) input.value.input.email = member.value?.memberMe?.email;
    }
    isLoading.value = false;
});

const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

const rules = computed(() => {
    return {
        input: {
            last_name: { 
                required: helpers.withMessage(validationMessage.required('姓'), required),
                maxLength: helpers.withMessage(validationMessage.maxLength('姓', 255), maxLength(255)),
            },
            first_name: { 
                required: helpers.withMessage(validationMessage.required('名'), required),
                maxLength: helpers.withMessage(validationMessage.maxLength('名', 255), maxLength(255)),
            },
            last_name_kana: { 
                required: helpers.withMessage(validationMessage.required('せい'), required),
                maxLength: helpers.withMessage(validationMessage.maxLength('せい', 255), maxLength(255)),
				regex: helpers.withMessage(validationMessage.furiKana("せい"), helpers.regex(/^[ぁ-ゔ\s　]+$/u)),
            },
            first_name_kana: { 
                required: helpers.withMessage(validationMessage.required('めい'), required),
                maxLength: helpers.withMessage(validationMessage.maxLength('めい', 255), maxLength(255)),
				regex: helpers.withMessage(validationMessage.furiKana("めい"), helpers.regex(/^[ぁ-ゔ\s　]+$/u)),
            },
            email: { 
                required: helpers.withMessage(validationMessage.required('メールアドレス'), required), 
                maxLength: helpers.withMessage(validationMessage.maxLength('メールアドレス', 255), maxLength(255)),
                email: helpers.withMessage(validationMessage.email('メールアドレス'), email), 
            },
            confirm_email: { 
                required: helpers.withMessage(validationMessage.required('メールアドレス (確認用)'), required),
                maxLength: helpers.withMessage(validationMessage.maxLength('メールアドレス (確認用)', 255), maxLength(255)),
                email: helpers.withMessage(validationMessage.required('メールアドレス (確認用)'), email), 
                sameAs: helpers.withMessage('メールアドレス (確認用)が一致していません ', sameAs(input.value.input.email))
            },
            phone: {
                regex: helpers.withMessage(validationMessage.format("電話番号"), helpers.regex(/^0\d{9,10}$/)),
            },
            content: {
                required: helpers.withMessage(validationMessage.required('お問い合わせ内容'), required),
            },
            policy: {
							required: helpers.withMessage(validationMessage.required('プライバシーポリシーに同意する'), required)
            }
        },
    }
});

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { sendMail, errors } = useContactMail();

// 保存ボタンクリック
const onClickConf = async() => {
	isLoading.value = true;
    scrollToTop();
    // 全体エラーをリセット
    error.value = '';
    // サーバサイドメッセージをリセット
    v$.value.$clearExternalResults();
    // バリデーション実行
    await v$.value.$validate();
    if (v$.value.$error) {
        error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
        isLoading.value = false;
        return false;
    }

    mode.value = 'conf';
    isLoading.value = false;
}

//メール送信
const onClickSave = async() => {
    isLoading.value = true;
    scrollToTop();
    const isSuccess = await sendMail(
        input.value.input.first_name,
        input.value.input.last_name,
        input.value.input.first_name_kana,
        input.value.input.last_name_kana,
        input.value.input.email,
        input.value.input.phone,
        input.value.input.content,
    );
    //エラーの場合
    if (! isSuccess) {
        if (errors.value) $externalResults.value = errors.value
        if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0]?.input;
        mode.value = 'input';
        isLoading.value = false;
        return false;
    }
    // mode.value = 'comp';
    isLoading.value = false;
    router.push({ path: `/contact/done` })
};

//お問い合わせ内容ご確認
const newLine = (content:string) => {
    return content.replace(/\n/g, '<br>');
}

//戻るボタン
const onBack = () => {
    isLoading.value = true;
    scrollToTop();
    mode.value = 'input';
    isLoading.value = false;
}
</script>
    
<style lang="scss" scoped>
    .cmn-link {
        text-decoration: underline;
        &:hover{
            text-decoration: none;
        }
    }
    .searchform .row {
        margin-bottom: 50px;
    }
		:deep(.container) {
			.l-column1 {
				padding-top: 30px;
				padding-bottom: 30px;
			}
		}
    .content {
        text-align: center;
				font-size: 14px;
    }

    .wrapAddGuestList {
        padding: 60px 20px 0;
        max-width: 640px;
        margin: 0 auto;
        line-height: 1.5;
        @media (max-width: 1100px) {
            // margin-right: 10px;
            // margin-left: 10px;
        }
        @include sp {
            padding-top: 20px;
        }
    }
		.btn-input {
			background: #2f587c;
			color: #fff;
		}
    .conf-info {
			margin-bottom: 20px;
			color: #b18a3e;
			font-size: 18px;
    }
		.conf-info-content {
			text-align: center;
			margin-bottom: 20px;
      color: #b18a3e;
      font-size: 24px;
		}
    .ft-24 {
        font-size: 24px;
    }
    .ft-16 {
        font-size: 16px;
    }
    .ft-14 {
        font-size: 14px;
    }
    a.close {
        text-decoration: none;
    }
    .text-center {
        text-align: center;
    }
    .btn.btn-block {
        margin: 0 auto;
        max-width: 460px;
    }
		.row-close {
			margin-top: 50px;
			@media(max-width: 680px) {
				margin-top: 30px;
			}
		}
		.comp-input {
			text-align: center;
			@media(max-width: 680px) {
				text-align: left;
			}
		}
</style>