<template>
  <Main
    :partsHidden="{
      spLogo: true,
      spH1Title: true,
      spBreadcrumbs: true,
      ShowFooterTop: true,
      FixedFooter: true,
    }" 
    :breadcrumbs="breadcrumbs"
    :backlink="backlink"
    title="メールアドレスを変更"
    >
    <template #main>
      <Loading v-if="isLoading" :fullscreen="true"></Loading>
      <div class="contener-xs" style="padding-top: 40px;">
        <p v-if="errorToken" class="input-error mb-40 cmn-aligncenter">{{ errorToken }}</p>
        <NuxtLink to="/mypage/" class="btn btn-secondary btn-block btn-md mb-25">マイページへ</NuxtLink>
      </div>
    </template>
  </Main>
  </template>

<script lang="ts" setup>
import { ref } from 'vue';

const route = useRoute();
const router = useRouter();

// 全体エラー
const errorToken = ref('')

// 更新中のLoading
const isLoading = ref(true);

// 更新API
const { action, errors } = useCheckChangeEmailUrl();

onMounted(async () => {
  // 全体エラーをリセット
  errorToken.value = '';
  const isSuccess = await action(route.params.token as string);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) errorToken.value = errors.value?.v$?.[0];
  } else {
    // 登録完了
    router.push({ path: '/email/change/done' })
  }
});
</script>