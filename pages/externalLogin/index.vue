<template>
<Main
  :partsHidden="{
    ShowFooterTop: true,
    FixedFooter: true,
  }" 
  title="ログイン"
  >
  <template #main>
    <div class="pageLogin">
      <ModalLoginBox
        :isExternalLogin="true"
      ></ModalLoginBox>
    </div>
  </template>
</Main>
</template>

<script lang="ts" setup>
const page = ref('top')
const emits = defineEmits<{
  (e: 'changePage', value: string): void;
}>();
</script>

<style lang="scss">
.pageLogin {
  padding-top: 20px;
  .wrap {
    max-width: 420px;
    color: #666;
    margin: 0 auto;
    padding: 0 20px 40px;
    .loading {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(#fff, .9);
      z-index: 9000;
      &:after {
        position: absolute;
        top: 50%;
        display: block;
        text-align: center;
        left: 0;
        right: 0;
        margin-top: -1em; 
      }
    }
  }
  .btn-2col {
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
    > a {
      width: 49%;
    }
  }
  .inputChecks {
    label {
      display: block;
      padding: 0;
      margin: 0;
      margin-bottom: 30px;
    }
  }
}
</style>