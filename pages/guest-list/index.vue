<template>
<Main
  :partsHidden="{
      ShowFooterTop: true,
      FixedFooter: true,
      spBackTop: false
  }" 
  >
  <template #main>
    <div class="cmn-aligncenter wrap">
      <p class="color-accent mb-20">ゲストからの回答を気軽に確認<br>名簿としてもご利用いただけます</p>
      <p class="mb-36 size--sm">ご利用には会員登録・ログインが必要です。</p>
      <div style="max-width: 266px;" class="mx-auto mb-30">
        <button type="button" class="btn btn-secondary btn-block" @click="showModalRegister()">新規会員登録へ</button>
      </div>
      <p class="mb-20 title-bd-center size--sm"><span>すでに会員の方はこちら</span></p>
      <div style="max-width: 266px;" class="mx-auto">
        <button type="button" class="btn btn-primary-outline btn-block" @click="showModalLogin()">ログインする</button>
      </div>
    </div>
  </template>
</Main>
</template>

<script lang="ts" setup>
const { showModalRegister } = useModalRegisterState();
const { showModalLogin } = useModalLoginState();
</script>

<style lang="scss" scoped>
.wrap {
  // padding-top: 60px;
  // margin-top: -20px;
  padding: 40px 20px 0;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  min-height: calc(100dvh - 116px);
  @include sp {
    min-height: 0;
  }
}
.title-bd-center {
  position: relative;
  > span {
    background: #fff;
    position: relative;
    z-index: 2;
    display: inline-block;
    padding: 0 20px;
  }
  &:after {
    content: " ";
    height: 1px;
    background: #D9D9D9;
    display: block;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    z-index: 1;
  }
}
</style>