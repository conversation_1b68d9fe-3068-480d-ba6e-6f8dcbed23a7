<template>
	<Main
  :partsHidden="paramPartsHidden"
  :breadcrumbs="breadcrumbs"
  title="ご利用ガイド"
  addClass="l-column1-nowrap"
  >
    <template #main>
      <article class="article-guides">
        <div class="article-guides_flex">
          <div class="article-guides_menu">
            <a @click="isMenuSelect = 0" href="#">ご利用の流れ</a>
            <a @click="isMenuSelect = 1" href="#">編集操作ガイド</a>
          </div>
          <div class="article-guides_wrap">
            <div class="article-guides_select">
              <InputSelect
                size="full"
                :options="[
                  {
                    label: 'ご利用の流れ',
                    value: 0
                  },
                  {
                    label: '編集操作ガイド',
                    value: 1
                  }
                ]"
                :value="isMenuSelect"
                @change="isMenuSelect = $event.target.value"
              />
            </div>
            <div v-if="isMenuSelect == 0" class="article-guides_section article-flows">
              <div class="article-flows_title">
                WEB招待状のご利用はとっても簡単な<br>4つのステップ
              </div>
              <ul class="article-flows_steps">
                <li class="step">
                  <a href="#step1">
                    <div class="step_img">
                      <img src="@/assets/images/guide/icon-guide_step_1.svg" alt="">
                    </div>
                    <div class="step_text">
                      <span>Step.1</span>
                      招待状デザインを選択
                    </div>
                  </a>
                </li>
                <li class="step">
                  <a href="#step2">
                    <div class="step_img">
                      <img src="@/assets/images/guide/icon-guide_step_2.svg" alt="">
                    </div>
                    <div class="step_text">
                      <span>Step.2</span>
                      招待状の詳細情報を入力
                    </div>
                  </a>
                </li>
                <li class="step">
                  <a href="#step3">
                    <div class="step_img">
                      <img src="@/assets/images/guide/icon-guide_step_3.svg" alt="">
                    </div>
                    <div class="step_text">
                      <span>Step.3</span>
                      ゲストへ招待状を送付
                    </div>
                  </a>
                </li>
                <li class="step">
                  <a href="#step4">
                    <div class="step_img">
                      <img src="@/assets/images/guide/icon-guide_step_4.svg" alt="">
                    </div>
                    <div class="step_text">
                      <span>Step.4</span>
                      ゲストからの回答を確認
                    </div>
                  </a>
                </li>
              </ul>

              <section>
                <h2 id="step1">Step.1 招待状デザインを選択</h2>
                <h3>デザインを選ぶ</h3>
                <p>デザイン一覧で好きなデザインを見つけたら「このデザインで作る」ボタンをクリックします。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_step1_1_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_step1_1_pc.jpg" alt="">
              </section>

              <section>
                <h2 id="step2">Step.2 招待状の詳細情報を入力</h2>
                <h3>日時と場所の設定</h3>
                <p>招待状を作成する際には、日時や場所、主催者の情報など、多くの詳細情報が必要です。Favoriではこれらを簡単に入力できます。</p>

                <h3>新郎新婦のプロフィール入力</h3>
                <p>主催者の名前や連絡先は、ゲストが参加する上で重要な情報です。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_step2_1_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_step2_1_pc.jpg" alt="">

                <h3>パーティー情報の登録</h3>
                <p>開催日や挙式・披露宴などの受付時間や開始時間の登録が可能。他にも会場情報の登録や2次会などの他のパーティー情報も追加することができます。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_step2_2_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_step2_2_pc.jpg" alt="">

                <h3>会費・ご祝儀の事前支払い設定</h3>
                <div class="soon mb-10" v-if="! isActivePrepaid">Comming Soon ...</div>
                <p>ゲストが事前に会費やご祝儀をクレジットカードで支払うことができます。システム利用料の負担設定や案内文の作成が可能。</p>

                <!-- 事前支払い機能の停止アラート ここから -->
                <div v-if="isPrepaymentPausePeriodRef" class="alert_prepaymentPause">
                  <img src="@/assets/images/icon-exclamation.svg" alt="">
                  <span>事前支払いサービスは一時停止させていただいております。<br>(2月10日12時 以前に作成いただいたWEB招待状では変わらずご利用可能です。)<br>詳しくは<a href="https://favori.wedding/information/buom21258j" target="_blank">こちら</a></span>
                </div>
                <!-- 事前支払い機能の停止アラート ここまで -->

                <img class="is-sp" src="@/assets/images/guide/guide_step2_3_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_step2_3_pc.jpg" alt="">

                <h3>出欠フォームの設定</h3>
                <p>回答期限の設定や案内文を作成ができます。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_step2_4_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_step2_4_pc.jpg" alt="">
              </section>

              <section>
                <h2 id="step3">Step.3 ゲストへ招待状を送付</h2>
                <h3>メールでの送付</h3>
                <p>メールは最も一般的な送付方法です。テンプレートを用いて簡単に送れます。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_step3_1_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_step3_1_pc.jpg" alt="">

                <h3>SNSでの送付</h3>
                <p>SNSは手軽に多くの人に招待状を送ることができます。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_step3_2_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_step3_2_pc.jpg" alt="">
              </section>

              <section>
                <h2 id="step4">Step.4 ゲストからの回答を確認！</h2>
                <h3>リアルタイムでの回答確認</h3>
                <p>Favoriでは、ゲストからの出欠回答を簡単に管理できます。リアルタイムでの確認や名簿ダウンロードが可能です。<br>ゲストからの回答はリアルタイムで更新され、すぐに確認できます。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_step4_1_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_step4_1_pc.jpg" alt="">
              </section>

              <div class="article-flows_button">
                <ButtonMainColor
                  baseColor="accent"
                  size="md"
                  href="#"
                  @click="onClickGuide()"
                >
                  編集操作ガイドへ
                </ButtonMainColor>
              </div>
            </div>

            <div v-if="isMenuSelect == 1" class="article-guides_section article-edits">
              <div class="article-edits_toc_title">全般</div>
              <ul class="article-edits_toc">
                <li><a href="#edit1-1">保存・会員登録について</a></li>
              </ul>
              <div class="article-edits_toc_title">WEB招待状</div>
              <ul class="article-edits_toc">
                <li><a href="#edit2-1">メインビジュアル画像or動画</a></li>
                <li><a href="#edit2-2">カウントダウン</a></li>
                <li><a href="#edit2-3">挨拶・メッセージ</a></li>
                <li><a href="#edit2-4">新郎・新婦プロフィール</a></li>
                <li><a href="#edit2-5">写真ギャラリー</a></li>
                <li><a href="#edit2-6">会費・ご祝儀の事前支払い</a></li>
                <li><a href="#edit2-7">パーティー情報</a></li>
                <li><a href="#edit2-8">アンケート項目</a></li>
                <li><a href="#edit2-9">その他のご案内</a></li>
                <li><a href="#edit2-10">回答期限設定</a></li>
                <li><a href="#edit2-11">出欠フォーム</a></li>
              </ul>

              <section>
                <h2>全般</h2>
                <h3 id="edit1-1">保存・会員登録について</h3>
                <p>編集したデータを保存するには会員登録（メールアドレス＆パスワードのみ）が必須となります。新規会員登録ボタンよりご登録ください。</p>
              </section>

              <section>
                <h2>WEB招待状</h2>
                <h3 id="edit2-1">メインビジュアル画像or動画</h3>
                <p>メインビジュアルは招待状の第一印象を決定づけます。静止画または動画をアップロードして、招待状に個性を加えましょう。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_1_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_1_pc.jpg" alt="">

                <h3 id="edit2-2">カウントダウン</h3>
                <p>挙式日（開催日）までのカウントダウンが表示できます。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_2_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_2_pc.jpg" alt="">

                <h3 id="edit2-3">挨拶・メッセージ</h3>
                <p>テキストボックスに直接入力することで、おふたりの挨拶やメッセージを編集。文字数制限は500文字。絵文字やHTMLタグは使用不可としてください。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_3_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_3_pc.jpg" alt="">

                <h3 id="edit2-4">新郎・新婦プロフィール</h3>
                <p>「プロフィール編集」から、おふたりの写真、名前、趣味やエピソードなどを入力。写真は正方形推奨、エピソードは300文字以内で。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_4_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_4_pc.jpg" alt="">

                <h3 id="edit2-5">写真ギャラリー</h3>
                <p>「写真追加」ボタンから、最大10枚までの写真をアップロード。各写真にはキャプションや説明文を追加可能。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_5_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_5_pc.jpg" alt="">

                <h3 id="edit2-6">会費・ご祝儀の事前支払い</h3>
                <p>「支払い設定」から、支払い方法や金額を設定。オンライン決済サービスへのリンクやQRコードを埋め込む際のガイドも提供。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_6_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_6_pc.jpg" alt="">

                <h3 id="edit2-7">パーティー情報</h3>
                <p>日時、場所、ドレスコード、アクセス方法などの詳細情報を入力。地図の埋め込みや交通手段の提案も可能。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_7_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_7_pc.jpg" alt="">

                <h3 id="edit2-8">アンケート項目</h3>
                <p>「アンケート追加」ボタンから、最大5つの質問を設定。選択式、自由入力式、評価スケールなど、質問形式を選択。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_8_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_8_pc.jpg" alt="">

                <h3 id="edit2-9">その他のご案内</h3>
                <p>「その他のご案内追加」から、任意の情報や注意事項を追加。テキスト、画像、リンクなどのコンテンツを組み合わせて作成。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_9_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_9_pc.jpg" alt="">

                <h3 id="edit2-10">回答期限設定</h3>
                <p>「期限設定」から、カレンダーを使用してゲストの回答期限を選択。期限後のフォームの動作（非表示、メッセージ表示など）も設定。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_10_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_10_pc.jpg" alt="">

                <h3 id="edit2-11">出欠フォーム</h3>
                <p>「フォーム設定」から、出席・欠席の選択肢、コメント欄の有無、アレルギー情報の入力欄など、フォームの詳細設定を行えます。</p>
                <img class="is-sp" src="@/assets/images/guide/guide_edit_11_sp.jpg" alt="">
                <img class="is-pc" src="@/assets/images/guide/guide_edit_11_pc.jpg" alt="">

              </section>

            </div>
          </div>
        </div>
      </article>
    </template>
  </Main>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const metaTitle = 'ご利用ガイド｜WEB招待状 Favori（ファヴォリ）';
const metaDescription = 'WEB招待状 Favori（ファヴォリ）のご利用ガイドです。ご利用の流れや編集操作ガイドをご覧いただけます。';
useHead({
  title: metaTitle,
  meta: [
    { hid: 'description', name: 'description', content: metaDescription },
    { hid: 'og:title', property: 'og:title', content: metaTitle },
    { hid: 'og:description', property: 'og:description', content: metaDescription },
    { hid: 'twitter:title', property: 'twitter:title', content: metaTitle },
    { hid: 'twitter:description', property: 'twitter:description', content: metaDescription },
  ],
})

const router = useRouter();
const route = useRoute();

const breadcrumbs = ref([
  {
    title: 'HOME',
    link: '/',
  },
  {
    title: 'ご利用ガイド',
  }
] as any);
const paramPartsHidden = {
  spBreadcrumbs: 'hidden',
  spH1Title: true,
  spLogo: true
}

const isMenuSelect = ref(0);

const onClickGuide = () => {
  isMenuSelect.value = 1;
  window.scroll({
    top: 0,
    behavior: 'smooth',
  });
};

const isPrepaymentPausePeriodRef = ref(false);
const isActivePrepaid = ref(false);
onMounted(() => {
  // API
  const { system } = useGetSystem();
  isPrepaymentPausePeriodRef.value = isPrepaymentPausePeriod(system, new Date());
  isActivePrepaid.value = useRuntimeConfig()?.public?.app?.is_active_prepaid;
});
</script>

<style lang="scss" scoped>
.is-pc{
  display: block;
  @include sp{
    display: none;
  }
}
.is-sp{
  display: none;
  @include sp{
    display: block;
  }
}
.article-flows{
  &_title{
    color: #AD871E;
    font-size: 24px;
    line-height: 1.3;
    text-align: center;
    margin: 0 auto 50px;
    @include sp{
      font-size: 20px;
      margin-top: 36px;
      margin-bottom: 36px;
    }
  }
  &_button{
    margin: 80px auto 0;
    max-width: 340px;
    width: 100%;
    @include sp{
      margin-top: 48px;
      max-width: auto;
    }
  }
  &_steps{
    list-style: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    position: relative;
    @include sp{
      flex-direction: column;
      &::before{
        content: '';
        display: block;
        width: 8px;
        height: 100%;
        background: #EFF8FF;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 36px;
        margin: auto;
        pointer-events: none;
      }
    }
    .step{
      position: relative;
      @include sp{
        width: 100%;
        & + .step{
          margin-top: 30px;
        }
      }
      &::before{
        content: '';
        display: block;
        width: 26px;
        height: 26px;
        background-image: url(@/assets/images/guide/icon-guide_step_arrow.svg);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
        transform: rotate(-90deg);
        position: absolute;
        top: 23px;
        right: -18px;
        @include sp{
          transform: rotate(0deg);
          width: 18px;
          height: 18px;
          top: auto;
          bottom: 8px;
          right: 0;
        }
      }
      &:last-child{
        &::before{
          display: none;
          @include sp{
            display: block;
          }
        }
        .step_img{
          @include sp{
            &::before{
              display: none;
            }
          }
        }
      }
      a{
        text-decoration: none;
        text-align: center;
        @include sp{
          display: flex;
          align-items: center;
          width: 100%;
        }
      }
      &_img{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 72px;
        height: 72px;
        border-radius: 50%;
        background: #2F587C;
        margin: 0 auto 16px;
        @include sp{
          margin: 0 18px 0 0;
          width: 56px;
          height: 56px;
        }
      }
      &_text{
        font-size: 14px;
        line-height: 1.2;
        color: #333;
        @include sp{
          text-align: left;
        }
        span{
          display: block;
          font-size: 12px;
          font-weight: bold;
          color: #AD871E;
          margin-bottom: 6px;
        }
      }
    }
  }
}
.article-edits{
  &_toc{
    margin-bottom: 40px;
    display: flex;
    flex-wrap: wrap;
    @include sp{
      margin-bottom: 20px;
      flex-direction: column;
      flex-wrap: nowrap;
    }
    &_title{
      color: #AD871E;
      font-size: 16px;
      line-height: 1.2;
      margin-bottom: 20px;
    }
    li{
      margin: 0 0 16px 0;
      @include sp{
        margin-bottom: 20px;
      }
      &:last-child{
        a{
          border-right: none;
        }
        @include sp{
          margin-bottom: 0;
        }
      }
    }
    a{
      text-decoration: none;
      color: #333;
      font-size: 16px;
      line-height: 1.6;
      padding: 0 16px;
      border-right: 1px solid #F4F4F4;
      @include sp{
        border: none;
        padding: 0 10px;
      }
      &:hover{
        text-decoration: underline;
      }
    }
  }
}
.article-guides{
  color: #333;
  font-size: 16px;
  line-height: 1.6;
  &_flex{
    display: flex;
    align-items: flex-start;
    justify-content: center;
    margin: 0 auto;
  }
  &_menu{
    padding: 0 20px;
    margin: 70px 0 0;
    max-width: 200px;
    width: 100%;
    @include sp{
      display: none;
    }
    a{
      display: block;
      padding: 10px 0;
      font-size: 12px;
      line-height: 1.2;
      text-decoration: none;
      color: #333;
      &:hover{
        text-decoration: underline;
      }
    }
  }
  &_select{
    display: none;
    @include sp{
      display: block;
      margin-top: 36px;
      margin-bottom: 40px;
    }
  }
  &_wrap{
    max-width: 795px;
    width: 100%;
    padding: 0 16px;
    margin: 0;
    @include pc{
      padding-top: 70px;
    }
  }
  &:deep(){
    h2{
      font-size: 20px;
      font-weight: bold;
      line-height: 1.2;
      padding: 23px 0;
      margin: 48px 0;
      border-top: 1px solid #2F587C;
      border-bottom: 1px solid #2F587C;
    }
    h3{
      font-size: 20px;
      font-weight: normal;
      line-height: 1.2;
      margin: 48px 0 20px;
      padding: 0 0 0 16px;
      position: relative;
      &::before{
        content: '';
        display: block;
        width: 4px;
        height: 28px;
        background: #2F587C;
        border-radius: 2px;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        margin: auto;
      }
    }
    p{
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 20px;
    }
    ol{
      margin: 0 0 15px;
      padding: 0 0 0 20px;
      list-style-type: decimal;
      li{
        list-style-type: decimal;
        & + li{
          margin-top: 1em;
        }
      }
    }
  }
}
</style>
