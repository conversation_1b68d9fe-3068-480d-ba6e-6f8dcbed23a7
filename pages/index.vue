<template>
<Main contentSize="full">
<template #main>
<Loading v-if="isLoading" :fullscreen="true" />
<article class="article-top">

<section class="section-mv" ref="mainVisual">
  <template v-if="pcCampaignBannerDatas?.[0]">
    <div class="mv-banner pc_only topBanner_pc_1">
      <a :href="pcCampaignBannerDatas?.[0]?.plan_text2" class="txt">
        <template v-if="pcCampaignBannerDatas?.[0]?.image?.url">
          <img :src="pcCampaignBannerDatas?.[0]?.image?.url" alt="">
        </template>
        <template v-else>
          {{pcCampaignBannerDatas?.[0]?.plan_text}}
        </template>
      </a>
    </div>
  </template>
  <template v-if="spCampaignBannerDatas?.[0]">
    <div class="mv-banner sp_only topBanner_sp_1">
      <a :href="spCampaignBannerDatas?.[0]?.plan_text2" class="txt topBanner_sp_1">
        <template v-if="spCampaignBannerDatas?.[0]?.image?.url">
          <img :src="spCampaignBannerDatas?.[0]?.image?.url" alt="">
        </template>
        <template v-else>
          {{spCampaignBannerDatas?.[0]?.plan_text}}
        </template>
      </a>
    </div>
  </template>
  <div class="swiper-mv">
    <Swiper
      :modules="[Navigation, Pagination, Autoplay, FreeMode]"
      :centeredSlides="true"
      :speed="500"
      :freeMode="true"
      :loop="true"
      :pagination="{ clickable: true }"
      :autoplay="{
        delay: 5000,
        disableOnInteraction: false,
      }">
      <SwiperSlide
        class="swiper-slide"
        v-for="(item, index) in pcMainVisualImages"
        :key="index"
      >
        <NuxtLink
          class="swiper-item"
          :to="item?.plan_text"
        >
          <img class="sp_only" :src="spMainVisualImages?.[index]?.image?.url" alt="">
          <img class="pc_only" :src="item?.image.url" alt="">
        </NuxtLink>
      </SwiperSlide>
    </Swiper>
  </div>
  <div class="mv_bottom">
    <div class="mv_bottom_wrap">
      <NuxtLink to="/products/webinvitation" class="btn btn-block btn-secondary-outline btn-bold">デザイン一覧を見る <i class="material-symbols-outlined icn-pull-right">chevron_right</i></NuxtLink>
    </div>
  </div>
</section>

<section class="section-support mb-40" style="padding-top: 0;">
  <div class="notice-title">
    <h2>お知らせ</h2>
  </div>
  <div class="box-news">
    <ul class="list-news">
      <li v-for="(content , index) in visibleNews" :key="index">
        <NuxtLink :to="'/information/'+content.id" class="news-link">
          <span class="date">{{ $dayjs(content.publishedAt).format('YYYY/MM/DD') }}</span>          
          <span class="cats">
            <span 
              class="cat" 
              v-for="(category , n) in content.category" :key="n"
              :style="getCategoryStyle(category)"
              >
                {{ category }}
            </span>
          </span>          
          <span class="title">{{ content.title }}</span>     
          <i class="material-icons icn-right">arrow_forward_ios</i>     
        </NuxtLink>
      </li>
    </ul>
    <div class="cmn-aligncenter mt-22 mb-10 fz-sm" v-if="newsData?.contents.length > 0">
      <NuxtLink to="/information" class="link-text">お知らせ一覧を見る</NuxtLink>
    </div>
  </div>
</section>

<hr class="section-divider">

<section class="section-top">
  <div class="box box01">
    <div class="box-body">
      <div class="inview--fadeInSlide" data-inview><span>
        <div class="icn"><img src="@/assets/images/top/top01-icn.svg" alt="簡単操作ですぐ作成"></div>
        <h2 class="title">簡単操作ですぐ作成</h2>
      </span></div>
      <div class="txt">
        <p>お名前や会場情報など掲載したい項目を<br class="pc_only">入力するだけでデザインが出来上がります</p>
      </div>
    </div>
    <div class="img inview--fadeIn" data-inview>
      <img class="sp_only" src="@/assets/images/top/top01_pc_v3.png" alt="簡単操作ですぐ作成">
      <img class="pc_only" src="@/assets/images/top/top01_pc_v3.png" style="max-width: 519px;" alt="簡単操作ですぐ作成">
    </div>
  </div>
  <div class="box box02">
    <div class="box-body">
      <div class="inview--fadeInUp" data-inview>
        <div class="icn"><img src="@/assets/images/top/top02-icn.svg" alt="デザイン満足度No.1"></div>
        <h2 class="title">デザイン満足度No.1</h2>
      </div>
      <div class="txt inview--fadeInUp" data-inview>
        <p>ふたりの門出をゲストに伝える特別な一通<br>100種以上のデザインから”ふたりらしい”がきっとみつかる</p>
        <NuxtLink to="/products/webinvitation" class="link-accent">デザイン一覧を見る</NuxtLink>
      </div>
    </div>
    <div class="img pc_only inview--fadeIn" data-inview>
      <img src="@/assets/images/top/top02_pc_v3.png" alt="高品質で豊富なデザイン">
    </div>
    <div class="sp_only imgs inview--fadeIn" data-inview>
    <Swiper
      :modules="[Navigation, Pagination, Autoplay, FreeMode]"
      :centeredSlides="true"
      :speed="500"
      :slidesPerView="3.5"
      :freeMode="true"
      :loop="true"
      :autoplay="{
        delay: 2000,
        disableOnInteraction: false,
      }">
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/01.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/02.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/03.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/04.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/05.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/06.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/07.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/08.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/09.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/10.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/11.png" alt="">
      </SwiperSlide>
      <SwiperSlide class="swiper-slide">
        <img src="@/assets/images/top/top02/12.png" alt="">
      </SwiperSlide>
    </Swiper>
    </div>
  </div>
  <div class="box box03">
    <div class="box-body">
      <div class="inview--fadeInSlide" data-inview><span>
        <div class="icn"><img src="@/assets/images/top/top03-icn.svg" alt="自動ゲストリスト作成"></div>
        <h2 class="title">自動ゲストリスト作成</h2>
      </span></div>
      <div class="txt">
        <p>ゲストからの返信内容から<br>自動でゲストリストが出来上がります！</p>
      </div>
    </div>
    <div class="img inview--fadeIn" data-inview>
      <img class="sp_only" src="@/assets/images/top/top03.png" alt="自動ゲストリスト作成">
      <img class="pc_only" src="@/assets/images/top/top03_pc.png" alt="自動ゲストリスト作成">
    </div>
  </div>
</section>

<section class="section-concept">
<h2 class="title-top inview--fadeInUp" data-inview>Concept</h2>
<h3 class="title-sub inview--fadeInUp" data-inview>全ての新郎・新婦さまが<br>
「絶対に使いたい」と思うサービスを</h3>
<div class="txt inview--fadeInUp" data-inview>
  <p>1万組を超える実績の中で<br class="sp_only">お客様の”声”を元に生まれたファヴォリ<br>
  新郎・新婦さまの<br>
  <span class="em">”こんなサービスが欲しかったんだ”</span><br>
  を形にしました！</p>
</div>
</section>

<section class="section-merit">
<h2 class="title-merit">
  <div class="title-merit_image inview--fadeInUp" data-inview>
    <img src="@/assets/images/top/merit_title.png" alt="Favoriならできる！">
  </div>
  <div class="title-merit_text">
    新郎さま 新婦さまに<br>メリットいっぱいの<br class="sp_only">機能たくさん！
  </div>
</h2>
<div class="box">
  <div class="box-body">
    <div>
      <div class="number"><img src="@/assets/images/top/merit_number_01.png" alt="01"></div>
      <h3 class="title">ゲスト情報を<br><span class="em">ファヴォリクラウド</span>に<br>そのまま<span class="em">引き継ぎ</span>！</h3>
    </div>
    <div class="img sp_only inview--fadeIn" data-inview><img src="@/assets/images/top/merit01.png" alt="作成されたゲストリストはファヴォリクラウドでも利用できる"></div>
    <div class="txt">
      <p>招待状や席次表などのペーパーアイテム作成や<br>引き出物宅配注文ができる姉妹サイトの<br>Favori CLOUDで<br class="pc_only">
        WEB招待状の<br class="sp_only">ゲストリストデータを<br class="pc_only">そのまま共有できます</p>
    </div>
  </div>
  <div class="img pc_only inview--fadeIn" data-inview><img src="@/assets/images/top/merit01_pc.png" alt="作成されたゲストリストはファヴォリクラウドでも利用できる"></div>
</div>
<div class="box">
  <div class="box-body">
    <div>
      <div class="number"><img src="@/assets/images/top/merit_number_02.png" alt="02"></div>
      <h3 class="title">ペーパーアイテムと<br class="pc_only">WEB招待状の<br class="sp_only">デザインを<br class="pc_only"><span class="em">お揃い</span>に🎵</h3>
    </div>
    <div class="img sp_only inview--fadeIn" data-inview><img src="@/assets/images/top/merit02_pc_v4.png?v=1.01" alt="ペーパーアイテムとWEB招待状のデザインをリンクできる"></div>
    <div class="txt">
      <p>Favori CLOUDのペーパーアイテムに<br>リンクしたデザインを多数ご用意<br>WEB招待状から当日のおもてなしまで<br>おふたりらしい世界観で統一することができます</p>
    </div>
  </div>
  <div class="img pc_only inview--fadeIn" data-inview><img src="@/assets/images/top/merit02_pc_v4.png?v=1.01" alt="ペーパーアイテムとWEB招待状のデザインをリンクできる"></div>
</div>
<div class="box">
  <div class="box-body">
    <div>
      <div class="number"><img src="@/assets/images/top/merit_number_03.png" alt="03"></div>
      <h3 class="title">ゲストにも、開催者にも<br>メリットたくさんの<br><span class="em">会費・ご祝儀の事前支払い</span>設定</h3>
    </div>
    <div class="img sp_only inview--fadeIn" data-inview><img src="@/assets/images/top/merit03.png" alt="ゲストにも 開催者にもメリットたくさんの会費・ご祝儀の事前支払い設定"></div>
    <div class="txt">
      <p>主催者の現金管理負担軽減や<br>ゲストは新札やご祝儀袋が不要になり<br>クレジットカード払いも可能でメリットたくさん</p>
      <!-- 事前支払い機能の停止アラート ここから -->
      <div v-if="isPrepaymentPausePeriod(system, new Date())" class="alert_prepaymentPause">
        <img src="@/assets/images/icon-exclamation.svg" alt="">
        <span>事前支払いサービスは一時停止させていただいております。<br>(2月10日12時 以前に作成いただいたWEB招待状では変わらずご利用可能です。)<br>詳しくは<a href="https://favori.wedding/information/buom21258j" target="_blank">こちら</a></span>
      </div>
      <!-- 事前支払い機能の停止アラート ここまで -->
    </div>
  </div>
  <div class="img pc_only inview--fadeIn" data-inview><img src="@/assets/images/top/merit03_pc.png" alt="ゲストにも 開催者にもメリットたくさんの会費・ご祝儀の事前支払い設定"></div>
</div>
<div class="box">
  <div class="box-body">
    <div>
      <div class="number"><img src="@/assets/images/top/merit_number_04.png" alt="04"></div>
      <h3 class="title">WEB招待状ならでは！<br>ゲストがお返事に<br><span class="em">動画や写真を添付</span>できる</h3>
    </div>
    <div class="img sp_only inview--fadeIn" data-inview><img src="@/assets/images/top/merit04.png" alt="ゲストにも 開催者にもメリットたくさんの会費・ご祝儀の事前支払い設定"></div>
    <div class="txt" data-inview>
      <p>ゲストからのお祝いメッセージを<br>そのまま保存できるので<br>ゲストリストが大切な宝物に</p>
    </div>
  </div>
  <div class="img pc_only inview--fadeIn" data-inview><img src="@/assets/images/top/merit04_pc.png" alt="ゲストにも 開催者にもメリットたくさんの会費・ご祝儀の事前支払い設定"></div>
</div>
</section>

<hr>

<section class="section-howtouse">
<h2 class="title-top">How to use</h2>
<div class="boxs-wrap">
<div class="boxs">
  <div class="box box-01">
    <div class="no">01.</div>
    <h3 class="title">デザインを選択</h3>
    <div class="img"><img src="@/assets/images/top/howtouse01.png" alt="デザインを選択"></div>
    <div class="txt"><p>好みのデザインを選ぶ</p></div>
  </div>
  <i class="icn-arrow material-symbols-outlined">arrow_forward_ios</i>
  <div class="box box-02">
    <div class="no">02.</div>
    <h3 class="title">情報を入力</h3>
    <div class="img"><img src="@/assets/images/top/howtouse02.png" alt="情報を入力"></div>
    <div class="txt"><p>招待状の情報を入力</p></div>
  </div>
  <i class="icn-arrow material-symbols-outlined">arrow_forward_ios</i>
  <div class="box box-03">
    <div class="no">03.</div>
    <h3 class="title">公開</h3>
    <div class="img"><img src="@/assets/images/top/howtouse03.png" alt="公開"></div>
    <div class="txt"><p>合言葉とURLを入力して公開</p></div>
  </div>
  <i class="icn-arrow material-symbols-outlined">arrow_forward_ios</i>
  <div class="box box-04">
    <div class="no">04.</div>
    <h3 class="title">ゲストに送る</h3>
    <div class="img"><img src="@/assets/images/top/howtouse04.png" alt="ゲストに送る"></div>
    <div class="txt"><p>LINEやメールで送信</p></div>
  </div>
</div>
</div>
</section>

<hr>

<section class="section-service">
  <h2 class="title-top-secondary">
    <span class="en">Service</span>
    <span class="ja">機能・サービス</span>
  </h2>
  <div class="boxs">
    <div class="box">
      <div class="img"><img src="@/assets/images/top/service01.jpg" alt="何種類でも作れる"></div>
      <div class="box_text">
        <h3 class="title"><span>何種類でも</span>作れる</h3>
        <div class="txt"><p>招待状は何種類でも作成いただけます<br>デザイン違いや送り分けも可能です</p></div>
      </div>
    </div>
    <div class="box">
      <div class="img"><img src="@/assets/images/top/service02.jpg" alt="カウントダウン"></div>
      <div class="box_text">
        <h3 class="title"><span>カウントダウン</span></h3>
        <div class="txt"><p>挙式当日までのカウントダウンを設置できます</p></div>
      </div>
    </div>
    <div class="box">
      <div class="img"><img src="@/assets/images/top/service03.jpg" alt="URLカスタマイズ"></div>
      <div class="box_text">
        <h3 class="title">URL<span>カスタマイズ</span></h3>
        <div class="txt"><p>招待状のURLは末尾の英字を自由にカスタマイズ可能です</p></div>
      </div>
    </div>
    <div class="box">
      <div class="img"><img src="@/assets/images/top/service04.jpg" alt="出欠確認が簡単"></div>
      <div class="box_text">
        <h3 class="title">出欠確認が<span>簡単</span></h3>
        <div class="txt"><p>1つの招待状で披露宴やその他の出欠確認ができます</p></div>
      </div>
    </div>
    <div class="box">
      <div class="img"><img src="@/assets/images/top/service05.jpg" alt="画像や動画の配置"></div>
      <div class="box_text">
        <h3 class="title"><span>画像</span>や<span>動画</span>の配置</h3>
        <div class="txt"><p>写真ギャラリーには画像が20枚まで<br>動画の場合は1本 配置できます</p></div>
      </div>
    </div>
    <div class="box">
      <div class="img"><img src="@/assets/images/top/service06.jpg" alt="必要な項目だけ"></div>
      <div class="box_text">
        <h3 class="title"><span>必要な</span>項目だけ</h3>
        <div class="txt"><p>必須表示の項目以外は表示/非表示を設定できます</p></div>
      </div>
    </div>
  </div>
  <div class="note">
    <p>その他の機能：<br>LINE,SNS,メールで招待状を送信  / ゲストの出欠や住所などを管理 / 出欠データのダウンロード / デザインのカスタマイズ / ゲスト回答項目の変更 / 招待状URLのカスタマイズ / 招待状の案内文テンプレート / 会場住所のGoogleマップ連携 / 会費の事前クレジットカード払い / 合言葉による招待状の閲覧制限 / ゲスト毎に招待状を複数作成 / ゲストへメールを一括送信 / QRコード作成</p>
  </div>
</section>

<hr>

<template v-if="instagramPosts.length">
  <section class="section-voice">
    <h2 class="title-top-secondary">
      <span class="en">Instagram</span>
      <span class="ja">インスタグラム</span>
    </h2>
    <ul>
      <li v-for="(post, i) in instagramPosts" :key="i">
        <a :href="post.permalink" target="_blank" class="link-img">
          <img v-if="post?.thumbnail_url" :src="post.thumbnail_url">
          <img v-else :src="post.media_url">
        </a>
      </li>
    </ul>
    <div class="cmn-aligncenter">
      <a href="https://www.instagram.com/favori_wedding/" target="_blank" class="link-text">結婚式に役立つ情報を発信中 <i class="icn-right material-symbols-outlined">chevron_right</i></a>
    </div>
  </section>
  <hr />
</template>

<section class="section-faq">
  <h2 class="title-top-secondary">
    <span class="en">FAQ</span>
    <span class="ja">よくあるご質問</span>
  </h2>
  <Loading v-if="isLoading" class="mt-20"></Loading>
  <ListFaqDetail
      v-else
      v-for="(content , index) in questionsData?.contents"
      :key="index"
      :question="content.question"
      :answer="content.summary"
      :link="'/question/'+content.id"
  ></ListFaqDetail>
  <div class="btn-wrap">
    <NuxtLink href="/question" class="btn btn-primary-outline btn-block">FAQ一覧</NuxtLink>
  </div>
</section>

<div class="btn-footer-fixed" :class="{ 'is-show': isScrolledPast }">
  <NuxtLink to="/products/webinvitation" class="btn btn-primary btn-block">まずは<span class="em">5</span>分で作ってみる <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink>
</div>

</article>
</template>
</Main>
</template>

<script lang="ts" setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Autoplay, FreeMode } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const isLoading = ref(true);

const microCms = new MicroCms();
const { data: questionsData , error, refresh} = await microCms.fetch('/question', {
  orders: 'publishedAt',
  filters: ['type[contains]特によくある質問']
}, false);
// たぶん microCms からとるのかな？ともうので 仮でFAQ出しておきます
const { data: newsData} = await microCms.fetch('/notice2', {
  orders: '-publishedAt',
  filters: 'page_type[contains]トップページ[or]page_type[contains]両方'
});

// API
const { system } = useGetSystem();

const itemsToShow = ref(5);
const visibleNews = computed(() => newsData?.value.contents.slice(0, itemsToShow.value));

const instagram = new Instagram();
const { posts:instagramPosts } = await instagram.posts(9);

// スクロール処理
const isScrolledPast = ref(false);
const mainVisual = ref(null);
const handleScroll = () => {
  if (mainVisual.value) {
    const mainVisualHeight = mainVisual.value.offsetHeight;
    isScrolledPast.value = window.scrollY > mainVisualHeight;
  }
};
onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  handleScroll(); // 初期ロード時にもチェックする
});
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});

// microCMSからメインビジュアル一覧を取得
const { data: microCmsData, error: microCmsError } = await microCms.fetch('/contents', {
  filters: 'code[equals]toppage_mainvisual'
}, false);
const pcMainVisualImages = ref([])
const spMainVisualImages = ref([])
watch(microCmsData, () => {
  let results = JSON.parse(JSON.stringify(microCmsData.value?.contents?.[0]?.item));
  if(results){
    pcMainVisualImages.value = results.filter(item => item.category.includes('PC'));
    spMainVisualImages.value = results.filter(item => item.category.includes('SP'));
  }
  isLoading.value = false;
});



const { data: microCmsCampaignBannerData, error: microCmsCampaignBannerError } = await microCms.fetch('/contents', {
  filters: 'code[equals]toppage_campaign_banner'
}, false);
const pcCampaignBannerDatas = ref([])
const spCampaignBannerDatas = ref([])
const dynamicStyles = ref([])
watch(microCmsCampaignBannerData, () => {
  let results = JSON.parse(JSON.stringify(microCmsCampaignBannerData.value?.contents?.[0]?.item));
  if(results){
    pcCampaignBannerDatas.value = results.filter(item => item.category.includes('PC'));
    spCampaignBannerDatas.value = results.filter(item => item.category.includes('SP'));
    dynamicStyles.value = [
      ...pcCampaignBannerDatas.value.map(banner => banner.plan_text3),
      ...spCampaignBannerDatas.value.map(banner => banner.plan_text3)
    ]
    applyDynamicStyles()
    applyDynamicStyles()
  }
});

const applyDynamicStyles = () => {
  // 既存のスタイルタグを削除
  const existingStyles = document.querySelectorAll('style[data-campaign-banner]')
  existingStyles.forEach(style => style.remove())

  // 新しいスタイルタグを作成し追加
  const styleElement = document.createElement('style')
  styleElement.setAttribute('data-campaign-banner', '')
  styleElement.textContent = dynamicStyles.value.join('\n')
  document.head.appendChild(styleElement)
}
onMounted(() => {
  dynamicStyles.value = [
    ...pcCampaignBannerDatas.value.map(banner => banner.plan_text3),
    ...spCampaignBannerDatas.value.map(banner => banner.plan_text3)
  ]
  applyDynamicStyles()
})
// dynamicStylesが変更されたら再適用
watch(dynamicStyles, applyDynamicStyles)

//アニメーション
onMounted(() => {
  let inviewTarget = document.querySelectorAll('[data-inview]');
  let callback = (entries: any) => {
    entries.forEach( async(entry: any) => {
      const target = entry.target;
      if (entry.isIntersecting) {
        target.classList.add('is-inview');
      }
    });
  }
  const observer = new IntersectionObserver(callback, {
    root: null,
    rootMargin: '-40% 0px',
    threshold: 0,
  });
  inviewTarget.forEach((target) => {
    observer.observe(target);
  });
});

</script>

<style lang="scss" scoped>
:deep(.main-contents){
  .contents .section-inner.l-column1 {
    padding-top: 0;
  }
}

:deep(){
  @include sp {
    .header {
      .header-inner {
        .nav-sp {
          bottom: 0;
        }
        .navi {
          position: absolute;
          top: 0;
        }
      }
    }
    .article-top {
      .btn-footer-fixed {
        bottom: 55px;
      }
    }
  }
}

.title-top {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.2;
  margin: 0 0 40px;
  text-align: center;
  color: #2F587C;
  &:after {
    content: " ";
    width: 150px;
    height: 1px;
    margin-left: auto;
    margin-right: auto;
    background: #2F587C;
    display: block;
    margin-top: 10px;
    @include sp {
      width: 50px;
      margin-top: 4px;
    }
  }
}
.title-top-secondary {
  color: var(--49454Fblack_Light, #49454F);
  text-align: center;
  font-family: Lora, $serif_font;
  font-size: 24px;
  font-weight: 400;
  line-height: 1.2; /* 28.8px */
  letter-spacing: 1.2px;
  margin: 0 0 32px;
  .ja {
    font-family: $serif_font;
    font-size: 12px;
    font-weight: 300;
    display: block;
    margin-top: 10px;
  }
}

.section-mv {
  overflow: hidden;
  .mv-banner {
    background: #FF7F2E;
    color: #FFF;
    font-size: 20px;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    &.pc_only {
      display: flex !important;
      @include sp{
        display: none !important;
      }
    }
    &.sp_only {
      display: none !important;
      @include sp{
        display: flex !important;
      }
    }
  }
  .swiper-mv {
    width: 100vw;
    --swiper-pagination-color: #2F587C;
    --swiper-pagination-bottom: 0;
    --swiper-pagination-color: #333;
    --swiper-pagination-bullet-inactive-color: #ccc;
    --swiper-pagination-bullet-width: 20px;
    --swiper-pagination-bullet-height: 2px;
    --swiper-pagination-bullet-border-radius: 0;
    --swiper-pagination-bullet-inactive-opacity: 1;
    .swiper-slide {
      margin-bottom: 0;
    }
    .swiper-item {
      display: block;
      width: 100vw;
      position: relative;
      overflow: hidden;
      &:after {
        content: " ";
        display: block;
        padding-top: 43%;
        @include sp {
          padding-top: 155%;
        }
      }
      img {
        height: 100%;
        width: auto;
        max-width: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        @supports ( object-fit: cover ) {
          height: 100%;
          width: 100%;
          top: 0;
          left: 0;
          transform: none;
          object-fit: cover;
        }
      }
    }
  }
  .mv_bottom{
    background: url(@/assets/images/top/mv-bottom.jpg) left bottom no-repeat;
    background-size: cover;
    padding: 24px;
    margin-bottom: 20px;
    &_wrap{
      max-width: 400px;
      width: 100%;
      margin: 0 auto;
      @include sp {
        max-width: 280px;
      }
    }
    :deep(.button--accent_reversal){
      font-weight: 600;
    }
  }
}

.section-top {
  padding: 80px 0 0;
  @include sp {
    padding-top: 45px;
  }
  .box {
    margin-bottom: 80px;
    text-align: center;
    @include sp {
      margin-bottom: 40px;
    }
    .icn {
      margin-bottom: 12px;
    }
    .title {
      color: var(--black, var(--text-black, #333));
      font-family: $serif_font;
      font-size: 36px;
      font-weight: 300;
      line-height: 1.4;
      letter-spacing: .04em;
      margin: 0;
      margin-bottom: 12px;
      @include sp {
        font-size: 22px;
      }
      // transition-delay: .5s;
    }
    .txt {
      margin-bottom: 24px;
      // transition-delay: 1s;
      .link-accent {
        font-size: 14px;
        font-weight: bold;
      }
      @include sp {
        max-width: 320px;
        margin-left: auto;
        margin-right: auto;
      }
    }
    p {
      color: var(--black, var(--text-black, #333));
      font-size: 16px;
      font-weight: 400;
      line-height: 1.5;
      letter-spacing: 0.02em;
      @include sp {
        font-size: 14px;
      }
    }
    .img {
      // transition-delay: 2s;
    }

    // .icn ,
    // .title ,
    // .txt {
    //   transform: translateY(80px);
    //   opacity: 0;
    //   transition: all 1s ease;
    // }
    // .img ,
    // .imgs {
    //   transform: scale(.9);
    //   opacity: 0;
    //   transition: all 1s ease;
    // }
    // .icn {
    //   transition-delay: 1s;
    // }
    // .title {
    //   transition-delay: 1s;
    // }
    // .txt {
    //   transition-delay: 2s;
    // }
    // .img ,
    // .imgs {
    //     transition-delay: 2.5s;
    // }
    // &.is-inview {
    //   .icn ,
    //   .title ,
    //   .txt ,
    //   .img ,
    //   .imgs {
    //     opacity: 1;
    //     transform: translateY(0) scale(1);
    //   }
    // }
  }
}

.box01 ,
.box03 {
  display: flex;
  align-items: center;
  justify-content: center;
  > * {
    padding: 0 15px;
  }
  @include sp {
    display: block;
    > * {
      padding: 0;
    }
  }
}
.box01{
  position: relative;
  z-index: 1;
  &::before{
    content: '';
    display: block;
    width: 870px;
    height: 486px;
    background: url(@/assets/images/top/top_bg_01_pc.jpg) left bottom no-repeat;
    position: absolute;
    bottom: -168px;
    right: 0;
    left: 0;
    margin: auto;
    z-index: -1;
    @include sp {
      display: none;
    }
  }
}
.section-top {
  .box03{
    margin-bottom: 150px;
    @include sp {
      margin-bottom: 56px;
    }
  }
}
.box03 {
  flex-direction: row-reverse;
  position: relative;
  z-index: 1;
  &::before{
    content: '';
    display: block;
    width: 495px;
    height: 382px;
    background: url(@/assets/images/top/top_bg_02_pc.png) center center no-repeat;
    position: absolute;
    bottom: -150px;
    right: 0;
    left: 220px;
    margin: auto;
    z-index: -1;
    @include sp {
      display: none;
    }
  }
  @include sp {
    .box-body {
      .txt {
        margin-bottom: 0;
        p {
          margin: 0;
        }
      }
    }
  }
}
.box02 {
  position: relative;
  .box-body {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
    @include sp {
      position: static;
      transform: none;
    }
  }
  &:after {
    content: " ";
    display: block;
    opacity: 0.5;
    background: linear-gradient(101deg, #FAFAFA 2.51%, #F5F3F1 14.03%, #DBE8F8 48.16%, #D4DCE6 68.2%, #EFEFF2 87.46%, #FFF 91.64%);
    position: absolute;
    height: 590px;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    @include sp {
      display: none;
    }
  }
  .img {
    position: relative;
    z-index: 2;
    img {
      margin-left: auto;
      margin-right: auto;
    }
  }

  .swiper-wrapper {
    .swiper-slide {
      padding: 5vw 0;
      img {
        width: 100%;
        transition: 0.35s ease;
        transform: scale(.95);
        transform-origin: center center;
        border-radius: 20px;
        overflow: hidden;
      }
    }
    .swiper-slide.swiper-slide-active {
      margin: 0 10px;
      img {
        border-radius: 20px;
        transform: scale(1.15);
      }
    }
  }
}

.section-concept {
  text-align: center;
  color: var(--white-100, #FFF);
  background: url(@/assets/images/top/concept_pc.jpg) center center no-repeat;
  @include sp {
    background-image: url(@/assets/images/top/concept.jpg);
  }
  background-size: cover;
  padding: 120px 0;
  .title-top {
    color: #fff;
    &:after {
      background: #fff;
      opacity: .6;
    }
  }
  .title-sub {
    font-family: $serif_font;
    font-size: 24px;
    font-style: normal;
    font-weight: 300;
    line-height: 1.5;
    letter-spacing: 0.05em;
    margin: 0 0 40px;
    @include sp {
      font-size: 20px;
    }
  }
  .txt {
    p {
      margin: 0;
      color: var(--white-100, #FFF);
      font-size: 16px;
      font-weight: 400;
      line-height: 1.6; /* 25.6px */
      .em {
        font-weight: 700;
        font-weight: bold;
        font-size: 28px;
        display: inline-block;
        @include sp {
          font-size: 16px;
        }
      }
    }
  }
}

.title-merit{
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 0 52px;
  &_image{
    max-width: 140px;
    width: 100%;
    @include sp {
      max-width: 115px;
    }
  }
  &_text{
    color: #333;
    font-size: 32px;
    font-weight: normal;
    line-height: 1.4;
    font-family: $serif_font;
    @include sp {
      font-size: 24px;
      padding-right: 18px;
    }
  }
}
.section-merit {
  // max-width: 1020px;
  padding: 52px 0;
  // margin-left: auto;
  // margin-right: auto;
  background: #fff;
  @include sp {
    padding-bottom: 1px;
  }
  .box {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-bottom: 50px;
    @include sp {
      display: block;
      margin-bottom: 69px;
    }
    > * {
      padding: 0 15px;
      @include sp {
        padding: 0;
      }
    }
    .number {
      max-width: 80px;
      width: 100%;
      margin: 0 auto;
      text-align: center;
    }
    .title {
      color: var(--black, #333);
      text-align: center;
      font-family: $serif_font;
      font-size: 28px;
      font-style: normal;
      font-weight: 300;
      line-height: 140%; /* 39.2px */
      letter-spacing: .04em;
      transition-delay: .5s;
      @include sp {
        font-size: 22px;
      }
      .em {
        color: #2F587C;
        font-weight: 600;
      }
    }
    .txt p {
      // max-width: 330px;
      color: #333;
      line-height: 1.6;
      @include sp {
        margin-left: auto;
        margin-right: auto;
      }
    }
    .txt {
      transition-delay: .5s;
      @include sp {
        padding-left: 15px;
        padding-right: 15px;
      }
    }
    &:nth-child(odd) {
      flex-direction: row-reverse;
    }
    position: relative;
    .soon {
      position: absolute;
      top: 50%;
      left: 50%;
      background: rgba(#fff, .9);
      transform: translate(-50%, -50%);
      padding: 10px 80px;
      font-size: 14px;
      // font-weight: bold;
      @include sp {
        padding: 10px 20px;
      }
    }
  }
}

.section-howtouse {
  padding: 52px 0 32px;
  .boxs-wrap {
    overflow-y: hidden;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; 
    overflow-scrolling: touch;
    padding-bottom: 20px;
  }
  .boxs {
    display: flex;
    justify-content: center;
    align-items: center;
    @include sp {
      width: 1000px;
    }
    .box.box-02 {
      transition-delay: .25s;
    }
    .box.box-03 {
      transition-delay: .5s;
    }
    .box.box-04 {
      transition-delay: .75s;
    }
  }
  .box {
    width: 180px;
    color: var(--black, #333);
    text-align: center;
    margin: 0 24px;
    .no {
      color: #2F587C;
      text-align: center;
      font-size: 13px;
      font-weight: 500;
      line-height: 1.5;
      margin: 0 0 5px;
    }
    .title {
      font-family: $serif_font;
      font-size: 18px;
      font-weight: 300;
      line-height: 1.4;
      letter-spacing: 0.72px;
      margin: 0 0 11px;
    }
    .img {
      margin-bottom: 10px;
      img {
        border-radius: 15px;
      }
    }
    p{
      font-size: 13px;
      margin: 0;
    }
  }
  .icn-arrow {
    color: #2F587C;
  }
}

.section-voice {
  padding: 52px 0 72px;
  @include sp {
    padding: 32px 10px 54px;
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    max-width: 980px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 20px;
    > * {
      width: 25%;
      padding: 3px;
      text-align: center;
      @include sp {
        width: 33.33%;
      }
    }
  }
  li:nth-child(n + 9) {
    display: none;
    @include sp {
      display: block;
    }
  }
  li a {
    display: block;
    position: relative;
    overflow: hidden;
    &:after {
      content: " ";
      display: block;
      padding-top: 100%;
    }
    img {
      height: 100%;
      width: auto;
      max-width: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      @supports ( object-fit: cover ) {
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        transform: none;
        object-fit: cover;
      }
    }
  }
}

.section-service {
  padding: 52px 0;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  @include sp {
    padding-left: 8px;
    padding-right: 8px;
  }
  .boxs {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    // align-items: center;
    margin-bottom: 20px;
  }
  .box {
    width: 30%;
    text-align: center;
    margin-bottom: 48px;
    @include sp {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 0 8px;
      margin-bottom: 24px;
    }
    .img {
      margin-bottom: 16px;
      img {
        border-radius: 6px;
      }
      @include sp {
        max-width: 132px;
        width: 100%;
        margin-right: 16px;
        margin-bottom: 0px;
      }
    }
    .box_text {
      @include sp {
        text-align: left;
      }
    }
    .title {
      color: var(--black, var(--text-black, #333));
      font-family: $serif_font;
      font-size: 24px;
      font-weight: 300;
      line-height: 1.4;
      letter-spacing: .04em;
      margin: 0;
      margin-bottom: 12px;
      @include sp {
        font-size: 18px;
        margin-bottom: 8px;
      }
      span{
        color: #2F587C;
        font-weight: 600;
      }
    }
    .txt {
      padding-left: 12px;
      padding-right: 12px;
      text-align: left;
      @include sp {
        padding: 0;
      }
    }
    .txt p {
      color: #333;
      font-size: 14px;
      padding: 0;
      margin: 0;
      @include sp {
        font-size: 12px;
      }
    }
  }

  // .boxs {
  //   .box {
  //     transform: translateY(80px);
  //     opacity: 0;
  //     transition: all 1s ease;
  //     &:nth-child(2) {
  //       transition-delay: .25s;
  //     }
  //     &:nth-child(3) {
  //       transition-delay: .5s;
  //     }
  //     &:nth-child(4) {
  //       transition-delay: .75s;
  //     }
  //     &:nth-child(5) {
  //       transition-delay: 1s;
  //     }
  //     &:nth-child(6) {
  //       transition-delay: 1.25s;
  //     }
  //   }
  //   &.is-inview .box {
  //     opacity: 1;
  //     transform: translateY(0);
  //   }
  // }

  .note p {
    font-size: 12px;
    @include sp {
      padding-left: 8px;
      padding-right: 8px;
      line-height: 1.6;
    }
  }
}

.section-faq {
  padding: 52px 0 0;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  .btn-wrap {
    width: 350px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 44px;
    @include sp {
      width: 280px;
    }
  }
}

.btn-footer-fixed {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 24px;
  padding: 16px;
  z-index: 10;
  transition: 0.35s ease;
  opacity: 0;
  pointer-events: none;
  a{
    pointer-events: none;
  }
  [data-scroll="top"] & {
    bottom: 24px;
    @include sp {
      bottom: 55px;
    }
  }
  &.is-show {
    opacity: 1;
    a{
      pointer-events: all;
    }
  }
  @include sp {
    bottom: 0;
    opacity: 1;
    a{
      pointer-events: all;
    }
  }
  .btn {
    max-width: 600px;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    margin: 0 auto;
    padding-top: 8px;
    .em {
      font-size: 20px;
    }
    .icn-right {
      position: absolute;
      right: 20px;
      top: 50%;
      line-height: 1;
      margin-top: -0.5em;
    }
  }
}
.section-support {
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;

  .box-news {
    margin-left: 18px;
    margin-right: 15px;
  }
  .notice-title {
    text-align: center;

    h2 {
      font-size: 16px; // フォントサイズを調整
      font-weight: normal;
      color: #2F587C; // テキストの色を設定
      position: relative;
      display: inline-block;
      padding-bottom: 5px;

      &::after {
        content: '';
        display: block;
        width: 50px; // ラインの長さ
        height: 2px; // ラインの太さ
        background-color: #2F587C;
        margin: 0 auto;
        margin-top: 5px;
      }
    }
  }
  .list-news {
    display: block;
    @include sp {
      max-height: 160px;
      overflow-y: auto;
      padding-right: 10px; 
    }
    li {
      border-bottom: 1px solid var(--Gray, #F4F4F4);
    }
    a {
      color: var(--49454Fblack_Light, #49454F);
      font-size: 16px;
      font-weight: 400;
      padding: 16px 0px;
      display: block;
      text-decoration: none;
      .title {
        margin-left: 10px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 75%;
        vertical-align: sub;
        line-height: 1.2;
        @include sp {
          margin-left: 0px;
          display: block;
          white-space: normal;
          max-width: calc(100% - 30px);
          line-height: 1.4;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }

      :deep(.cat) {
        display: inline-block;
        width: 200px;
      }
    }
  }
  .link-text {
    color: #B18A3E; 
    font-size: 14px;
    font-weight: bold;
    text-decoration: none;
    &:hover {
      text-decoration: underline; // ホバー時に下線を表示
    }
  }
}
.section-divider {
  border: 0;
  height: 10px;
  background-color: #F4F4F4;
  width: 100%;
  margin: 0;
}



.inview--fadeInUp {
  transform: translateY(80px);
  opacity: 0;
  transition: all 1s ease;
  &.is-inview {
    opacity: 1;
    transform: translateY(0);
  }
}
.inview--fadeIn {
  transform: scale(.95);
  opacity: 0;
  transition: all .5s ease;
  &.is-inview {
    opacity: 1;
    transform: scale(1);
  }
}
.inview--fadeInSlide {
  transform: translateY(80px);
  transition: all 1s ease;
  overflow: hidden;
  transform: translate(0, 100%);
  transition: all .5s ease;
  > span {
    display: block;
    transform: translate(0, -100%);
    transition: all .5s ease;
  }
  &.is-inview {
    & ,
    & > span {
      transform: translate(0, 0);
    }
  }
}
</style>