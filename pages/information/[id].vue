<template>
  <div class="page-news-detail">
    <Main
      :breadcrumbs="[
        {
          title: 'HOME',
          link: '/',
        },
        {
          title: 'お知らせ',
          link: '/information',
        },
        {
          title: noticeData.title
        }
      ]"
      :title="noticeData.title"
      :partsHidden="{
        spHeaderTitle: true
      }"
    >
      <template #main>
        <article class="article-news">
          <section class="news_wrap">
            <div v-if="noticeData?.eyecatch" class="news_eyecatch">
              <img :src="noticeData?.eyecatch?.url" alt="">
            </div>
            <div class="news_contents">
              <div class="news_info">
                <div class="date">{{ $dayjs(noticeData?.publishedAt).format('YYYY/MM/DD') }}</div>
                <span class="cats">
                  <span 
                      class="category" 
                      v-for="(category , n) in noticeData?.category" :key="n"
                      :style="getCategoryStyle(category)"
                      >
                          {{ category }}
                  </span>
              </span> 
              </div>
              <div v-html="noticeData.detail"></div>
            </div>
          </section>
        </article>
      </template>
    </Main>
  </div>
</template>

<script lang="ts" setup>
const { $dayjs } = useNuxtApp() as any;
  const route = useRoute();
  const microCms = new MicroCms();

  // 検索結果
  const { data: noticeData } = await microCms.fetch('/notice2/'+route.params.id);
</script>

<style lang="scss" scoped>
.article-news{
  margin-bottom: -46px;
  @include sp {
    margin-bottom: -56px;
  }
}
.news {
  &_wrap{
    padding: 20px 16px;
  }
  &_eyecatch {
    margin-bottom: 24px;
    img{
      width: 100%;
    }
  }
  &_info {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 12px;
    .date {
      color: #9C9C9C;
      font-size: 12px;
      margin-right: 8px;
    }
    .category {
      display: inline-block;
      font-size: 10px;
      line-height: 1;
      font-weight: bold;
      padding: 4px 11px;
      color: #AD871E;
      border: 1px solid #AD871E;
      border-radius: 20px;
    }
  }
  &_contents {
    max-width: 648px;
    width: 100%;
    margin: 0 auto;
    :deep() {
      p{
        font-size: 14px;
        line-height: 1.5;
        color: #333;
      }
      span.button-news {
        display: inline-block;
        min-width: 320px;
        padding: 12px;
        font-size: 12px;
        line-height: 1.2;
        font-weight: 500;
        text-align: center;
        color: $color-main;
        border: 1px solid $color-main;
        border-radius: 4px;
        cursor: pointer;
        transition: 0.2s;
        @include sp {
          min-width: auto;
          width: 100%;
        }
        &:hover {
          background: $color-main;
          color: #fff;
        }
      }
      img {
        height: auto;
      }
    }
  }
}
</style>
