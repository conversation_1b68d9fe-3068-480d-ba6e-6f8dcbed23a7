<template>
    <Main
        :breadcrumbs="[
        {
            title: 'HOME',
            link: '/',
        },
        {
            title: 'お知らせ',
        }
        ]"
        title="お知らせ"
        :partsHidden="{
        spH1Title: true,
        spHeaderTitle: true,
        }" 
    >
        <template #main>
            <article class="article-questions" style="padding-top: 0;">
                <section class="section-support mb-40" style="padding-top: 0;">
                <div class="box-news">
                    <ul class="list-news">
                    <li v-for="(content , index) in visibleNews" :key="index">
                        <NuxtLink :to="'/information/'+content.id">
                        <span class="date">{{ $dayjs(content.publishedAt).format('YYYY/MM/DD') }}</span>          
                        <span class="cats">
                            <span 
                                class="cat" 
                                v-for="(category , n) in content.category" :key="n"
                                :style="getCategoryStyle(category)"
                                >
                                    {{ category }}
                            </span>
                        </span>          
                        <span class="title">{{ content.title }}</span>     
                        <i class="material-icons icn-right">arrow_forward_ios</i>     
                        </NuxtLink>
                    </li>
                    </ul>
                    <!-- <div class="cmn-aligncenter mt-22 mb-10 fz-sm" v-if="newsData?.contents.length > itemsToShow"> -->
                    <div class="cmn-aligncenter mt-22 mb-10 fz-sm" v-if="isShow">
                    <NuxtLink to="#" @click.prevent="showMore" class="link-text">もっと見る <i class="material-icons icn-sm ml-5 arrow-down">arrow_forward_ios</i></NuxtLink>
                    </div>
                </div>
                </section>
            </article> 
        </template>
    </Main>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const { $dayjs } : any = useNuxtApp();
const microCms = new MicroCms();

const isShow = ref(true);
const itemsToShow = ref(50);
const newsData = ref<any>({ contents: [] });
const totalCount = ref(0);

const fetchNews = async (limit: number, offset: number) => {
    const response = await microCms.fetchWithPaging('/notice2', { orders: '-publishedAt' }, limit, offset);
    if (response && response.data && response.data.value && response.data.value.contents) {
        newsData.value.contents = newsData.value.contents.concat(response.data.value.contents);
        if(totalCount.value == 0){
            totalCount.value = response.data.value.totalCount;
        }
        if(newsData.value.contents.length == response.data.value.totalCount){
            isShow.value = false;
        }
    }
};

await fetchNews(50, 0); // 初期データの取得

const visibleNews = computed(() => newsData.value.contents.slice(0, itemsToShow.value));

const showMore = async () => {
    const currentLength = newsData.value.contents.length;
    await fetchNews(50, currentLength);
    itemsToShow.value = newsData.value.contents.length;
};
</script>

<style lang="scss" scoped>
.article-questions {
    margin-bottom: -46px;
    @include sp {
        margin-bottom: -56px;
    }
}
:deep(.main-contents .contents .section-inner.l-column1) {
    background: #F4F4F4;
    padding-bottom: 0px;
}
:deep(.section-support .list-news a .title) {
    margin-left: 0;
    display: block;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    max-width: 95%;
    line-height: 1.3;
    margin-top: 8px;
}
:deep(.section-support .link-text) {
    color: inherit;
    font-size: 14px; 
    font-weight: normal; 
    text-decoration: none;
}

.arrow-down {
    transform: rotate(90deg);
}
</style>