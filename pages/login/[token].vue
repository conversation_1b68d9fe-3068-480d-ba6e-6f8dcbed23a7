<template>
<Main>
<template #main>
  <Loading :fullscreen="true"></Loading>
</template>
</Main>
</template>

<script lang="ts" setup>
import { mySetCookie } from '@/utils/functions';
const router = useRouter();
const route = useRoute();
const { setAccessToken } = useAccessTokenState();
const { isSnsRegister } = useModalRegisterState();

onMounted(async () => {
  // すでに登録済みなのに会員登録した場合
  if (isSnsRegister()) {
    router.push({ path: '/register', query: { sns: route.query.sns, error_code: 'IS_REGISTERED' }})
    return false;
  }

  const accessToken = route.params.token as string;

  // 現行Favoriから呼び出されるログイン機能
  if (route.query?.mode == 'externalLogin') {
    // アクセストークンを送信する
    window.opener.postMessage({ accessToken: accessToken }, useRuntimeConfig().public.app.accept_external_login_domein);
    window.close();
    return false;
  }

  setAccessToken(accessToken, 'default');
  // なりすましログインフラグ削除
  mySetCookie('isAdminUserLogin', '');
  // ↓ だと getLoginCheck時に BearerToken を読み込まないのでリダイレクト処理
  // router.push({ path: `/mypage` })
  location.href = '/mypage'
});
</script>