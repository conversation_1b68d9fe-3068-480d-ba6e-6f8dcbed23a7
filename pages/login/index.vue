<template>
    <Index></Index>
</template>

<script lang="ts" setup>
import Index from '@/pages/index.vue'
const { showModalLogin, setLoginErrors } = useModalLoginState();

const route = useRoute();
const router = useRouter();
const page = ref('top')

// エラーがあれば表示
const errors = ref({} as any)
if (route.query.sns && route.query.error_code) {
  errors.value[route.query.sns as string] = SNS_LOGIN_ERRORS?.[route.query.error_code as string];
}
onMounted(() => {
  // エラーを保存
  setLoginErrors(errors.value);

  // 現行Favoriから呼び出されるログイン機能
  if (route.query?.mode == 'externalLogin') {
    router.push({path: '/externalLogin'});
    return false;
  }

  // エラーを消しておく
  // router.push({});
  showModalLogin();
})
</script>