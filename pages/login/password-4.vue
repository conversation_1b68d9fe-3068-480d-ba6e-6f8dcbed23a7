
<template>
  <Main>

    <template #title>
      <Titleh1>パスワード再設定</Titleh1>
    </template>
  
    <template #main>
      <div class="wrap">
        <h2>パスワード再設定しました</h2>
        <p>パスワード再設定完了メールを送信しました <br>再度ログインをお願いいたします </p>
        <div class="btn_wrap">
          <ButtonMainColor size="lg">ログインする</ButtonMainColor>
        </div>
      </div>
    </template>


  </Main>
</template>

<script lang="ts" setup>
import { provide } from "vue"

const ListHeaderDrawerMenu02 = {
    title: 'カテゴリ',
    datail: [
      {
        class: "category",
        menu: "セットアイテム",
        innermenu: [
          {
            menu: "セットアイテム01",
            link: "#01"
          },
          {
            menu: "セットアイテム02",
            link: "#02"
          },
          {
            menu: "セットアイテム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ペーパーアイテム",
        innermenu: [
          {
            menu: "ペーパーアイテム01",
            link: "#01"
          },
          {
            menu: "ペーパーアイテム02",
            link: "#02"
          },
          {
            menu: "ペーパーアイテム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ウェルカムスペース・演出アイテム",
        innermenu: [
          {
            menu: "ウェルカムスペース・演出アイテム01",
            link: "#01"
          },
          {
            menu: "ウェルカムスペース・演出アイテム02",
            link: "#02"
          },
          {
            menu: "ウェルカムスペース・演出アイテム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "プチギフト",
        innermenu: [
          {
            menu: "プチギフト01",
            link: "#01"
          },
          {
            menu: "プチギフト02",
            link: "#02"
          },
          {
            menu: "プチギフト03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "両親贈呈品・子育て感謝状",
        innermenu: [
          {
            menu: "両親贈呈品・子育て感謝状01",
            link: "#01"
          },
          {
            menu: "両親贈呈品・子育て感謝状02",
            link: "#02"
          },
          {
            menu: "両親贈呈品・子育て感謝状03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "結婚式アルバム",
        innermenu: [
          {
            menu: "結婚式アルバム01",
            link: "#01"
          },
          {
            menu: "結婚式アルバム02",
            link: "#02"
          },
          {
            menu: "結婚式アルバム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "結婚報告はがき・年賀状",
        innermenu: [
          {
            menu: "結婚報告はがき・年賀状01",
            link: "#01"
          },
          {
            menu: "結婚報告はがき・年賀状02",
            link: "#02"
          },
          {
            menu: "結婚報告はがき・年賀状03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "引き出物宅配",
        innermenu: [
          {
            menu: "引き出物宅配01",
            link: "#01"
          },
          {
            menu: "引き出物宅配02",
            link: "#02"
          },
          {
            menu: "引き出物宅配03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "内祝い・お返しギフト",
        innermenu: [
          {
            menu: "内祝い・お返しギフト01",
            link: "#01"
          },
          {
            menu: "内祝い・お返しギフト02",
            link: "#02"
          },
          {
            menu: "内祝い・お返しギフト03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ギフト・プレゼント・贈答品",
        innermenu: [
          {
            menu: "ギフト・プレゼント・贈答品01",
            link: "#01"
          },
          {
            menu: "ギフト・プレゼント・贈答品02",
            link: "#02"
          },
          {
            menu: "ギフト・プレゼント・贈答品03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "オリジナルグッズ",
        innermenu: [
          {
            menu: "オリジナルグッズ01",
            link: "#01"
          },
          {
            menu: "オリジナルグッズ02",
            link: "#02"
          },
          {
            menu: "オリジナルグッズ03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "結婚式ムービー",
        innermenu: [
          {
            menu: "結婚式ムービー01",
            link: "#01"
          },
          {
            menu: "結婚式ムービー02",
            link: "#02"
          },
          {
            menu: "結婚式ムービー03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ウェディング・ブライダルアクセサリー",
        innermenu: [
          {
            menu: "ウェディング・ブライダルアクセサリー01",
            link: "#01"
          },
          {
            menu: "ウェディング・ブライダルアクセサリー02",
            link: "#02"
          },
          {
            menu: "ウェディング・ブライダルアクセサリー03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ウェディングドレス",
        innermenu: [
          {
            menu: "ウェディングドレス01",
            link: "#01"
          },
          {
            menu: "ウェディングドレス02",
            link: "#02"
          },
          {
            menu: "ウェディングドレス03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "二次会景品",
        innermenu: [
          {
            menu: "二次会景品01",
            link: "#01"
          },
          {
            menu: "二次会景品02",
            link: "#02"
          },
          {
            menu: "二次会景品03",
            link: "#03"
          },
        ],
      }, 
    ],
}

const ListHeaderDrawerMenu = {
  ListHeaderDrawerMenu02,
}

const apiFixedHeaderLogin = {
  // 本来はAPIでデータを取得
  login: true,
  name: '鈴木 このみ',
  date: '2023年4月1日',
}

const apiFixedHeaderBadge = {
  // 本来はAPIでデータを取得
  noticeBadges: 3,
  favoriteBadges: 2,
  cartBadges: 1,
}


const apiFixedHeaderRecentItem = {
  // 本来はAPIでデータを取得
  recent: [
    {
      thumbnail: "/images/sample/smallthumbnail01.png",
      link: "#01"
    },
    {
      thumbnail: "/images/sample/smallthumbnail02.png",
      link: "#02"
    },
    {
      thumbnail: "/images/sample/smallthumbnail03.png",
      link: "#03"
    },
    {
      thumbnail: "/images/sample/smallthumbnail04.png",
      link: "#04"
    },
  ],
}

const apiListItems = {
  param: [
    {
      ranking: true,
      layout: 'twoColumn',
      datail: [
        {
          title: 'Lucky Gray[GN]',
          id: 'gift00000000001',
          link: '/gift/00000000001',
          thumbnail: "/images/sample/thumbnail06.png",
          price: "297",
          unit: "部",
          stars: 3.3,
          review: 12,
          tags: [
            "ゴールド箔",
            "リボン",
          ],
        }, 
        {
          title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
          id: 'gift00000000002',
          link: '/gift/00000000001',
          thumbnail: "/images/sample/thumbnail06.png",
          price: "297",
          unit: "部",
          stars: 4.7,
          review: 12,
          tags: [
            "ゴールド箔",
            "リボン",
          ],
        }, 
        {
          title: 'Dolce duo バウムクーヘン(モダンブラウン)',
          id: 'gift00000000003',
          link: '/gift/00000000001',
          thumbnail: "/images/sample/thumbnail06.png",
          price: "297",
          unit: "部",
          stars: 2,
          review: 12,
          tags: [
            "ゴールド箔",
            "リボン",
          ],
        }, 
        {
          title: 'Dolce duo バウムクーヘン(モダンブラウン)',
          id: 'gift00000000004',
          link: '/gift/00000000001',
          thumbnail: "/images/sample/thumbnail06.png",
          price: "297",
          unit: "部",
          stars: 0,
          review: 12,
          tags: [
            "ゴールド箔",
            "リボン",
          ],
        }, 
        {
          title: 'Dolce duo バウムクーヘン(モダンブラウン)',
          id: 'gift00000000005',
          link: '/gift/00000000001',
          thumbnail: "/images/sample/thumbnail06.png",
          price: "297",
          unit: "部",
          stars: 5,
          review: 12,
          tags: [
            "ゴールド箔",
            "リボン",
          ],
        }, 
      ],
    },
  ],
}



const apiBreadcrumbs = {
  parents: [
    {
      title: "HOME",
      link: '/',
    },
    {
      title: "マイページ",
      link: '/mypage/',
    },
  ],
  current: [
    {
      title: "パスワード再設定",
    },
  ]
}


const apiFooterBanner = {
  datail: [
    {
      alt: "バナー1",
      image: "/images/sample/banner01.png",
      link: '/article/00000000001',
      target: "blank",
    },
    {
      alt: "バナー2",
      image: "/images/sample/banner02.png",
      link: '/article/00000000002',
    },
  ],
}

const apiShipDate = {
  datail: [
    {
      label: '招待状',
      schedule: [
        {
          type: '通常納期',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期',
          shipdate: '8月16日(月)',
        },
      ],
    },
    {
      label: '席次表・席札<wbr>メニュー表',
      schedule: [
        {
          type: '通常納期2',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期2',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期2',
          shipdate: '8月16日(月)',
        },
      ],
    },
    {
      label: 'ウェルカム<wbr>ボード',
      schedule: [
        {
          type: '通常納期3',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期3',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期3',
          shipdate: '8月16日(月)',
        },
      ],
    },
    {
      label: 'プチ<wbr>ギフト',
      schedule: [
        {
          type: '通常納期4',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期4',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期4',
          shipdate: '8月16日(月)',
        },
      ],
    },
    {
      label: '結婚報告<wbr>はがき',
      schedule: [
        {
          type: '通常納期5',
          shipdate: '8月20日(金)',
        },
        {
          type: '特急納期5',
          shipdate: '8月18日(水)',
        },
        {
          type: '超特急納期5',
          shipdate: '8月16日(月)',
        },
      ],
    },
  ],
}

const apiFooterAbouts = {
  datail: [
    {
      alt: "Favoriの充実したサービスについて",
      image: "/images/sample/about.png",
      link: '/article/00000000001',
    },
    {
      alt: "お問い合わせ",
      image: "/images/sample/contact.png",
      link: '/article/00000000002',
    },
  ],
}

const dataListItems = apiListItems;

provide('stateBreadcrumbs', apiBreadcrumbs);
  
provide('stateBanner', apiFooterBanner);
provide('stateDate', apiShipDate);
provide('stateAbouts', apiFooterAbouts);
  
provide('stateMenu', ListHeaderDrawerMenu);
  
provide('stateLogin', apiFixedHeaderLogin);
provide('stateBadge', apiFixedHeaderBadge);
provide('stateRecent', apiFixedHeaderRecentItem);

const eventInFavorite = (target) => {
  // console.log(target+' ok');
}
</script>

<style lang="scss" scoped>
.wrap{
  max-width: 420px;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;
}
.btn_wrap{
  margin: 40px 0 30px;
}
h2{
  color: #B18A3E;
  font-size: 24px;
  font-weight: normal;
  margin: 0 0 20px;
  text-align: center;
}
p{
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 45px;
  text-align: center;
}
a{
  color: #49454F;
  font-size: 14px;
  text-decoration: none;
  &:hover{
    text-decoration: underline;
  }
}
</style>