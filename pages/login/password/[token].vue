<template>
<Main
  :partsHidden="{
      spHeaderTitle: true,
      spH1Title: true,
      spBreadcrumbs: true,
      ShowFooterTop: true,
      FixedFooter: true,
  }" 
  :breadcrumbs="breadcrumbs"
  title="パスワード再設定"
  >
  <template #main>
    <article class="article">
      <Loading v-if="isLoading" :fullscreen="true"></Loading>
      <div class="contener-xs cmn-aligncenter" v-if="errorToken">
        <h2 class="cmn-title size--lg cmn-aligncenter mb-40">パスワード再設定</h2>
        <p v-if="errorToken" class="input-error mb-40">{{ errorToken }}</p>
        <div class="cmn-aligncenter">
          <NuxtLink to="/" class="link-text mb-40"><i class="material-icons icn-left">arrow_back</i> トップへ戻る</NuxtLink>
        </div>
      </div>
      <div class="contener-xs" v-else>
        <h2 class="cmn-title size--lg cmn-aligncenter">パスワード再設定</h2>
        <p class="cmn-aligncenter mb-35">新しいパスワードを入力してください </p>
        <p class="cmn-aligncenter size--sm color-blacklight mb-30">英小文字 英大文字 数字をそれぞれ1文字以上含み <br>
        8～50文字になるよう入力してください </p>
        <div class="mb-25">
          <InputPassword 
            title="新しいパスワード"
            size="full" 
            placeholder="パスワードを入力してください"
            :value="String(input.password)"
            :error="getValidationMessage(v$.password)"
            @update="input.password = $event"
            />
        </div>
        <div class="mb-40">
          <InputPassword 
            title="新しいパスワード (確認用)"
            size="full" 
            placeholder="パスワードを再度入力してください"
            :value="String(input.password_conf)"
            :error="getValidationMessage(v$.password_conf)"
            @update="input.password_conf = $event"
            />
            <p v-if="error" class="input-error">{{ error }}</p>
        </div>
        <button class="btn btn-secondary btn-block btn-md mb-25" @click="onClickSave">パスワードを変更する</button>
        <div class="cmn-aligncenter">
          <NuxtLink to="/" class="link-text mb-40"><i class="material-icons icn-left">arrow_back</i> トップへ戻る</NuxtLink>
        </div>
      </div>
    </article>
  </template>
</Main>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers, minLength, maxLength, sameAs } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";

const route = useRoute();
const router = useRouter();

// 全体エラー
const errorToken = ref('')

// 更新中のLoading
const isLoading = ref(true);

// 更新API
const { action, errors } = useCheckResetPasswordUrl();

onMounted(async () => {
  // 全体エラーをリセット
  errorToken.value = '';
  const isSuccess = await action(route.params.token as string);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) errorToken.value = errors.value?.v$?.[0];
  } else {
    // 登録完了
    // router.push({ path: '/login/password/forget/' })
  }
});


// 全体エラー
const error = ref('')

// 入力項目
const input = ref({
  password:'',
  password_conf: ''
} as {
  password: string;
  password_conf: string;
})

const rules = computed(() => {
  return {
    password: { 
      required: helpers.withMessage(validationMessage.required('パスワード'), required),
      minLength: helpers.withMessage(validationMessage.minLength('パスワード', 8), minLength(8)),
      maxLength: helpers.withMessage(validationMessage.maxLength('パスワード', 50), maxLength(50)),
      regex:  helpers.withMessage(validationMessage.password('パスワード'), validationPassword)
    },
    password_conf: { 
      required: helpers.withMessage(validationMessage.required('パスワード (確認用)'), required),
      minLength: helpers.withMessage(validationMessage.minLength('パスワード', 8), minLength(8)),
      maxLength: helpers.withMessage(validationMessage.maxLength('パスワード', 50), maxLength(50)),
      regex:  helpers.withMessage(validationMessage.password('パスワード'), validationPassword),
      sameAs: helpers.withMessage('パスワードが一致していません ', sameAs(input.value.password))
    },
  };
});


// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { update, errors:updateErrors } = useResetPassword();

// 編集モーダル非表示
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    // error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await update(route.params.token as string, input.value.password);
  if (! isSuccess) {
    if (updateErrors.value) $externalResults.value = updateErrors.value;
    if (updateErrors.value?.v$?.[0]) error.value = updateErrors.value?.v$?.[0];
  isLoading.value = false;
    return false;
  }

  isLoading.value = false;
  router.push({ path: `/login/password/reset` })
};
</script>

<style lang="scss">
.article {
  padding-top: 20px;
}
</style>