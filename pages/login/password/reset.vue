
<template>
<Main
  :partsHidden="{
      spHeaderTitle: true,
      spH1Title: true,
      spBreadcrumbs: true,
      ShowFooterTop: true,
      FixedFooter: true,
  }" 
  :breadcrumbs="breadcrumbs"
  title="パスワード再設定"
  >
    <template #main>
      <div class="contener-xs" style="padding-top: 40px;">
        <h2 class="cmn-title size--lg cmn-aligncenter mt-0">パスワード再設定</h2>
        <p class="cmn-aligncenter mb-55">パスワード再設定完了メールを送信しました <br>再度ログインをお願いいたします </p>
        <NuxtLink to="/login/" class="btn btn-secondary btn-block btn-md mb-25">ログインする</NuxtLink>
      </div>
    </template>
  </Main>
</template>

<script lang="ts" setup>
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage',
  },
  {
    title: "パスワード再設定",
  },
];
</script>