<template>
  <div class="lp-page">
    <!-- ローディング表示 -->
    <div v-if="pending" class="lp-loading">
      <p>読み込み中...</p>
    </div>

    <!-- コンテンツ表示（HTML/リッチエディタ共通） -->
    <div v-else-if="(lpResult || lpData) && ((lpResult && lpResult.contents && lpResult.contents.length > 0) || (lpData && lpData.contents && lpData.contents.length > 0))" ref="contentRef" class="lp-content">
      <!-- HTML直接入力の場合 -->
      <div
        v-if="directHasHtmlContent"
        :class="{ 'lp-html-content': directHasHtmlContent }"
        v-html="processedContent"
      ></div>

      <!-- PC/SP別コンテンツがある場合 -->
      <template v-else-if="(lpResult || lpData) && ((lpResult && lpResult.contents && lpResult.contents[0] && lpResult.contents[0].body_pc) || (lpData && lpData.contents && lpData.contents[0] && lpData.contents[0].body_pc))">
        <!-- PC用コンテンツ -->
        <div
          class="lp-content-pc"
          :class="{ 'lp-rich-content': directHasRichContent }"
          v-html="processedPcContent"
        ></div>

        <!-- SP用コンテンツ -->
        <div
          class="lp-content-sp"
          :class="{ 'lp-rich-content': directHasRichContent }"
          v-html="processedSpContent"
        ></div>
      </template>

      <!-- PC用コンテンツだけの場合 -->
      <div
        v-else
        :class="{ 'lp-rich-content': directHasRichContent }"
        v-html="processedContent"
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useRoute, useHead, useSeoMeta, navigateTo, useLazyAsyncData } from '#app';
import { useLandingPage } from '@/composables/useLandingPage';
import { useToastMessageState } from '@/composables/useToastMessageState';

const route = useRoute();
const { addToastMessage } = useToastMessageState();

// パスパラメータを取得（コンテンツパス）
const contentPath = route.params.path as string;

// LP用コンポーザブルを使用
const {
  lpData,
  error,
  fetchLandingPage,
  hasHtmlContent,
  hasPcContent,
  getDisplayContent,
  getShortcodeData
} = useLandingPage();

const directHasHtmlContent = computed(() => {
  const data = lpResult.value || lpData.value;
  return !!data?.contents?.[0]?.body_html;
});

const directHasRichContent = computed(() => {
  const data = lpResult.value || lpData.value;
  return !!data?.contents?.[0]?.body && !data?.contents?.[0]?.body_html;
});

// レスポンシブ判定
const isMobile = ref(false);

// DOM参照
const contentRef = ref<HTMLElement | null>(null);

// HTMLエンコードされた文字列をデコードする関数（SSR対応）
const decodeHtmlEntities = (str: string): string => {
  if (process.server) {
    // サーバーサイドでは基本的なHTMLエンティティのみデコード
    return str
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ');
  }
  // クライアントサイドでは従来の方法
  const textarea = document.createElement('textarea');
  textarea.innerHTML = str;
  return textarea.value;
};

// ショートコードをHTMLに置き換える関数
const replaceShortcodes = (content: string, data: any): string => {
  const shortcodeHandlers: Record<string, () => string> = {
    'coupon-code': () => `
      <div class="shortcode shortcodeCouponCode">
        <div class="shortcodeCouponCode_title">クーポンコード</div>
        <div class="shortcodeCouponCode_value" style="color: ${data.couponCodeTextColor};">${data.couponCodeNumber}</div>
        <button
          class="shortcodeCouponCode_button"
          style="background-color: ${data.couponCodeBtnBgColor}; color: ${data.couponCodeBtnColor};"
          onclick="return false;"
          data-copy="${data.couponCodeNumber}"
        >
          クーポンコードをコピーする
        </button>
      </div>`,
    'copy-box': () => `
      <div class="shortcode shortcodeCopyBox">
        <button
          class="shortcodeCopyBox_button"
          onclick="return false;"
          data-copy="${data.copyBoxText}"
        >
          ${data.copyBoxText}
        </button>
      </div>`,
  };

  const decodedContent = decodeHtmlEntities(content);
  return decodedContent.replace(/\[([a-zA-Z0-9_-]+)\]/g, (_, shortcode) => {
    const handler = shortcodeHandlers[shortcode];
    return handler ? handler() : _;
  });
};

// 処理済みコンテンツ（HTMLまたはリッチエディタ）
const processedContent = computed(() => {
  if (hasHtmlContent.value) {
    return getDisplayContent.value;
  }

  // PC/SP別コンテンツがない場合は通常のbodyを表示
  if (!hasPcContent.value) {
    const content = getDisplayContent.value;
    if (!content) return '';
    return replaceShortcodes(content, getShortcodeData.value);
  }

  return '';
});

// PC用処理済みコンテンツ
const processedPcContent = computed(() => {
  const data = lpResult.value || lpData.value;
  if (!data?.contents?.[0]?.body_pc) return '';

  const content = data.contents[0].body_pc;
  if (!content) return '';

  return replaceShortcodes(content, getShortcodeData.value);
});

// SP用処理済みコンテンツ
const processedSpContent = computed(() => {
  const data = lpResult.value || lpData.value;
  if (!data?.contents?.[0]) return '';

  const content = data.contents[0].body_sp || data.contents[0].body || '';
  if (!content) return '';

  return replaceShortcodes(content, getShortcodeData.value);
});

// レスポンシブ判定（SSR対応）
const checkMobile = () => {
  if (process.client) {
    isMobile.value = window.innerWidth <= 465;
  }
};

// コピーボタンの設定
const setupCopyButtons = () => {
  if (!contentRef.value) return;

  const copyButtons = contentRef.value.querySelectorAll('[data-copy]');
  copyButtons.forEach(button => {
    button.addEventListener('click', async (e) => {
      e.preventDefault();
      const textToCopy = (button as HTMLElement).dataset.copy || '';
      try {
        await navigator.clipboard.writeText(textToCopy);
        addToastMessage({message: `「${textToCopy}」をコピーしました`});
      } catch (err) {
        console.error('クリップボードへのコピーに失敗しました:', err);
      }
    });
  });
};

// コンテンツ変更時の処理
watch(processedContent, async () => {
  await nextTick();
  setupCopyButtons();
});

// SSR用のデータ取得（コンテンツとSEO用）
const { data: lpResult, pending, error: asyncError } = await useLazyAsyncData(`lp-ssr-${contentPath}`, async () => {
  const data = await fetchLandingPage(contentPath);
  return data;
});

// SSRでSEO設定を即座に適用
if (lpResult.value) {
  const currentData = lpResult.value;
  const seoData = currentData.seo?.[0];
  const title = seoData?.seo_title || currentData.title || '';
  const description = seoData?.seo_desctiption || seoData?.seo_description || '';
  const ogImage = seoData?.seo_eyecatch?.url || '';
  const isIndexable = seoData?.seo_index !== false;

  const finalTitle = title ? `${title}｜WEB招待状 「ふたりの人生をつなぐ。」Favori（ファヴォリ）` : 'WEB招待状 「ふたりの人生をつなぐ。」Favori（ファヴォリ）';

  useHead({
    title: finalTitle,
    meta: [
      { name: 'description', content: description },
      { name: 'robots', content: isIndexable ? 'index,follow' : 'noindex,nofollow' },
      { property: 'og:title', content: finalTitle },
      { property: 'og:description', content: description },
      { property: 'og:type', content: 'website' },
      ...(ogImage ? [
        { property: 'og:image', content: ogImage },
        { property: 'og:image:alt', content: finalTitle }
      ] : []),
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: finalTitle },
      { name: 'twitter:description', content: description },
      ...(ogImage ? [{ name: 'twitter:image', content: ogImage }] : [])
    ]
  });
}

// CSR用のカスタムCSS/JS取得
const fetchCustomStyles = async () => {
  if (process.client) {
    try {
      const data = await fetchLandingPage(contentPath);
      if (data) {
        // カスタムCSS適用
        const cssContent = data.custom_css || '';
        if (cssContent) {
          const existingStyles = document.querySelectorAll('style[data-lp-custom-css]');
          existingStyles.forEach(style => style.remove());

          const styleElement = document.createElement('style');
          styleElement.setAttribute('data-lp-custom-css', 'true');
          styleElement.textContent = cssContent;
          document.head.appendChild(styleElement);
        }

        // カスタムJS適用
        const jsContent = data.custom_js || '';
        if (jsContent) {
          const existingScripts = document.querySelectorAll('script[data-lp-custom-js]');
          existingScripts.forEach(script => script.remove());

          const scriptElement = document.createElement('script');
          scriptElement.setAttribute('data-lp-custom-js', 'true');
          scriptElement.textContent = jsContent;
          document.head.appendChild(scriptElement);
        }
      }
    } catch (error) {
      console.error('Failed to fetch custom styles:', error);
    }
  }
};

// マウント時の処理（CSR専用の初期化）
onMounted(async () => {
  if (process.client) {
    // レスポンシブ判定
    checkMobile();
    window.addEventListener('resize', checkMobile);

    // CSR用のカスタムCSS/JS取得・適用
    await fetchCustomStyles();

    // コピーボタンの設定
    await nextTick();
    setupCopyButtons();
  }
});


// データ取得失敗時の404処理（エラー状態を監視）
watch([() => error.value, () => asyncError.value, () => pending.value], ([errorVal, asyncErrorVal, pendingVal]) => {
  if (!pendingVal && (errorVal || asyncErrorVal) && !lpResult.value) {
    if (process.client) {
      throw createError({
        statusCode: 404,
        statusMessage: 'LP Page Not Found',
        fatal: true
      });
    }
  }
}, { immediate: true });


</script>

<style lang="scss" scoped>
.lp-page {
  min-height: 100vh;
  .lp-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;
    p {
      font-size: 16px;
      color: #666;
    }
  }
  .lp-content-pc,
  .lp-content-sp {
    :deep() {
      figure {
        text-align: center;
      }
    }
  }
  .lp-content {
    width: 100%;
    max-width: 465px;
    margin: 0 auto;
    @media (min-width: 466px) {
      max-width: 980px;
    }

    // PC/SP別コンテンツの切り替え
    .lp-content-pc {
      display: none;
      @media (min-width: 466px) {
        display: block;
      }
    }

    .lp-content-sp {
      display: block;
      @media (min-width: 466px) {
        display: none;
      }
    }
    :deep() {
      figure {
        margin: 0;
      }
      img {
        max-width: 100%;
        height: auto;
      }
      button {
        cursor: pointer;
      }
      // ショートコード用スタイル
      .shortcodeCouponCode {
        text-align: center;
        margin: 0;
        padding: 20px 20px 52px;
        &_title {
          color: #D7A241;
          font-size: 22px;
          font-weight: bold;
          line-height: 1.5;
          margin-bottom: 20px;
          padding-bottom: 6px;
          border-bottom: 1px solid #D7A241;
        }
        &_value {
          color: #D7A241;
          font-size: 32px;
          font-weight: bold;
          line-height: 1.2;
          margin-bottom: 20px;
        }
        &_button {
          color: #FFF;
          background: #FF8F79;
          font-size: 14px;
          font-weight: bold;
          line-height: 1.3;
          border: none;
          padding: 10px 12px;
          cursor: pointer;
          border-radius: 5px;
          box-shadow: -1px 2px 4px rgba(0,0,0,0.25);
          transition: 0.25s ease;
          &:hover {
            box-shadow: -1px 2px 8px rgba(0,0,0,0.3);
          }
          &:active {
            box-shadow: none;
          }
        }
      }
      .shortcodeCopyBox {
        text-align: center;
        margin: 24px auto;
        padding: 0 20px 30px;
        &_button {
          display: block;
          width: 100%;
          background-color: #fff;
          border: none;
          border-top: 1px solid #D9D9D9;
          border-bottom: 1px solid #D9D9D9;
          color: #555;
          padding: 14px;
          font-size: 16px;
          font-weight: bold;
          line-height: 1;
          cursor: pointer;
          transition: 0.25s ease;
          &:hover {
            opacity: 0.7;
          }
          &:active {
            opacity: 0.5;
          }
        }
      }
    }
  }
  .lp-not-found {
    text-align: center;
    padding: 60px 20px;
    h1 {
      font-size: 24px;
      margin-bottom: 20px;
      color: #333;
    }
    p {
      font-size: 16px;
      color: #666;
    }
  }
}
</style>