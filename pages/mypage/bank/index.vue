<template>
	<Main
  :partsHidden="paramPartsHidden" 
  :breadcrumbs="breadcrumbs"
  title="振込口座の登録・変更"
	:backlink="backlink"
  >
		<template #main>
			<Loading v-if="isLoading"></Loading>
			<article v-else>
				<div class="alert-message" v-if="hasMoneyTransfer?.hasMoneyTransfer">
					ただいまお振込手続き中のため<br>
					変更できません
				</div>
				<div class="custom-card" v-if="isMemberBankAccount">
					<p class="title">まだ振込口座が登録されていません</p>
					<p class="description">
						WEB招待状でゲストから事前支払いを受け付けた会費・ご祝儀のお振込には、口座情報の登録が必要です。
					</p>
				</div>
				<div class="custom-card mb-40" v-else>
					<ul class="list-info mb-20">
						<li><dl>
							<dt>銀行名 / 銀行コード</dt>
							<dd>{{ member?.memberMe?.member_bank_account?.bank_name }} / {{ member?.memberMe?.member_bank_account?.bank_code }}</dd>
						</dl></li>
						<li><dl>
							<dt>支店名 / 支店コード</dt>
							<dd>{{ member?.memberMe?.member_bank_account?.branch_name }} / {{ member?.memberMe?.member_bank_account?.branch_code }}</dd>
						</dl></li>
						<li><dl>
							<dt>口座種別</dt>
							<dd>{{ getLabelForAccountType(member?.memberMe?.member_bank_account?.account_type) }}</dd>
						</dl></li>
						<li><dl>
							<dt>口座番号</dt>
							<dd>{{ member?.memberMe?.member_bank_account?.account_number }}</dd>
						</dl></li>
						<li><dl>
							<dt>口座名義</dt>
							<dd>{{ member?.memberMe?.member_bank_account?.account_name }}</dd>
						</dl></li>
						<li><dl>
							<dt>電話番号</dt>
							<dd>{{ member?.memberMe?.member_bank_account?.phone }}</dd>
						</dl></li>
					</ul>
				</div>
				<div class="notice-box">
					<h2 class="notice-title">
					<span class="notice-icon">!</span>
					注意事項
					</h2>
					<div class="notice-content">
					<div class="notice-item">
						<span class="notice-mark">※</span>
						<p>集まった会費・ご祝儀は、「カ）テトテ」からこちらのページで登録された振込先口座へ、選択頂いた振込日にお振込みします。</p>
					</div>
					<div class="notice-item">
						<span class="notice-mark">※</span>
						<p>振込口座に誤りがあった場合、再申請からお振り込みまで1週間程度かかります。開催日に間に合わない可能性がございますので、あらかじめご了承ください。</p>
					</div>
					</div>
				</div>
				<NuxtLink 
					to="/mypage/bank/regist" 
					class="btn btn-secondary btn-block"
					:class="{ 'disabled-link': hasMoneyTransfer?.hasMoneyTransfer }"
				>振込口座を登録する</NuxtLink>
				<hr class="hr-full">
				<section class="section-search-results">
					<h2 class="cmn-title">よくある質問</h2>
					<ListFaqDetail
							v-for="(content , index) in questionsData?.contents"
							:key="index"
							:question="content.question"
							:answer="content.summary"
							:link="'/question/'+content.id"
					></ListFaqDetail>
				</section>
			</article>
			<ModalBankEdit 
				v-if="showEditModal"
				:member="member.memberMe"
				@close="showEditModal = false"
				@reload="onReloaded"
			/>
		</template>
	</Main>
</template>

<script lang="ts" setup>
const router = useRouter();
const route = useRoute();
import { ref } from 'vue';
import ModalBankEdit from '@/components/organisms/mypage/bank/ModalBankEdit.vue'

const { addToastMessage } = useToastMessageState();
const { showLoading, hideLoading } = useLoadingState();
const { $dayjs } : any = useNuxtApp();
const isLoading = ref(true);
// 全体エラー
const error = ref('');
const showEditModal = ref(false);
const microCms = new MicroCms();
const isMemberBankAccount = ref(true);

// レイアウト設定
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage',
  },
  {
    title: "振込口座登録",
  },
];
const paramPartsHidden = {
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
  spH1Title: true,
  spLogo: true
}


//口座種別
const selectAccountTypeEnum = ref([
	{ value: AccountTypeEnum.Normal, label: '普通' },
	{ value: AccountTypeEnum.Current, label: '当座' },
	{ value: AccountTypeEnum.Savings, label: '貯蓄' },
]);

const { member, refetch } = useGetOneMemberMe();

const { hasMoneyTransfer, refetch: refetchHasMoneyTransfer, loading } = useGetHasMoneyTransfer(MoneyTransferStatusEnum.Reservation);

const { data: questionsData , error: questionsError, refresh: questionsDataRefresh} = await microCms.fetch('/question', {
	orders: 'publishedAt',
  filters: 'type[contains]特によくある質問'
});

const backlink = ref('' as string);
onMounted(async () => {
  const backUrl = getBacklink();
  backlink.value = backUrl === '/mypage/bank/regist' ? '/mypage' : backUrl;
  isLoading.value = true;
  await refetch();
	if(member?.value?.memberMe?.member_bank_account) {
		isMemberBankAccount.value = false;
	}
	isLoading.value = false;
});

// 口座種別のラベルを取得
const getLabelForAccountType = (accountType: any) => {
	const accountTypeItem = selectAccountTypeEnum.value.find(
		item => item.value === accountType
	);
	return accountTypeItem ? accountTypeItem.label : '';
};

const onReloaded = async() => {
  isLoading.value = true;
  await refetch();
	if(member?.value?.memberMe?.member_bank_account) {
		isMemberBankAccount.value = false;
	}
  isLoading.value = false;
}
</script>

<style lang="scss" scoped>

article {
  padding: 20px 18px;
	.hr-full {
		margin-left: -18px;
		margin-right: -18px;
		margin-top: 65px;
		@include sp {
			margin-top: 55px;
		}
	}
}

.custom-card {
  margin: 0 auto;
	max-width: 804px;
  width: 100%;
  margin: 0 auto;
}

.title {
  font-size: 1.1rem;
  color: #B18A3E; 
}

.description {
  color: #4A5568; 
}

.register-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #B18A3E; 
  color: #FFFFFF;
  border-radius: 9999px;
  transition: background-color 0.3s;
}

.notice-box {
	padding: 0px 18px 15px;
	border: 1px solid #ff0000;
	border-radius: 5px;
	margin: 20px auto 20px;
	max-width: 804px;
  width: 100%;
}

.notice-title {
  color: #ff0000;
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 10px;
  display: flex;
  align-items: center;

	@include sp {
		font-size: 16px;
	}
}

.notice-icon {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  font-size: 16px;
  border: 1px solid #ff0000;
  border-radius: 50%;
  margin-top: 2px;
  margin-right: 10px;
	@include sp {
		width: 18px;
		height: 18px;
		font-size: 14px;
	}
}

.notice-content {
  font-size: 14px;
  line-height: 1.5;
	@include sp {
		font-size: 12px;
	}
}

.notice-item {
  display: flex;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.notice-mark {
  flex-shrink: 0;
  margin-right: 5px;
}

p {
  margin: 0;
}

.row {
	margin-bottom: 10px;
}
.small-text {
	font-size: 0.8rem;
	margin-top: 10px;
}
.small-text-yuutyo {
	font-size: 0.8rem;
	margin-top: 10px;
	color:#B18A3E;
}
.contener {
	margin: 20px;
}
.section-search-results {
	max-width: 804px;
  width: 100%;
	margin: 0px auto;
}

.full-width-line {
  border: none;
  border-bottom: 8px solid #F4F4F4;
  margin: 0;
  width: 100%;
  position: relative;
  top: 0;
	// margin-right: calc(50% - 50vw);
  // margin-left: calc(50% - 50vw);
  // padding-left: calc(50vw - 50%);
  // padding-right: calc(50vw - 50%);
	@include sp {
		border-bottom-width: 4px;
  }
}
.btn-bank {
	display: block;
	width: 70%;
	margin: 35px auto 0px;
	@include sp {
		width: 100%;
		margin: 28px auto 0px;
  }
}
.alert-message {
	background-color: #FF0000;
	color: #fff;
	text-align: center;
	padding: 15px;
	font-size: 18px;
	font-weight: bold;
	border-radius: 5px;
	width: 80%;
	margin: 20px auto;
	box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);

	@include sp {
		font-size: 16px;
    padding: 10px;
    width: 90%;
	}
}
.disabled-link {
  pointer-events: none; /* クリックを無効化 */
  opacity: 0.5; /* 見た目を薄くする */
  cursor: not-allowed;
	background-color: #AAAAAA;
}

</style>