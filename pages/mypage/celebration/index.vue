<template>
<Main
  :partsHidden="{
    ShowFooterTop: true,
    FixedFooter: true,
    spBreadcrumbs: true,
    spLogo: true,
    spH1Title: true,
    FixedFooterBottomReceiver: 'visible',
    spFixedFooterBottomReceiver: true
  }" 
  :breadcrumbs="breadcrumbs"
  :backlink="backlink"
  title="会費・ご祝儀"
  >
  <template #main>
  <article class="article-main article-celebration">
    <Loading v-if="isLoading"></Loading>
    <div v-else-if="! celebrationFeeDetails.length">
      <div class="contener-sm mb-10 box-nodata">
        <h2 class="cmn-title">まだ事前決済を受け付けていません</h2>
        <p class="mb-24">WEB招待状の作成・編集画面で「事前決済を利用する」を選ぶと <br class="pc_only">ゲストに会費・ご祝儀を事前決済してもらうことができます </p>
        <NuxtLink href="/mypage/webinvitation" class="btn btn-secondary btn-block">WEB招待状を作成・編集する</NuxtLink>
      </div>
      <div class="info-wrap">
        <div class="box-info">
          <i class="icn material-symbols-outlined">info</i>
          <div>
            <p>
              実際の口座振込額は 事前支払いの合計金額からシステム利用料と振込手数料を引いた金額となります
            </p>
            <h3>システム利用料について</h3>
            <p>
              決済金額の3.1%をシステム利用料として頂戴しております<br>
              システム利用料のご負担は「ゲスト」「あなた（主催者）」「ゲストに任せる」より設定いただけます
            </p>
            <h3>振込手数料について</h3>
            <p>
              1回の引き落としにつき660円の振込手数料が発生いたします<br>
              なお 会費・ご祝儀の事前支払いのお振込は 招待状毎にお振込みとなります<br>
              お振込み金額を分割したり まとめることはできかねております事ご了承くださいませ
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="box-celebrationFeeDetails" :data-count="celebrationFeeDetails.length">
    <swiper
      class="swiper-celebrationFeeDetails"
      :modules="[Navigation, Pagination]"
      :speed="500"
      :pagination="{ clickable: true }"
      :navigation="{ clickable: true }"
      @slideChange="onChangeCelebrationFeeDetail"
    >
      <swiperSlide class="swiper-slide" v-for="(celebrationFeeDetail) in celebrationFeeDetails" :key="celebrationFeeDetail.id">
        <div class="box-celebrationFeeDetail">
          <div class="img"><img src="@/assets/images/mypage/celebration/webinvitation-01.jpg"></div>
          <div class="box-main">
            <p class="date" v-if="celebrationFeeDetail.event_date">開催日：{{ $dayjs(celebrationFeeDetail.event_date).format('YYYY/MM/DD') }}</p>
            <h2 class="name">{{ celebrationFeeDetail.name }}</h2>
            <div class="info">
              <p>回答数：{{ celebrationFeeDetail.guest_event_answers_count }}</p>
              <p v-if="celebrationFeeDetail.scheduled_transfer_date">振込予定日：{{ $dayjs(celebrationFeeDetail.scheduled_transfer_date).format('YYYY/MM/DD') }}</p>
            </div>
          </div>
        </div>
      </swiperSlide>
    </swiper>
    </div>

    <div v-if="currentCelebrationFeeDetail">
      <div class="box-total" v-if="currentCelebrationFeeDetail.payment_amount > 0">
      <!-- <div class="box-total"> -->
        <h2 class="cmn-title">振込予定額</h2>
        <table class="table-total">
          <tr>
            <th>集計</th>
            <td>{{ setNumberFormat(currentCelebrationFeeDetail.payment_amount) }} <small>円</small></td>
          </tr>
          <template v-if="currentCelebrationFeeDetail.events.length > 1">
          <tr class="line-gray" v-for="(event, index) in currentCelebrationFeeDetail.events" :key="index">
            <th>　{{ event.event_name }}</th>
            <td>{{ setNumberFormat(event.prepaid_amount_total) }} <small>円</small></td>
          </tr>
          </template>
          <tr>
            <th>システム利用料</th>
            <td v-if="currentCelebrationFeeDetail.system_amount == 0">{{ setNumberFormat(currentCelebrationFeeDetail.system_amount) }} <small>円</small></td>
            <td v-else>-{{ setNumberFormat(currentCelebrationFeeDetail.system_amount) }} <small>円</small></td>
          </tr>
          <tr>
            <th>振込手数料</th>
            <td>-{{ setNumberFormat(currentCelebrationFeeDetail.transfer_amount) }} <small>円</small></td>
          </tr>
          <tr class="line-primary">
            <th>振込予定金額合計</th>
            <td>{{ setNumberFormat(currentCelebrationFeeDetail.total_amount) }} <small>円</small></td>
          </tr>
        </table>
      </div>
      <hr class="hr-full" v-if="currentCelebrationFeeDetail.payment_amount > 0">
      <!-- <hr class="hr-full"> -->
    </div>
    <template v-if="currentCelebrationFeeDetail?.events">
      <nav class="nav-tab" v-if="currentCelebrationFeeDetail?.events.length > 1"><ul>
        <li v-for="(event, index) in currentCelebrationFeeDetail.events" :key="index">
          <a @click="currentEvent = event" :class="{'current': currentEvent.event_name == event.event_name}">{{ event.event_name }}</a>
        </li>
      </ul></nav>
      <div class="box-event" v-if="currentEvent">
        <div class="event-info">
          <div class="event-amount">
            <span class="label-default">事前決済</span>
            <span class="amount">{{ setNumberFormat(currentEvent.prepaid_amount_total) }} <small>円</small></span>
          </div>
          <div class="event-counts">
            <span>事前支払：</span>{{ currentEvent.prepaid_count_total }}人
            <span>　当日持参：</span>{{ currentEvent.pay_at_venue_count_total }}人
            <span>　既に支払済：</span>{{ currentEvent.paid_count_total }}人
          </div>
          <div class="event-link">
            <a href="#" @click.prevent="isShowAboutSystemModal = true" class="cmn-link is-question"><img src="@/assets/images/icon-question-gl.svg">システム料について</a>
          </div>
        </div>
        <ul class="guests">
          <li class="box-group" v-for="guest in currentEvent.guests" :key="guest.id">
            <GuestInfo 
              :guest="guest"
            />
            <GuestInfo 
              v-for="child_guest in guest.child_guests" 
              :key="child_guest.id"
              :guest="child_guest"
            />
          </li>
        </ul>
      </div>
    </template>
    <div class="info-wrap" v-if="celebrationFeeDetails.length">
      <div class="box-info">
        <i class="icn material-symbols-outlined">info</i>
        <div>
          <p>
            実際の口座振込額は 事前支払いの合計金額からシステム利用料と振込手数料を引いた金額となります
          </p>
          <h3>システム利用料について</h3>
          <p>
            決済金額の3.1%をシステム利用料として頂戴しております<br>
            システム利用料のご負担は「ゲスト」「あなた（主催者）」「ゲストに任せる」より設定いただけます
          </p>
          <h3>振込手数料について</h3>
          <p>
            1回の引き落としにつき660円の振込手数料が発生いたします<br>
            なお 会費・ご祝儀の事前支払いのお振込は 招待状毎にお振込みとなります<br>
            お振込み金額を分割したり まとめることはできかねております事ご了承くださいませ
          </p>
        </div>
      </div>
    </div>
    <Modal v-if="isShowAboutSystemModal" class="modalAboutSystem" size="full" @close="isShowAboutSystemModal = false">
      <template #header>
        システム料について
      </template>
      <template #main>
        <div class="modalAboutSystem_wrap">
          <div class="modalAboutSystem_image">
            <img src="@/assets/images/aboutSystem.png">
          </div>
          <p>「システム料」のタグは 会費・ご祝儀の事前決済で<span class="modalAboutSystem_explain">ゲスト様がおふたりに代わってシステム利用料をご負担されたとき</span>に表示されるものです。</p>
        </div>
      </template>
    </Modal>
  </article>
  </template>
</Main>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination } from 'swiper';
import GuestInfo from '@/components/organisms/mypage/celebration/GuestInfo.vue'

const { $dayjs } : any = useNuxtApp();

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "会費・ご祝儀"
  }
];

// APIから読み込み
const { celebrationFeeDetails, refetch } = useGetManyCelebrationFeeDetail();

const isLoading = ref(true);
const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('', '^/mypage');
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
  currentCelebrationFeeDetail.value = celebrationFeeDetails.value?.[0];
  if (currentCelebrationFeeDetail.value?.events) currentEvent.value = currentCelebrationFeeDetail.value?.events?.[0];
});

const isShowAboutSystemModal = ref(false);
const currentCelebrationFeeDetail = ref({} as any)
const currentEvent = ref({} as any)

const onChangeCelebrationFeeDetail = (swiper:any) => {
  const celebrationFeeDetail = celebrationFeeDetails.value?.[swiper.realIndex];
  currentCelebrationFeeDetail.value = celebrationFeeDetail;
  currentEvent.value = celebrationFeeDetail.events?.[0];
}
</script>

<style lang="scss" scoped>
.cmn-link {
  &.is-question{
    display: inline-block;
    margin-top: 14px;
    font-size: 12px;
    line-height: 1;
    img{
      width: 14px;
      margin-right: 4px;
      vertical-align: text-bottom;
    }
  }
}
.event-link{
  width: 100%;
}

.box-celebrationFeeDetails {
  padding: 16px 0;
  background: #F4F4F4;
  // margin-right: calc(50% - 50vw);
  // margin-left: calc(50% - 50vw);
  // padding-left: calc(50vw - 50%);
  // padding-right: calc(50vw - 50%);
  &[data-count="0"] {
    padding: 0;
    height: 0;
    overflow: hidden;
  }
}
.swiper-celebrationFeeDetails {
  // margin-right: calc(50% - 50vw);
  // margin-left: calc(50% - 50vw);
  // padding-left: calc(50vw - 50%);
  // padding-right: calc(50vw - 50%);
  padding-bottom: 20px;
  --swiper-pagination-bottom: 0;
  --swiper-pagination-color:#2F587C; 
  --swiper-pagination-bullet-inactive-color:#ffffff; 
  --swiper-pagination-bullet-inactive-opacity: 1;
  --swiper-navigation-color: rgba(#000, .5);
  --swiper-navigation-size: 16px;
  --swiper-navigation-sides-offset: 10px;
  --swiper-navigation-top-offset: 40%;
  @include sp {
    margin: 0;
    padding: 0 0 20px;
  }
}
.box-celebrationFeeDetail {
  display: block;
  background: #fff;
  padding: 12px;
  display: flex;
  max-width: 560px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  border-radius: 4px;
  @include sp {
    margin: 0 16px;
  }
  .img {
    width: 48px;
    height: 84px;
    flex-shrink: 0;
    margin-right: 10px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.14);
    img{
      width: 48px;
      height: 84px;
      object-fit: cover;
    }
  }
  .box-main {
    width: 100%;
  }
  .name {
    padding: 0;
    margin: 0;
    color: #AD871E;
    font-size: 18px;
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 8px;
    word-wrap: break-word;
    overflow-wrap: anywhere;
    @include lineClamp(2);
    min-height: 2.5em;
    line-height: 1.25;
  }
  .date {
    color: #49454F;
    font-size: 12px;
    line-height: 1.2;
    margin-bottom: 4px;
  }
  .info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    p{
      color: #49454F;
      font-size: 12px;
      line-height: 1.2;
      padding: 0;
      margin: 0;
    }
  }
}

.nav-tab {
  // margin-right: calc(50% - 50vw);
  // margin-left: calc(50% - 50vw);
  // padding-left: calc(50vw - 50%);
  // padding-right: calc(50vw - 50%);
  ul {
    max-width: 560px;
    margin-left: auto;
    margin-right: auto;
  }
}

.box-event {
  max-width: 560px;
  margin-left: auto;
  margin-right: auto;
  .event-info {
    padding: 20px 16px 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    @include sp {
      display: block;
    }
    .event-amount {
      text-align: center;
      @include sp {
        margin-bottom: 15px;
      }
      .label-default {
        margin-right: 10px;
      }
      .amount {
        color: #AD871E;
        font-size: 20px;
        font-weight: 400;
        small {
          font-size: 12px;
          color: #333;
        }
      }
    }
    .event-counts {
      text-align: center;
      color: #9C9C9C;
      font-size: 12px;
      line-height: 1.6;
      span {
        font-weight: 700;
      }
    }
  }
  .guests {
    // margin-right: calc(50% - 50vw);
    // margin-left: calc(50% - 50vw);
    // padding-left: calc(50vw - 50%);
    // padding-right: calc(50vw - 50%);
    border-top: 1px solid #D9D9D9;
  }
  .box-group {
    margin-left: 16px;
    margin-right: 16px;
    border-bottom: 1px solid #d9d9d9;
  }
  .box-guest {
    padding: 10px 0;
    & + .box-guest{
      border-top: 1px dotted #D9D9D9;
    }
    h3, p {
      margin: 0;
      padding: 0;
      line-height: 1.6;
    }
    .guest-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
    }
    .guest-sub {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .guest-attendance {
        width: 70px;
        flex-shrink: 0;
      }
    }
    .guest-tag {
      display: inline-block;
      font-size: 12px;
      line-height: 1.2;
      padding: 4px 8px;
      border: 1px solid currentColor;
      border-radius: 12px;
      margin-left: 8px;
      &.is-system{
        color: #9C9C9C;
      }
      &.is-gift{
        color: #E65C7A;
      }
    }
    .name {
      font-size: 18px;
      font-weight: normal;
      small {
        font-size: 12px;
      }
    }
    .date {
      color: #9C9C9C;
      font-size: 12px;
      line-height: 1.6;
    }
    .amount {
      font-size: 18px;
      font-weight: normal;
      text-align: right;
      small {
        font-size: 11px;
      }
    }
    .gift-amount {
      color: #9C9C9C;
      font-size: 12px;
      line-height: 1.6;
      text-align: right;
      .txt {
        color: #E65C7A;
        margin-right: .5em;
        .icn {
          font-size: 15px;
          vertical-align: middle;
        }
      }
    }
  }
}

.info-wrap {
  padding: 24px 16px 36px;
  max-width: 560px;
  margin-left: auto;
  margin-right: auto;
  .box-info {
    h3 {
      font-size: 12px;
      color: #333;
      margin: 1.2em 0 .25em;
    }
  }
}

.box-total {
  padding: 10px 16px 16px;
  max-width: 560px;
  margin-left: auto;
  margin-right: auto;
  .table-total {
    width: 100%;
    th, td {
      line-height: 1.4;
      padding: 2px 0;
    }
    tr.line-gray {
      color: #9C9C9C;
    }
    tr.line-primary {
      td {
        color: #AD871E;
        font-size: 18px;
        small {
          color: #333;
        }
      }
    }
    th {
      text-align: left;
      font-weight: normal;
      font-size: 12px;
    }
    td {
      text-align: right;
      font-size: 14px;
      small {
        font-size: 11px;
      }
    }
  }
}

.box-nodata {
  text-align: center;
  max-width: 480px;
  @include sp {
    text-align: left;
  }
}

.modalAboutSystem{
  &_image{
    margin-bottom: 20px;
  }
  &_explain{
    color: #E65C7A;
  }
}
</style>
<style lang="scss">
.box-celebrationFeeDetails .swiper-pagination {
  overflow: auto;
  white-space: nowrap;
}
</style>