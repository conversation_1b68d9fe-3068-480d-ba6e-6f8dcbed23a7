<template>
<div class="guest-detail">
  <Main
    :partsHidden="{
      ShowFooterTop: true,
      FixedFooter: true,
      spBreadcrumbs: true,
      spLogo: true
    }" 
    :breadcrumbs="breadcrumbs"
    title="個別ゲスト情報"
    :backlink="backlink"
    >
    <template #main>
      <Loading v-if="isLoading"></Loading>
      <div v-else>
        <div class="selectList">
          <TabTrigger :data="['ゲスト情報', '連名ゲスト情報']" :defaultTabIndex="tabIndex" @onClickTab="tabIndex = $event" />
        </div>
        <ul class="wrapGuestList tabsbj">
          <li :class="{'is-active': tabIndex == 0}">
            <ShowGuestTabBasic
              :guestListId="guestListId"
              :guest="guest"
              @reload="reload"
            ></ShowGuestTabBasic>
          </li>
          <li :class="{'companion': true, 'is-active': tabIndex == 1}">
            <ShowGuestTabCompanion
              :guestListId="guestListId"
              :guest="guest"
              :parentGuest="parentGuest"
              @reload="reload"
            ></ShowGuestTabCompanion>
          </li>
        </ul>
      </div>
    </template>
  
    <template #column1after>
    </template>


    <template #modalWindow>
      <slot name="modalWindow" />
    </template>
  </Main>
</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRouteHash } from '@vueuse/router'
import ShowGuestTabBasic from '@/components/organisms/mypage/guest-list/guest/ShowGuestTabBasic.vue'
import ShowGuestTabCompanion from '@/components/organisms/mypage/guest-list/guest/ShowGuestTabCompanion.vue'
// import ShowGuestTabGift from '@/components/organisms/mypage/guest-list/guest/ShowGuestTabGift.vue'

const route = useRoute();
const hash = useRouteHash()
const guestListId = route.params.guestListId as string;
const id = route.params.id as string;

// 編集モーダル表示フラグ
const tabIndex = ref(0)
if (hash.value == '#tabCompanion') {
  tabIndex.value = 1;
} else if (hash.value == '#tabGift') {
  tabIndex.value = 2;
}

// APIから guest を読み込み
const { guest, refetch } = useGetOneGuest(id, true)

const isLoading = ref(true);
const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('/mypage/guest-list/'+guestListId);
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
})
const reload = async() => {
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
}

watch(tabIndex, () => {
  if (tabIndex.value == 1) {
    hash.value = '#tabCompanion';
  } else if (tabIndex.value == 2) {
    hash.value = '#tabGift';
  } else {
    hash.value = '';
  }
})

const paramPartsHidden = {
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
}

const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "ゲストリスト",
    link: '/mypage/guest-list/',
  },
  {
    title: "ゲスト一覧",
    link: '/mypage/guest-list/'+guestListId,
  },
  {
    title: "個別ゲスト情報",
  },
];
</script>

<style lang="scss">
.guest-detail {
  .l-column1 {
    padding-top: 0;
    padding-bottom: 60px;
  }
  .selectList {
    display: flex;
    border-bottom: 1px solid $color-grayborder;
  }

  .wrapGuestList {
    .update {
      margin: 19px 23px 5px;
      dl {
        display: flex;
        justify-content: flex-end;
        font-size: 11px;
        color: var(--Gray_dark, #9C9C9C);
        letter-spacing: 0.22px;
        // letter-spacing: 2px;
        dt {
          color: $color-graytext2;
        }
        dd {
          margin-left: 5px;
          // color: $color-accent;
        }
      }
    }

    .detailWrap {
      margin-right: auto;
      padding: 0 24px 40px;
      .subTitle {
        position: relative;
        padding: 25px 0 16px;
        h2 {
          margin: 0;
          font-weight: 400;
          font-size: 18px;
          line-height: 120%;
          letter-spacing: 0.04em;
          color: $color-accent;
        }
        .btns {
          position: absolute;
          right: 0;
          top: 25px;
        }
      }
      .manageGuestList {
        position: absolute;
        top: 50%;
        right: 7px;
        transform: translateY(-50%);
        button {
          width: 6px;
          height: 17px;
          font-size: 0;
          vertical-align: top;
          background: url(@/assets/images/icon-more_vert.svg) no-repeat center center/contain;
        }
        &.editGuestList {
          top: 56%;
          right: 1px;
          button {
            width: 24px;
            height: 23px;
            background-image: url(@/assets/images/icon-edit.svg);
          }
        }
      }


      .profileImage {
        display: flex;
        align-items: center;
        color: $color-blacktext2;
        line-height: 120%;
        font-weight: 400;
        img {
          vertical-align: text-bottom;
        }
        .name {
          margin-left: 25px;
          padding-top: 6px;
          strong {
            font-size: 20px;
            font-weight: 400;
            line-height: 130%;
            letter-spacing: 0.8px;
          }
          small {
            margin-left: 7px;
            color: $color-graytext2;
            font-size: 16px;
            letter-spacing: 0.32px;
          }
          .principal {
            display: inline-block;
            position: relative;
            top: -1px;
            margin-left: 7px;
            padding: 3px 10px;
            border-radius: 10px;
            border: 1px solid $color-accent;
            background: $color-mainbackground;
            color: $color-accent;
            text-align: center;
            font-size: 10px;
            font-weight: 400;
            line-height: 1;
            letter-spacing: 0.2px;
          }
          p {
            margin-top: 6px;
            font-size: 14px;
            opacity: 0.7;
          }
        }
      }
      .profileDetail {
        width: 560px;
        max-width: 100%;
        margin-top: 17px;
      }
      & ~ .detailWrap {
        &.is-borderon {
          border-top: 9px solid $color-lightgray2;
        }
      }
      .subTitle + .profileDetail {
        margin-top: 3px;
      }
      &.giftWrap {
        padding-bottom: 25px;
      }
      &.wrappingWrap {
        padding-bottom: 30px;
        .giftDetail {
          margin-top: 0;
        }
        :deep(.listgift) {
          .gifts ~ .gifts[data-v-b51e2f2c] {
            margin-top: 13px;
          }
        }
      }
      &.cardWrap {
        .giftDetail {
          margin-top: 0;
        }
      }
    }

    .giftDetail {
      margin-top: 15px;
    }
    .companionControl {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 17px;
      width: 560px;
      max-width: 100%;
      margin-top: 23px;
      .controlWindow {
        right: 0;
        left: auto;
      }
      .edit {
        font-size: 12px;
        line-height: 100%;
        letter-spacing: 0.24px;
        button {
          position: relative;
          padding: 0 0 0 23px;
          color: $color-accent;
          &::before {
            @include BA;
            left: 0;
            width: 18px;
            height: 18px;
            background-image: url(@/assets/images/icon-edit.svg);
          }
        }
      }
      .button--reversal {
        width: auto;
        min-width: 13.3em;
      }
    }
    .companion {
      .detailWrap .profileImage {
        .img {
          width: 62px;
          .loading .txt { display: none; }
        }
        .name {
          margin-left: 18px;
          padding-top: 0;
        }
      }
    }
  }

  .tabContents {
    padding: 29px 25px;
    p {
      margin-bottom: 5px;
      font-size: 12px;
      line-height: 145%;
      letter-spacing: 0.02em;
      color: $color-blackLight;
    }
    .button--main {
      width: 350px;
      max-width: 100%;
      margin-top: 23px;
    }
  }

  #modalWindow01 ,
  #modalWindow04 {
    :deep(.contents) {
      padding: 2px 0 25px;
    }
  }

  #modalWindow02 {
    :deep(.contents) {
      padding-top: 24px;
      strong {
        display: inline-block;
        color: $color-blacktext2;
        font-size: 16px;
        font-weight: 400;
        line-height: 160%;
        letter-spacing: 0.32px;
      }
      p {
        margin: 21px 0 5px;
        color: $color-blackLight;
        font-size: 12px;
        line-height: 145%;
        letter-spacing: 0.24px;
      }
    }
  }

  @include sp {
  :deep(.breadcrumbs) {
    display: none;
  }

  :deep(.container) {
    margin-top: 0;
  }


  h1 {
    display: none;
  }

  .wrapGuestList {
    .update {
      margin: 17px 15px 5px;
      dl {
        letter-spacing: 1.1px;
      }
    }
    .detailWrap {
      padding: 0 16px 40px;
      .subTitle {
        padding: 21px 0 19px;
        h2 {
          font-size: 16px;
        }
      }
      .profileImage {
        .img {
          width: 72px;
        }
        .name {
          margin-left: 15px;
          padding-top: 3px;
          strong {
            font-size: 18px;
          }
          small {
            font-size: 14px;
          }
          p {
            margin-top: 3px;
            letter-spacing: -1px;
          }
        }
      }
      .profileDetail {
        margin-top: 12px;
      }
      &.giftWrap ,
      &.wrappingWrap {
        padding-bottom: 17px;
      }
    }
    .manageGuestList {
      right: 15px;
    }
    .companion .detailWrap .profileImage {
      // img {
      //   width: 50px;
      // }
      .name {
        margin-left: 14px;
        padding-top: 8px;
        strong {
          font-size: 16px;
        }
        small {
          font-size: 13px;
        }
        .principal {
          top: -3px;
        }
        p {
          margin-top: 3px;
        }
      }
    }
  }

  #modalWindow01 ,
  #modalWindow04 {
    :deep(.contents) {
      padding: 23px 0 25px;
    }
  }

  .tabContents {
    padding: 22px 15px;
  }
  }
}

</style>