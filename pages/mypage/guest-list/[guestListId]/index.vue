<template>
<div class="page-guest-list guest-list is-spbreadcrumbsoff">
  <Main
    :partsHidden="{
      ShowFooterTop: true,
      FixedFooter: true,
      spBreadcrumbs: true,
      spLogo: true,
      spScrollTop: false
    }" 
    :breadcrumbs="breadcrumbs"
    :title="guestList?.name"
    :backlink="backlink"
    contentSize="lg"
    >
    <template #contents-title>
      <NuxtLink :to="backlink" class="backward"><img src="@/assets/images/icon-arrow_backward-b.svg" alt="戻る"></NuxtLink>
      {{ guestList?.name }}
      <a v-if="guests.length" @click="showGuestsDownloadModal = true" class="link-accent"><i class="icn-left material-symbols-outlined">download</i> リストをダウンロード</a>
    </template>
    <template #main>
      <a v-if="guests.length" @click="showGuestsDownloadModal = true" class="link-sp-header"><i class="icn-left material-symbols-outlined">download</i></a>
      <ModalGuestsDownload
        v-if="showGuestsDownloadModal"
        :guestList="guestList"
        @close="showGuestsDownloadModal = false"
      ></ModalGuestsDownload>
      <Loading v-if="isLoadingAll" :fullscreen="true" :isShowIcon="true"></Loading>
      <Loading v-if="isLoading" :isShowIcon="true"></Loading>
      <div v-else>
        <ShowGuestList 
          v-if="guests"
          :events="(guestList?.event_list) ? Object.values(guestList.event_list).map(name => ({name: name})) : []"
          :guestListId="guestListId"
          :guests="guests"
          :orderBy="filter.orderBy?.[0]"
          @onChangeSort="onChangeSort"
          @onReloaded="async () => { await refetch() }"
        />
      </div>
    </template>
  </Main>
</div>
</template>

<script lang="ts" setup>
import ModalGuestsDownload from '@/components/organisms/mypage/guest-list/ModalGuestsDownload.vue'
import {  ref } from 'vue';
const route = useRoute();
const guestListId = route.params.guestListId as string;

// APIから guestList を読み込み
const { guestList } = useGetOneGuestList(String(guestListId));
const filter = ref({
  guest_list_id: guestListId,
  is_update_member_confirm_type: false,
  orderBy: [
    {
      column: QueryGuestsOrderByColumn.CreatedAt,
      order: SortOrder.Desc
    }
  ]
} as any);
const { guests, refetch, loading} = useGetManyGuest(filter.value);

// ゲスト一覧取得したとき 「新着」FLGのデータがあれば「未読」に変更する 一覧取得
const { update, errors } = useBulkUpdateGuest();
const onChangeMemberConfirmType = async() => {
  if (! guests.value) return false;
  let items = [] as any;
  for (let i = 0; i < guests.value.length; i++) {
    const guest = guests.value[i];
    if (guest?.member_confirm_type == 'New') {
      items.push({
        id: guest.id,
        member_confirm_type: 'Unread'
      });
    }
  }
  if (items.length) {
    const isSuccess = await update(items);
  }
}
const isLoading = ref(true);
const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('/mypage/guest-list');
  isLoading.value = true;
  if (route.query.order_by) {
    filter.value.orderBy[0].order = route.query.order_by;
  }
  if (route.query.order_key) {
    filter.value.orderBy[0].column = route.query.order_key;
  }

  await refetch();
  isLoading.value = false;
  setTimeout(function(){
    onChangeMemberConfirmType();
  }, 1000);
})

// ソート
import { QueryGuestsOrderByColumn, SortOrder } from '@/composables/generated';
const onChangeSort = async (data: {column:QueryGuestsOrderByColumn, order: SortOrder}) => {
  filter.value.orderBy[0].column = data.column;
  filter.value.orderBy[0].order = data.order;
  isLoading.value = true;
  await refetch(filter.value);
  isLoading.value = false;
}

const breadcrumbs = ref([
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "ゲストリスト",
    link: './',
  },
  {
    title: "ゲスト一覧",
  },
]);

const showGuestsDownloadModal = ref(false);
</script>

<style lang="scss">
.page-guest-list {
  .contents-title {
    @include sp {
      display: none !important;
    }
  }
  // ゲストリスト表示時の配置を画面中央あたり になるよう配置下げる。※招待状編集画面で出るときと同じ高さ。
  .section-inner {
    position: relative;
    .loading {
      position: absolute;
      bottom: auto;
      left: 0;
      right: 0;
      margin: 0;
      padding: 0;
      top: 50vh;
      transform: translateY(-50%);
      margin-top: -80px;
    }
  }
  .section-inner.l-column1 {
    @include sp {
      min-height: 75vh !important;
    }
  }
}
</style>

<style lang="scss" scoped>
.contents-title {
  position: relative;
  .link-accent {
    // position: absolute;
    // right: 0;
    // top: 50%;
    // margin-top: -1em;
    margin-top: 10px;
    float: right;
    font-size: 14px;
    line-height: 1;
    margin-right: 20px;
    .icn-left {
      vertical-align: middle;
      font-size: 1.2em;
    }
  }
}
.section-inner {
  @include sp {
    padding: 0 !important;
  }
}
.guest-list {

  :deep(.container > .contents) {
    position: relative;
    // padding-bottom: 100px;
    overflow: hidden;
  }
  :deep(.l-column1) {
    height: 100%;
    padding-top: 0;
    padding-bottom: 0;

  }
}

.selectList {
  display: flex;
  border-bottom: 1px solid $color-grayborder;
}

.wrapGuestList {
  height: 100%;
  li.is-active {
    height: 100%;
  }
}

.tabContents {
  padding: 29px 25px;
  p {
    margin-bottom: 5px;
    font-size: 12px;
    line-height: 145%;
    letter-spacing: 0.02em;
    color: $color-blackLight;
  }
  .button--main {
    width: 350px;
    max-width: 100%;
    margin-top: 23px;
  }
}
.required::after {
  content: '*';
  margin-left: 5px;
  vertical-align: top;
  font-size: 12px;
  color: $color-alert2;
}


#modalWindow01 {
  :deep(.contents) {
    p {
      margin-bottom: 5px;
      font-size: 12px;
      line-height: 145%;
      letter-spacing: 0.02em;
      color: $color-blackLight;
    }
  }
}

#modalPrivateInformation {
  :deep(.contents) {
    padding: 2px 0 25px;
  }
}

#modalWindow07 {
  :deep(.contents) {
  
    strong {
      display: inline-block;
      margin-bottom: 30px;
      color: $color-blacktext2;
      font-size: 16px;
      font-weight: 400;
      line-height: 160%;
      letter-spacing: 0.32px;
    }
    .wrap{
      max-width: 680px;
      padding: 23px 73px 20px 90px;
    }
    .row + .row{
      margin-top: 28px;
    }
    .note{
      color: #333;
      background: #F4F4F4;
      font-size: 12px;
      line-height: 1.5;
      border-radius: 4px;
      padding: 10px;
      margin: 12px 0;
    }
    .checkbox_note{
      font-size: 10px;
      color: #5A5A5A;
      line-height: 1.5;
      padding-left: 26px;
      margin-top: -6px;
      margin-left: 1em;
      text-indent: -1em;
    }
    .edit{
      display: inline-block;
      font-size: 14px;
      line-height: 1.2;
      color: #B18A3E;
      padding: 10px 0;
      text-decoration: none;
      img{
        width: 18px;
        height: 18px;
        vertical-align: text-bottom;
      }
      &:hover{
        text-decoration: underline;
      }
    }
    .button_wrap{
      max-width: 164px;
      margin: 0 0 0 auto;
    }
    .text-right{
      text-align: right;
    }
    .is-border-top{
      border-top: 1px solid #F4F4F4;
      padding-top: 16px;
    }
    h2{
      color: #B18A3E;
      font-size: 18px;
      font-weight: normal;
      line-height: 1.2;
      margin: 0 0 21px;
      img{
        vertical-align: text-bottom;
      }
    }
    h3{
      color: #333;
      font-size: 14px;
      font-weight: normal;
      margin: 20px 0;
      line-height: 1.2;
    }
    p{
      font-size: 12px;
      line-height: 1.45;
      color: #333;
      margin-bottom: 32px;
    }
    hr{
      border: 2px solid #F4F4F4;
    }
    .profileImage{
      display: flex;
      align-items: center;
      margin-bottom: 28px;
      & > img{
        margin-right: 25px;
      }
    }
    .tagGroup{
      .tag{
        display: inline-block;
        color: $color-main;
        font-size: 11px;
        line-height: 1.6;
        border: 1px solid $color-main;
        border-radius: 26px;
        padding: 2px 8px 2px 12px;
        margin-right: 4px;
        margin-bottom: 4px;
      }
    }
    .unit{
      color: #333;
      margin: 0 20px 0 4px;
      font-size: 12px;
    }
    .freeItem {
      .row {
        & + h3 {
          padding-top: 28px;
          border-top: 1px solid $color-lightgray;
        }
      }
    }

    .deleteRow {
      margin: 20px 0 24px;
      text-align: right;
      button {
        position: relative;
        padding: 0 0 0 22px;
        color: $color-alert;
        font-size: 12px;
        line-height: 120%;
        letter-spacing: 0.24px;
        &::before {
          @include BA;
          left: 0;
          width: 18px;
          height: 18px;
          background-image: url(@/assets/images/icon-delete.svg);
        }
      }
    }
  }
}

#modalWindow08 {
  :deep(.contents) {
    padding-top: 24px;
    strong {
      display: inline-block;
      color: $color-blacktext2;
      font-size: 16px;
      font-weight: 400;
      line-height: 160%;
      letter-spacing: 0.32px;
    }
    p {
      margin: 21px 0 5px;
      color: $color-blackLight;
      font-size: 12px;
      line-height: 145%;
      letter-spacing: 0.24px;
    }
  }
}

.link-sp-header {
  display: none;
  position: fixed;
  right: 0;
  top: -45px;
  color: #fff;
  z-index: 100;
  padding: 7px;
  transition: 0.35s ease;
  [data-scroll="top"] & {
    top: 0;
  }
  @include sp {
    display: block;
  }
}

@include sp {
.breadcrumbs {
  display: none;
}

h1 {
  display: none;
}

.selectList {
  .addGuestList {
    margin-left: 3px;
  }
}
.addGuest {
  bottom: 70px;
}
.tabContents {
  padding: 22px 15px;
}

.wrapGuestList {
  li {
    display: flex;
    flex-direction: column;
    :deep(.subTitle) {
      order: 2;
    }
    :deep(.manageGuestList) {
      order: 1;
    }
    :deep(.showList) {
      order: 3;
    }
  }
}

}

</style>