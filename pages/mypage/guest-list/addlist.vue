<template>
<Main
  :partsHidden="{
    ShowFooterTop: true,
    FixedFooter: true,
    spH1Title: true,
    spBreadcrumbs: true,
    spLogo: true
  }" 
  :breadcrumbs="breadcrumbs"
  title="ゲストリストを追加"
  :backlink="backlink"
  >
  <template #main>
    <article class="article-main">
    <Loading v-if="isLoading"></Loading>
    <div v-else class="wrapAddGuestList">
      <div v-if="error" class="input-error" style="margin-bottom: 20px;">{{ error }}</div>
      <form class="searchform mb-20" @submit.prevent="onClickSave()">
        <InputText 
          title="新規ゲストリスト名"
          placeholder="例) 2次会"
          :required="true" 
          size="full" 
          :value="String(input.input.name)"
          :error="getValidationMessage(v$.input.name)"
          @input="input.input.name = $event.target.value"
          />
      </form>
      <button type="button" @click="onClickSave()" class="btn btn-secondary btn-block">追加する</button>
    </div>
  </article>
  </template>
</Main>
</template>

<script lang="ts" setup>
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { CreateGuestListInput } from '@/composables/generated';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();

const router = useRouter();

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "ゲストリスト",
    link: '/mypage/guest-list',
  },
  {
    title: "ゲストリストを追加"
  }
];

// 入力項目
const input = ref({
  input: {
    name: ''
  } as CreateGuestListInput
})

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return {
    input: {
      name: { 
        required: helpers.withMessage(validationMessage.required('ゲストリスト名'), required) 
      },
    }
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

/**
 * 登録
 */
// 登録API
const { create, errors } = useCreateGuestList();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await create(input.value.input);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }

  // ToastMessage
  addToastMessage({message: 'ゲストリスト「'+input.value.input.name+'」を追加しました '});
  router.push({ path: `/mypage/guest-list` })
};


const paramPartsHidden = {
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
}

const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('/mypage/guest-list');
});
</script>

<style lang="scss" scoped>
.wrapAddGuestList {
  padding: 24px 16px;
  p {
    margin-bottom: 5px;
    font-size: 12px;
    line-height: 145%;
    letter-spacing: 0.02em;
    color: $color-blackLight;
  }
}
</style>