<template>
<Main
  :partsHidden="{
    ShowFooterTop: true,
    FixedFooter: true,
    spH1Title: true,
    spBreadcrumbs: true,
    spLogo: true,
    spBackTop: false
  }" 
  :breadcrumbs="breadcrumbs"
  title="ゲストリスト"
  :backlink="backlink"
  >
  <template #contents-before>
    <section class="guestlist-header-bnrs" v-if="headerTexts.length"><div class="inner">
      <template v-if="pcCampaignBannerDatas?.[0]">
        <div class="mv-banner pc_only topBanner_pc_1">
          <a :href="pcCampaignBannerDatas?.[0]?.plan_text2" class="txt">
            <template v-if="pcCampaignBannerDatas?.[0]?.image?.url">
              <img :src="pcCampaignBannerDatas?.[0]?.image?.url" alt="">
            </template>
            <template v-else>
              {{pcCampaignBannerDatas?.[0]?.plan_text}}
            </template>
          </a>
        </div>
      </template>
      <template v-if="spCampaignBannerDatas?.[0]">
        <div class="mv-banner sp_only topBanner_sp_1">
          <a :href="spCampaignBannerDatas?.[0]?.plan_text2" class="txt topBanner_sp_1">
            <template v-if="spCampaignBannerDatas?.[0]?.image?.url">
              <img :src="spCampaignBannerDatas?.[0]?.image?.url" alt="">
            </template>
            <template v-else>
              {{spCampaignBannerDatas?.[0]?.plan_text}}
            </template>
          </a>
        </div>
      </template>
    </div></section>
  </template>
  <template #main>
  <article class="article-main guest-list">
    <!-- <div class="sort-wrap">
      <ControlWindowBtn data-mode="sort">
        <template #button>
          <button class="link-sort">
            <span v-if="orderBy.column === QueryGuestListsOrderByColumn.Name && orderBy.order === SortOrder.Asc">名前 昇順</span>
            <span v-else-if="orderBy.column === QueryGuestListsOrderByColumn.Name && orderBy.order === SortOrder.Desc">名前 降順</span>
          </button>
        </template>
        <ul>
          <li :class="{'is--current': (orderBy.column === QueryGuestListsOrderByColumn.Name && orderBy.order === SortOrder.Asc)}" @click.prevent="onClickSort(QueryGuestListsOrderByColumn.Name, SortOrder.Asc)">名前 昇順</li>
          <li :class="{'is--current': (orderBy.column === QueryGuestListsOrderByColumn.Name && orderBy.order === SortOrder.Desc)}" @click.prevent="onClickSort(QueryGuestListsOrderByColumn.Name, SortOrder.Desc)">名前 降順</li>
        </ul>
      </ControlWindowBtn>
    </div> -->
    <Loading v-if="isLoading"></Loading>
    <ul class="wrapGuestList" v-else>
      <li v-for="(guestList) in guestLists" :key="guestList.id">
        <NuxtLink :to="'/mypage/guest-list/'+guestList.id">
          <h2>{{ guestList.name }}</h2>
          <p>
            <span>登録数：<span class="amount">{{ guestList?.guests.length }}</span>名</span>
            <span v-if="[...new Set(guestList?.web_invitations?.map((item:any) => item.editor_settings?.blocks?.find((block:any) => block.id == 'information')?.contents?.date).filter(date => date != null))]?.[0]">
              開催日：{{ $dayjs([...new Set(guestList?.web_invitations?.map((item:any) => item.editor_settings?.blocks?.find((block:any) => block.id == 'information')?.contents?.date).filter(date => date != null))]?.[0]).format('YYYY/MM/DD') }}
            </span>
            <span v-else>
              開催日：——/—/—
            </span>
            <span v-if="guestList?.guests.length && guestList?.latest_guest_updated_at">更新：{{ $dayjs(guestList.latest_guest_updated_at).format('YYYY/MM/DD') }}</span>
            <span v-else-if="guestList?.updated_at">更新：{{ $dayjs(guestList.updated_at).format('YYYY/MM/DD') }}</span>
          </p>
          <span class="num" v-if="guestList?.guests.filter(guest => guest.member_confirm_type == 'New').length">{{ guestList?.guests.filter(guest => guest.member_confirm_type == 'New').length }}</span>
          <div class="menu">
            <ControlWindowBtn>
              <ul>
                <li v-if="! guestList.is_default" @click.prevent="onClickShowEditModal(guestList)">リスト名を変更する</li>
                <li @click.prevent="onClickShowCopyModal(guestList)">リストを複製する</li>
                <li v-if="! guestList.is_default && ! guestList.web_invitations.length" @click.prevent="onClickShowDeleteModal(guestList)">リストを削除する</li>
              </ul>
            </ControlWindowBtn>
          </div>
        </NuxtLink>
      </li>
    </ul>
    <div class="addGuestList" v-if="! isLoading">
      <NuxtLink to="/mypage/guest-list/addlist" class="btn btn-secondary btn-block"><i class="icn-left material-icons">add</i> ゲストリストを追加</NuxtLink>
    </div>
  </article>
    <section class="guestlist-bottom-bnrs" v-if="banners.length">
      <div class="inner">
        <div class="swiper-bnrs-wrap">
          <Swiper
            class="swiper-bnrs"
            navigation
            :modules="[Navigation, Autoplay]"
            :slidesPerView="1"
            :speed="500"
            :loop="true"
            :autoplay="{
              delay: 5000,
              disableOnInteraction: false,
            }">
            <SwiperSlide v-for="(item , index) in banners" :key="index" class="swiper-slide">
              <a v-if="item?.plan_text2" class="swiper-item" :href="item?.plan_text2">
                <img :src="item.image.url" :alt="item.plan_text">
              </a>
              <spam v-else class="swiper-item">
                <img :src="item.image.url" :alt="item.plan_text">
              </spam>
            </SwiperSlide>
          </Swiper>
        </div>
      </div>
    </section>
  </template>

  <template #modalWindow>
    <ModalUpdateGuestList 
      v-if="showUpdateModal"
      :guestList="guestList" 
      @close="showUpdateModal = false"
    />
    <ModalCopyGuestList 
      v-if="showCopyModal"
      :guestList="guestList" 
      @created="createdGuestList"
      @close="showCopyModal = false;"
    />
    <ModalDeleteGuestList 
      v-if="showDeleteModal"
      :guestList="guestList" 
      @deleted="deletedGuestList"
      @close="showDeleteModal = false;"
    />
  </template>
</Main>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { QueryGuestListsOrderByColumn, SortOrder } from '@/composables/generated';

import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Autoplay, FreeMode } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const { $dayjs } : any = useNuxtApp();

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "ゲストリスト"
  }
];
const orderBy = ref({
  column: QueryGuestListsOrderByColumn.CreatedAt,
  order: SortOrder.Asc
} as QueryGuestListsOrderByOrderByClause);

// APIから guestLists を読み込み
const { guestLists, refetch } = useGetManyGuestList([{
  column: QueryGuestListsOrderByColumn.IsDefault,
  order: SortOrder.Desc
}, orderBy.value]);

const guestList = ref({} as {
  id: string;
  name: string;
});

const isLoading = ref(true);
const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink();
  isLoading.value = true;
  await refetch({orderBy: [{
    column: QueryGuestListsOrderByColumn.IsDefault,
    order: SortOrder.Desc
  },orderBy.value]});
  isLoading.value = false;
});


// 編集モーダル表示時
const onClickSort = async (column:QueryGuestListsOrderByColumn, order: SortOrder) => {
  orderBy.value.column = column;
  orderBy.value.order = order;
  isLoading.value = true;
  await refetch({orderBy: [orderBy.value]});
  isLoading.value = false;
}


/**
 * 編集
 */
// 編集モーダル表示フラグ
const showUpdateModal = ref(false)

// 編集モーダル表示時
const onClickShowEditModal = (targetGuestList: {
  id: string;
  name: string;
  member: {
    id: string;
  }
}) => {
  guestList.value = targetGuestList;
  showUpdateModal.value = true;
}


/**
 * 複製
 */

 // 複製モーダル表示フラグ
 const showCopyModal = ref(false)

// 複製モーダル表示時
const onClickShowCopyModal = (targetGuestList: {
  id: string;
  name: string;
  member: {
    id: string;
  }
}) => {
  guestList.value = targetGuestList;
  showCopyModal.value = true;
}

// リロードしてメッセージを表示
const createdGuestList = async (message: string) => {
  isLoading.value = true;
  await refetch();
  showCopyModal.value = false;
  isLoading.value = false;
}


/**
 * 削除
 */

 // 削除モーダル表示フラグ
 const showDeleteModal = ref(false)

// 削除モーダル表示時
const onClickShowDeleteModal = (targetGuestList: {
  id: string;
  name: string;
  member: {
    id: string;
  }
}) => {
  guestList.value = targetGuestList;
  showDeleteModal.value = true;
}

// リロードしてメッセージを表示
const deletedGuestList = async (message: string) => {
  showDeleteModal.value = false;
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
}

// microCMSからメインビジュアル一覧を取得
const microCms = new MicroCms();
const { data: microCmsData, error: microCmsError } = await microCms.fetch('/contents', {
  filters: 'code[equals]guestlist_footer_banner'
});
const banners = computed(() => {
  if (! microCmsHeaderText.value?.contents?.[0]?.item) return [];
  try {
    let results = JSON.parse(JSON.stringify(microCmsData.value?.contents?.[0]?.item));
    return results;
  } catch (error) {
    
  }
  return [];
});

const { data: microCmsHeaderText, error: microCmsHeaderTextError } = await microCms.fetch('/contents', {
  filters: 'code[equals]guestlist_header_banner'
}, false);
const headerTexts = computed(() => {
  if (! microCmsHeaderText.value?.contents?.[0]?.item) return [];
  try {
    let results = JSON.parse(JSON.stringify(microCmsData.value?.contents?.[0]?.item));
    return results;
  } catch (error) {
    
  }
  return [];
});

const pcCampaignBannerDatas = ref([])
const spCampaignBannerDatas = ref([])
const dynamicStyles = ref([])
watch(microCmsHeaderText, () => {
  let results = JSON.parse(JSON.stringify(microCmsHeaderText.value?.contents?.[0]?.item));
  if(results){
    pcCampaignBannerDatas.value = results.filter(item => item.category.includes('PC'));
    spCampaignBannerDatas.value = results.filter(item => item.category.includes('SP'));
    dynamicStyles.value = [
      ...pcCampaignBannerDatas.value.map(banner => banner.plan_text3),
      ...spCampaignBannerDatas.value.map(banner => banner.plan_text3)
    ]
    applyDynamicStyles()
    applyDynamicStyles()
  }
});

const applyDynamicStyles = () => {
  // 既存のスタイルタグを削除
  const existingStyles = document.querySelectorAll('style[data-guestlist-campaign-banner]')
  existingStyles.forEach(style => style.remove())

  // 新しいスタイルタグを作成し追加
  const styleElement = document.createElement('style')
  styleElement.setAttribute('data-campaign-banner', '')
  styleElement.textContent = dynamicStyles.value.join('\n')
  document.head.appendChild(styleElement)
}
onMounted(() => {
  dynamicStyles.value = [
    ...pcCampaignBannerDatas.value.map(banner => banner.plan_text3),
    ...spCampaignBannerDatas.value.map(banner => banner.plan_text3)
  ]
  applyDynamicStyles()
})
// dynamicStylesが変更されたら再適用
watch(dynamicStyles, applyDynamicStyles)
</script>

<style lang="scss" scoped>

.article-main.guest-list {
  .modalWindow01 ,
  .modalWindow02 {
    :deep(.modalContainer .contents) {
      p {
        margin-bottom: 3px;
        color: $color-blackLight;
        font-size: 12px;
        font-weight: 400;
        line-height: 145%;
        letter-spacing: 0.24px;
      }
      strong {
        color: $color-blacktext2;
        font-size: 14px;
        font-weight: 400;
        line-height: 150%;
        letter-spacing: 0.28px;
        display: block;
        margin: 8px 0 15px;
      }
      .attention {
        margin-top: 24px;
        padding: 10px;
        border-radius: 4px;
        background: $color-lightgray2;
      }
    }
  }
  .l-column1-inner {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 1050px;
    margin: 0 auto;
  }
  .wrapGuestList {
    li {
      position: relative;
      border-bottom: 1px solid $color-grayborder;
      a {
        display: block;
        padding: 21px 24px 19px;
        @include sp {
          padding: 15px 16px;
        }
        text-decoration: none;
        transition: 0.35s ease;
        &:hover {
          background: $color-lightbackground;
        }
      }
      h2 {
        color: $color-blacktext2;
        font-size: 20px;
        font-weight: 400;
        line-height: 120%;
        letter-spacing: 0.72px;
        margin: 0;
      }
      p {
        color: $color-graytext2;
        font-family: Noto Sans JP;
        font-size: 12px;
        line-height: 100%;
        margin: 0;
        margin-bottom: 5px;
        margin-top: 8px;
        .amount{
          font-size: 16px;
        }
        span {
          display: inline-block;
          & ~ span {
            margin-left: 7px;
            &::before {
              content: '|';
              margin-right: 6px;
            }
          }
        }
      }
      .menu {
        position: absolute;
        top: 50%;
        right: 18px;
        @include sp {
          right: 10px;
        }
      }
      .num {
        position: absolute;
        top: 50%;
        margin-top: -11px;
        right: 74px;
        color: #fff;
        background: #AD871E;
        padding: 4px 5px;
        min-width: 28px;
        min-height: 24px;
        text-align: center;
        font-size: 14px;
        line-height: 1;
        font-weight: 600;
        border-radius: 50px;
        @include sp {
          right: 52px;
        }
      }
    }
  }
  .addGuestList {
    margin-top: 18px;
    padding: 0 16px;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    max-width: 440px;
    @include sp {
      max-width: none;
    }
  }
}
@include sp {
h1 {
  display: none;
}
.guest-list {

  .wrapGuestList {
    li {
      padding: 0;
      h2 {
        font-size: 18px;
        padding-right: 30px;
      }
      p {
        font-size: 11px;
        margin-top: 7px;
        .amount{
          font-size: 16px;
        }
      }
    }
  }
}
}

.sort-wrap {
  text-align: right;
  padding-top: 20px;
  padding-bottom: 20px;
  @include sp {
    display: none;
  }
}

.guest-list {
  position: relative;
}
.mv-banner {
  background: #FF7F2E;
  color: #FFF;
  font-size: 20px;
  font-weight: 700;
  display: flex;
  justify-content: center;
  align-items: center;
  // height: 40px;
}
.guestlist-header-bnrs {
  // background: #FF7F2E;
  // color: #FFF;
  // font-size: 20px;
  // font-weight: 700;
  // text-align: center;
  // line-height: 1.6;
  // padding: 4px;
  .mv-banner {
    margin-bottom: 20px;
    &.pc_only {
      display: flex !important;
      @include sp{
        display: none !important;
      }
    }
    &.sp_only {
      display: none !important;
      @include sp{
        display: flex !important;
      }
    }
  }
  @include sp {
    position: sticky;
    z-index: 10;
    top: 0px;
    left: 0;
    right: 0;
    [data-scroll="top"] & {
      top: 36px;
    }
  }
}
.guestlist-bottom-bnrs {
  // min-height: 100px;
  > .inner {
    position: fixed;
    left: 259px;
    right: 0;
    bottom: 0;
    transition: all 0.35s ease;
  }
  .mypage-drawer-menu.close ~ .contents & .inner {
    left: 78px;
  }
  .swiper-bnrs-wrap {
    max-width: 744px;
    margin-left: auto;
    margin-right: auto;
    background: rgba(#EFF8FF, .7);
    padding: 10px 10px;
  }
  @include sp {
    // height: 0 !important;
    margin-bottom: 55px;
    > .inner {
      position: fixed;
      left: 0;
      right: 0;
      bottom: -500px;
      [data-scroll="top"] & {
        bottom: 55px;
      }
    }
  }
}

</style>

<style lang="scss">
.guestlist-bottom-bnrs {
  .swiper-bnrs-wrap {
    // overflow: hidden;
    .swiper {
      // overflow: visible;
      padding: 0 10px;
    }
    .swiper-wrapper {
      // justify-content: center;
      @include sp {
        justify-content: unset;
      }
    }
    .swiper-slide {
      padding: 0 10px;
      text-align: center;
    }
    .swiper-button-next:after, 
    .swiper-button-prev:after {
      font-size: 14px;
      color: #000;
      font-weight: bold;
      opacity: .6;
    }
    .swiper-button-prev {
      left: -10px;
    }
    .swiper-button-next {
      right: -10px;
    }
  }
}
</style>