<template>
<div class="guest-recipient is-spuioff">
  <Main :partsHidden="paramPartsHidden">

    <template #title>
    </template>
  
    <template #main>
      <h2>送信が完了しました！</h2>
      <p>ご回答いただきありがとうございました </p>
      <p>当日の詳細につきましては また後日あらためてご案内いたします <br>
        当日 皆さまにお会いできることを心から楽しみにしております </p>
      <div class="next">
        <ButtonMainColor baseColor="glay" to="#">閉じる</ButtonMainColor>
      </div>
    </template>
  
    <template #column1after>
    </template>

    <template #modalWindow>
    </template>

  </Main>
</div>
</template>

<script lang="ts" setup>
const paramPartsHidden = {
  Breadcrumbs: 'hidden',
  ShowFooterTop: 'hidden',
  FixedFooterBottomReceiver: 'visible',
  FixedFooter: 'hidden',
}

const ListHeaderDrawerMenu02 = {
    title: 'カテゴリ',
    datail: [
      {
        class: "category",
        menu: "セットアイテム",
        innermenu: [
          {
            menu: "セットアイテム01",
            link: "#01"
          },
          {
            menu: "セットアイテム02",
            link: "#02"
          },
          {
            menu: "セットアイテム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ペーパーアイテム",
        innermenu: [
          {
            menu: "ペーパーアイテム01",
            link: "#01"
          },
          {
            menu: "ペーパーアイテム02",
            link: "#02"
          },
          {
            menu: "ペーパーアイテム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ウェルカムスペース・演出アイテム",
        innermenu: [
          {
            menu: "ウェルカムスペース・演出アイテム01",
            link: "#01"
          },
          {
            menu: "ウェルカムスペース・演出アイテム02",
            link: "#02"
          },
          {
            menu: "ウェルカムスペース・演出アイテム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "プチギフト",
        innermenu: [
          {
            menu: "プチギフト01",
            link: "#01"
          },
          {
            menu: "プチギフト02",
            link: "#02"
          },
          {
            menu: "プチギフト03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "両親贈呈品・子育て感謝状",
        innermenu: [
          {
            menu: "両親贈呈品・子育て感謝状01",
            link: "#01"
          },
          {
            menu: "両親贈呈品・子育て感謝状02",
            link: "#02"
          },
          {
            menu: "両親贈呈品・子育て感謝状03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "結婚式アルバム",
        innermenu: [
          {
            menu: "結婚式アルバム01",
            link: "#01"
          },
          {
            menu: "結婚式アルバム02",
            link: "#02"
          },
          {
            menu: "結婚式アルバム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "結婚報告はがき・年賀状",
        innermenu: [
          {
            menu: "結婚報告はがき・年賀状01",
            link: "#01"
          },
          {
            menu: "結婚報告はがき・年賀状02",
            link: "#02"
          },
          {
            menu: "結婚報告はがき・年賀状03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "引き出物宅配",
        innermenu: [
          {
            menu: "引き出物宅配01",
            link: "#01"
          },
          {
            menu: "引き出物宅配02",
            link: "#02"
          },
          {
            menu: "引き出物宅配03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "内祝い・お返しギフト",
        innermenu: [
          {
            menu: "内祝い・お返しギフト01",
            link: "#01"
          },
          {
            menu: "内祝い・お返しギフト02",
            link: "#02"
          },
          {
            menu: "内祝い・お返しギフト03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ギフト・プレゼント・贈答品",
        innermenu: [
          {
            menu: "ギフト・プレゼント・贈答品01",
            link: "#01"
          },
          {
            menu: "ギフト・プレゼント・贈答品02",
            link: "#02"
          },
          {
            menu: "ギフト・プレゼント・贈答品03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "オリジナルグッズ",
        innermenu: [
          {
            menu: "オリジナルグッズ01",
            link: "#01"
          },
          {
            menu: "オリジナルグッズ02",
            link: "#02"
          },
          {
            menu: "オリジナルグッズ03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "結婚式ムービー",
        innermenu: [
          {
            menu: "結婚式ムービー01",
            link: "#01"
          },
          {
            menu: "結婚式ムービー02",
            link: "#02"
          },
          {
            menu: "結婚式ムービー03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ウェディング・ブライダルアクセサリー",
        innermenu: [
          {
            menu: "ウェディング・ブライダルアクセサリー01",
            link: "#01"
          },
          {
            menu: "ウェディング・ブライダルアクセサリー02",
            link: "#02"
          },
          {
            menu: "ウェディング・ブライダルアクセサリー03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ウェディングドレス",
        innermenu: [
          {
            menu: "ウェディングドレス01",
            link: "#01"
          },
          {
            menu: "ウェディングドレス02",
            link: "#02"
          },
          {
            menu: "ウェディングドレス03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "二次会景品",
        innermenu: [
          {
            menu: "二次会景品01",
            link: "#01"
          },
          {
            menu: "二次会景品02",
            link: "#02"
          },
          {
            menu: "二次会景品03",
            link: "#03"
          },
        ],
      }, 
    ],
}

const ListHeaderDrawerMenu = {
  ListHeaderDrawerMenu02,
}

const apiFixedHeaderLogin = {
  // 本来はAPIでデータを取得
  login: true,
  name: '鈴木 このみ',
  date: '2023年4月1日',
}

const apiFixedHeaderBadge = {
  // 本来はAPIでデータを取得
  noticeBadges: 3,
  favoriteBadges: 2,
  cartBadges: 1,
}

const apiFixedHeaderRecentItem = {
  // 本来はAPIでデータを取得
  recent: [
    {
      thumbnail: "/images/sample/smallthumbnail01.png",
      link: "#01"
    },
    {
      thumbnail: "/images/sample/smallthumbnail02.png",
      link: "#02"
    },
    {
      thumbnail: "/images/sample/smallthumbnail03.png",
      link: "#03"
    },
    {
      thumbnail: "/images/sample/smallthumbnail04.png",
      link: "#04"
    },
  ],
}

provide('stateMenu', ListHeaderDrawerMenu);
  
provide('stateLogin', apiFixedHeaderLogin);
provide('stateBadge', apiFixedHeaderBadge);
provide('stateRecent', apiFixedHeaderRecentItem);

</script>

<style lang="scss" scoped>
:deep(.l-column1) {
  width: 560px;
  max-width: 100%;
  margin: 0 auto;
  padding-top: 11px;
  text-align: center;
}
p {
  font-size: 16px;
  line-height: 160%;
  & ~ p {
    margin-top: 27px;
  }
}
h2 {
  font-size: 24px;
  font-weight: 400;
  line-height: 130%;
  letter-spacing: 0.48px;
  color: $color-accent;
}

.next {
  max-width: 400px;
  margin: 23px auto 0;
}

@include sp {
:deep(.l-column1) {
  padding: 9px 16px 0;
  text-align: left;
}
p {
  line-height: 150%;
  font-size: 14px;
  & ~ p {
    margin-top: 21px;
  }
}

h2 {
  margin-bottom: 24px;
  font-size: 18px;
}

.next {
  margin-top: 39px;
}

}

</style>