<template>
<div class="guest-recipient is-spuioff">
  <Main :partsHidden="paramPartsHidden">

    <template #title>
    </template>
  
    <template #main>
      <p>お手数ではございますが 以下の項目についてご入力をお願いいたします </p>
      <strong><b>*</b>は必須項目です</strong>
      <div class="row">
        <h2>基本情報</h2>
        <FormBasicInformation :partsHidden="paramFormPartsHidden01" />
      </div>
      <div class="row is-border-top">
        <h2>お連れ様</h2>
        <FormBasicInformation :partsHidden="paramFormPartsHidden02" />
      </div>
      <div class="row">
        <h2>詳細情報</h2>
        <FormAddress :partsHidden="paramFormPartsHidden03" />
      </div>
      <div class="next">
        <ButtonMainColor to="./confirm">確認画面へ</ButtonMainColor>
      </div>
    </template>
  
    <template #column1after>
    </template>

    <template #modalWindow>
      <Modal id="ModalOldkanji" class="close closing">
        <ModalOldkanji class="standAlone" @emitClose="closeModal" />
      </Modal>
    </template>

  </Main>
</div>
</template>

<script lang="ts" setup>
const paramPartsHidden = {
  Breadcrumbs: 'hidden',
  ShowFooterTop: 'hidden',
  FixedFooterBottomReceiver: 'visible',
  FixedFooter: 'hidden',
}

const paramFormPartsHidden01 = {
  Honorific: 'hidden',
  Attendance: 'hidden',
  Title: 'hidden',
  Group: 'hidden',
  Tag: 'hidden',
  Allergy: 'hidden',
  Birthday: 'hidden',
  Deletecompanion: 'hidden',
  Addcompanion: 'hidden',
}

const paramFormPartsHidden02 = {
  Oldkanji: 'hidden',
  Honorific: 'hidden',
  Attendance: 'hidden',
  Title: 'hidden',
  Group: 'hidden',
  Tag: 'hidden',
  Allergy: 'hidden',
  Birthday: 'hidden',
}

const paramFormPartsHidden03 = {
  Delivery: 'hidden',
  Name: 'hidden',
}


const ListHeaderDrawerMenu02 = {
    title: 'カテゴリ',
    datail: [
      {
        class: "category",
        menu: "セットアイテム",
        innermenu: [
          {
            menu: "セットアイテム01",
            link: "#01"
          },
          {
            menu: "セットアイテム02",
            link: "#02"
          },
          {
            menu: "セットアイテム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ペーパーアイテム",
        innermenu: [
          {
            menu: "ペーパーアイテム01",
            link: "#01"
          },
          {
            menu: "ペーパーアイテム02",
            link: "#02"
          },
          {
            menu: "ペーパーアイテム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ウェルカムスペース・演出アイテム",
        innermenu: [
          {
            menu: "ウェルカムスペース・演出アイテム01",
            link: "#01"
          },
          {
            menu: "ウェルカムスペース・演出アイテム02",
            link: "#02"
          },
          {
            menu: "ウェルカムスペース・演出アイテム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "プチギフト",
        innermenu: [
          {
            menu: "プチギフト01",
            link: "#01"
          },
          {
            menu: "プチギフト02",
            link: "#02"
          },
          {
            menu: "プチギフト03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "両親贈呈品・子育て感謝状",
        innermenu: [
          {
            menu: "両親贈呈品・子育て感謝状01",
            link: "#01"
          },
          {
            menu: "両親贈呈品・子育て感謝状02",
            link: "#02"
          },
          {
            menu: "両親贈呈品・子育て感謝状03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "結婚式アルバム",
        innermenu: [
          {
            menu: "結婚式アルバム01",
            link: "#01"
          },
          {
            menu: "結婚式アルバム02",
            link: "#02"
          },
          {
            menu: "結婚式アルバム03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "結婚報告はがき・年賀状",
        innermenu: [
          {
            menu: "結婚報告はがき・年賀状01",
            link: "#01"
          },
          {
            menu: "結婚報告はがき・年賀状02",
            link: "#02"
          },
          {
            menu: "結婚報告はがき・年賀状03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "引き出物宅配",
        innermenu: [
          {
            menu: "引き出物宅配01",
            link: "#01"
          },
          {
            menu: "引き出物宅配02",
            link: "#02"
          },
          {
            menu: "引き出物宅配03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "内祝い・お返しギフト",
        innermenu: [
          {
            menu: "内祝い・お返しギフト01",
            link: "#01"
          },
          {
            menu: "内祝い・お返しギフト02",
            link: "#02"
          },
          {
            menu: "内祝い・お返しギフト03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ギフト・プレゼント・贈答品",
        innermenu: [
          {
            menu: "ギフト・プレゼント・贈答品01",
            link: "#01"
          },
          {
            menu: "ギフト・プレゼント・贈答品02",
            link: "#02"
          },
          {
            menu: "ギフト・プレゼント・贈答品03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "オリジナルグッズ",
        innermenu: [
          {
            menu: "オリジナルグッズ01",
            link: "#01"
          },
          {
            menu: "オリジナルグッズ02",
            link: "#02"
          },
          {
            menu: "オリジナルグッズ03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "結婚式ムービー",
        innermenu: [
          {
            menu: "結婚式ムービー01",
            link: "#01"
          },
          {
            menu: "結婚式ムービー02",
            link: "#02"
          },
          {
            menu: "結婚式ムービー03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ウェディング・ブライダルアクセサリー",
        innermenu: [
          {
            menu: "ウェディング・ブライダルアクセサリー01",
            link: "#01"
          },
          {
            menu: "ウェディング・ブライダルアクセサリー02",
            link: "#02"
          },
          {
            menu: "ウェディング・ブライダルアクセサリー03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "ウェディングドレス",
        innermenu: [
          {
            menu: "ウェディングドレス01",
            link: "#01"
          },
          {
            menu: "ウェディングドレス02",
            link: "#02"
          },
          {
            menu: "ウェディングドレス03",
            link: "#03"
          },
        ],
      }, 
      {
        class: "category",
        menu: "二次会景品",
        innermenu: [
          {
            menu: "二次会景品01",
            link: "#01"
          },
          {
            menu: "二次会景品02",
            link: "#02"
          },
          {
            menu: "二次会景品03",
            link: "#03"
          },
        ],
      }, 
    ],
}

const ListHeaderDrawerMenu = {
  ListHeaderDrawerMenu02,
}

const apiFixedHeaderLogin = {
  // 本来はAPIでデータを取得
  login: true,
  name: '鈴木 このみ',
  date: '2023年4月1日',
}

const apiFixedHeaderBadge = {
  // 本来はAPIでデータを取得
  noticeBadges: 3,
  favoriteBadges: 2,
  cartBadges: 1,
}

const apiFixedHeaderRecentItem = {
  // 本来はAPIでデータを取得
  recent: [
    {
      thumbnail: "/images/sample/smallthumbnail01.png",
      link: "#01"
    },
    {
      thumbnail: "/images/sample/smallthumbnail02.png",
      link: "#02"
    },
    {
      thumbnail: "/images/sample/smallthumbnail03.png",
      link: "#03"
    },
    {
      thumbnail: "/images/sample/smallthumbnail04.png",
      link: "#04"
    },
  ],
}

provide('stateMenu', ListHeaderDrawerMenu);
  
provide('stateLogin', apiFixedHeaderLogin);
provide('stateBadge', apiFixedHeaderBadge);
provide('stateRecent', apiFixedHeaderRecentItem);

</script>

<style lang="scss" scoped>
:deep(.l-column1) {
  width: 560px;
  max-width: 100%;
  margin: 0 auto;
  padding-top: 41px;
}
.row {
  margin-top: 59px;
  & ~ .row {
    margin-top: 23px;
    padding-top: 36px;
  }
  &.is-border-top {
    border-top: 1px solid $color-lightgray;
  }
  :deep(.wrap) {
    padding: 0;
  }
}
p {
  font-size: 14px;
  line-height: 1.47;
  letter-spacing: 0.3px;
}
strong {
  display: block;
  margin: 13px 0 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0.3px;
  b {
    margin-right: 5px;
    vertical-align: top;
    font-size: 12px;
    color: $color-alert2;
  }
}
h2 {
  margin: 0 0 23px;
  font-weight: 400;
  font-size: 18px;
  line-height: 120%;
  letter-spacing: 0.04em;
  color: $color-accent;
}

.next {
  max-width: 400px;
  margin: 26px auto 0;
}

@include sp {
:deep(.l-column1) {
  padding: 25px 16px 0;
}

strong {
  margin: 14px 0 57px;
}

h2 {
    font-size: 16px;
}
.row {
  & ~ .row {
    margin-top: 15px;
    padding-top: 24px;
  }
}
}

</style>