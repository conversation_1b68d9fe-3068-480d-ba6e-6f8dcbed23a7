<template>
<div class="page-mypage-top">
<Main
  :partsHidden="{
    ShowFooterTop: true,
    FixedFooter: true,
    spBreadcrumbs: true,
    h1Title: true,
    FixedFooterBottomReceiver: 'visible',
    spFixedFooterBottomReceiver: true,
    spLogo: true,
    spBackTop: false
  }" 
  contentSize="lg"
  title="マイページ"
  :breadcrumbs="breadcrumbs"
  >
  <template #main>
  <article class="article-main article-mypage-top">
    <section class="mypage-profile-box">
      <div class="box">
        <div class="box-body">
          <div class="user-info">
            <div v-if="profiles.length > 1" class="username">
              <div class="name">{{ profiles?.[0]?.last_name }} {{ profiles?.[0]?.first_name }} <small>様</small></div>
              <div class="img"><img src="@/assets/images/icon-heart.svg" alt=""></div>
              <div class="name">{{ profiles?.[1]?.last_name }} {{ profiles?.[1]?.first_name }} <small>様</small></div>
            </div>
            <div v-else class="username">
              <div class="name">{{ member?.memberMe?.last_name }} {{ member?.memberMe?.first_name }} <small>様</small></div>
              <div class="img"><img src="@/assets/images/icon-heart.svg" alt=""></div>
              <div class="name">パートナー <small>様</small></div>
            </div>
            <NuxtLink to="/mypage/profile/" class="btn btn-primary-outline"><i class="icn-left material-symbols-outlined">person_edit</i> お二人のプロフィール</NuxtLink>
          </div>
          <div class="hr"></div>
          <div class="wedding-info">
            <div class="wedding-date" v-if="member?.memberMe?.wedding_info?.wedding_date">
              <div class="title">挙式日</div>
              <div class="date">
                <span class="year">{{ $dayjs(member?.memberMe?.wedding_info?.wedding_date).format('YYYY') }}</span>
                <span class="day">{{ $dayjs(member?.memberMe?.wedding_info?.wedding_date).format('MM.DD') }}</span>
                <span class="week">{{ $dayjs(member?.memberMe?.wedding_info?.wedding_date).locale('en').format('ddd') }}.</span>
              </div>
              <button @click="showEditModal = true" type="button" class="link-text"><i class="icn-left material-symbols-outlined">calendar_month</i> 変更</button>
            </div>
            <div class="wedding-date" v-else>
              <div class="title">挙式日</div>
              <div class="no-date">
                挙式日未設定
              </div>
              <button @click="showEditModal = true" type="button" class="btn btn-primary-outline"><i class="icn-left material-symbols-outlined">calendar_month</i> 変更</button>
            </div>
          </div>
        </div>
        <footer class="box-footer">
          <NuxtLink v-if="member?.webInvitations.length == 0" to="/mypage/webinvitation" class="btn btn-secondary btn-block"><i class="icn-left material-symbols-outlined">edit</i> 新しい招待状を作る</NuxtLink>
          <NuxtLink v-else to="/mypage/webinvitation" class="btn btn-secondary btn-block"><i class="icn-left material-symbols-outlined">edit</i> My招待状一覧へ</NuxtLink>
        </footer>
      </div>
    </section>
    <section class="section-support">
      <div class="notice-title">
        <h2>お知らせ</h2>
      </div>
      <div class="box-news">
        <ul class="list-news">
          <li v-for="(content , index) in visibleNews" :key="index">
            <NuxtLink :to="'/information/'+content.id" class="news-link">
              <span class="date">{{ $dayjs(content.publishedAt).format('YYYY/MM/DD') }}</span>          
              <span class="cats">
                <span 
                  class="cat" 
                  v-for="(category , n) in content.category" :key="n"
                  :style="getCategoryStyle(category)"
                  >
                    {{ category }}
                </span>
              </span>          
              <span class="title">{{ content.title }}</span>     
              <i class="material-icons icn-right">arrow_forward_ios</i>     
            </NuxtLink>
          </li>
        </ul>
        <div class="cmn-aligncenter mt-22 mb-10 fz-sm" v-if="newsData?.contents.length > 0">
          <NuxtLink to="/information" class="link-text">お知らせ一覧を見る</NuxtLink>
        </div>
      </div>
    </section>
    <div class="separator"></div>
    <nav class="mypage-top-nav"><ul>
      <li>
        <NuxtLink to="/mypage/webinvitation" class="link-text">
          <span class="img"><img src="@/assets/images/mypage/top/icon-webinvitation.svg" alt="WEB招待状"></span>
          <span class="txt">My招待状</span>
        </NuxtLink>
      </li>
      <li>
        <NuxtLink to="/mypage/guest-list" class="link-text">
          <span class="img"><img src="@/assets/images/mypage/top/icon-guestlist.svg" alt="ゲストリスト"></span>
          <span class="txt">ゲストリスト</span>
          <span class="badge" v-if="newGuestCnt != 0">{{ newGuestCnt }}</span>
        </NuxtLink>
      </li>
      <li>
        <NuxtLink v-if="useRuntimeConfig()?.public?.app?.is_active_prepaid" to="/mypage/celebration" class="link-text">
          <span class="img"><img src="@/assets/images/mypage/top/icon-gift.svg" alt="会費・ご祝儀"></span>
          <span class="txt">会費・ご祝儀</span>
        </NuxtLink>
        <div v-else class="link-text link-text-disabled">
          <span class="img"><img src="@/assets/images/mypage/top/icon-gift.svg" alt="会費・ご祝儀"></span>
          <span class="txt">会費・ご祝儀</span>
          <span class="soon">Comming Soon ...</span>
        </div>
      </li>
      <li>
        <a href="https://www.favori-cloud.com/invitation_designs" target="_blank" class="link-text">
          <span class="img"><img src="@/assets/images/mypage/top/icon-paper.svg" alt="紙の招待状"></span>
          <span class="link-tag">宛名印字0円</span>
          <span class="txt"><small>10枚〜購入可能</small><br />紙の招待状</span>
          <i class="icn-blank material-symbols-outlined">open_in_new</i>
        </a>
      </li>
      <li>
        <a href="#modal-seating-chart-banner" @click.prevent="showLpModal()" class="link-text">
          <span class="img"><img src="@/assets/images/mypage/top/icon-menu.svg" alt="席次表・席札・メニュー表"></span>
          <span class="link-tag">最大10%OFF</span>
          <span class="txt">席次表・席札・<br>メニュー表</span>
          <i class="icn-blank material-symbols-outlined">open_in_new</i>
        </a>
      </li>
      <li>
        <a href="https://favori-item.com/products/favori_giftcard" target="_blank" class="link-text">
          <span class="img"><img src="@/assets/images/mypage/top/icon-card.svg" alt="引き出物カード"></span>
          <span class="link-tag">最大29%OFF</span>
          <span class="txt"><small>3品選べる</small><br />引き出物カード</span>
          <i class="icn-blank material-symbols-outlined">open_in_new</i>
          <span class="icn-new">NEW</span>
        </a>
      </li>
      <li>
        <a href="https://www.favori-cloud.com/gift_designs" target="_blank" class="link-text">
          <span class="img"><img src="@/assets/images/mypage/top/icon-delivery.svg" alt="引き出物宅配"></span>
          <span class="link-tag">最大28%OFF</span>
          <span class="txt">引き出物宅配</span>
          <i class="icn-blank material-symbols-outlined">open_in_new</i>
        </a>
      </li>
      <li>
        <a href="https://favori-item.com/collections/all-wedding-movie" target="_blank" class="link-text">
          <span class="img"><img src="@/assets/images/mypage/top/icon-movie.svg" alt="演出ムービー"></span>
          <span class="txt">演出ムービー</span>
          <i class="icn-blank material-symbols-outlined">open_in_new</i>
        </a>
      </li>
    </ul></nav>
    <div class="bnr-wrap">
      <a @click="showModalMypagePopup = true" class="link-img"><img src="@/assets/images/mypage/popup/bnr.png" alt="席次表を簡単作成"></a>
    </div>
    <div class="separator"></div>
    <section class="mypage-top-support pc_only">
      <h2 class="cmn-title cmn-title-en mb-20">
        <span class="en">Support</span>
        <span class="ja">お客様サポート</span>
      </h2>
      <p class="size--sm mb-16">Favoriのご利用方法やヘルプが必要なときはこちら</p>
      <NuxtLink to="/support" class="btn btn-primary-outline btn-block mb-30"><i class="icn-left material-symbols-outlined">help</i>サポートトップ</NuxtLink>
      <div class="links">
        <NuxtLink to="/question" class="link-text mr-20">よくある質問 <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink>
        <NuxtLink to="/guide" class="link-text ml-20">ご利用ガイド <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink>
      </div>
    </section>
    <nav class="nav-links sp_only">
      <ul>
        <li v-if="useRuntimeConfig()?.public?.app?.is_active_prepaid">
          <NuxtLink to="/mypage/bank" class="nav-item">
            <i class="icn-left material-symbols-outlined">account_balance</i>
            <span class="txt">振込口座の登録・変更</span>
            <i class="icn-right material-symbols-outlined">chevron_right</i>
            <span class="label-danger ml-10" style="margin-top: -3px;" v-if="member?.memberMe?.isAccountAlertRegist"><i class="icn material-icons">error</i> 振込口座をご登録ください</span>
          </NuxtLink>
        </li>
        <li><NuxtLink to="/mypage/member" class="nav-item"><i class="icn-left material-symbols-outlined">account_circle</i><span class="txt">会員情報の確認・変更</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
        <li><NuxtLink to="/mypage/member/email" class="nav-item"><i class="icn-left material-symbols-outlined">email</i><span class="txt">パスワード、メールアドレスの登録・変更</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
        <li><a @click="showModalLogout()" class="nav-item"><i class="icn-left material-symbols-outlined">logout</i><span class="txt">ログアウト</span><i class="icn-right material-symbols-outlined">chevron_right</i></a></li>
        <li><NuxtLink to="/question" class="nav-item"><i class="icn-left material-symbols-outlined">help</i><span class="txt">よくある質問</span><i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></li>
      </ul>
    </nav>
    <!-- <section class="mypage-top-bottom-bnr sp_only">
      <NuxtLink to="/"><img src="@/assets/images/mypage/top/bnr-bottom.png" alt="会費・ご祝儀"></NuxtLink>
    </section> -->
    <section class="mypage-top-bnrs" v-if="banners.length">
      <div class="swiper-bnrs-wrap">
      <Swiper
        class="swiper-bnrs"
        :modules="[Navigation, Pagination, Autoplay]"
        :slidesPerView="1"
        :speed="500"
        :loop="true"
        :pagination="{ clickable: true }"
        :autoplay="{
          delay: 5000,
          disableOnInteraction: false,
        }"
        :breakpoints="{
          768: {
            slidesPerView: 3,
          },
        }">
        <SwiperSlide v-for="(item , index) in banners" :key="index" class="swiper-slide">
          <a v-if="item?.plan_text2" class="swiper-item" :href="item?.plan_text2">
            <img :src="item.image.url" :alt="item.plan_text">
          </a>
          <spam v-else class="swiper-item">
            <img :src="item.image.url" :alt="item.plan_text">
          </spam>
        </SwiperSlide>
      </Swiper>
      </div>
    </section>
    <section class="section-contact pc_only">
      <div class="box">
        <h2 class="cmn-title mt-0">お問い合わせ</h2>
        <p>24時間のチャットや電話 メールなどでもお問い合わせいただけます </p>
        <NuxtLink to="/contact" class="btn btn-primary-outline btn-block mb-16">フォームからお問い合わせする</NuxtLink>
        <p class="color-accent size--md">ファヴォリサポート窓口 <a href="tell:03-6274-8695" class="link-accent size--lg ml-16"><i class="icn-left material-symbols-outlined">call</i> 03-6274-8695</a></p>
        <p class="size--sm">営業時間：11:00～18:00（土日祝を除く）</p>
      </div>
    </section>
  </article>
  <ModalMemberEdit 
    v-if="showEditModal"
    :member="member.memberMe"
    @close="showEditModal = false"
    @reload="async () => { await refetch() }"
  />
  <ModalMypagePopup 
    v-if="showModalMypagePopup"
    @close="showModalMypagePopup = false"
  />
  <ModalLp
    v-if="showModalMypageLp"
    @close="closeLpModal()"
    view="マイページ（「席次表・席札・メニュー表」ボタン）"
  />
  </template>
</Main>
</div>
</template>

<script lang="ts" setup>
const { showModalLogout } = useModalLogoutState();
import ModalMemberEdit from '@/components/organisms/mypage/member/ModalMemberEdit.vue'

import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation, Pagination, Autoplay } from 'swiper';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
  },
];

const route = useRoute();
const router = useRouter();
const { $dayjs } : any = useNuxtApp();
const { member, refetch } = useGetOneMemberMe();
const showEditModal = ref(false);
const showModalMypagePopup = ref(false);
const showModalMypageLp = ref(false);

// APIから guestLists を読み込み
const { newGuestCnt } = useGetOneMemberMe();

// プロフィールの初期値設定
const profiles = computed(() => {
  if(!member?.value?.memberMe?.family_profiles || member?.value?.memberMe?.family_profiles.length == 0){ return false }
  return JSON.parse(JSON.stringify(member?.value?.memberMe?.family_profiles));
});

// 口座登録アラートフラグ
const isAccountAlertRegist = computed(() => {
  return JSON.parse(JSON.stringify(member?.value?.memberMe?.isAccountAlertRegist));
});

// microCMSから例文一覧を取得
const microCms = new MicroCms();
const search = ref({
  orders: 'publishedAt',
  filters: 'content_type[contains]例文デフォルト',
} as {
  orders: string;
  filters: string;
});
const { data: microCmsData, error: microCmsError } = await microCms.fetch('/contents', {
  filters: 'code[equals]mypage_top_banner'
});
const { data: newsData} = await microCms.fetch('/notice2', {
  orders: '-publishedAt',
  filters: 'page_type[contains]マイページ[or]page_type[contains]両方'
});

const itemsToShow = ref(5);
const visibleNews = computed(() => newsData?.value.contents.slice(0, itemsToShow.value));

const banners = computed(() => {
  try {
    let results = JSON.parse(JSON.stringify(microCmsData.value?.contents?.[0]?.item));
    let i = 0;
    while (results.length < 6) {
      results.push(results[i]);
      i++;
    }
    return results;
  } catch (error) {
    
  }
  return [];
});

// LPモーダルを表示する関数
const savedScrollPosition = ref(0);
const showLpModal = () => {
  // スクロール位置を保存
  savedScrollPosition.value = window.scrollY;

  // モーダル表示前にURLにハッシュを追加（履歴エントリを作成）
  if (!route.hash || route.hash !== '#modal-seating-chart-banner') {
    window.history.pushState(null, '', `${route.path}#modal-seating-chart-banner`);
  }
  showModalMypageLp.value = true;
};

// LPモーダルを閉じる関数
const closeLpModal = () => {
  // モーダルを閉じる際にハッシュを削除
  window.history.pushState(null, '', route.path);
  showModalMypageLp.value = false;
  nextTick(() => {
    window.scrollTo({
      top: savedScrollPosition.value,
      behavior: 'auto'
    });
  });
};

// ブラウザの「戻る」「進む」ボタンの操作を検知するイベントリスナーを設定
const handlePopState = () => {
  if (window.location.hash === '#modal-seating-chart-banner') {
    showModalMypageLp.value = true;
  } else if (showModalMypageLp.value) {
    showModalMypageLp.value = false;
    nextTick(() => {
      window.scrollTo({
        top: savedScrollPosition.value,
        behavior: 'auto'
      });
    });
  }
};
window.addEventListener('popstate', handlePopState);
onBeforeUnmount(() => {
  window.removeEventListener('popstate', handlePopState);
});

// ハッシュの監視
watch(() => route.hash, (newHash) => {
  // #modal-seating-chart-bannerがついているかどうかでモーダル表示制御
  if (newHash === '#modal-seating-chart-banner') {
    showModalMypageLp.value = true;
  } else if (showModalMypageLp.value) {
    showModalMypageLp.value = false;
    nextTick(() => {
      window.scrollTo({
        top: savedScrollPosition.value,
        behavior: 'auto'
      });
    });
  }
}, { immediate: true });

</script>

<style lang="scss" scoped>
.article-mypage-top {
  // margin-right: calc(50% - 50vw);
  // margin-left: calc(50% - 50vw);
  // width: 100vw;
  background: #fff;
  @include sp {
    margin-left: 0;
    margin-right: 0;
    width: auto;
    background: #fff;
    // min-height: 100vh;
    // min-height: 100dvh;
  }
}
.mypage-profile-box {
  background: url(@/assets/images/mypage/top/mv-bg.jpg) center center no-repeat;
  background-size: cover;
  padding: 16px;
  .box {
    padding: 24px 32px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.95);
    max-width: 580px;
    margin-left: auto;
    margin-right: auto;
    @include sp {
      padding: 24px;
    }
  }
  .box-body {
    display: flex;
    justify-content: space-around;
    // align-items: center;
    margin-bottom: 16px;
  }
  .box-footer {
    text-align: center;
    margin-top: 0;
    .btn {
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }
  }
  .hr {
    width: 1px;
    border-right: 1px dashed #9C9C9C;
  }
  .user-info {
    // padding-top: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .username {
      // margin-bottom: 16px;
      text-align: center;
      .name {
        font-size: 16px;
        display: inline-block;
        font-family: $serif_font;
        small {
          font-size: .8em;
        }
        @include sp {
          display: block;
        }
      }
      .img {
        display: inline-block;
        margin-left: 10px;
        margin-right: 10px;
        vertical-align: middle;
        @include sp {
          display: block;
          margin: 5px 0;
        }
      }
    }
    .btn {
      // padding-left: 0;
      // padding-right: 0;
    }
  }
  .wedding-info {
    text-align: center;
    .title {
      font-size: 12px;
      font-family: $serif_font;
      font-weight: 300;
      margin-bottom: 8px;
      @include sp {
        font-size: 14px;
      }
    }
    .date {
      color: #2F587C;
      font-family: "Cinzel", serif;
      font-size: 16px;
      font-weight: 400;
      letter-spacing: .05em;
      line-height: 1;
      margin-bottom: 14px;
      > * {
        display: inline-block;
        vertical-align: middle;
        @include sp {
          display: block;
          margin-bottom: 5px;
        }
      }
      .day {
        font-size: 2em;
        margin-left: .25em;
        margin-right: .25em;
      }
    }
    .no-date {
      color: var(--Gray_dark, #9C9C9C);
      font-family: "Noto Sans JP";
      padding: 30px 0;
    }
  }
}
.separator {
  height: 10px;
  width: 100%;
  background-color: #F4F4F4; // グレーの線の色
  margin: 20px 0; // 上下にスペースを追加する場合
}

.mypage-top-nav {
  background: #fff;
  text-align: center;
  padding-bottom: 0;
  margin-bottom: 0;
  padding-top: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  ul {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
    li {
      width: 25%;
      border-bottom: 1px solid var(--Gray_light, #F4F4F4);
      border-right: 1px solid var(--Gray_light, #F4F4F4);
      @include pc {
        &:nth-child(5) ,
        &:nth-child(6) ,
        &:nth-child(7) ,
        &:nth-child(8) {
          border-bottom: none;
        }
        &:nth-child(4n) {
          border-right: none;
        }
      }
      @include sp {
        width: 33.33%;
        &:nth-child(7) ,
        &:nth-child(8) ,
        &:nth-child(9) {
          border-bottom: none;
        }
        &:nth-child(3n) {
          border-right: none;
        }
      }
    }
  }
  .link-text {
    font-size: 12px;
    width: 100%;
    text-align: center;
    display: block;
    position: relative;
    padding: 12px 0;
    overflow: hidden;
    .img {
      margin-top: 10px;
      height: 57px;
      display: block;
    }
    .txt {
      display: block;
      margin-top: 6px;
      font-weight: 700;
      small {
        font-size: 10px;
        font-weight: normal;
      }
    }
    .badge {
      position: absolute;
      top: 15px;
      right: 25px;
      font-size: 14px;
      font-weight: 600;
      padding: 4px 6px;
      color: #fff;
      font-family: $en_font;
      border-radius: 100px;
      background: #AD871E;
    }
    .soon {
      position: absolute;
      top: 30px;
      left: 0;
      right: 0;
      opacity: 1 !important;
      background: rgba(#fff, .8);
      padding: 5px 0;
      font-weight: bold;
      font-size: 12px;
    }
    .link-tag {
      position: absolute;
      top: 65px;
      left: 4px;
      color: var(--2, #E65C7A);
      font-size: 10px;
      font-weight: 600;
      border: 1px solid var(--2, #E65C7A);
      background: #FFF;
      padding: 1px 2px;
    }
    .icn-blank {
      position: absolute;
      color: #AD871E;
      font-size: 14px;
      right: 10px;
      top: 10px;
    }
    .icn-new {
      position: absolute;
      left: -25px;
      top: -10px;
      color: #FFF;
      font-family: Roboto;
      font-size: 9px;
      font-weight: 500;
      background: #E65C7A;
      width: 64px;
      height: 32px;
      transform: rotate(-45deg);
      line-height: 48px;
    }
    text-decoration: none;
    transition: all 0.3s ease;
    &:hover {
      text-decoration: none;
      opacity: .6;
    }
  }
}
.mypage-top-support {
  max-width: 384px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  margin-bottom: 42px;
}

.section-contact {
  background: var(--Gray_light, #F4F4F4);
  padding: 28px 16px 16px;
  .box {
    max-width: 640px;
    border-radius: 8px;
    border: 1px solid #2F587C;
    background: var(--white-100, #FFF);
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    padding: 20px;
    .btn {
      max-width: 260px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

.mypage-top-bnrs {
  overflow: hidden;
  padding-bottom: 24px;
}
.swiper-bnrs-wrap {
  // margin-left: -20%;  
  // margin-right: -20%;  
  // &[data-cnt="1"] ,
  // &[data-cnt="2"] ,
  // &[data-cnt="3"] ,
  // &[data-cnt="4"] {
  //   max-width: 1000px;
  //   margin-left: auto;
  //   margin-right: auto;
  // }
}
.swiper-bnrs {
  padding-bottom: 40px;
  --swiper-pagination-color: #2F587C;
  .swiper-item {
    display: block;
    width: 100%;
    position: relative;
    // overflow: hidden;
    // &:after {
    //   content: " ";
    //   display: block;
    //   padding-top: .56 * 100%;
    // }
    // img {
    //   height: 100%;
    //   width: auto;
    //   max-width: none;
    //   position: absolute;
    //   top: 50%;
    //   left: 50%;
    //   transform: translate(-50%, -50%);
    //   @supports ( object-fit: cover ) {
    //     height: 100%;
    //     width: 100%;
    //     top: 0;
    //     left: 0;
    //     transform: none;
    //     object-fit: cover;
    //   }
    // }
  }
  .swiper-slide {
    transform: scale(.9);
    transform-origin: center;
    transition: all 0.3s ease;
    opacity: 0.4;
    @include sp {
      opacity: 1;
    }
    &.swiper-slide-active + .swiper-slide {
      transform: scale(1);
      opacity: 1;
    }
  }
  .swiper-pagination {
    bottom: -20px;
  }
}

.nav-links {
  li {
    border-bottom: 1px solid #F4F4F4;
  }
  .nav-item {
    font-size: 12px;
    color: #494949;
    text-decoration: none;
    position: relative;
    display: block;
    // min-height: 48px;
    line-height: 1.6;
    padding: 14px 50px;
    background: #fff;
    .icn-left {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      font-weight: 300;
      color: #2F587C;
    }
    .icn-right {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
.mypage-top-bottom-bnr {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 60px;
}

.section-support {
  margin: 20px auto 35px;
  max-width: 800px;
  @include sp {
    padding: 0px;
  }
  .box-news {
    margin-left: 18px;
    margin-right: 15px;
  }
  .notice-title {
    text-align: center;

    h2 {
      font-size: 16px; // フォントサイズを調整
      font-weight: normal;
      color: #2F587C; // テキストの色を設定
      position: relative;
      display: inline-block;
      padding-bottom: 5px;

      &::after {
        content: '';
        display: block;
        width: 50px; // ラインの長さ
        height: 2px; // ラインの太さ
        background-color: #2F587C; // ラインの色
        margin: 0 auto;
        margin-top: 5px;
      }
    }
  }
  .list-news {
    display: block;
    @include sp {
      max-height: 160px;
      overflow-y: auto;
      padding-right: 10px; 
    }
    li {
      border-bottom: 1px solid var(--Gray, #F4F4F4);
    }
    a {
      color: var(--49454Fblack_Light, #49454F);
      font-size: 16px;
      font-weight: 400;
      padding: 16px 0px;
      display: block;
      text-decoration: none;
      .title {
        margin-left: 10px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 75%;
        vertical-align: sub;
        line-height: 1.2;
        @include sp {
          margin-left: 0px;
          display: block;
          white-space: normal;
          max-width: calc(100% - 30px);
          line-height: 1.4;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
  .link-text {
    color: #B18A3E; 
    font-size: 14px;
    font-weight: bold;
    text-decoration: none;
    &:hover {
      text-decoration: underline; // ホバー時に下線を表示
    }
  }
}
.bnr-wrap {
  text-align: center;
  padding-top: 20px;
  padding-left: 20px;
  padding-right: 20px;
  max-width: 460px;
  margin-left: auto;
  margin-right: auto;
}
</style>
<style lang="scss">
.page-mypage-top {
  @include sp {
    background: #F4F4F4;
    height: 100vh;
  }
  .main-contents .contents .section-inner.l-column1 {
    padding-bottom: 0;
    background: #f4f4f4;
  }
}
</style>