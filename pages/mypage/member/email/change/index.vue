<template>
<Main
  :partsHidden="{
    spLogo: true,
    spH1Title: true,
    spBreadcrumbs: true,
    ShowFooterTop: true,
    FixedFooter: true,
  }" 
  :breadcrumbs="breadcrumbs"
  :backlink="backlink"
  title="メールアドレスを変更"
  >
  <template #main>
    <article class="article">
      <div class="contener-xs">
        <Loading v-if="isLoading" :fullscreen="true"></Loading>
        <p class="mb-30">変更したいメールアドレスを入力し 認証メールを送信してください</p>
        <div class="article-inner">
          <div class="mb-30">
            <InputEmail 
              title="メールアドレス"
              :required="true"
              size="full" 
              placeholder="<EMAIL>"
              :value="String(input.email)"
              :error="getValidationMessage(v$.email)"
              @update="input.email = $event"
              />
            <p v-if="error.indexOf('メールアドレス') !== -1" class="input-error">{{ error }}</p>
          </div>
          <div class="mb-25">
            <InputPassword 
              title="パスワード"
              :required="true"
              size="full" 
              placeholder="パスワードを入力してください"
              :value="String(input.password)"
              :error="getValidationMessage(v$.password)"
              @update="input.password = $event"
              />
              <p v-if="error.indexOf('メールアドレス') === -1" class="input-error">{{ error }}</p>
          </div>
        </div>
        <div class="btn-warp">
          <button class="btn btn-secondary btn-block btn-md" @click="onClickSave">確認メールを送信する</button>
        </div>
      </div>
    </article>
  </template>
</Main>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers, minLength, maxLength, email, not, sameAs } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";

const router = useRouter();

// レイアウト設定
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage',
  },
  {
    title: "メールアドレスを変更",
  },
];

// 入力項目
const input = ref({
  email:'',
  password:'',
} as {
  email: string;
  password: string;
})

// 全体エラー
const error = ref('')

const { member } = useGetOneMemberMe();

const rules = computed(() => {
  return {
    email: { 
      required: helpers.withMessage(validationMessage.required('メールアドレス'), required),
      email: helpers.withMessage(validationMessage.email('メールアドレス'), email),
      notSameAs: helpers.withMessage('変更前のメールアドレスと同じです', not(sameAs(member?.value?.memberMe?.email))),
      
    },
    password: { 
      required: helpers.withMessage(validationMessage.required('パスワード'), required),
      minLength: helpers.withMessage(validationMessage.minLength('パスワード', 8), minLength(8)),
      maxLength: helpers.withMessage(validationMessage.maxLength('パスワード', 50), maxLength(50)),
      regex:  helpers.withMessage(validationMessage.password('パスワード'), validationPassword)
    },
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { action, errors } = useSendChangeEmailUrl();

// 更新中のLoading
const isLoading = ref(false);

// 編集モーダル非表示
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    // error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await action(input.value.email, input.value.password);
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    isLoading.value = false;
    return false;
  }

  isLoading.value = false;
  router.push({ path: `/mypage/member/email/change/send` })
};

const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('', '^/mypage');
});
</script>

<style lang="scss" scoped>
.article {
  padding-top: 30px;
  .article-inner  {
    max-width: 266px;
  }
  @include sp {
    // position: relative;
    // height: 100vh;
    padding-bottom: 50px;
    .btn-warp {
      padding: 20px;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      transition: 0.35s ease;
      [data-scroll="top"] & {
        bottom: 55px;
      }
    }
  }
}
</style>