<template>
<Main
  :partsHidden="paramPartsHidden" 
  :breadcrumbs="breadcrumbs"
  :backlink="backlink"
  title="パスワード、メールアドレスの登録・変更"
  addClass="l-column1-nowrap"
  >
  <template #main>
    <Loading v-if="isLoading"></Loading>
    <article class="article" v-else>
      <div class="contener-sm" v-if="member.memberMe.sns_login_id && ! member.memberMe?.is_use_password">
        <ul class="list-info mb-5">
          <li><dl>
            <dt>パスワード</dt>
            <dd>未設定</dd>
          </dl></li>
        </ul>
        <p class="mb-30">※メールアドレスでのログインには<br>
          　パスワードの設定が必要です</p>
        <NuxtLink to="/mypage/member/password/create" class="btn btn-secondary btn-block mb-40">パスワードを設定する</NuxtLink>
        <hr>
        <ul class="list-info mb-20">
          <li><dl>
            <dt>メールアドレス</dt>
            <dd>{{ member.memberMe.email }}</dd>
          </dl></li>
        </ul>
        <button type="button" class="btn btn-secondary btn-block mb-5" disabled>メールアドレスを変更する</button>
        <p class="mb-30">※メールアドレスの変更をするには<br>
        　パスワードの設定が必要です</p>
      </div>
      <div class="contener-sm" v-else>
        <ul class="list-info mb-20">
          <li><dl>
            <dt>パスワード</dt>
            <dd>******</dd>
          </dl></li>
        </ul>
        <NuxtLink to="/mypage/member/password" class="btn btn-secondary btn-block mb-40">パスワードを変更する</NuxtLink>
        <hr>
        <ul class="list-info mb-20">
          <li><dl>
            <dt>メールアドレス</dt>
            <dd>{{ member.memberMe.email }}</dd>
          </dl></li>
        </ul>
        <NuxtLink to="/mypage/member/email/change" class="btn btn-secondary btn-block mb-40">メールアドレスを変更する</NuxtLink>
      </div>
    </article>
  </template>
</Main>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const { $dayjs } : any = useNuxtApp();

// レイアウト設定
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage',
  },
  {
    title: "会員情報の確認・変更",
  },
];
const paramPartsHidden = {
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
  spH1Title: true,
  spLogo: true
}

const { member, refetch } = useGetOneMemberMe();

const isLoading = ref(true);
const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('', '^/mypage');
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
});

const onReloaded = async() => {
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
}

const showEditModal = ref(false);
</script>

<style lang="scss" scoped>
.article {
  padding-top: 32px;
  @include sp {
    padding-top: 0;
  }
}
</style>