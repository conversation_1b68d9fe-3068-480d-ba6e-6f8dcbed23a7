<template>
<Main
  :partsHidden="paramPartsHidden" 
  :breadcrumbs="breadcrumbs"
  :backlink="backlink"
  title="会員情報の確認・変更"
  addClass="l-column1-nowrap"
  >
  <template #main>
    <Loading v-if="isLoading"></Loading>
    <article class="article" v-else>
      <div class="contener-sm">
        <ul class="list-info mb-40">
          <li><dl>
            <dt>会員番号</dt>
            <dd>{{ member.memberMe.alternate_member_number }}</dd>
          </dl></li>
          <li><dl>
            <dt>お名前</dt>
            <dd>{{ member.memberMe.last_name }} {{ member.memberMe.first_name }}</dd>
          </dl></li>
          <li><dl>
            <dt>お名前（カナ）</dt>
            <dd>{{ member.memberMe.last_name_kana }} {{ member.memberMe.first_name_kana }}</dd>
          </dl></li>
          <li><dl>
            <dt>お名前（ローマ字）</dt>
            <dd>{{ member.memberMe.last_name_romaji }} {{ member.memberMe.first_name_romaji }}</dd>
          </dl></li>
          <li><dl>
            <dt>挙式日</dt>
            <dd><span v-if="member.memberMe.wedding_info?.wedding_date">{{ $dayjs(member.memberMe.wedding_info?.wedding_date).format('YYYY/MM/DD') }}</span></dd>
          </dl></li>
          <li><dl>
            <dt>挙式会場名</dt>
            <dd>{{ member.memberMe.wedding_info?.wedding_venue }}</dd>
          </dl></li>
          <li><dl>
            <dt>招待人数（目安）</dt>
            <dd>{{ GUEST_COUNT_OPTIONS.find(item => item.value == member.memberMe?.wedding_info?.guest_count)?.label }}</dd>
          </dl></li>
          <!-- <li><dl>
            <dt>メールアドレス</dt>
            <dd>{{ member.memberMe.email }}</dd>
          </dl></li> -->
          <li><dl>
            <dt>お誕生日</dt>
            <dd><span v-if="member.memberMe.birthdate">{{ $dayjs(member.memberMe.birthdate).format('YYYY/MM/DD') }}</span></dd>
          </dl></li>
          <li><dl>
            <dt>登録ログイン方法</dt>
            <dd v-if="member.memberMe.sns_login_id?.google && member.memberMe.is_use_password">メールアドレス・Googleログイン</dd>
            <dd v-else-if="member.memberMe.sns_login_id?.line && member.memberMe.is_use_password">メールアドレス・LINEログイン</dd>
            <dd v-else-if="member.memberMe.sns_login_id?.google">Googleログイン</dd>
            <dd v-else-if="member.memberMe.sns_login_id?.line">LINEログイン</dd>
            <dd v-else>メールアドレス</dd>
          </dl></li>
          <!-- <li v-else><dl>
            <dt>パスワード</dt>
            <dd>******</dd>
          </dl></li> -->
        </ul>
        <button class="btn btn-secondary btn-block mb-16" @click="showEditModal = true">会員情報を変更する</button>
        <NuxtLink v-if="! member.memberMe.sns_login_id" to="/mypage/member/password" class="btn btn-primary-outline btn-block mb-40">パスワードを変更する</NuxtLink>
      </div>
      <!-- <hr class="mb-35"> -->
      <!-- <div class="contener-sm">
        <NuxtLink to="/mypage/member/withdraw" class="btn btn-default-outline btn-block">退会する</NuxtLink>
      </div> -->
    </article>
    <ModalMemberEdit 
      v-if="showEditModal"
      :member="member.memberMe"
      @close="showEditModal = false"
      @reload="onReloaded"
    />
  </template>
</Main>
</template>

<script lang="ts" setup>
import ModalMemberEdit from '@/components/organisms/mypage/member/ModalMemberEdit.vue'

import { ref } from 'vue';

const { $dayjs } : any = useNuxtApp();

// レイアウト設定
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage',
  },
  {
    title: "会員情報の確認・変更",
  },
];
const paramPartsHidden = {
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
  spH1Title: true,
  spLogo: true
}

const { member, refetch } = useGetOneMemberMe();

const isLoading = ref(true);
const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('', '^/mypage');
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
});

const onReloaded = async() => {
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
}

const showEditModal = ref(false);
</script>

<style lang="scss" scoped>
.article {
  padding-top: 32px;
  @include sp {
    padding-top: 0;
  }
}
</style>