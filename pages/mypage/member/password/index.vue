<template>
<Main
  :partsHidden="{
    spLogo: true,
    spH1Title: true,
    spBreadcrumbs: true,
    ShowFooterTop: true,
    FixedFooter: true,
  }" 
  :breadcrumbs="breadcrumbs"
  :backlink="backlink"
  title="パスワードを変更"
  >
  <template #main>
    <article class="article">
      <div class="contener-xs">
        <Loading v-if="isLoading" :fullscreen="true"></Loading>
        <p class="mb-30">パスワードは英小文字 英大文字 数字をそれぞれ1文字以上含み 8～50文字になるよう入力してください</p>
        <div class="article-inner">
          <div class="mb-5">
            <InputPassword 
              title="現在のパスワード"
              size="full" 
              placeholder="パスワードを入力してください"
              :value="String(input.password)"
              :error="getValidationMessage(v$.password)"
              @update="input.password = $event"
              />
              <p v-if="error" class="input-error">{{ error }}</p>
          </div>
          <div class="mb-25">
            <p><NuxtLink to="/mypage/member/password/reset" class="link-accent"><i class="icn-left material-symbols-outlined">help</i> パスワードをお忘れですか？</NuxtLink></p>
          </div>
          <div class="mb-25">
            <InputPassword 
              title="新しいパスワード"
              size="full" 
              placeholder="パスワードを入力してください"
              :value="String(input.new_password)"
              :error="getValidationMessage(v$.new_password)"
              @update="input.new_password = $event"
              />
          </div>
          <div class="mb-40">
            <InputPassword 
              title="新しいパスワード (確認用)"
              size="full" 
              placeholder="パスワードを再度入力してください"
              :value="String(input.new_password_conf)"
              :error="getValidationMessage(v$.new_password_conf)"
              @update="input.new_password_conf = $event"
              />
          </div>
        </div>
        <div class="btn-warp">
          <button class="btn btn-secondary btn-block btn-md" @click="onClickSave">変更する</button>
        </div>
      </div>
    </article>
  </template>
</Main>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { required, helpers, minLength, maxLength, sameAs } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
const { addToastMessage } = useToastMessageState();

const router = useRouter();

// レイアウト設定
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage',
  },
  {
    title: "パスワードを変更",
  },
];

// 入力項目
const input = ref({
  password:'',
  new_password:'',
  new_password_conf: ''
} as {
  password: string;
  new_password: string;
  new_password_conf: string;
})

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return {
    password: { 
      required: helpers.withMessage(validationMessage.required('現在のパスワード'), required),
      minLength: helpers.withMessage(validationMessage.minLength('現在のパスワード', 8), minLength(8)),
      maxLength: helpers.withMessage(validationMessage.maxLength('現在のパスワード', 50), maxLength(50)),
      // regex:  helpers.withMessage(validationMessage.password('現在のパスワード'), validationPassword)
    },
    new_password: { 
      required: helpers.withMessage(validationMessage.required('新しいパスワード'), required),
      minLength: helpers.withMessage(validationMessage.minLength('新しいパスワード', 8), minLength(8)),
      maxLength: helpers.withMessage(validationMessage.maxLength('新しいパスワード', 50), maxLength(50)),
      regex:  helpers.withMessage(validationMessage.password('新しいパスワード'), validationPassword)
    },
    new_password_conf: { 
      required: helpers.withMessage(validationMessage.required('新しいパスワード (確認用)'), required),
      minLength: helpers.withMessage(validationMessage.minLength('新しいパスワード (確認用)', 8), minLength(8)),
      maxLength: helpers.withMessage(validationMessage.maxLength('新しいパスワード (確認用)', 50), maxLength(50)),
      regex:  helpers.withMessage(validationMessage.password('新しいパスワード (確認用)'), validationPassword),
      sameAs: helpers.withMessage('パスワードが一致していません ', sameAs(input.value.new_password))
    },
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 更新API
const { update, errors } = useUpdateMemberPassword();
const { logout } = useLogoutMember();

// 更新中のLoading
const isLoading = ref(false);

// 編集モーダル非表示
const onClickSave = async() => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    // error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await update(input.value.password, input.value.new_password);
  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    isLoading.value = false;
    return false;
  }

  isLoading.value = false;
  addToastMessage({message: 'パスワードを変更しました '});
  router.push({ path: `/mypage/member/email` })
};

const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('', '^/mypage');
});
</script>

<style lang="scss" scoped>
.article {
  padding-top: 30px;
  .article-inner  {
    max-width: 266px;
  }
  @include sp {
    // position: relative;
    // height: 100vh;
    padding-bottom: 50px;
    .btn-warp {
      padding: 20px;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      transition: 0.35s ease;
      [data-scroll="top"] & {
        bottom: 55px;
      }
    }
  }
}
</style>