<template>
  <Main
    :partsHidden="paramPartsHidden" 
    :breadcrumbs="breadcrumbs"
    title="パスワードをお忘れの方"
  >
    <template #main>
      <div class="pageLogin">
        <ModalLoginContentsPasswordStep1
          v-if="page == 'passwordStep1'"
          @changePage="page = $event"
        ></ModalLoginContentsPasswordStep1>
        <ModalLoginContentsPasswordStep2
          v-else-if="page == 'passwordStep2'"
          @changePage="page = $event"
        ></ModalLoginContentsPasswordStep2>
      </div>
    </template>
</Main>
</template>

<script lang="ts" setup>
const route = useRoute();
const router = useRouter();

// レイアウト設定
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage',
  },
  {
    title: "パスワードを変更",
    link: '/mypage/member/password',
  },
  {
    title: "パスワードをお忘れの方",
  },
];
const paramPartsHidden = {
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
}

const page = ref('passwordStep1')

// エラーがあれば表示
const errors = ref({} as any)
if (route.query.sns && route.query.error_code) {
  errors.value[route.query.sns as string] = SNS_LOGIN_ERRORS?.[route.query.error_code as string];
}
onMounted(() => {
  // エラーを消しておく
  router.push({});
})

const { logout } = useLogoutMember();
watch(() => page.value, async () => {
  // console.log(page.value);
  if (page.value == 'top') {
    router.push({ path: `/mypage/member/password` })
  } else if (page.value == 'passwordStep2') {
    // 変更完了したらログアウトする
    try {
      await logout();
    } catch (error) {
      // console.log(error);
    }
  }
});
</script>

<style lang="scss">
.pageLogin {
  .wrap {
    max-width: 420px;
    color: #666;
    margin: 0 auto;
    padding: 0 20px 40px;
    .loading {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(#fff, .9);
      z-index: 9000;
      &:after {
        position: absolute;
        top: 50%;
        display: block;
        text-align: center;
        left: 0;
        right: 0;
        margin-top: -1em; 
      }
    }
  }
}
</style>