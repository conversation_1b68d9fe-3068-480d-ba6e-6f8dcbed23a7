<template>
<Main
  :partsHidden="{
    spLogo: true,
    spH1Title: true,
    spBreadcrumbs: true,
    ShowFooterTop: true,
    FixedFooter: true,
    isBacklinkAction: false,
    backlink: false,
  }" 
  :breadcrumbs="breadcrumbs"
  :backlink="backlink"
  title="退会手続き"
  >
  <template #main>
    <article class="article">
      <div class="contener-sm" style="max-width: 520px;">
        <Loading v-if="loading" :fullscreen="true"></Loading>
        
        <div v-if="hasUnpaidInvitations">
          <p class="mb-13 color-danger withdraw-title">未入金の会費・ご祝儀があります</p>
          <p class="mb-0">事前決済で振り込まれた会費・ご祝儀がある場合</p>
          <p class="color-danger mb-0">運営から入金されるまで退会することができません</p>
          <p class="mb-40">お手数ですが会費・ご祝儀の入金が完了した後に再度お手続きをお願いいたします</p>
          <div style="max-width: 440px; margin: 0 auto;">
            <button class="btn btn-secondary btn-block btn-md mb-25" @click="goToMypage">入金予定を確認する</button>
          </div>
        </div>
        
        <div v-else>
          <p class="mb-16">Favoriを退会されると 現在保存されている注文履歴やプロフィール お届け先などの情報が<span class="cmn-coloraccent">全て削除されます </span><br>退会手続きをしてもよろしいですか？</p>
          <p class="color-danger mb-40">退会手続きが完了した時点で 現在保存されている注文履歴やプロフィール お届け先などの情報は全てなくなりますのでご注意ください <br>
  一度退会されると 過去の会員情報は復元できません </p>
          <div style="max-width: 440px; margin: 0 auto;">
            <button class="btn btn-secondary btn-block btn-md mb-25" @click="showWithdrawModal = true">退会手続きへ</button>
          </div>
        </div>
      </div>
    </article>
    <ModalMemberWithdraw 
      v-if="showWithdrawModal"
      @close="showWithdrawModal = false"
    />
  </template>
</Main>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { useGetHasUnpaidInvitations } from '@/composables/useGetHasUnpaidInvitations';
import ModalMemberWithdraw from '@/components/organisms/mypage/member/ModalMemberWithdraw.vue'

const router = useRouter();
const config = useRuntimeConfig();

// レイアウト設定
const breadcrumbs = [
{
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage',
  },
  {
    title: "退会手続き",
  },
];
// 更新中のLoading
const showWithdrawModal = ref(false);

// 環境変数から退会画面のバックリンクIDを取得してURLを直接設定
const withdrawBacklinkId = config.public.app.withdraw_backlink_id;
const backlink = computed(() => `/question/${withdrawBacklinkId}`);

// 未払いの招待状があるかどうかを確認
const { hasUnpaidInvitations, loading } = useGetHasUnpaidInvitations();

// マイページへ戻る
const goToMypage = () => {
  router.push('/mypage/celebration');
};
</script>

<style lang="scss" scoped>
.article {
  padding-top: 30px;
}

.withdraw-title {
  font-size: 16px;
  font-weight: 500;
}
</style>