<template>
  <div class="webInvitation">
    <Main
      :partsHidden="paramPartsHidden"
      :breadcrumbs="breadcrumbs"
      title="お二人のプロフィール"
      :backlink="backlink"
    >
      <template #main>
        <Loading v-if="isLoading"></Loading>
        <div class="wrap" v-else>
          <ShowProfileForm
            mode="confirm"
          ></ShowProfileForm>
          <div class="button_wrap">
            <ButtonMainColor size="md" baseColor="accent" @click="isShowModal = true">お二人のプロフィールを変更する</ButtonMainColor>
          </div>
        </div>
      </template>

      <template #modalWindow>
        <ModalUpdateProfile
          v-if="isShowModal"
          @close="isShowModal = false;"
          @update="onUpdateProfile()"
        ></ModalUpdateProfile>
      </template>

    </Main>
  </div>
</template>

<script lang="ts" setup>
import { register } from 'swiper/element/bundle';
import { extractUuids, setImages } from '@/composables/useGetImageFromUUID';
const { $dayjs } = useNuxtApp() as any;
const route = useRoute();
const router = useRouter();

// 更新中のLoading
const isLoading = ref(false);
const isShowModal = ref(false);

// API
const { member, refetch } = useGetOneMemberMe();

const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('/mypage', '^/mypage');
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
});

// プロフィールのアップデート
const onUpdateProfile = async() => {
  isShowModal.value = false;
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
};


// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "お二人のプロフィール"
  }
];

const paramPartsHidden = {
  ShowFooterTop: true,
  FixedFooter: true,
  spBreadcrumbs: true,
  spLogo: true
}
</script>

<style lang="scss" scoped>
@include pc {
  :deep(.l-column1) {
    padding-top: 0;
  }
}
@include sp {
  :deep(.container) {
    margin-top: 0;
  }
  :deep(.contents-title) {
    display: none;
  }
  :deep(.breadcrumbs) {
    display: none;
  }
}
.wrap {
  max-width: 804px;
  width: 100%;
  margin: 0 auto;
  padding: 16px;
  // @include sp {
  //   margin-top: -60px;
  // }
}
</style>