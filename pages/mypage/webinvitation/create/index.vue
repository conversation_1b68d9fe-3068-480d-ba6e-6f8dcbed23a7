<template>
  <div class="webInvitation">
    <Main
      :partsHidden="paramPartsHidden"
      :breadcrumbs="breadcrumbs"
      :title="(! isShowProfileModal) ? 'My招待状 新規作成' : 'お二人のプロフィール'"
    >
      <template #contents-title>
        <NuxtLink v-if="! isShowProfileModal" :to="backlink" class="backward"><img src="@/assets/images/icon-arrow_backward-b.svg" alt="戻る"></NuxtLink>
        <a v-else @click="isShowProfileModal = false" class="backward"><img src="@/assets/images/icon-arrow_backward-b.svg" alt="戻る"></a>
        <span v-if="! isShowProfileModal">My招待状 新規作成</span>
        <span v-else>お二人のプロフィール</span>
      </template>
      <template #main>
        <NuxtLink v-if="! isShowProfileModal" :to="backlink" class="link-sp-header"><img src="@/assets/images/icon-arrow_forward-w.svg" alt="戻る"></NuxtLink>
        <a v-else @click="isShowProfileModal = false" class="link-sp-header"><img src="@/assets/images/icon-arrow_forward-w.svg" alt="戻る"></a>
        <PageCreateProfile
          v-if="isShowProfileModal"
          @close="isShowProfileModal = false;"
          @update="onUpdateProfile"
        ></PageCreateProfile>
        <template v-else>
          <div class="webInvitation_selectedTheme">
            <div class="webInvitation_selectedTheme_wrap">
              <div class="webInvitation_selectedTheme_image">
                <div v-if="detailData?.m_specification_products?.[0]?.product_images?.[0]?.src">
                  <img :src="detailData.m_specification_products[0].product_images[0].src">
                </div>
              </div>
              <div class="webInvitation_selectedTheme_text">
                <span>選択したデザイン</span>
                <p>WEB招待状<br>デザインテーマ「{{ detailData.name }}」</p>
              </div>
            </div>
          </div>
          <div class="wrap">
            <h2 class="cmn-title">基本設定</h2>
            <p v-if="wholeError" class="input-error">{{ wholeError }}</p>
            <div class="row">
              <InputText
                title="名称"
                :size="200"
                placeholder="友人宛招待状 など"
                :error="error?.name?.$errors?.[0]?.$message"
                :value="input.name"
                @input="input.name = $event.target.value"
              />
            </div>
            <div class="row">
              <InputSelect
                title="回答内容の追加先ゲストリスト"
                size="full"
                :options="options"
                :value="String(input?.guest_list_id)"
                :error="error?.guest_list_id?.$errors?.[0]?.$message"
                @change="input.guest_list_id = $event.target.value"
              />
              <div class="cmn-alignright">
                <a @click.prevent="isShowAddGuestListModal = true" class="cmn-link" href="#">ゲストリスト追加</a>
              </div>
            </div>
            <div class="about">
              <div class="about_wrap">
                <div class="about_title">ゲストの回答はメインリストに追加されます</div>
                <div class="about_text">おふたりの結婚式にあわせて<br class="sp_only">「披露宴」「二次会」などでリストを作成し<br>回答を集めることもできます</div>
                <div class="about_image">
                  <img src="@/assets/images/about_guestlist.png" alt="">
                </div>
              </div>
            </div>
          </div>
          <div class="webInvitation_footer">
            <ShowFooterBarFrow
              :data="modalFooterBarFrow"
              @onClickBtn1="onClickCreate()"
            />
          </div>
        </template>

        <ModalAddGuestList
          v-if="isShowAddGuestListModal"
          @close="isShowAddGuestListModal = false"
          @create="onAddGuestListModal"
        ></ModalAddGuestList>
      </template>

    </Main>
  </div>
</template>

<script lang="ts" setup>
const route = useRoute();
const router = useRouter();
import { useLoadingState } from '@/composables/useLoading';
const { showLoading, hideLoading } = useLoadingState();
import { required, maxLength, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { extractUuids, setImages } from '@/composables/useGetImageFromUUID';
import { QueryGuestListsOrderByColumn, SortOrder } from '@/composables/generated';

const webInvitationId = ref(route.query.id ? route.query.id : 1);
const themeId = ref(route.query.theme ? route.query.theme : 1);

// API
const { create, errors } = useCreateWebInvitation();
const { member, refetch:memberRefetch } = useGetOneMemberMe();
const { webInvitationData } = useGetOneMSpecificationProduct(themeId);
const { system } = useGetSystem();

const input = ref({
  name: '',
  guest_list_id: '',
});
const isShowAddGuestListModal = ref(false);
const isShowProfileModal = ref(false);

// プロフィール情報の更新
const onUpdateProfile = async () => {
  showLoading();
  await memberRefetch();
  await onClickCreate();
  // isShowProfileModal.value = false;
}

// 画像の取得
const detailData = ref({});
const getImagesFromUUIDs = async (obj) => {
  let updateObj = toRaw(obj);
  let uuids = extractUuids(updateObj);
  const { getImages, refetch, imageLoading } = await useGetManyImages2(uuids);
  await refetch();
  watch(() => getImages, (images, prevImages) => {
    if (images.value) {
      const updatedObjWithImages = setImages(updateObj, images.value);
      detailData.value = updatedObjWithImages;
    }
  }, {
    deep: true,
    immediate: true
  });
}
watch(webInvitationData, async (newVal) => {
  detailData.value = newVal;
  if (newVal) {
    await getImagesFromUUIDs(newVal);
  }
}, {
  deep: true,
  immediate: true
})

// microCMSから例文一覧を取得
const microCms = new MicroCms();
const search = ref({
  orders: 'publishedAt',
  filters: 'content_type[contains]例文デフォルト',
} as {
  orders: string;
  filters: string;
});
const { data: microCmsData, error: microCmsError } = await microCms.fetch('/content', search);

// APIから guestTag を読み込み
const orderBy = ref({
  column: QueryGuestListsOrderByColumn.CreatedAt,
  order: SortOrder.Asc
} as QueryGuestListsOrderByOrderByClause);

const { guestLists, refetch:guestListRefetch } = useGetManyGuestList([{
  column: QueryGuestListsOrderByColumn.IsDefault,
  order: SortOrder.Desc
}, orderBy.value])

// 敬称リスト
const options = computed(() => {
  const options = guestLists.value.map(guestList => {
    if(guestList.is_default){
      input.value.guest_list_id = guestList.id;
    }
    return {value: guestList.id, label: guestList.name};
  });
  return [{value: '', label: 'ゲストリストを選択'}].concat(options);
});

// 全体エラー
const wholeError = ref('')
// 個別エラー
const error = ref('')
const rules = computed(() => {
  let rules = {
    name: {
      required: helpers.withMessage(validationMessage.required('ウェブ招待状名'), required),
      maxLength: helpers.withMessage(validationMessage.maxLength('ウェブ招待状名', 255), maxLength(255))
    },
    guest_list_id: {
      required: helpers.withMessage(validationMessage.required('ゲストリスト'), required)
    }
  };
  return rules;
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 作成するボタンクリック
const onClickCreate = async() => {
  // 全体エラーをリセット
  error.value = '';
  v$.value.$clearExternalResults();

  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = v$.value;
    return false;
  }

  // プロフィール情報が未入力の場合はモーダル表示
  if(!member?.value?.memberMe?.family_profiles || member?.value?.memberMe?.family_profiles.length < 2){
    window.scrollTo({top: 0, behavior: "smooth"});
    isShowProfileModal.value = true;
    return false;
  }else{
    // プロフィール情報も入力されている場合は 作成処理
    onCreateWebInvitation();
  }
};

// 新規作成処理
const onCreateWebInvitation = async() => {
  onSetProfileInfo();
  showLoading();
  // 事前支払いの一時停止期間
  if(isPrepaymentPausePeriod(system.value, new Date())) {
    form.value.blocks[6].contents.isUseGift = false;
  }

  let createDataId = await create({
    input: {
      name: input.value.name,
      m_web_invitation_id: webInvitationId.value,
      guest_list_id: input.value.guest_list_id,
      editor_settings: form.value,
      block_settings: {}
    }
  });
  hideLoading();

  // エラーの場合
  if (!createDataId) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0] && errors.value?.v$?.[0] != 'Validation failed for the field [CreateWebInvitation].') error.value = errors.value?.v$?.[0];
    if (errors.value?.input?.editor_settings?.[0]) wholeError.value = errors.value?.input?.editor_settings?.[0];
    return false;
  }
  router.push({ path: '/mypage/webinvitation/editor/form', query: {id: createDataId, create: 1}});
};

// プロフィールの初期値設定
const onSetProfileInfo = async() => {
  if(member?.value?.memberMe?.family_profiles){
    const sortedProfiles = member.value.memberMe.family_profiles
      .map((profile: any) => {
        let sortKey;
        if (profile?.type == 'GROOM') sortKey = 1;
        else if (profile?.type == undefined || !profile?.type || !profile?.type == '') sortKey = 2;
        else if (profile?.type == 'BRIDE') sortKey = 3;
        else sortKey = 4;
        return { ...profile, sortKey };
      })
      .sort((a, b) => a.sortKey - b.sortKey);

    // 並び替えた配列でループ
    sortedProfiles.forEach((profile: any, index: number) => {
      if (index == 0) {
        form.value.blocks[0].contents.groomName = `${profile?.first_name_romaji || ''}`;
        form.value.blocks[3].contents[0].name = `${profile?.last_name ? profile.last_name + ' ' : ''}${profile?.first_name || ''}`;
        form.value.blocks[3].contents[0].role = '新郎';
      } else if (index == 1) {
        form.value.blocks[0].contents.brideName = `${profile?.first_name_romaji || ''}`;
        form.value.blocks[3].contents[1].name = `${profile?.last_name ? profile.last_name + ' ' : ''}${profile?.first_name || ''}`;
        form.value.blocks[3].contents[1].role = '新婦';
      } else {
        form.value.blocks[3].contents.push({
          isShowVisual: true,
          selectVisual: "images",
          images: [],
          movie: [],
          name: `${profile?.last_name ? profile.last_name + ' ' : ''}${profile.first_name}`,
          isShowRole: true,
          role: "",
          textAlign: "left",
          message: ""
        });
      }
    });
  }
  if(member?.value?.memberMe?.wedding_info && member?.value?.memberMe?.wedding_info?.wedding_venue){
    form.value.blocks[5].contents.events[0].venue = member?.value?.memberMe?.wedding_info?.wedding_venue;
  }

  // 例文の追加
  if(microCmsData?.value?.contents){
    microCmsData.value.contents.forEach(content => {
      // 挨拶・メッセージ : exapmle_default_greeting_1
      if(content?.code == 'exapmle_default_greeting_1'){
        form.value.blocks[2].contents.message = content.plane_text;
      }
      // 新郎・新婦プロフィール : exapmle_default_profile_1
      if(content?.code == 'exapmle_default_profile_1'){
        form.value.blocks[3].contents[0].message = content.plane_text;
        form.value.blocks[3].contents[1].message = content.plane_text;
      }
      // 会費・ご祝儀の事前支払い : exapmle_default_payment_1
      if(content?.code == 'exapmle_default_payment_1'){
        form.value.blocks[6].contents.message = content.plane_text
      }
      // 回答期限下に表示される案内 : exapmle_default_reply_deadline_1
      if(content?.code == 'exapmle_default_reply_deadline_1'){
        form.value.blocks[8].contents.limit.message = content.plane_text
      }
    });
  }
};

// ゲストリストの追加
const onAddGuestListModal = async() => {
  guestListRefetch();
  isShowAddGuestListModal.value = false;
};

// レイアウト設定
const form = ref({
  blocks: [
    {
      name: 'メインビジュアル',
      id: 'mainVisual',
      contents: {
        selectVisual: 'images',
        images: [],
        movie: [],
        groomName: '',
        brideName: ''
      }
    },
    {
      name: 'カウントダウン',
      id: 'countDown',
      visible: true,
      contents: {}
    },
    {
      name: '挨拶・メッセージ',
      id: 'message',
      visible: true,
      contents: {
        isShowVisual: false,
        selectVisual: 'images',
        images: [],
        movie: [],
        textAlign: 'center',
        message: ''
      }
    },
    {
      name: '新郎・新婦プロフィール',
      id: 'profile',
      visible: true,
      contents: [
        {
          isShowVisual: true,
          selectVisual: 'images',
          images: [],
          movie: [],
          name: '',
          isShowRole: true,
          role: '',
          textAlign: 'center',
          message: ''
        },
        {
          isShowVisual: true,
          selectVisual: 'images',
          images: [],
          movie: [],
          name: '',
          isShowRole: true,
          role: '',
          textAlign: 'center',
          message: ''
        }
      ]
    },
    {
      name: '写真ギャラリー',
      id: 'gallery',
      visible: true,
      contents: []
    },
    {
      name: 'パーティー情報',
      id: 'information',
      visible: true,
      contents: {
        date: null,
        type: 'ceremony_reception_same_venue',
        events: [
          {
            plans: [
              {
                isShowPlan: true,
                hour: '0',
                minute: '00'
              },
              {
                isShowPlan: true,
                hour: '0',
                minute: '00'
              },
              {
                isShowPlan: true,
                hour: '0',
                minute: '00'
              }
            ],
            otherPlans: [
              {
                isShowPlan: true,
                hour: '0',
                minute: '00'
              },
              {
                isShowPlan: true,
                hour: '0',
                minute: '00'
              },
              {
                isShowPlan: true,
                hour: '0',
                minute: '00'
              }
            ],
            venue: '',
            venue_kana: '',
            zip: '',
            address: '',
            isShowMaps: false,
            tel: '',
            url: '',
            feeOption: 'gift_system',
            feeAmount: ['', ''],
            isShowVisual: false,
            selectVisual: 'images',
            images: [],
            isShowOtherVisual: false,
            otherSelectVisual: 'images',
            otherImages: []
          }
        ]
      }
    },
    {
      name: '会費・ご祝儀の事前支払い',
      id: 'gift',
      visible: true,
      contents: {
        isUseGift: false,
        isUseInVenue: true,
        partySettings: [
          {
            isUse: true,
            eventName: "挙式"
          },
          {
            isUse: true,
            eventName: "披露宴"
          }
        ],
        isUseInAfterParty: true,
        message: '',
        textAlign: 'center',
        usageFee: 'host',
        scheduleDate: ''
      }
    },
    {
      name: 'フリー項目',
      id: 'freeField',
      visible: true,
      contents: []
    },
    {
      name: '出欠フォーム',
      id: 'guestAnswer',
      visible: true,
      contents: {
        selectList: [
          {
            title: '新郎新婦ゲスト選択',
            id: '',
            disabled: false,
            required: true,
            visible: true,
          },
          {
            title: 'お名前',
            id: '',
            disabled: false,
            required: true,
            visible: true,
          },
          {
            title: 'お名前（ふりがな）',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お名前（ローマ字）',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '関係性',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '間柄',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'プロフィール写真',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '性別',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お誕生日',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '住所',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '電話番号',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'メールアドレス',
            id: '',
            disabled: false,
            required: true,
            visible: true,
          },
          {
            title: 'アレルギー項目の入力',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お祝い画像・動画',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お祝いメッセージ',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '連名入力',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          }
        ],
        questionnaire: [],
        limit: {
          date: '',
          setting: 1,
          textAlign: 'center',
          message: '',
        },
        attendance: {
          isHideAttendance: false,
          isHideSkip: false,
          isAddFields: false,
          fields: []
        }
      }
    }
  ]
});

const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "My招待状",
    link: '/mypage/webinvitation',
  },
  {
    title: "My招待状 新規作成"
  }
];

// #modalWindow01へのデータ
const modalFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "デザイン選択へ戻る",
          link: '/products/webinvitation',
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "作成する",
          link: false,
        },
      ],
    },
  ],
}

const paramPartsHidden = {
  ShowFooterTop: true,
  FixedFooter: true,
  spBreadcrumbs: true,
  spLogo: true
}

const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('/products/webinvitation');
});

</script>

<style lang="scss" scoped>
:deep(.container) {
  @include pc {
    .l-column1{
      padding-top: 0;
      max-width: 100%;
    }
  }
}
@include sp {
  :deep(.container) {
    margin-top: 0;
  }
  :deep(.contents-title) {
    display: none;
  }
  :deep(.breadcrumbs) {
    display: none;
  }
}
.cmn-title{
  margin: 20px 0;
}
.webInvitation{
  &_selectedTheme{
    background: $color-lightgray;
    position: relative;
    z-index: 1;

    @include sp {
      // margin-top: -60px;
      padding: 0 8px;
      background: transparent;
    }

    &_wrap{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      max-width: 400px;
      margin: 0 auto;
      padding: 20px;
      @include sp {
        align-items: flex-start;
        border: 1px solid #D9D9D9;
        max-width: 100%;
        border-radius: 4px;
        margin-top: 20px;
        padding: 12px;
      }
    }
    &_image{
      background: #ccc;
      display: block;
      width: 124px;
      min-width: 124px;
      aspect-ratio: 75 / 132;
      border: 1px solid #D9D9D9;
      border-radius: 4px;
      overflow: hidden;
      margin-right: 24px;
      @include sp {
        width: 64px;
        min-width: 64px;
        height: 64px;
        margin-right: 8px;
        border-radius: 2px;
      }
      img {
        width: 124px;
        aspect-ratio: 75 / 133;
        object-fit: cover;
        @include sp {
          width: 64px;
          height: 64px;
        }
      }
    }

    &_text{
      font-size: 12px;
      color: #333;
      p{
        margin: 0;
      }
      span{
        display: inline-block;
        font-size: 10px;
        margin-bottom: 4px;
        color: #999;
      }
    }
  }
}
.webInvitation_footer{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 11;
}
.wrap{
  max-width: 592px;
  margin: 0 auto;
  padding: 40px 16px 0;
  @include sp {
    padding-top: 22px;
  }
}
.row{
  width: 100%;
  margin: 0 0 24px;
  @include sp {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
  .cmn-link{
    display: inline-block;
    margin: 10px 0 0 auto;
    text-align: right;
    position: relative;
    padding-left: 26px;
    font-size: 14px;
    line-height: 100%;
    &::before {
      @include BA;
      left: 0;
      width: 24px;
      height: 24px;
      background-image: url(@/assets/images/icon-plus-g.svg);
    }
  }
}


.link-sp-header {
  display: none;
  position: fixed;
  left: 0;
  top: -45px;
  color: #fff;
  z-index: 100;
  padding: 7px;
  transition: 0.35s ease;
  [data-scroll="top"] & {
    top: 0;
  }
  img {
    transform: rotate(180deg);
  }
  @include sp {
    display: block;
  }
}

.about{
  color: #333;
  background: #F4F4F4;
  text-align: center;
  padding: 20px;
  margin-bottom: 32px;
  border-radius: 8px;
  &_title{
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
  }
  &_text{
    font-size: 12px;
    line-height: 1.45;
    margin-bottom: 18px;
  }
  &_image{
    max-width: 220px;
    margin: 0 auto;
  }
}
</style>