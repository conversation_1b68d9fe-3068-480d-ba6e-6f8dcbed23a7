<template>
  <div class="webInvitation">
    <Main
      :partsHidden="paramPartsHidden"
      :breadcrumbs="breadcrumbs"
      title="My招待状 コピー"
      :backlink="backlink"
    >
      <template #main>
        <div class="webInvitation_selectedTheme">
          <div class="webInvitation_selectedTheme_wrap">
            <div class="webInvitation_selectedTheme_image">
              <div v-if="themeImage?.src">
                <img :src="themeImage.src">
              </div>
            </div>
            <div class="webInvitation_selectedTheme_text">
              <span>選択したデザイン</span>
              <p>WEB招待状<br>デザインテーマ「Modern A」</p>
            </div>
          </div>
        </div>
        <div class="wrap">
          <h2 class="cmn-title">基本設定</h2>
          <div class="row">
            <InputText
              title="名称"
              :size="168"
              placeholder="友人宛招待状 など"
              :value="input.name"
              @input="input.name = $event.target.value"
            />
          </div>
          <div class="row">
            <InputSelect
              title="回答内容の追加先ゲストリスト"
              size="full"
              :options="options"
              :value="String(input?.guest_list_id)"
              @change="input.guest_list_id = $event.target.value"
            />
            <div class="cmn-alignright">
              <a @click.prevent="isShowAddGuestListModal = true" class="cmn-link" href="#">ゲストリスト追加</a>
            </div>
          </div>
          <div class="row">
            {{ error }}
          </div>
        </div>
        <div class="webInvitation_footer">
          <ShowFooterBarFrow
            :data="modalFooterBarFrow"
            @onClickBtn1="onClickCreate()"
          />
        </div>
        <ModalAddGuestList
          v-if="isShowAddGuestListModal"
          @close="isShowAddGuestListModal = false"
          @create="onAddGuestListModal"
        ></ModalAddGuestList>
      </template>

    </Main>
  </div>
</template>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
import { useLoadingState } from '@/composables/useLoading';
const { showLoading, hideLoading } = useLoadingState();
import { required, email, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { extractUuids, setImages } from '@/composables/useGetImageFromUUID';
import { QueryGuestListsOrderByColumn, SortOrder } from '@/composables/generated';

const { create, createErrors } = useCreateWebInvitation();


// 画像の取得
const themeImage = ref({
  uuid: null,
  src: null
});
const getImagesFromUUIDs = async (obj) => {
  let updateObj = toRaw(obj);
  let uuids = extractUuids(updateObj);
  const { getImages, refetch: imageRefetch, imageLoading } = await useGetManyImages2(uuids);
  await imageRefetch();
  watch(() => getImages, (images, prevImages) => {
    if (images.value) {
      const updatedObjWithImages = setImages(updateObj, images.value);
      themeImage.value = updatedObjWithImages;
    }
  }, {
    deep: true,
    immediate: true
  });
}

// to オブジェクトからパラメータを取得
const name = ref('');
const webInvitationId = ref('');
const guestListId = ref('');
const editorSettings = ref({});
const backlink = ref('' as string);
onMounted(() => {
  backlink.value = getBacklink('/mypage/webinvitation');
  const myData = JSON.parse(localStorage.getItem('copyWebInvitation'));
  name.value = myData.name + 'のコピー';
  input.value.name = myData.name + 'のコピー';
  webInvitationId.value = myData.webInvitationId;
  guestListId.value = myData.guestListId;
  input.value.guest_list_id = myData.guestListId;
  editorSettings.value = myData.editorSettings;
  input.value.editor_settings = myData.editorSettings;
  themeImage.value.uuid = myData.coverUUID;
  getImagesFromUUIDs(themeImage.value);
});

const input = ref({
  name: name,
  guest_list_id: guestListId,
  editor_settings: editorSettings
});
const isShowAddGuestListModal = ref(false);

// APIから guestTag を読み込み
const orderBy = ref({
  column: QueryGuestListsOrderByColumn.CreatedAt,
  order: SortOrder.Asc
} as QueryGuestListsOrderByOrderByClause);

const { guestLists, refetch:guestListRefetch } = useGetManyGuestList([{
  column: QueryGuestListsOrderByColumn.IsDefault,
  order: SortOrder.Desc
}, orderBy.value])

// 敬称リスト
const options = computed(() => {
  const options = guestLists.value.map(guestList => {
    return {value: guestList.id, label: guestList.name};
  });
  return [{value: '', label: 'ゲストリストを選択'}].concat(options);
});

// 全体エラー
const error = ref('')
const rules = computed(() => {
  return {
    name: {
      required: helpers.withMessage(validationMessage.required('ウェブ招待状名'), required)
    },
    guest_list_id: {
      required: helpers.withMessage(validationMessage.required('ゲストリスト'), required)
    }
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 作成するボタンクリック
const onClickCreate = async() => {
  // 全体エラーをリセット
  error.value = '';
  v$.value.$clearExternalResults();

  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  showLoading();

  let createDataId = await create({
    input: {
      name: input.value.name,
      m_web_invitation_id: webInvitationId.value,
      guest_list_id: input.value.guest_list_id,
      editor_settings: input.value.editor_settings,
      block_settings: {}
    }
  });
  hideLoading();
  router.push({ path: '/mypage/webinvitation/editor/form', query: {id: createDataId}});
};

// ゲストリストの追加
const onAddGuestListModal = async() => {
  guestListRefetch();
  isShowAddGuestListModal.value = false;
};

const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "My招待状",
    link: '/mypage/webinvitation',
  },
  {
    title: "My招待状 コピー"
  }
];

// #modalWindow01へのデータ
const modalFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "キャンセル",
          link: '/mypage/webinvitation',
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "次へ",
          link: false,
        },
      ],
    },
  ],
}

const paramPartsHidden = {
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
}
</script>

<style lang="scss" scoped>
:deep(.container) {
  @include pc {
    .l-column1{
      padding-top: 0;
      max-width: 100%;
    }
  }
}
@include sp {
  :deep(.container) {
    margin-top: 0;
  }
  :deep(.contents-title) {
    display: none;
  }
  :deep(.breadcrumbs) {
    display: none;
  }
}
.cmn-title{
  margin: 20px 0;
}
.webInvitation{
  &_selectedTheme{
    background: $color-lightgray;
    position: relative;
    z-index: 1;

    // @include sp {
    //   margin-top: -60px;
    // }

    &_wrap{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      max-width: 400px;
      margin: 0 auto;
      padding: 20px 20px 12px;
    }
    &_image{
      background: #ccc;
      display: block;
      width: 124px;
      min-width: 124px;
      aspect-ratio: 75 / 132;
      border: 1px solid #D9D9D9;
      border-radius: 4px;
      overflow: hidden;
      margin-right: 24px;
      img {
        width: 124px;
        aspect-ratio: 75 / 133;
        object-fit: cover;
      }
    }

    &_text{
      font-size: 12px;
      color: #333;
      span{
        display: inline-block;
        font-size: 10px;
        margin-bottom: 4px;
        color: #999;
      }
    }
  }
}
.webInvitation_footer{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 11;
}
.wrap{
  max-width: 592px;
  margin: 40px auto;
  padding: 0 16px;
}
.row{
  max-width: calc(100% - 32px);
  width: 100%;
  margin: 0 16px 24px;
  .cmn-link{
    display: inline-block;
    margin: 10px 0 0 auto;
    text-align: right;
    position: relative;
    padding-left: 26px;
    font-size: 14px;
    line-height: 100%;
    &::before {
      @include BA;
      left: 0;
      width: 24px;
      height: 24px;
      background-image: url(@/assets/images/icon-plus-g.svg);
    }
  }
}
</style>