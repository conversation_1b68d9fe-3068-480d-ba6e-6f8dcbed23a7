<template>
  <div class="webInvitation">
    <Main :partsHidden="paramPartsHidden" :breadcrumbs="breadcrumbs" :title="formTitle" :backlink="backlink"
      :isShowSortButton="true">
      <template #main>
        <div id="selectTheme" class="webInvitation_selectedTheme">
          <div class="webInvitation_selectedTheme_wrap">
            <div class="webInvitation_selectedTheme_image">
              <div v-if="detailData?.m_web_invitation?.m_specification_product?.product_images?.[0]?.src">
                <img :src="detailData.m_web_invitation.m_specification_product.product_images[0].src">
              </div>
            </div>
            <div class="webInvitation_selectedTheme_text">
              <div class="webInvitation_selectedTheme_title">デザイン</div>
              <div class="webInvitation_selectedTheme_templateName">
                {{ detailData?.m_web_invitation?.m_specification_product?.product?.name }}</div>
              <ButtonMainColor baseColor="reversal" @click="onClickDesignChangeButton()">
                他のデザインに変更
              </ButtonMainColor>
              <div class="webInvitation_selectedTheme_title webInvitation_selectedTheme_guestListTitle">ゲストリスト</div>
              <div class="webInvitation_selectedTheme_guestList">{{ detailData?.guest_list?.name }}</div>
              <ButtonMainColor baseColor="reversal" :disabled="onCheckGuestAnswer"
                @click="onClickGuestListChangeButton()">
                他のゲストリストに変更
              </ButtonMainColor>
              <div v-if="onCheckGuestAnswer" class="webInvitation_selectedTheme_guestListAlert">ゲスト回答済みのため変更不可</div>
            </div>
          </div>
        </div>
        <div class="wholeError" v-if="wholeError">
          <div class="input-error">{{ wholeError }}</div>
        </div>
        <EditorFormWrap ref="editorWrap" :form="form" :formUpdated="formUpdated" :aspect="imageAspectRatio"
          :isGuestAnswer="onCheckGuestAnswer" :isGuestPaid="onCheckGuestPaid" :isHasPrePaidGuest="hasPrePaidGuest"
          :createdAt="createdAt" imageUploadType="user" :error="error"
          :isLockSpecifiedPosition="isLockSpecifiedPosition" :maxMainVisualImages="maxMainVisualImages"
          :getAspectRatioForPosition="getAspectRatioForPosition" @change="onUpdateBlock" @sort="onUpdateSort"
          @change-shipping-date="onChangeShippingDate"></EditorFormWrap>

        <ShowFooterBarFrow :data="modalFooterBarFrow" @onClickBtn0="onClickPreview()" @onClickBtn1="onClickSave()" />

        <ModalConfirmWebInvitation v-if="isShowModalDateConfirm" label="続行する" @close="isShowModalDateConfirm = false"
          @select="onClickSaveDataConfirm()">
          <h2 class="confirm_title">他のWEB招待状の挙式日（開催日）が異なります</h2>
          <p class="confirm_alert">
            同じゲストリスト内に、開催日が異なる招待状があると保存ができかねます。ゲストリストのご変更をお願いします<br>※続行するを選択すると今まで作成された招待状の挙式日が変更されます</p>
        </ModalConfirmWebInvitation>

        <ModalConfirmWebInvitation v-if="isShowModalChangeDesignConfirm" label="移動する"
          @close="isShowModalChangeDesignConfirm = false" @select="onChangeDesign()">
          <h2 class="confirm_title">デザイン選択画面へ移動</h2>
          <p class="confirm_alert">保存していない内容はリセットされてしまいますが、デザイン選択画面に移動してもよろしいですか？</p>
        </ModalConfirmWebInvitation>

        <ModalConfirmWebInvitation v-if="isShowModalChangeDesignAlert" label="閉じる" :isShowCancel="false"
          @close="isShowModalChangeDesignAlert = false" @select="isShowModalChangeDesignAlert = false">
          <h2 class="confirm_title">開催日を入力してください</h2>
          <p class="confirm_alert">開催日が入ってない状態では保存ができないためデザイン変更ができません。開催日を入力した後再度デザイン変更をお願いします。</p>
        </ModalConfirmWebInvitation>

        <ModalConfirmWebInvitation v-if="isShowModalChangeGuestListConfirm" label="移動する"
          @close="isShowModalChangeGuestListConfirm = false" @select="onChangeGuestList()">
          <h2 class="confirm_title">ゲストリスト変更画面へ移動</h2>
          <p class="confirm_alert">保存していない内容はリセットされてしまいますが、ゲストリスト変更画面に移動してもよろしいですか？</p>
        </ModalConfirmWebInvitation>

        <ModalConfirmWebInvitation v-if="isShowModalChangeGuestListAlert" label="閉じる" :isShowCancel="false"
          @close="isShowModalChangeGuestListAlert = false" @select="isShowModalChangeGuestListAlert = false">
          <h2 class="confirm_title">開催日を入力してください</h2>
          <p class="confirm_alert">開催日が入ってない状態では保存ができないためゲストリスト変更ができません。開催日を入力した後再度ゲストリスト変更をお願いします。</p>
        </ModalConfirmWebInvitation>

        <ModalConfirmWebInvitation v-if="isShowModalUnmountedConfirm" ref="confirmModal" label="移動する"
          @select="handleConfirm()" @close="handleCancel()">
          <h2 class="confirm_title">ページの移動</h2>
          <p class="confirm_alert">編集した内容がリセットされてしまいますが、別ページに移動してもよろしいですか？</p>
        </ModalConfirmWebInvitation>

        <transition name="fade">
          <div v-if="isShowSaveToast" class="saveToast">
            <div class="saveToast_text">保存完了しました</div>
            <div class="saveToast_button">
              <NuxtLink to="/mypage/webinvitation">My招待状へ</NuxtLink>
            </div>
          </div>
        </transition>
      </template>

    </Main>
  </div>
</template>

<script lang="ts" setup>
import { required, email, helpers, minLength, maxLength, sameAs } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import { useCloned } from '@vueuse/core';
import { GraphQLValidationErrors } from "@/utils/graphql";
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();
import { useLoadingState } from '@/composables/useLoading';
const { showLoading, hideLoading } = useLoadingState();
const config = useRuntimeConfig()
const router = useRouter();
const route = useRoute();
// 画像の取得
import { extractUuids, setImages } from '@/composables/useGetImageFromUUID';
// 画像トリミング処理
import { useImageCropping } from '@/composables/useImageCropping';
import { useAspectRatioWatcher } from '@/composables/useAspectRatioWatcher';

// API
const { member, refetch: memberRefetch } = useGetOneMemberMe();
const { system } = useGetSystem();

// 画像トリミング処理composable
const { resetCroppingForChangedBlocks, calculateOptimalCropping, recalculateAfterImageLoad } = useImageCropping();
const {
  detectAspectRatioChanges,
  getAspectRatioForPosition: getAspectRatioForPositionFromComposable,
  checkIsLockSpecifiedPosition,
  getMaxMainVisualImages: getMaxMainVisualImagesFromComposable
} = useAspectRatioWatcher();

interface Props {
  errors: GraphQLValidationErrors | null;
}
const form = ref({});
const formUpdated = ref({});
const guests = ref();
const isShowSaveToast = ref(false);
const createdAt = ref();

// 画像の取得
const getImagesFromUUIDs = async (obj) => {
  let updateObj = toRaw(obj);
  let uuids = extractUuids(updateObj);
  const { getImages, refetch, imageLoading } = await useGetManyImages2(uuids);
  await refetch();
  watch(() => getImages, (images, prevImages) => {
    if (images.value) {
      const updatedObjWithImages = setImages(updateObj, images.value);
      detailData.value = updatedObjWithImages;

      let editorSettings = updatedObjWithImages.editor_settings;
      // 開催日のデフォルトセット
      if (route?.query?.create == '1' && member?.value?.memberMe?.wedding_info && member?.value?.memberMe?.wedding_info?.wedding_date && !updatedObjWithImages?.editor_settings?.[5]?.contents?.date) {
        editorSettings.blocks[5].contents.date = member?.value?.memberMe?.wedding_info?.wedding_date;
      }

      // 事前支払いの一時停止期間
      createdAt.value = updateObj.created_at;
      if (isPrepaymentPausePeriod(system.value, createdAt.value)) {
        let blockIndex = editorSettings?.blocks.findIndex((target) => {
          return 'gift' == target.id;
        });
        if (editorSettings?.blocks?.[blockIndex]) {
          editorSettings.blocks[blockIndex].contents.isUseGift = false;
        }
      }

      form.value = editorSettings;
      formUpdated.value = editorSettings;
      return updatedObjWithImages;
    }
  }, {
    deep: true,
    immediate: true
  });
}

// 送金予定日の変更
const shippingDate = ref('');
const onChangeShippingDate = async (date: any) => {
  shippingDate.value = date;
}

// クエリパラメーターがある場合
showLoading(true);
let queryId = route?.query.id;
let isUpdateMode = ref(false);
let getData: any;
const { webInvitationData, refetch, loading } = useGetOneWebInvitation(queryId);
refetch();
isUpdateMode.value = true;
getData = webInvitationData;

// titleの作成
const isSP = ref(window.innerWidth < 768)
// ウィンドウリサイズイベントのハンドラー
const handleResize = () => {
  isSP.value = window.innerWidth < 768
}
onMounted(async () => {
  showLoading(true);
  await refetch();
  hideLoading();
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  hideLoading();
  window.removeEventListener('resize', handleResize)
})
const formTitle = computed(() => {
  return isSP.value ? `編集 - ${detailData?.value?.name}` : 'My招待状 編集'
})

// 子要素のrefを受け取る
const editorWrap = ref(null);

// 更新API
const { create, errors: createErrors } = useCreateWebInvitation();
const { update, errors: updateErrors, } = useUpdateWebInvitation();

// 保存時に使用する
const eventNameList = ref([]);
const allEventNames = ref([]);
onMounted(async () => {
  try {
    const response = await fetch(config.public.s3PublicUrlEventNameList as string)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    eventNameList.value = await response.json()
  } catch (error) {
  }
})
// すべてのイベント名を集める関数
const collectAllEventNames = () => {
  allEventNames.value = [];
  let information = onFilterUpdateBlock('information')?.contents;
  if (information && information.events) {
    information.events.forEach((event: any) => {
      allEventNames.value.push(event.eventName);
    });
  }
}
// イベント名の重複チェック関数
const onCheckDuplicateEventName = (name: string, currentIndex: number): boolean => {
  return allEventNames.value.some((eventName, index) => index !== currentIndex && eventName === name);
}
const onCheckDuplicateEventList = (name: string): boolean => {
  return eventNameList.value.includes(name);
}

// 保存ボタンクリック
const wholeError = ref();
const isSave = ref(false);
const isShowModalDateConfirm = ref(false);
const onClickSave = async () => {
  if (isSave.value) { return false }
  // 全体エラーをリセット
  error.value = '';

  // サーバサイドメッセージをリセット
  v$.value.$reset();
  v$.value.$clearExternalResults();

  await v$.value.$validate();

  if (v$.value.$error || v$.value.$errors.length != 0) {
    error.value = v$.value.$errors;

    const propertyPath = v$.value.$errors[0].$propertyPath;
    const parts = propertyPath.split('.');
    const pathIndex = parts[1];
    const blockName = formUpdated.value.blocks[pathIndex].id;
    if (editorWrap.value && editorWrap.value.onScrollTargetBlock) {
      editorWrap.value.onScrollTargetBlock(blockName);
    }

    isSave.value = false;
    return false;
  }

  let information = onFilterUpdateBlock('information')?.contents;
  let informationIndex = formUpdated.value?.blocks.findIndex((element) => {
    return element.id == 'information';
  });
  if (information && information.events) {
    collectAllEventNames(); // すべてのイベント名を収集

    let eventErrors = [];

    information.events.forEach((event: any, index) => {
      if (index == 0 && information == 'other_party') {
        if (onCheckDuplicateEventName(event.eventName, index) || onCheckDuplicateEventList(event.eventName)) {
          eventErrors.push({
            $propertyPath: `blocks.${informationIndex}.contents.events.${index}.eventName`,
            $property: "eventName",
            $validator: "eventName",
            $uid: `blocks.${informationIndex}.contents.events.${index}.date-eventName`,
            $message: "プリセットのパーティー名または他のパーティー名と重複しています",
            $params: {},
            $response: false,
            $pending: false
          });
        }
        if (event.eventName.trim() == '') {
          eventErrors.push({
            $propertyPath: `blocks.${informationIndex}.contents.events.${index}.eventName`,
            $property: "eventName",
            $validator: "eventName",
            $uid: `blocks.${informationIndex}.contents.events.${index}.date-eventName`,
            $message: "パーティー名の入力は必須です",
            $params: {},
            $response: false,
            $pending: false
          });
        }
      } else if (event.isOtherParty) {
        if (onCheckDuplicateEventName(event.eventName, index) || onCheckDuplicateEventList(event.eventName)) {
          eventErrors.push({
            $propertyPath: `blocks.${informationIndex}.contents.events.${index}.eventName`,
            $property: "eventName",
            $validator: "eventName",
            $uid: `blocks.${informationIndex}.contents.events.${index}.date-eventName`,
            $message: "プリセットのパーティー名または他のパーティー名と重複しています",
            $params: {},
            $response: false,
            $pending: false
          });
        }
        if (event.eventName.trim() == '') {
          eventErrors.push({
            $propertyPath: `blocks.${informationIndex}.contents.events.${index}.eventName`,
            $property: "eventName",
            $validator: "eventName",
            $uid: `blocks.${informationIndex}.contents.events.${index}.date-eventName`,
            $message: "パーティー名の入力は必須です",
            $params: {},
            $response: false,
            $pending: false
          });
        }
      } else {
        if (onCheckDuplicateEventName(event.eventName, index)) {
          eventErrors.push({
            $propertyPath: `blocks.${informationIndex}.contents.events.${index}.eventName`,
            $property: "eventName",
            $validator: "eventName",
            $uid: `blocks.${informationIndex}.contents.events.${index}.date-eventName`,
            $message: "プリセットのパーティー名または他のパーティー名と重複しています",
            $params: {},
            $response: false,
            $pending: false
          });
        }
      }
    });

    if (eventErrors.length > 0) {
      error.value = eventErrors;
      editorWrap.value.onScrollTargetBlock('information');
      isSave.value = false;
      return false;
    }
  }

  showLoading(true);
  isSave.value = true;

  let isSuccess = await onSave(false);

  // エラーの場合
  if (!isSuccess) {
    if (updateErrors.value?.v$.length == 1 && updateErrors.value?.v$?.[0] == '他のWeb招待状の開催日が異なります') {
      isShowModalDateConfirm.value = true;
    } else if (updateErrors.value?.v$?.[0]) {
      wholeError.value = updateErrors.value?.v$?.[0];
      editorWrap.value.onScrollTargetBlock('selectTheme');
    }
    isSave.value = false;
    hideLoading();
    return false;
  }

  hideLoading();

  // ToastMessage
  form.value = JSON.parse(JSON.stringify(formUpdated.value));
  isShowSaveToast.value = true;
  setTimeout(() => {
    isShowSaveToast.value = false;
  }, 5000);
  isSave.value = false;
};

const onClickSaveDataConfirm = async () => {
  if (isSave.value) { return false }
  isShowModalDateConfirm.value = false;
  showLoading(true);
  isSave.value = true;

  await onSave(true);

  hideLoading();

  // ToastMessage
  form.value = JSON.parse(JSON.stringify(formUpdated.value));
  isShowSaveToast.value = true;
  setTimeout(() => {
    isShowSaveToast.value = false;
  }, 5000);
  isSave.value = false;
};

// JSONからbase64が入っている部分を削除もしくはnullに設定
const removeBase64Data = async (obj: any) => {
  const newObj = JSON.parse(JSON.stringify(obj));

  function traverse(o) {
    if (Array.isArray(o)) {
      return o.map(item => traverse(item));
    } else if (typeof o === 'object' && o !== null) {
      Object.keys(o).forEach(key => {
        if ((key === 'src' || key === 'srcVideo') && typeof o[key] === 'string') {
          if (o[key].startsWith('data:')) {
            delete o[key]; // base64の場合は項目ごと削除
          } else {
            o[key] = null; // base64以外の場合はnullに設定
          }
        } else if (typeof o[key] === 'object' && o[key] !== null) {
          o[key] = traverse(o[key]);
        }
      });
    }
    return o;
  }

  return traverse(newObj);
}

// 会費を数値のみに変更する
const changeEventFeeToNumber = (obj: any) => {
  let objData = JSON.parse(JSON.stringify(obj));
  let informationIndex = objData?.blocks.findIndex((element) => {
    return element.id == 'information';
  });
  if (informationIndex !== -1 && informationIndex && objData?.blocks[informationIndex]?.contents?.events) {
    objData.blocks[informationIndex].contents.events.forEach((event: any, index) => {
      if (Array.isArray(event.feeAmount)) {
        event.feeAmount = event.feeAmount.map(amount => {
          let numberAmount: number | string = '';
          if (amount && typeof amount === 'string') {
            numberAmount = amount ? Number(amount.replace(/,/g, '')) : '';
          } else {
            numberAmount = amount ? amount : '';
          }
          return numberAmount
        });
      }
    });
  }
  return objData
}

const onSave = async (is_synced: boolean) => {
  let isSuccess;
  let removed_base64_json_data = await removeBase64Data(formUpdated.value);
  let json_data = changeEventFeeToNumber(removed_base64_json_data);
  if (isUpdateMode.value) {
    isSuccess = await update({
      is_synced: is_synced,
      input: {
        id: queryId,
        m_web_invitation_id: detailData.value?.m_web_invitation?.id,
        guest_list_id: detailData.value?.guest_list?.id,
        editor_settings: json_data,
        block_settings: sortUpdated.value,
        scheduled_transfer_date: shippingDate.value
      }
    });
  } else {
    isSuccess = await create({
      is_synced: is_synced,
      input: {
        guest_list_id: detailData.value?.guest_list?.id,
        m_web_invitation_id: detailData.value?.m_web_invitation?.id,
        editor_settings: json_data,
        block_settings: sortUpdated.value,
        scheduled_transfer_date: shippingDate.value
      }
    });
  }
  return isSuccess;
}

/**
 * 他のデザインに変更ボタンのクリック
 */
const isShowModalChangeDesignConfirm = ref(false);
const isShowModalChangeDesignAlert = ref(false);
const onClickDesignChangeButton = async () => {
  let before = JSON.stringify(form.value);
  let after = JSON.stringify(formUpdated.value);
  const informationBlock = formUpdated.value?.blocks.find(block => block.id === 'information');
  if (!informationBlock?.contents?.date) {
    isShowModalChangeDesignAlert.value = true;
  } else if (before != after) {
    isShowModalChangeDesignConfirm.value = true;
  } else {
    onChangeDesign();
  }
}
const onChangeDesign = async () => {
  isCheckUnmounted.value = true;
  form.value = JSON.parse(JSON.stringify(formUpdated.value));

  // セッションストレージにformデータを保存
  sessionStorage.setItem('webInvitationFormData', JSON.stringify(formUpdated.value));

  // テンプレート一覧に移動
  router.push({
    path: '/products/webinvitation',
    query: {
      reselect: queryId,
      guest: detailData.value.guest_list.id
    }
  });

  isShowModalChangeDesignConfirm.value = false;
}

/**
 * ゲストリスト変更ボタンのクリック
 */
const isShowModalChangeGuestListConfirm = ref(false);
const isShowModalChangeGuestListAlert = ref(false);
const onClickGuestListChangeButton = async () => {
  let before = JSON.stringify(form.value);
  let after = JSON.stringify(formUpdated.value);
  const informationBlock = formUpdated.value?.blocks.find(block => block.id === 'information');
  if (!informationBlock?.contents?.date) {
    isShowModalChangeGuestListAlert.value = true;
  } else if (before != after) {
    isShowModalChangeGuestListConfirm.value = true;
  } else {
    onChangeGuestList();
  }
}
const onChangeGuestList = async () => {
  isCheckUnmounted.value = true;
  form.value = JSON.parse(JSON.stringify(formUpdated.value));

  // セッションストレージにformデータを保存
  sessionStorage.setItem('webInvitationFormData', JSON.stringify(formUpdated.value));

  // テンプレート一覧に移動
  router.push({
    path: '/mypage/webinvitation/editor/guest-list',
    query: {
      id: queryId
    }
  });

  isShowModalChangeGuestListConfirm.value = false;
}

// 設定変更後に保存前に移動しようとすると確認モーダルを表示する
const isShowModalUnmountedConfirm = ref(false);
const isCheckUnmounted = ref(false);
const unmountedConfirm = ref(null);
const unmountedTo = ref(null);
const confirmModal = ref<HTMLElement | null>(null);
const handleNavigationConfirmation = (to, from, next) => {
  isShowModalUnmountedConfirm.value = true;
};
router.beforeEach((to, from, next) => {
  let before = JSON.stringify(form.value);
  let after = JSON.stringify(formUpdated.value);
  if (before != after && !isCheckUnmounted.value && !isShowModalChangeDesignConfirm.value) {
    unmountedConfirm.value = next;
    unmountedTo.value = to;
    handleNavigationConfirmation(to, from, next);
    next(false);
  } else {
    next();
  }
});
const handleConfirm = () => {
  isShowModalUnmountedConfirm.value = false;
  isCheckUnmounted.value = true;
  router.push({ path: unmountedTo.value?.path })
};
const handleCancel = () => {
  isShowModalUnmountedConfirm.value = false;
  unmountedConfirm.value(false);
};

// プレビューボタンのクリック
const onClickPreview = async () => {
  // リンクの作成
  const previewLink = router.resolve({
    path: `/mypage/webinvitation/view/${queryId}`,
    query: {
      prev: 1
    }
  });
  // セッションストレージにformデータを保存
  sessionStorage.setItem('webInvitationFormData', JSON.stringify(formUpdated.value));
  window.open(previewLink.href, '_blank');
}

// #modalWindow01へのデータ
const modalFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'reversal',
          disabled: false,
          slot: "プレビュー",
          link: null,
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "保存する",
          link: null,
        },
      ],
    },
  ],
}

// ブロック設定
const sortUpdated = ref([]);
const sort = ref({
  sort: [
    {
      name: 'カウントダウン',
      id: 'countDown',
      visible: true
    },
    {
      name: '挨拶・メッセージ',
      id: 'message',
      visible: true
    },
    {
      name: '新郎・新婦プロフィール',
      id: 'profile',
      visible: true
    },
    {
      name: '写真ギャラリー',
      id: 'gallery',
      visible: true
    },
    {
      name: 'パーティー情報',
      id: 'information',
      visible: true
    },
    {
      name: '会費・ご祝儀の事前支払い',
      id: 'gift',
      visible: true
    },
    {
      name: 'フリー項目',
      id: 'freeField',
      visible: true
    },
    {
      name: '出欠フォーム',
      id: 'guestAnswer',
      visible: true
    }
  ]
});

//フォーム設定
const imageAspectRatio = ref({
  mainVisual: {
    width: 9,
    height: 16
  },
  message: {
    width: 16,
    height: 9
  },
  profile: {
    width: 1,
    height: 1
  },
  information: {
    width: 59,
    height: 42
  },
  gallery: {
    width: 16,
    height: 9
  }
});

// if (!queryId) {
form.value = {
  blocks: [
    {
      name: 'メインビジュアル',
      id: 'mainVisual',
      contents: {
        selectVisual: 'images',
        images: [],
        movie: [],
        groomName: '',
        brideName: ''
      }
    },
    {
      name: 'カウントダウン',
      id: 'countDown',
      visible: true,
      contents: {}
    },
    {
      name: '挨拶・メッセージ',
      id: 'message',
      visible: true,
      contents: {
        isShowVisual: false,
        selectVisual: 'images',
        images: [],
        movie: [],
        textAlign: 'center',
        message: ''
      }
    },
    {
      name: '新郎・新婦プロフィール',
      id: 'profile',
      visible: true,
      contents: [
        {
          isShowVisual: true,
          selectVisual: 'images',
          images: [],
          movie: [],
          name: '',
          isShowRole: true,
          role: '',
          textAlign: 'center',
          message: ''
        },
        {
          isShowVisual: true,
          selectVisual: 'images',
          images: [],
          movie: [],
          name: '',
          isShowRole: true,
          role: '',
          textAlign: 'center',
          message: ''
        }
      ]
    },
    {
      name: '写真ギャラリー',
      id: 'gallery',
      visible: true,
      contents: []
    },
    {
      name: 'パーティー情報',
      id: 'information',
      visible: true,
      contents: {
        date: null,
        type: 'ceremony_reception_same_venue',
        events: [
          {
            plans: [
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              },
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              },
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              }
            ],
            otherPlans: [
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              },
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              },
              {
                isShowPlan: true,
                hour: '',
                minute: ''
              }
            ],
            venue: '',
            venue_kana: '',
            zip: '',
            address: '',
            isShowMaps: false,
            tel: '',
            url: '',
            feeOption: 'gift_system',
            feeAmount: ['', ''],
            isShowVisual: false,
            selectVisual: 'images',
            images: [],
            isShowOtherVisual: false,
            otherSelectVisual: 'images',
            otherImages: []
          }
        ]
      }
    },
    {
      name: '会費・ご祝儀の事前支払い',
      id: 'gift',
      visible: true,
      contents: {
        isUseGift: null,
        isUseInVenue: true,
        partySettings: [],
        isUseInAfterParty: true,
        message: '',
        textAlign: 'center',
        usageFee: 'host',
        scheduleDate: ''
      }
    },
    {
      name: 'フリー項目',
      id: 'freeField',
      visible: true,
      contents: []
    },
    {
      name: '出欠フォーム',
      id: 'guestAnswer',
      visible: true,
      contents: {
        selectList: [
          {
            title: '新郎新婦ゲスト選択',
            id: '',
            disabled: false,
            required: true,
            visible: true,
          },
          {
            title: 'お名前',
            id: '',
            disabled: false,
            required: true,
            visible: true,
          },
          {
            title: 'お名前（ふりがな）',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お名前（ローマ字）',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '関係性',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '間柄',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'プロフィール写真',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '性別',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お誕生日',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '住所',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '電話番号',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'メールアドレス',
            id: '',
            disabled: false,
            required: true,
            visible: true,
          },
          {
            title: 'アレルギー項目の入力',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お祝い画像・動画',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: 'お祝いメッセージ',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          },
          {
            title: '連名入力',
            id: '',
            disabled: false,
            required: false,
            visible: true,
          }
        ],
        questionnaire: [],
        limit: {
          date: '',
          setting: 1,
          textAlign: 'center',
          message: '',
        },
        attendance: {
          isHideAttendance: false,
          isHideSkip: false,
          isAddFields: false,
          fields: []
        }
      }
    }
  ]
}
// }
formUpdated.value = JSON.parse(JSON.stringify(form.value));
sortUpdated.value = JSON.parse(JSON.stringify(sort.value));

interface DetailData {
  m_web_invitation?: {
    web_invitation_design_images?: {
      upload_file_name?: string | null
    }[]
  }
}
const detailData = ref<DetailData>({
  m_web_invitation: {
    web_invitation_design_images: [
      {
        upload_file_name: null
      }
    ]
  }
});

// デザイン設定から画像の縦横比情報を取得
const designAspectRatio = computed(() => {
  const designSettings = detailData.value?.m_web_invitation?.m_specification_product?.product?.design_settings;
  if (designSettings) {
    try {
      const settings = typeof designSettings === 'string' ? JSON.parse(designSettings) : designSettings;
      return settings;
    } catch (error) {
      return null;
    }
  }
  return null;
});

// 位置指定ありかどうかの判定
const isLockSpecifiedPosition = computed(() => {
  return checkIsLockSpecifiedPosition(designAspectRatio.value, imageAspectRatio.value);
});

// MV画像の最大枚数を取得
const maxMainVisualImages = computed(() => {
  return getMaxMainVisualImagesFromComposable(
    isLockSpecifiedPosition.value,
    designAspectRatio.value,
    imageAspectRatio.value
  );
});

// 位置ごとのトリミング比率を取得
const getAspectRatioForPosition = (position: number) => {
  return getAspectRatioForPositionFromComposable(
    position,
    isLockSpecifiedPosition.value,
    designAspectRatio.value,
    imageAspectRatio.value
  );
};

// トリミング比率の変更を監視して自動反映
const previousAspectRatios = ref({});
watch([designAspectRatio, isLockSpecifiedPosition], (newVal, oldVal) => {
  if (newVal && formUpdated.value?.blocks) {
    const mainVisualBlock = formUpdated.value.blocks.find(block => block.id === 'mainVisual');
    if (mainVisualBlock?.contents?.images) {
      let hasChanged = false;
      const updatedImages = mainVisualBlock.contents.images.map((image, index) => {
        if (!image) return image;

        const newAspectRatio = getAspectRatioForPosition(index);
        const previousRatio = previousAspectRatios.value[index];

        // トリミング比率が変わった場合、トリミング位置を初期状態に戻す
        if (previousRatio &&
          (previousRatio.width !== newAspectRatio.width ||
            previousRatio.height !== newAspectRatio.height)) {
          hasChanged = true;
          return {
            ...image,
            x: 0,
            y: 0,
            width: newAspectRatio.width,
            height: newAspectRatio.height
          };
        }

        return image;
      });

      if (hasChanged) {
        const updatedBlock = {
          ...mainVisualBlock,
          contents: {
            ...mainVisualBlock.contents,
            images: updatedImages
          }
        };
        onUpdateBlock({ key: 'mainVisual', value: updatedBlock });
      }

      // 現在のアスペクト比を保存
      if (isLockSpecifiedPosition.value && designAspectRatio.value?.mainVisual?.specifiedImages) {
        designAspectRatio.value.mainVisual.specifiedImages.forEach((ratio, index) => {
          previousAspectRatios.value[index] = { ...ratio };
        });
      }
    }
  }
}, { deep: true });

// 位置指定あり → なしにデザイン変更した場合の処理
const handleDesignChangeToNonSpecified = async () => {
  // メインビジュアルの処理
  const mainVisualBlock = formUpdated.value.blocks.find(block => block.id === 'mainVisual');
  if (mainVisualBlock?.contents?.images) {
    const images = mainVisualBlock.contents.images;
    let updatedImages = [];

    // 動画が1枚でもあれば1枚の動画を残して、他の画像・動画を削除
    const videoIndex = images.findIndex(image => image && (image.srcVideo || image.type === 'VIDEO'));
    if (videoIndex !== -1) {
      updatedImages = [images[videoIndex]];
    } else {
      // 動画がない場合は最初の画像のみ残す
      const imageIndex = images.findIndex(image => image && (image.src || image.uuid));
      if (imageIndex !== -1) {
        updatedImages = [images[imageIndex]];
      }
    }

    // 残った画像・動画のトリミング比率を再計算
    if (updatedImages.length > 0) {
      const currentAspectRatio = imageAspectRatio.value?.mainVisual || { width: 9, height: 16 };

      for (let index = 0; index < updatedImages.length; index++) {
        const image = updatedImages[index];
        if (image && (image.uuid || image.src)) {
          try {
            // 現在のトリミング比率と新しいアスペクト比を比較
            const currentCropAspect = image.cropBoxWidth && image.cropBoxHeight ?
              image.cropBoxWidth / image.cropBoxHeight :
              (image.width && image.height ? image.width / image.height : null);
            const targetAspect = currentAspectRatio.width / currentAspectRatio.height;

            // アスペクト比が異なる場合のみ再計算
            if (!currentCropAspect || Math.abs(currentCropAspect - targetAspect) > 0.01) {
              const optimalCropping = await calculateOptimalCropping(image, currentAspectRatio);

              if (optimalCropping) {
                updatedImages[index].x = Number(optimalCropping.x);
                updatedImages[index].y = Number(optimalCropping.y);
                updatedImages[index].width = Number(optimalCropping.width);
                updatedImages[index].height = Number(optimalCropping.height);
                updatedImages[index].cropBoxWidth = Number(optimalCropping.width);
                updatedImages[index].cropBoxHeight = Number(optimalCropping.height);

                if (updatedImages[index].rotate === undefined) updatedImages[index].rotate = 0;
                if (updatedImages[index].scaleFactor === undefined) updatedImages[index].scaleFactor = 1;
              }
            }
          } catch (error) {
            // エラーがあっても処理を続行
            continue;
          }
        }
      }
    }

    const updatedBlock = {
      ...mainVisualBlock,
      contents: {
        ...mainVisualBlock.contents,
        images: updatedImages
      }
    };
    onUpdateBlock({ key: 'mainVisual', value: updatedBlock });
  }
};

// 個別画像のトリミング再計算ヘルパー関数
const recalculateImageCropping = async (image, aspectRatio) => {
  try {
    // 現在のトリミング比率と新しいアスペクト比を比較
    const currentCropAspect = image.cropBoxWidth && image.cropBoxHeight ?
      image.cropBoxWidth / image.cropBoxHeight :
      (image.width && image.height ? image.width / image.height : null);
    const targetAspect = aspectRatio.width / aspectRatio.height;

    // アスペクト比が異なる場合のみ再計算
    if (!currentCropAspect || Math.abs(currentCropAspect - targetAspect) > 0.01) {
      const optimalCropping = await calculateOptimalCropping(image, aspectRatio);

      if (optimalCropping) {
        return {
          x: Number(optimalCropping.x),
          y: Number(optimalCropping.y),
          width: Number(optimalCropping.width),
          height: Number(optimalCropping.height),
          cropBoxWidth: Number(optimalCropping.width),
          cropBoxHeight: Number(optimalCropping.height),
          rotate: image.rotate !== undefined ? image.rotate : 0,
          scaleFactor: image.scaleFactor !== undefined ? image.scaleFactor : 1
        };
      }
    }
    return null;
  } catch (error) {
    return null;
  }
};

// デザイン変更時の動画処理
const handleDesignChangeVideoProcessing = () => {
  // 位置指定なしの場合のみ動画処理を実行
  if (
    detailData.value?.m_web_invitation?.image_aspect_settings_json &&
    detailData.value.m_web_invitation.image_aspect_settings_json?.mainVisual?.isLockSpecifiedPosition !== true
  ) {
    const mainVisualBlock = formUpdated.value.blocks.find(block => block.id === 'mainVisual');

    if (mainVisualBlock && mainVisualBlock.contents && mainVisualBlock.contents.images && mainVisualBlock.contents.images.length > 1) {
      // 動画が含まれている場合、最初の動画1つだけを残す
      const firstVideo = mainVisualBlock.contents.images.find(image => image && image.type === 'VIDEO');
      if (firstVideo) {
        mainVisualBlock.contents.images.splice(0, mainVisualBlock.contents.images.length);
        mainVisualBlock.contents.images.push(firstVideo);

        // formUpdated.valueを更新
        const updatedFormUpdated = JSON.parse(JSON.stringify(formUpdated.value));
        formUpdated.value = updatedFormUpdated;

        // form.valueも更新
        const updatedForm = JSON.parse(JSON.stringify(formUpdated.value));
        form.value = updatedForm;
      }
    }
  }
};

// 位置指定の変更を監視して処理を実行
const previousIsLockSpecifiedPosition = ref(false);
watch(isLockSpecifiedPosition, async (newVal, oldVal) => {
  // 位置指定ありからなしに変更された場合
  if (oldVal === true && newVal === false) {
    await handleDesignChangeToNonSpecified();
    setTimeout(() => {
      handleDesignChangeVideoProcessing();
    }, 100);
  }
  previousIsLockSpecifiedPosition.value = newVal;
}, { immediate: true });

// デザイン変更を監視
const previousDesignId = ref(null);
watch(() => detailData.value?.m_web_invitation?.id, async (newDesignId, oldDesignId) => {
  if (oldDesignId && newDesignId && oldDesignId !== newDesignId) {
    if (!isProcessingAspectRatioChange.value) {
      isProcessingAspectRatioChange.value = true;

      try {
        await new Promise(resolve => setTimeout(resolve, 100));

        // データが更新されてから処理を実行
        const currentForm = form.value;
        const currentDetailData = detailData.value;

        if (currentForm?.blocks && currentDetailData?.m_web_invitation?.image_aspect_settings_json) {
          const aspectSettings = currentDetailData.m_web_invitation.image_aspect_settings_json;

          // 各ブロックのform.valueから現在の画像アスペクト比を計算
          const calculateOldAspectFromFormValue = (blockType: string) => {
            const block = currentForm.blocks.find((b: any) => b.type === blockType);
            if (!block?.value) return { width: 16, height: 9 };
            
            try {
              const blockData = JSON.parse(block.value);
              if (blockData.images && blockData.images.length > 0) {
                const firstImage = blockData.images[0];
                if (firstImage.width && firstImage.height) {
                  return { 
                    width: firstImage.width, 
                    height: firstImage.height 
                  };
                }
              }
              
              // gallery ブロックの場合は images.items をチェック
              if (blockType === 'gallery' && blockData.images?.items && blockData.images.items.length > 0) {
                const firstItem = blockData.images.items[0];
                if (firstItem.width && firstItem.height) {
                  return { 
                    width: firstItem.width, 
                    height: firstItem.height 
                  };
                }
              }
            } catch (e) {
            }

            return { width: 16, height: 9 }; // デフォルト値
          };

          // 全ブロックタイプのトリミングをリセット
          const allBlockTypes = ['mainVisual', 'message', 'profile', 'information', 'gallery', 'freeField'];
          const forceResetBlocks = allBlockTypes.map(type => ({
            type,
            oldAspect: calculateOldAspectFromFormValue(type),
            newAspect: aspectSettings[type] || { width: 16, height: 9 }
          })).filter(block => aspectSettings[block.type]);

          if (forceResetBlocks.length > 0) {
            const { hasChanges, updatedBlocks } = await resetCroppingForChangedBlocks(forceResetBlocks, currentForm.blocks);

            if (hasChanges) {
              form.value = { ...form.value, blocks: updatedBlocks };
              formUpdated.value = { ...formUpdated.value, blocks: updatedBlocks };
            }
          }
        }
        setTimeout(() => {
          handleDesignChangeVideoProcessing();
        }, 200);
      } finally {
        isProcessingAspectRatioChange.value = false;
      }
    }
  }
  previousDesignId.value = newDesignId;
}, { immediate: true });

// 最大枚数の変更を監視
const previousMaxImages = ref(3);
watch(maxMainVisualImages, (newMax, oldMax) => {
  previousMaxImages.value = newMax;
}, { immediate: true });

// 画像縦横比の変更を監視してトリミングをリセット
const previousImageAspectRatio = ref(null);
const isProcessingAspectRatioChange = ref(false);

watch(imageAspectRatio, async (newRatio, oldRatio) => {
  // 処理中は重複実行を防ぐ
  if (isProcessingAspectRatioChange.value) return;

  // 初回読み込み時はスキップ
  if (!oldRatio || !previousImageAspectRatio.value) {
    previousImageAspectRatio.value = JSON.parse(JSON.stringify(newRatio));
    return;
  }

  isProcessingAspectRatioChange.value = true;

  try {
    // アスペクト比の変更を検知
    const changedBlocks = detectAspectRatioChanges(newRatio, oldRatio);

    if (changedBlocks.length > 0) {

      // トリミングをリセット
      const { hasChanges, updatedBlocks } = await resetCroppingForChangedBlocks(changedBlocks, form.value.blocks);

      if (hasChanges) {
        form.value = { ...form.value, blocks: updatedBlocks };
        formUpdated.value = { ...formUpdated.value, blocks: updatedBlocks };
      }

      // 画像読み込み後にトリミング比率を再計算
      recalculateAfterImageLoad(changedBlocks, form.value.blocks, detailData.value, 1000).then(({ hasChanges: hasRecalcChanges, updatedBlocks: recalcBlocks }) => {
        if (hasRecalcChanges) {
          const mergedBlocks = mergeBlocksPreservingSrc(form.value.blocks, recalcBlocks);
          form.value = { ...form.value, blocks: mergedBlocks };
          formUpdated.value = { ...formUpdated.value, blocks: mergedBlocks };
        }
      }).catch(error => {
      });
    }
    previousImageAspectRatio.value = JSON.parse(JSON.stringify(newRatio));
  } finally {
    isProcessingAspectRatioChange.value = false;
  }
}, { deep: true });

// src情報を保持しつつブロック情報をマージする関数
const mergeBlocksPreservingSrc = (currentBlocks: any[], updatedBlocks: any[]) => {
  if (!currentBlocks || !updatedBlocks) return updatedBlocks;

  const mergedBlocks = JSON.parse(JSON.stringify(updatedBlocks));

  const preserveSrcInBlock = (current: any, updated: any) => {
    if (Array.isArray(current) && Array.isArray(updated)) {
      current.forEach((item, index) => {
        if (updated[index]) {
          preserveSrcInBlock(item, updated[index]);
        }
      });
    } else if (current && updated && typeof current === 'object' && typeof updated === 'object') {
      // src情報を現在の値から保持
      if (current.src && current.uuid === updated.uuid) {
        updated.src = current.src;
      }
      if (current.srcVideo && current.uuid === updated.uuid) {
        updated.srcVideo = current.srcVideo;
      }
      // 子オブジェクトも再帰的に処理
      Object.keys(updated).forEach(key => {
        if (current[key] && updated[key]) {
          preserveSrcInBlock(current[key], updated[key]);
        }
      });
    }
  };

  currentBlocks.forEach((currentBlock, index) => {
    const updatedBlock = mergedBlocks.find((b: any) => b.id === currentBlock.id);
    if (updatedBlock) {
      preserveSrcInBlock(currentBlock, updatedBlock);
    }
  });

  return mergedBlocks;
};

// デザイン変更時のアスペクト比設定変更を監視
const previousDesignAspectRatio = ref(null);
watch(() => detailData.value?.m_web_invitation?.image_aspect_settings_json, async (newSettings, oldSettings) => {
  if (isProcessingAspectRatioChange.value) return;

  // 初回読み込み時はスキップ
  if (!oldSettings || !previousDesignAspectRatio.value) {
    if (newSettings) {
      previousDesignAspectRatio.value = JSON.parse(JSON.stringify(newSettings));
    }
    return;
  }

  // 設定が同じ場合はスキップ
  if (JSON.stringify(newSettings) === JSON.stringify(oldSettings)) {
    return;
  }

  isProcessingAspectRatioChange.value = true;

  try {
    const changedBlocks = detectAspectRatioChanges(newSettings, oldSettings);

    if (changedBlocks.length > 0) {

      // 既存の画像がある場合のみトリミングをリセット
      if (form.value?.blocks) {
        const { hasChanges, updatedBlocks } = await resetCroppingForChangedBlocks(changedBlocks, form.value.blocks);

        if (hasChanges) {
          form.value = { ...form.value, blocks: updatedBlocks };
          formUpdated.value = { ...formUpdated.value, blocks: updatedBlocks };
        }

        // 画像読み込み後にトリミング比率を再計算
        recalculateAfterImageLoad(changedBlocks, form.value.blocks, detailData.value, 1000).then(({ hasChanges: hasRecalcChanges, updatedBlocks: recalcBlocks }) => {
          if (hasRecalcChanges) {
            const mergedBlocks = mergeBlocksPreservingSrc(form.value.blocks, recalcBlocks);
            form.value = { ...form.value, blocks: mergedBlocks };
            formUpdated.value = { ...formUpdated.value, blocks: mergedBlocks };
          }
        }).catch(error => {
        });
      }
    }

    // imageAspectRatioも同時に更新
    if (newSettings) {
      imageAspectRatio.value = newSettings;
      previousImageAspectRatio.value = JSON.parse(JSON.stringify(newSettings));
      previousDesignAspectRatio.value = JSON.parse(JSON.stringify(newSettings));
    }
  } finally {
    isProcessingAspectRatioChange.value = false;
  }
}, { deep: true });


const hasPrePaidGuest = ref();
watch(getData, async (newVal) => {
  if (newVal && newVal?.scheduled_transfer_date) {
    shippingDate.value = newVal.scheduled_transfer_date;
  }
  if (newVal && newVal.editor_settings && !isSave.value) {
    detailData.value = newVal;

    let editorSettings = newVal?.editor_settings;

    // 開催日のデフォルトセット
    if (route?.query?.create == '1' && member?.value?.memberMe?.wedding_info && member?.value?.memberMe?.wedding_info?.wedding_date && !newVal?.editor_settings?.[5]?.contents?.date) {
      editorSettings.blocks[5].contents.date = member?.value?.memberMe?.wedding_info?.wedding_date;
    }

    if (editorSettings) {
      // 事前支払いの一時停止期間
      createdAt.value = newVal.created_at;
      if (isPrepaymentPausePeriod(system.value, createdAt.value)) {
        let blockIndex = editorSettings?.blocks.findIndex((target) => {
          return 'gift' == target.id;
        });
        if (editorSettings?.blocks?.[blockIndex]) {
          editorSettings.blocks[blockIndex].contents.isUseGift = false;
        }
      }

      form.value = editorSettings;
      formUpdated.value = editorSettings;
    }
    if (newVal?.m_web_invitation?.image_aspect_settings_json) {
      const newSettings = newVal.m_web_invitation.image_aspect_settings_json;
      imageAspectRatio.value = newSettings;

      // デザイン変更時の監視用に前回の値も更新
      if (!previousImageAspectRatio.value) {
        previousImageAspectRatio.value = JSON.parse(JSON.stringify(newSettings));
      }
      if (!previousDesignAspectRatio.value) {
        previousDesignAspectRatio.value = JSON.parse(JSON.stringify(newSettings));
      }
    }

    // image_aspect_settings_jsonのmainVisualにisLockSpecifiedPositionが無い場合の動画処理
    if (
      newVal?.m_web_invitation?.image_aspect_settings_json &&
      newVal.m_web_invitation.image_aspect_settings_json?.mainVisual?.isLockSpecifiedPosition != true
    ) {
      const mainVisualBlock = editorSettings.blocks.find(block => block.id === 'mainVisual');
      if (mainVisualBlock && mainVisualBlock.contents && mainVisualBlock.contents.images && mainVisualBlock.contents.images.length > 1) {
        // 動画が含まれている場合、最初の動画1つだけを残す
        const firstVideo = mainVisualBlock.contents.images.find(image => image.type === 'VIDEO');
        if (firstVideo) {
          mainVisualBlock.contents.images.splice(0, mainVisualBlock.contents.images.length);
          mainVisualBlock.contents.images.push(firstVideo);
          const updatedEditorSettings = JSON.parse(JSON.stringify(editorSettings));
          form.value = updatedEditorSettings;
          formUpdated.value = updatedEditorSettings;
        }
      }
    }
    if (newVal?.guests) {
      guests.value = newVal.guests;
    }
    if (newVal?.has_pre_paid_guest) {
      hasPrePaidGuest.value = newVal.has_pre_paid_guest;
    }
    await getImagesFromUUIDs(newVal);

    nextTick(() => {
      // ハッシュを取得
      const { hash } = window.location;
      if (hash) {
        // ハッシュが存在する場合、その要素にスクロール
        const element = document.querySelector(hash);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }
    });
  }
}, {
  deep: true,
  immediate: true
})

const onCheckGuestAnswer = computed(() => {
  if (!guests || !guests.value || guests.value.length <= 0) { return false };
  return true;
});
const onCheckGuestPaid = computed(() => {
  if (!guests || !guests.value || guests.value.length <= 0) { return false };
  guests.value.forEach((guest) => {
    if (guest.payment_method == 'ADVANCE_PAYMENT') {
      return true;
    }
  });
  return false;
});

const onUpdateBlock = (target: { key: string, value: string }) => {
  let formItem = JSON.parse(JSON.stringify(formUpdated.value));
  let blockIndex = formUpdated.value?.blocks.findIndex((element) => {
    return element.id == target.key;
  });
  formItem.blocks[blockIndex] = target.value;

  formUpdated.value = formItem;
}
const onUpdateSort = (target: any) => {
  if (target.length) {
    let formItem = JSON.parse(JSON.stringify(formUpdated.value));
    formItem.blocks = target;
    formUpdated.value = formItem;
  }
}


const onFilterUpdateBlock = (id: string) => {
  const block = formUpdated.value.blocks.find(block => block.id === id);
  return block ? block : null;
};

// 入力項目
const error = ref('');
const $externalResults = ref({} as GraphQLValidationErrors);
// バリデーションルール
const rules = computed(() => {
  return {
    blocks: formUpdated.value.blocks.map(item => {
      switch (item.id) {
        case 'information':
          return {
            contents: {
              date: {
                required: helpers.withMessage(validationMessage.required('開催日'), validationRequiredIf(onFilterUpdateBlock('information')?.visible))
              }
            }
          };
        case 'guestAnswer':
          return {
            contents: {
              limit: {
                date: {
                  eventDate: helpers.withMessage(validationMessage.eventDate(), validationEventDate(onFilterUpdateBlock('information')?.contents?.date))
                }
              }
            }
          };
        default:
          return {};
      }
    })
  }
});
const v$ = useVuelidate(rules, formUpdated, { $externalResults });

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "My招待状",
    link: '/mypage/webinvitation',
  },
  {
    title: "My招待状 編集"
  }
];

const paramPartsHidden = {
  ShowFooterTop: true,
  FixedFooter: true,
  spBreadcrumbs: true,
  spLogo: true
}

const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('/mypage/webinvitation');
});
</script>

<style lang="scss" scoped>
:deep(.contents-title) {
  @include sp {
    display: none;
  }
}

:deep(.container) {
  margin-top: 0;
}

@include pc {
  :deep(.container) {
    .l-column1 {
      padding-top: 0;
      max-width: 100%;
    }
  }
}

.wholeError {
  max-width: 580px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

.webInvitation {
  &_selectedTheme {
    background: #FFF;
    border-bottom: 10px solid #F4F4F4;
    position: relative;
    z-index: 1;

    @include sp {
      background: #F4F4F4;
      border-bottom: none;
    }

    &_wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;
      max-width: 340px;
      padding: 20px 20px 24px;
      gap: 32px;

      :deep(a.button--md.button--reversal) {
        width: 150px;
        padding: 11px 6px;

        &.button--disabled {
          color: #D9D9D9;
          background: #F4F4F4;
          border-color: #D9D9D9;
          opacity: 1;
        }
      }
    }

    &_image {
      background: #ccc;
      display: block;
      width: 114px;
      aspect-ratio: 75 / 132;
      border: 1px solid #D9D9D9;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 114px;
        aspect-ratio: 75 / 133;
        object-fit: cover;
      }
    }

    &_text {
      max-width: calc(100% - 150px);
      word-break: break-all;
    }

    &_title {
      color: #49454F;
      font-weight: 400;
      font-size: 12px;
      line-height: 1.45;
      letter-spacing: 0.02em;
      margin-bottom: 4px;
    }

    &_guestListTitle {
      margin-top: 24px;
    }

    &_templateName {
      color: #333;
      font-family: 'Encorpada Classic';
      font-weight: 400;
      font-size: 12px;
      line-height: 1.3;
      letter-spacing: 0.02em;
      margin: 4px 0;
    }

    &_guestList {
      color: #333;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.2;
      letter-spacing: 0;
      vertical-align: middle;
      margin: 4px 0;
    }

    &_guestListAlert {
      color: #9C9C9C;
      font-weight: 400;
      font-size: 12px;
      line-height: 1.45;
      letter-spacing: 0.02em;
      margin: 4px 0 0;
      white-space: nowrap;
    }
  }
}

.confirm {
  &_title {
    margin: 0 0 16px;
    color: #333;
    font-size: 18px;
    font-weight: 400;
    line-height: 130%;
    letter-spacing: 0.48px;
  }

  &_alert {
    margin: 0;
    color: #FF1B1B;
    font-size: 14px;
    line-height: 1.4;
  }
}

:deep(.frowFooter) {
  @include sp {
    position: fixed;
    z-index: 800;
  }
}

.saveToast {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 70px;
  margin: auto;
  background: #FFF;
  border-radius: 10px;
  padding: 9px 16px;
  max-width: 360px;
  width: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.18);

  &_text {
    display: inline-block;
    min-height: 18px;
    color: $color-main;
    font-size: 15px;
    font-weight: bold;
    padding-left: 26px;
    background-image: url('@/assets/images/icon-check-saveToast.svg');
    background-repeat: no-repeat;
    background-position: left center;
    background-size: 18px;
  }

  &_button {
    display: flex;

    a {
      color: #FFF;
      background: $color-accent;
      font-size: 12px;
      padding: 8px 16px;
      font-weight: bold;
      border-radius: 4px;
      text-decoration: none;
      min-width: 124px;
      text-align: center;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.35s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

:deep(.modalConfirm .wrap) {
  padding-bottom: 0;
}
</style>