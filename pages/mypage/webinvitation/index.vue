<template>
  <div class="webInvitation">
    <Main 
      :partsHidden="paramPartsHidden" 
      :breadcrumbs="breadcrumbs" 
      title="My招待状"
      :backlink="backlink"
    >
      <template #main>
        <Loading v-if="isLoading"></Loading>
        <template v-else>
          <div class="webInvitation_main">
            <div v-if="filteredWebInvitationsData.length" class="webInvitation_boxes">
              <div class="webInvitation_boxes_check">
                <InputCheckSingle
                  label="公開中の招待状のみ表示"
                  name="ViewPublicWebInvitation"
                  :value="isCheckPublic"
                  @change="onChangePublic($event)"
                />
              </div>
              <div class="webInvitation_box"
                :class="[{ 'is-public': item.is_public }]"
                v-for="(item, index) in filteredWebInvitationsData" :key="index">
                <div class="webInvitation_box_menu">
                  <a @click.stop="onToggleMenu(index)" class="webInvitation_box_menu_button"></a>
                  <transition name="fade">
                    <ul v-if="activeMenu === index" class="popup_menu">
                      <li><NuxtLink :to="'/mypage/webinvitation/setting/name?id=' + item.id">名称変更/ゲストリスト変更</NuxtLink></li>
                      <li><a @click="onCopyWebInvitation(item.id)">コピーを作成</a></li>
                      <li><a @click="onShowDeleteModal(item.id, item.name)" :class="[{ 'is-disabled': item.is_public || onCheckGuestAnswer(item?.guests) }]">削除<span v-if="item.is_public"> ※公開中は削除できません</span></a></li>
                    </ul>
                  </transition>
                </div>
                <div class="webInvitation_box_main">
                  <div class="webInvitation_box_title">
                    <h2
                      v-if="item.name"
                      :title="item.name"
                    >
                      {{ item.name }}
                    </h2>
                  </div>
                  <div class="webInvitation_box_image">
                    <a class="webInvitation_box_image_link" href="#" @click.prevent="onClickPreview(item.id, item?.editor_settings)">
                      <template v-if="item?.m_web_invitation?.m_specification_product?.product_images?.[0]?.src">
                        <img :src="item?.m_web_invitation?.m_specification_product?.product_images?.[0]?.src">
                        <span>プレビュー</span>
                      </template>
                    </a>
                  </div>
                  <div class="webInvitation_box_text">
                    <div class="date">作成日：{{ onChangeDate(item.created_at) }}</div>
                    <div v-if="item.guest_list" class="user cmn-link">
                      <NuxtLink :to="'/mypage/guest-list/' + item?.guest_list?.id">{{ item.guest_list.name }}</NuxtLink>
                    </div>
                  </div>
                  <div class="webInvitation_box_buttons">
                    <ButtonMainColor baseColor="accent_reversal" :to="'/mypage/webinvitation/editor/form?id=' + item.id">編集</ButtonMainColor>
                    <div class="webInvitation_box_settings">
                      <div>
                        <ButtonMainColor
                          class="button_public"
                          :class="{'is-public': item.is_public}"
                          baseColor="accent_reversal"
                          :to="'/mypage/webinvitation/setting/release?id=' + item.id"
                        >
                          {{ item.is_public ? '公開中' : '公開設定' }}
                        </ButtonMainColor>
                      </div>
                      <div>
                        <ButtonMainColor baseColor="reversal" :class="[{ 'is-disabled': !item.is_public }]" :to="'/mypage/webinvitation/share?id=' + item.id">ゲストに送る</ButtonMainColor>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="webInvitation_box_footer" v-if="item?.public_url">
                  <div class="webInvitation_box_guestCheck">
                    <div class="webInvitation_box_guestCheck_label">ゲストの回答を確認</div>
                    <div class="webInvitation_box_guestCheck_text" v-if="onGetLimitDate(item?.editor_settings?.blocks)">回答期限まであと{{onGetLimitDate(item?.editor_settings.blocks)}}日</div>
                  </div>
                  <NuxtLink :to="'/mypage/guest-list/' + item?.guest_list?.id">{{item?.guests.length ? item?.guests.length : 0}}件</NuxtLink>
                </div>
              </div>
            </div>
            <div v-if="allWebInvitationsData && allWebInvitationsData.length && Math.ceil(allWebInvitationsData.length / perPage) > 1 && perPage">
              <WebInvitationPagination
                :current="pageNum"
                :total="allWebInvitationsData ? Math.ceil(allWebInvitationsData.length / perPage) : 0"
                @change="onChangePagination"
              ></WebInvitationPagination>
            </div>
            <div class="webInvitation_create row cmn-alignright" v-if="filteredWebInvitationsData.length">
              <ButtonMainColor
                to="/products/webinvitation"
                addClasses="plus-w"
                baseColor="accent"
                :buttonsize="200"
              >新規作成</ButtonMainColor>
            </div>
          </div>
          <div v-if="!filteredWebInvitationsData.length" class="contener-sm sp_ph0 cmn-aligncenter" style="margin-top: 40px;">
            <h2 class="cmn-title mb-16">My招待状が未作成です</h2>
            <p class="mb-24">招待状を作成すると<br>一覧に追加されます。</p>
            <div class="mb-22"><img src="@/assets/images/mypage/webinvitation/nodata.svg"></div>
            <p class="mb-24">まずはデザインを選んで、<br>招待状を作ってみましょう！</p>
            <div style="max-width: 343px; margin: 0 auto;"><NuxtLink to="/products/webinvitation" class="btn btn-secondary btn-block size--sm"><i class="icn-left material-icons-outlined">mail</i> WEB招待状を作る</NuxtLink></div>
          </div>
          <ModalDeleteWebInvitation
            v-if="isShowDeleteModal"
            :id='isActiveId'
            :name='isActiveName'
            @close="isShowDeleteModal = false"
            @deleted="onDeletedWebInvitation"
          ></ModalDeleteWebInvitation>
        </template>
      </template>
    </Main>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();
const { $dayjs } : any = useNuxtApp();

const activeMenu = ref(null);
const onToggleMenu = (index:number) => {
  activeMenu.value = activeMenu.value === index ? null : index;
};
const handleClickOutside = (event:any) => {
  if (!event.target.closest('.popup_menu') && !event.target.closest('.webInvitation_box_menu_button')) {
    activeMenu.value = null;
  }
};
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});

const { webInvitationsData, refetch, loading } = useGetManyWebInvitation();
refetch();

const perPage = 6;
const pageNum = ref(route?.query?.page ? parseInt(route.query.page as string) : 1);
const isCheckPublic = ref(false);

const onChangeDate = (inputDate: any) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  const date = new Date(new Date(inputDate).toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }));
  return date.toLocaleDateString('ja-JP', options);
};

const onCheckGuestAnswer = (guests: any) => {
  if(!guests || guests.length <= 0) { return false };
  return true;
};

const onGetLimitDate = (newData: any) => {
  if (!newData) { return false; }
  const today = new Date(new Date().toLocaleString('ja-JP', { timeZone: "Asia/Tokyo" }))
  let guestAnswerData = newData.find((element) => element.id == 'guestAnswer');
  let informationData = newData.find((element) => element.id == 'information');
  let date = informationData?.contents?.date;
  if (!date || date == '--') { return false; }
  let targetDate;

  if (guestAnswerData?.contents?.limit?.setting == 1) {
    targetDate = $dayjs(date).add(-1, "month").format('YYYY-MM-DD'); // 期限日の1ヶ月前
  } else if (guestAnswerData?.contents?.limit?.setting == 2) {
    targetDate = $dayjs(date).add(-2, "month").format('YYYY-MM-DD'); // 期限日の2ヶ月前
  } else if (guestAnswerData?.contents?.limit?.setting == 3) {
    targetDate = $dayjs(guestAnswerData.contents.limit.date).format('YYYY-MM-DD'); // 指定された日付
  } else {
    return false; // 設定が存在しない場合はfalseを返す
  }

  // 期限までの日数を計算
  const diffDays = $dayjs(targetDate).diff(today, 'days');
  if(diffDays < 0){
    return '0'
  }
  return diffDays
}

// 画像の取得
const webData = ref([]);
const getImagesFromUUIDs = async (obj) => {
  let updateObj = toRaw(obj);
  let uuids = extractUuids(updateObj);
  const { getImages, refetch, imageLoading } = await useGetManyImages2(uuids);
  await refetch();
  watch(() => getImages, (images, prevImages) => {
    if (images.value) {
      const updatedObjWithImages = setImages(updateObj, images.value);
      webData.value = updatedObjWithImages;
    }
  }, {
    deep: true,
    immediate: true
  });
}
watch(webInvitationsData, async(newVal) => {
  if(newVal){
    webData.value = newVal;
    await getImagesFromUUIDs(newVal)
  }
}, {
  deep: true,
  immediate: true
})

const allWebInvitationsData = computed(() => {
  if (webData.value) {
    let data = webData.value;
    if(isCheckPublic.value){
      data = data.filter(item => item.is_public);
    }
    return data;
  }else{
    return []
  }
});
const filteredWebInvitationsData = computed(() => {
  if (webData.value && allWebInvitationsData.value) {
    let data = allWebInvitationsData.value;
    if(data.length && data.length <= perPage){
      return data
    }else if(data.length){
      let filterData = data.slice((pageNum.value - 1) * perPage, (pageNum.value) * perPage);
      return filterData;
    }
    return []
  }else{
    return []
  }
});

const onChangePagination = (value:number) => {
  pageNum.value = Number(value);
  const query = { ...route.query };

  if (value == 1) {
    delete query.page;
  } else {
    query.page = value.toString();
  }

  router.push({
    path: route.path,
    query: query
  });
}

// 公開絞り込み
const onChangePublic = (event:any) => {
  pageNum.value = 1;
  isCheckPublic.value = event;
}

// フォーム更新時
const isLoading = ref(false);
const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink();
  isLoading.value = true;
  await refetch();
  isLoading.value = false;
});

// プレビューボタンのクリック
const onClickPreview = async (id: string, editor_settings: any) => {
  // リンクの作成
  const previewLink = router.resolve({
    path: `/mypage/webinvitation/view/${id}`,
    query: {
      prev: 1
    }
  });
  // セッションストレージにformデータを保存
  sessionStorage.setItem('webInvitationFormData', JSON.stringify(editor_settings));
  window.open(previewLink.href, '_blank');
}

// ウェブ招待状のコピー
const { copy, errors: copyError } = useCopyWebInvitation();
const onCopyWebInvitation = async(id: string) => {
  isLoading.value = true;
  const isSuccess = await copy(id);
  activeMenu.value = null;
  pageNum.value = 1;
  if (isSuccess) {
    addToastMessage({ message: `「${isSuccess?.data?.copyWebInvitation?.name}」が作成されました`});
  } else {
    addToastMessage({ message: copyError?.value?.v$?.[0] });
  }
  await refetch();
  isLoading.value = false;
};

// ウェブ招待状の削除
const isShowDeleteModal = ref(false);
const isActiveId = ref('');
const isActiveName = ref('');
const onShowDeleteModal = async(id: string, name: string) => {
  isActiveId.value = id;
  isActiveName.value = name;
  isShowDeleteModal.value = true;
};
// リロードしてメッセージを表示
const onDeletedWebInvitation = async (message: string) => {
  await refetch();
  pageNum.value = 1;
  isShowDeleteModal.value = false;
}

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "My招待状",
  },
];

const paramPartsHidden = {
  ShowFooterTop: true,
  FixedFooter: true,
  spBreadcrumbs: true,
  spLogo: true,
  spBackTop: false,
  spH1Title: true
}
</script>

<style lang="scss" scoped>
.icon-chevron {
  position: relative;
  padding-right: 24px;

  &:hover {
    text-decoration: underline;
  }

  &::after {
    content: "";
    display: inline-block;
    position: absolute;
    top: 50%;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
    transform: translateY(-50%);
    display: block;
    right: 0;
    width: 12px;
    height: 12px;
    background-image: url(@/assets/images/icon-chevron_right.svg);
    opacity: 0.5;
  }
}

.webInvitation {
  &_boxes {
    margin: 0 auto 30px;
    padding: 0 16px;
    &_check{
      padding: 16px 0 4px;
      @include sp {
        padding: 16px 0 12px;
      }
    }
  }
  &_main {
    position: relative;
  }

  &_box {
    max-width: 664px;
    width: 100%;
    border: 1px solid $color-grayborder;
    border-radius: 4px;
    margin: 0 auto 12px;
    position: relative;

    &.is-public{
      background: $color-lightbackground;
    }

    &_main {
      display: grid;
      grid-template-columns: 94px 164px 1fr;
      grid-template-rows: 20px 1fr;
      gap: 10px;
      padding: 17px;
      @include sp {
        grid-template-columns: 94px 1fr;
      }
    }

    &_title {
      grid-row: 1 / 2;
      grid-column: 2 / 3;
      @include sp {
        grid-row: 1 / 2;
        grid-column: 1 / 3;
      }
      h2 {
        margin: 0 0 16px;
        font-size: 16px;
        font-weight: normal;
        max-width: calc(100% - 20px);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    &_image {
      grid-row: 1 / 3;
      grid-column: 1 / 2;
      display: flex;
      width: 94px;
      margin-right: 8px;
      @include sp {
        grid-row: 2 / 4;
        grid-column: 1 / 2;
      }
      img{
        width: 94px;
        height: 162px;
        background: #ccc;
        margin-right: 17px;
        border: 1px solid #D9D9D9;
        border-radius: 2px;
        overflow: hidden;
        object-fit: cover;
      }
      span{
        display: inline-flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        margin-top: 7px;
        &::before{
          content: '';
          display: inline-block;
          width: 12px;
          height: 12px;
          margin-right: 4px;
          background: url(@/assets/images/icon-search-b.svg) no-repeat center;
          background-size: contain;
        }
      }
      &_link{
        display: block;
        position: relative;
        width: 100%;
        height: 100%;
        color: #333;
        text-align: center;
        text-decoration: none;
        @include pc {
          &:hover {
            text-decoration: underline;
            span{
              text-decoration: underline;
            }
          }
        }
      }
    }

    &_text {
      grid-row: 2 / 3;
      grid-column: 2 / 3;
      @include sp {
        grid-row: 2 / 3;
        grid-column: 2 / 3;
      }
      .date {
        font-size: 11px;
        margin-bottom: 4px;
        color: $color-graytext2;
      }
      .user{
        @include sp {
          margin-bottom: 6px;
        }
        a{
          color: inherit;
          text-decoration: none;
          font-size: 12px;
          font-weight: bold;
        }
        &::before{
          content: '';
          display: inline-block;
          width: 24px;
          height: 24px;
          margin-right: 2px;
          background-color: currentColor;
          mask-image: url('@/assets/images/icon-person-gl.svg');
          mask-repeat: no-repeat;
          mask-position: center;
          mask-size: 18px;
          vertical-align: middle;
        }
      }
    }

    &_buttons {
      grid-row: 1 / 3;
      grid-column: 3 / 4;
      margin-top: 36px;
      @include sp {
        grid-row: 3 / 4;
        grid-column: 2 / 3;
        margin: 0;
      }
      .is-disabled {
        border-color: #D9D9D9 !important;
        color: #D9D9D9 !important;
        pointer-events: none;
      }
      :deep(a.is-public) {
        span {
          display: inline-flex;
          align-items: center;
          &::before {
            content: '';
            display: inline-block;
            width: 16px;
            height: 10px;
            margin-right: 5px;
            background-color: currentColor;
            mask-image: url('@/assets/images/icon-visibility-on.svg');
            mask-repeat: no-repeat;
            mask-position: center;
            mask-size: 16px;
            vertical-align: middle;
          }
        }
      }
    }

    &_settings {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid $color-grayborder;
      margin-top: 12px;
      padding-top: 12px;

      &>div {
        width: 100%;

        &+div {
          margin-left: 8px;
        }
      }
    }

    &_footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 17px;
      border-top: 1px solid $color-grayborder;
      font-size: 13px;
      a{
        color: $color-accent;
        text-decoration: none;
        position: relative;
        padding-right: 24px;
        &::after{
          content: '';
          display: inline-block;
          position: absolute;
          top: 50%;
          background-repeat: no-repeat;
          background-position: center center;
          background-size: contain;
          transform: translateY(-50%);
          right: 0;
          width: 14px;
          height: 12px;
          background: #333;
          mask-image: url('@/assets/images/icon-chevron_right.svg');
          mask-size: contain;
          mask-position: center;
          mask-repeat: no-repeat;
        }
      }
    }

    &_guestCheck{
      color: #333;
      @include pc{
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      &_label{
        font-size: 13px;
        @include pc{
          margin-right: 8px;
        }
      }
      &_text{
        font-size: 10px;
      }
    }

    &_menu {
      position: absolute;
      top: 12px;
      right: 12px;
      z-index: 1;

      ul {
        position: absolute;
        right: 0;
        top: 30px;
        margin: 0;
        padding: 0;
        width: 265px;
        border-radius: 4px;
        background: #fff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        transition: 0.35s ease-in-out;

        li {
          display: block;
          width: 100%;
        }

        a {
          display: block;
          width: 100%;
          padding: 12px 16px;
          line-height: 1;
          font-size: 16px;
          color: #1C1B1F;
          background: #fff;
          cursor: pointer;
          transition: 0.35s ease-in-out;
          text-decoration: none;
          &:hover {
            background: #EFF8FF;
            text-decoration: none;
          }
          &.is-disabled{
            opacity: 0.5;
            background: #FFF !important;
            pointer-events: none;
            pointer-events: none;
          }
        }
      }
      &_button {
        display: block;
        width: 30px;
        height: 30px;
        background: url(@/assets/images/icon-more_vert.svg) no-repeat center center;
        cursor: pointer;
        transition: 0.35s ease-in-out;
      }
    }
  }
  &_create{
    margin: 16px 0 30px;
    padding: 0 20px;
    position: sticky;
    bottom: 20px;
    width: 100%;
    max-width: 100%;
    @include pc {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 25px;
      margin: auto;
      padding: 0;
      text-align: center;
    }
    @include sp {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 10;
      pointer-events: none;
      transition: 0.35s ease;
      [data-scroll="top"] & {
        bottom: 50px;
      }
    }
    :deep(a){
      max-width: 140px;
      border-radius: 50px;
      pointer-events: auto;
      font-size: 14px;
      font-weight: 500;
      @include pc {
        max-width: 240px;
      }
      span::before{
        background: #fff;
        mask-image: url(@/assets/images/icon-plus-g.svg);
        mask-size: cover;
        mask-position: center;
        width: 21px;
        height: 21px;
        left: -5px;
      }
    }
  }
}
a.button--accent{
  border: none;
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.35s ease-in-out;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>