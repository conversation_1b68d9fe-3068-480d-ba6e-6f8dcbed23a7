<template>
  <div class="webInvitation">
    <Main
      :partsHidden="paramPartsHidden"
      :breadcrumbs="breadcrumbs"
      title="名称変更/ゲストリスト変更"
      :backlink="backlink"
    >
      <template #main>
        <div class="wrap">
          <div class="row">
            <InputText
              title="名称"
              :required="true"
              :size="200"
              placeholder="友人宛招待状 など"
              :error="error?.name?.$errors?.[0]?.$message"
              :value="input?.name"
              @input="input.name = $event.target.value"
            />
          </div>
          <div class="row">
            <InputSelect
              title="回答内容の追加先ゲストリスト"
              size="full"
              :class="{'hasError': guestListError}"
              :disabled="settingData?.guests.length > 0 ? true : false"
              :required="true"
              :options="options"
              :value="String(input?.guest_list_id)"
              :error="error?.guest_list_id?.$errors?.[0]?.$message"
              @change="input.guest_list_id = $event.target.value"
            />
            <div v-if="settingData?.guests.length > 0" class="input-disabled">ゲスト回答があるため変更できません</div>
            <div v-if="guestListError" class="input-error">{{ guestListError }}</div>
            <div class="cmn-alignright">
              <a @click.prevent="isShowAddGuestListModal = true" class="cmn-link" href="#">ゲストリスト追加</a>
            </div>
          </div>
          <div v-if="wholeError" class="input-error">{{ wholeError }}</div>
        </div>
        <div class="webInvitation_footer">
          <ShowFooterBarFrow
            :data="modalFooterBarFrow"
            @onClickBtn1="onClickSave()"
          />
        </div>
        <ModalAddGuestList
          v-if="isShowAddGuestListModal"
          @close="isShowAddGuestListModal = false"
          @create="onAddGuestListModal"
        ></ModalAddGuestList>
      </template>
    </Main>
  </div>
</template>

<script lang="ts" setup>
const router = useRouter();
const route = useRoute();
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();
import { useLoadingState } from '@/composables/useLoading';
const { showLoading, hideLoading } = useLoadingState();
import { required, maxLength, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";
import { QueryGuestListsOrderByColumn, SortOrder } from '@/composables/generated';

const input = ref({
  name: '',
  guest_list_id: '',
});
const isShowAddGuestListModal = ref(false);

// 情報の取得
const settingData = ref({});
// クエリパラメーターがある場合
let queryId = route?.query.id;
let isUpdateMode = ref(false);
let getData: any;
if (queryId) {
  const { webInvitationData } = useGetOneWebInvitation(queryId);
  isUpdateMode.value = true;
  getData = await webInvitationData;
}
// 更新API
const { update, errors:updateErrors } = useUpdateWebInvitation();

const guests = ref();
watch(getData, (newVal) => {
  settingData.value = newVal;
  input.value.name = newVal?.name;
  input.value.guest_list_id = newVal?.guest_list?.id;
  guests.value = newVal?.guests;
}, {
  deep: true,
  immediate: true
})

// APIから guestTag を読み込み
const orderBy = ref({
  column: QueryGuestListsOrderByColumn.CreatedAt,
  order: SortOrder.Asc
} as QueryGuestListsOrderByOrderByClause);

const { guestLists, refetch } = useGetManyGuestList([{
  column: QueryGuestListsOrderByColumn.IsDefault,
  order: SortOrder.Desc
}, orderBy.value])

// 敬称リスト
const options = computed(() => {
  const options = guestLists.value.map(guestList => {
    return {value: guestList.id, label: guestList.name};
  });
  return [{value: '', label: 'ゲストリストを選択'}].concat(options);
});

// 全体エラー
const error = ref('')
const rules = computed(() => {
  let rules = {
    name: {
      required: helpers.withMessage(validationMessage.required('ウェブ招待状名'), required),
      maxLength: helpers.withMessage(validationMessage.maxLength('ウェブ招待状名', 255), maxLength(255))
    },
    guest_list_id: {
      required: helpers.withMessage(validationMessage.required('ゲストリスト'), required)
    }
  };
  return rules;
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

// 設定保存処理
const wholeError = ref('');
const guestListError = ref('');
const onClickSave = async() => {
  // 全体エラーをリセット
  wholeError.value = '';
  guestListError.value = '';
  error.value = '';
  v$.value.$clearExternalResults();

  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = v$.value;
    return false;
  }

  showLoading();

  let isSuccess = await onSave(false);

  // エラーの場合
  if (! isSuccess) {
    if (updateErrors.value?.v$.length == 1 && updateErrors.value?.v$?.[0] == '他のWeb招待状の開催日が異なります'){
      guestListError.value = '移動先ゲストリストのWEB招待状と挙式日（開催日）が異なるため、ゲストリストを変更できません';
    }else if (updateErrors.value?.v$?.[0]) {
      wholeError.value = updateErrors.value?.v$?.[0];
    }
    hideLoading();
    return false;
  }

  hideLoading();

  // ToastMessage
  addToastMessage({ message: '名称/ゲストリストを変更しました ' });
  router.push({ path: '/mypage/webinvitation/' });
};

const onSave = async (is_synced: boolean) => {
  let isSuccess;
  isSuccess = await update({
    is_synced: is_synced,
    input: {
    id: queryId,
    name: input.value.name,
    guest_list_id: input.value.guest_list_id,
    m_web_invitation_id: settingData.value.m_web_invitation.id
  }
  });
  return isSuccess;
}

// ゲストリストの追加
const onAddGuestListModal = async() => {
  refetch();
  isShowAddGuestListModal.value = false;
};

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "ゲストリスト",
    link: '/mypage/guest-list',
  },
  {
    title: "ゲストリストを追加"
  }
];

// #modalWindow01へのデータ
const modalFooterBarFrow = {
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "My招待状一覧へ戻る",
          link: '/mypage/webinvitation',
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: false,
          slot: "変更する",
          link: false,
        },
      ],
    },
  ],
}

const paramPartsHidden = {
  ShowFooterTop: true,
  FixedFooter: true,
  spBreadcrumbs: true,
  spLogo: true
}

const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('/mypage/webinvitation');
});

</script>

<style lang="scss" scoped>
@include sp {
  :deep(.container) {
    margin-top: 0;
  }
  :deep(.contents-title) {
    display: none;
  }
  :deep(.breadcrumbs) {
    display: none;
  }
}
.webInvitation_footer{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 11;
}
.wrap{
  max-width: 560px;
  margin: 0 auto;
  padding: 20px 16px;
}
.row{
  margin-bottom: 24px;
  .cmn-link{
    display: inline-block;
    margin: 10px 0 0 auto;
    text-align: right;
    position: relative;
    padding-left: 26px;
    font-size: 14px;
    line-height: 100%;
    &::before {
      @include BA;
      left: 0;
      width: 24px;
      height: 24px;
      background-image: url(@/assets/images/icon-plus-g.svg);
    }
  }
}
.input-disabled {
  font-size: 12px;
  margin-top: 5px;
  display: block;
  color: #333;
  opacity: 0.5;
}
</style>