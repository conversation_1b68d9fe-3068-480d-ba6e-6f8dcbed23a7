<template>
  <div class="webInvitation">
    <Main
      :partsHidden="paramPartsHidden"
      :breadcrumbs="breadcrumbs"
      title="公開設定"
    >
      <template #main>
        <Loading v-if="isLoading"></Loading>
        <div v-else class="wrap">
          <div class="webInvitation_settings">
            <dl>
              <dt>公開状態</dt>
              <dd>
                <div class="status">
                  <div class="status_active">{{ settingData.is_public ? '公開' : '非公開' }}</div>
                  <div class="status_change" v-if="!isInitialSetting">
                    <a v-if="settingData.is_public" class="cmn-link public-button link-to-public" @click="onChangeValue('is_public', false);">非公開にする</a>
                    <a v-else class="cmn-link public-button link-to-close" @click="onChangeValue('is_public', true);">公開にする</a>
                  </div>
                </div>
              </dd>
            </dl>
            <dl>
              <dt>合言葉</dt>
              <dd>
                <div class="password">
                  <div class="password_switch">
                    <span class="icn-password"></span>
                    <InputSwitch
                      :checked="settingData.is_password"
                      @update:checked="onChangeValue('is_password', $event);"
                    ></InputSwitch>
                  </div>
                  <div class="password_text" v-if="settingData.is_password">
                    <InputText
                      :class="{'hasError': error && getValidationMessage(v$.password)}"
                      :isShowPasswordButton="isChangePassword"
                      :value="settingData.password"
                      :isFocusSelectText="true"
                      @input="onChangeValue('password', $event.target.value)"
                    />
                  </div>
                </div>
                <p v-if="$externalResults?.input?.password?.[0]" class="input-error">{{ $externalResults?.input?.password?.[0] }}</p>
                <p v-if="error" class="input-error">{{ getValidationMessage(v$.password) }}</p>
                <div class="password_description">
                  合言葉を設定すると、ゲストが招待状を見るときに合言葉の入力を求められます。
                </div>
              </dd>
            </dl>
            <dl>
              <dt>招待URL</dt>
              <dd>
                <div v-if="getData && getData.public_url" class="url_input">
                  <span class="url_text">{{ getWebInvitationUrl('https://favori.wedding/wi/'+getData.public_url) }} </span>
                </div>
                <div v-else class="url_input">
                  <span class="url_text">https://favori.wedding/wi/</span>
                  <InputText
                    placeholder="shunpei_konomi"
                    :class="{'hasError': error && getValidationMessage(v$.public_url)}"
                    :value="settingData.public_url"
                    @input="onChangeValue('public_url', $event.target.value)"
                    :size="155"
                  />
                </div>
                <p v-if="error" class="input-error">{{ getValidationMessage(v$.public_url) }}</p>
                <p v-if="$externalResults?.input?.public_url?.[0]" class="input-error">{{ $externalResults?.input?.public_url?.[0] }}</p>
                <p class="exclaim">※URLは半角英数字、ハイフン (-)、アンダースコア (_)、ピリオド (.) の記号のみが使用できます。</p>
              </dd>
            </dl>
            <p v-if="wholeError" class="input-error" v-html="wholeError"></p>
            <p class="alert">※URLは一度設定すると 変更できません </p>
          </div>
        </div>
        <div class="webInvitation_footer">
          <ShowFooterBarFrow
            :data="modalFooterBarFrow"
            @onClickBtn1="onClickSave()"
          />
        </div>
        <ModalConfirmWebInvitation
          v-if="isShowModalConfirm"
          label="OK"
          @close="isShowModalConfirm = false"
          @select="isConfirmSelect = true"
        >
          <p>変更は反映されませんがよろしいですか？</p>
        </ModalConfirmWebInvitation>
        <ModalConfirmWebInvitation
          v-if="isShowModalPublish"
          label="公開する"
          @close="isShowModalPublish = false"
          @select="onSaveSetting()"
        >
          <p>招待状を公開しますか？</p>
        </ModalConfirmWebInvitation>
        <ModalConfirmWebInvitation
          v-if="isShowModalUnpublish"
          label="非公開にする"
          @close="isShowModalUnpublish = false"
          @select="onSaveSetting()"
        >
          <p>招待状を非公開にしますか？</p>
        </ModalConfirmWebInvitation>
      </template>
    </Main>
  </div>
</template>

<script lang="ts" setup>
const router = useRouter();
const route = useRoute();
import { onBeforeRouteLeave } from 'vue-router';
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();
import { useLoadingState } from '@/composables/useLoading';
const { showLoading, hideLoading } = useLoadingState();
import { required, maxLength, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { GraphQLValidationErrors } from "@/utils/graphql";

const isLoading = ref(true);
const isChangePassword = ref(false);
const isInitialSetting = ref(true);

const isShowModalPublish = ref(false);
const isShowModalUnpublish = ref(false);
const isShowModalConfirm = ref(false);

const isChange = ref(false);
const isConfirmSelect = ref(false);
onBeforeRouteLeave((to, from, next) => {
  if (!isChange.value) {
    next();
  } else {
    isShowModalConfirm.value = true;
    const unwatch = watch(() => isConfirmSelect.value, (newVal) => {
      unwatch();
      if (!newVal) {
        next(false);
      }else{
        next();
      }
    });
  }
});

// 情報の取得
const settingData = ref({
  is_public: false,
  is_password: false,
  password: '',
  public_url: ''
});

// クエリパラメーターがある場合
let queryId = route?.query.id;
let getData: any;
if (queryId) {
  const { webInvitationData, refetch } = useGetOneWebInvitation(queryId);
  getData = await webInvitationData;
}
// 更新API
const { update, errors } = useUpdateWebInvitation();

// #modalWindow01へのデータ
const modalFooterBarFrow = ref({
  datail: [
    {
      type: 'button',
      data: [
        {
          buttonsize: 'harf',
          color: 'glay',
          disabled: false,
          slot: "My招待状一覧へ戻る",
          link: '/mypage/webinvitation',
        },
        {
          buttonsize: 'harf',
          color: 'accent',
          disabled: true,
          slot: "公開する",
          link: false,
        },
      ],
    },
  ],
});

// バリデーション
const rules = computed(() => {
  let rules = {
    password: settingData?.value?.is_password ? { required: helpers.withMessage('合言葉を入力してください', required) } : {},
    public_url: {
      required: helpers.withMessage(validationMessage.required('招待URL'), required),
      maxLength: helpers.withMessage(validationMessage.maxLength('招待URL', 255), maxLength(255)),
      publicUrl: helpers.withMessage(validationMessage.publicUrl('招待URL'), validationPublicUrl)
    }
  } as any;

  return rules;
});

// 全体エラー
const error = ref('')

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, settingData, { $externalResults });

watch(settingData, () => {
  v$.value.$touch()
})

// ランダムな英数字を生成
const onGenerateUrl = () => {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let randomStr = '';
  for(let i = 0; i < 10; i++) {
    randomStr += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return randomStr;
};

watch(getData, (newVal) => {
  if(newVal){
    isLoading.value = false;

    settingData.value = {
      is_public: getData.value.is_public,
      is_password: getData.value.is_password,
      password: getData.value.password,
      public_url: getData.value.public_url,
      editor_settings: getData.value.editor_settings
    }
    if(getData.value?.public_url && getData.value?.public_url != ''){
      isInitialSetting.value = false;
      modalFooterBarFrow.value.datail[0].data[1].slot = '設定する';
      modalFooterBarFrow.value.datail[0].data[1].disabled = false;
    } else {
      settingData.value.public_url = onGenerateUrl();
      modalFooterBarFrow.value.datail[0].data[1].disabled = false;
    }
  }
}, {
  immediate: true
});

// 値の変更
const onChangeValue = (key:string, changeData:any) => {
  isChange.value = true;
  if(!isChangePassword.value && key == 'password'){
    isChangePassword.value = true;
  }
  if(isInitialSetting.value && key == 'public_url'){
    if(changeData.length == 0){
      modalFooterBarFrow.value.datail[0].data[1].disabled = true;
    }else{
      modalFooterBarFrow.value.datail[0].data[1].disabled = false;
    }
  }
  settingData.value[key] = changeData;
};

// 設定保存処理
const onClickSave = async() => {
  let is_public = settingData.value.is_public;
  if(isInitialSetting.value){
    is_public = true;
  }

  if(is_public){
    isShowModalPublish.value = true;
  }else{
    isShowModalUnpublish.value = true;
  }
};

const wholeError = ref();
const onSaveSetting = async() => {
  // 全体エラーをリセット
  error.value = '';
  wholeError.value = '';
  v$.value.$clearExternalResults();

  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    isShowModalPublish.value = false;
    isShowModalUnpublish.value = false;
    return false;
  }

  let isError = false;
  if(settingData.value?.editor_settings?.blocks){
    settingData.value.editor_settings.blocks.forEach((block) => {
      if(block?.id == 'information'){
        if(!block.contents.date || block.contents.date == ''){
          wholeError.value = 'ウェブ招待状の編集が完了していません<br>編集を完了してから再度公開設定を行ってください';
          isShowModalPublish.value = false;
          isShowModalUnpublish.value = false;
          isError = true;
          return false;
        }
      }
    });
  }
  if(isError){ return false }

  showLoading();

  let updateData = {
    id: queryId,
    is_public: settingData.value.is_public,
    public_url: settingData.value.public_url.toLowerCase(),
    is_password: settingData.value.is_password,
    guest_list_id: getData.value.guest_list.id,
    m_web_invitation_id: getData.value.m_web_invitation.id
  }
  if(isInitialSetting.value){
    updateData.is_public = true;
  }
  if(isChangePassword.value){
    updateData.password = settingData.value.password;
  }

  let isSuccess = await update({
    input: updateData
  });

  if (! isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value) error.value = errors.value?.input?.public_url[0];
    hideLoading();
    isShowModalPublish.value = false;
    isShowModalUnpublish.value = false;
    return false;
  }

  hideLoading();
  isChange.value = false;

  // ToastMessage
  addToastMessage({ message: '公開設定を変更しました ' });
  router.push({ path: '/mypage/webinvitation/' });
};


// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "ゲストリスト",
    link: '/mypage/guest-list',
  },
  {
    title: "ゲストリストを追加"
  }
];

const paramPartsHidden = {
  ShowFooterTop: true,
  FixedFooter: true,
  spBreadcrumbs: true,
  spLogo: true
}
</script>

<style lang="scss" scoped>
@include sp {
  :deep(.container) {
    margin-top: 0;
  }
  :deep(.contents-title) {
    display: none;
  }
  :deep(.breadcrumbs) {
    display: none;
  }
  :deep(.l-column1) {
    padding-top: 30px;
  }
}
.wrap {
  padding-top: 60px;
}
.webInvitation{
  &_settings{
    max-width: 350px;
    padding: 0 20px;
    margin: 0 auto 48px;
    dl{
      margin-bottom: 14px;
      border-bottom: 1px solid rgba(33, 33, 33, 0.08);
      dt{
        color: #999;
        font-size: 12px;
      }
      dd{
        color: #333;
        font-size: 14px;
        margin: 3px 0 16px;
      }
    }
    .alert{
      color: #FF1B1B;
      font-size: 14px;
      text-align: center;
      border: none;
      padding: 0;
    }
  }
}
.password{
  display: flex;
  align-items: center;
  min-height: 47px;
  &_switch{
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
  }
  &_description{
    font-size: 12px;
    line-height: 1.45;
    color: #333;
    background: #F4F4F4;
    margin-top: 12px;
    padding: 12px;
    border-radius: 4px;
  }
}

.status{
  display: flex;
  align-items: center;
  &_active{
    width: 76px;
  }
}
.status_change{
  margin-top: 6px;
  a{
    display: inline-block;
    box-sizing: border-box;
    position: relative;
    border: 1px solid currentColor;
    padding: 9px 20px 9px 42px;
    line-height: 1;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    &::before{
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      position: absolute;
      top: 1px;
      bottom: 0;
      left: 20px;
      margin: auto;
    }
    &.link-to-close{
      &::before{
        background-image: url(@/assets/images/icon-eye-open.svg);
      }
    }
    &.link-to-public{
      &::before{
        background-image: url(@/assets/images/icon-eye-close.svg);
      }
    }
  }
}
.exclaim{
  font-size: 12px;
  line-height: 1.45;
  color: #666;
  margin-top: 10px;
  margin-bottom: 10px;
  padding-left: 1em;
  text-indent: -1em;
}

.icn-password{
  display: inline-block;
  width: 18px;
  height: 18px;
  margin: 0 4px 0 4px;
  background-image: url(@/assets/images/icon-lock.svg);
}
.url_input{
  display: flex;
  align-items: center;
  min-height: 44px;
}
.url_text{
  margin-right: 10px;
}
</style>