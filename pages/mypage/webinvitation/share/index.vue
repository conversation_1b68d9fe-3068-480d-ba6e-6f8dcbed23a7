<template>
  <div class="webInvitation">
    <Main
      :partsHidden="paramPartsHidden"
      :breadcrumbs="breadcrumbs"
      title="おふたりの招待状を送る"
      :backlink="backlink"
    >
      <template #main>
        <div class="share">
          <p>招待状URL入りの招待文を作成して <br class="sp_only">あなたの送り方でゲストに送りましょう </p>
          <div class="share_wrap">
            <MessageEditor
              max-length="500"
              :message="messageContent"
              :textAlign="textAlign"
              :exampleType="ExampleType1Enum.MessageToGuests"
              :isShowHeader="true"
              :isShowAlgin="false"
              :isShowFilterSettings="false"
              @input="onChangeMessage($event)"
              @align="textAlign = $event"
            ></MessageEditor>
            <p class="exclaim">※メッセージの最後に【招待状URL】と【合言葉】が自動で挿入されます</p>
            <div class="share_button">
              <div class="share_button_wrap1">
                <ButtonMainColor class="icon-mail" @click="onClickShareMail">メールで送る</ButtonMainColor>
                <ButtonMainColor class="icon-line" @click="onClickShareLine">LINEで送る</ButtonMainColor>
              </div>
              <div class="share_button_wrap2">
                <a href="#" @click="onClickQRCode()" class="link icn-qr">QRコード作成</a>
                <a href="#" @click="onClickCopyUrl()" class="link icn-copy">URLコピー</a>
                <a href="#" @click="onClickPreview()" class="link icn-external">招待状を確認</a>
              </div>
            </div>
          </div>
        </div>
        <ModalQRCode
          v-if="isShowQRCodeModal"
          @close="isShowQRCodeModal = false"
          :url="getWebInvitationUrl(publicUrl)"
        ></ModalQRCode>
      </template>
    </Main>
  </div>
</template>

<script lang="ts" setup>
import { useToastMessageState } from '@/composables/useToastMessageState';
const { addToastMessage } = useToastMessageState();
const router = useRouter();
const route = useRoute();

// 情報の取得
const settingData = ref({});
const publicUrl = ref('');
const password = ref(null);
const isLoadQRCodeFlag = ref(false);

// クエリパラメーターがある場合
let queryId:string = route?.query.id;
let isUpdateMode = ref(false);
let getData: any;
if (queryId) {
  const { webInvitationData, refetch } = useGetOneWebInvitation(queryId);
  isUpdateMode.value = true;
  getData = await webInvitationData;
}
watch(getData, (newVal) => {
  if(newVal){
    settingData.value = newVal;
    if(newVal?.is_password){
      password.value = newVal.password;
    }
    publicUrl.value = `https://favori.wedding/wi/${newVal?.public_url}`;
    isLoadQRCodeFlag.value = true;
  }
}, {
  deep: true,
  immediate: true
})

const textAlign = ref('left');
const messageContent = ref('');

const onChangeMessage = (value:string) => {
  messageContent.value = value;
  sessionStorage.setItem(queryId, value);
};
onMounted(() => {
  const savedMessageContent = sessionStorage.getItem(queryId);
  if (savedMessageContent) {
    messageContent.value = savedMessageContent;
  }
});

const onGetPasswordText = () => {
  let text = '';
  if(password.value){
    text = `あいことば：${password.value}`;
  }
  return text;
};

const convertLineBreaks = (text: string) => {
  if(!text || text == ''){ return '' }
  return text.replace(/\n/g, '%0D%0A');
};

const onClickShareMail = () => {
  let address = '';
  let ccAddress = '';
  let subject = '結婚式のご案内';
  let body = convertLineBreaks(messageContent.value) + '%0D%0A%0D%0A' + getWebInvitationUrl(publicUrl.value) + '%0D%0A' + onGetPasswordText();

  location.href = 'mailto:' + address + '?cc=' + ccAddress + '&subject=' + subject + '&body=' + body;
};

const isShowQRCodeModal = ref(false);
const onClickQRCode = async() => {
  if(!isLoadQRCodeFlag.value){
    await new Promise<void>((resolve) => {
      watch(isLoadQRCodeFlag, (newValue) => {
        if (newValue == true) {
          resolve();
        }
      });
    });
  }
  isShowQRCodeModal.value = true;
};

const onClickShareLine = () => {
  let lineMessage = messageContent.value && messageContent.value != ''  ? messageContent.value + '\n\n' + getWebInvitationUrl(publicUrl.value) + '\n' + onGetPasswordText() : getWebInvitationUrl(publicUrl.value) + '\n' + onGetPasswordText();
  let lineShareUrl = `https://line.me/R/share?text=${encodeURIComponent(lineMessage)}`;
  if (! navigator.userAgent.match(/(iPhone|iPad|iPod|Android)/i)) {
    // PCの場合はLINE公式のシェアURLを使用
    lineShareUrl = `https://social-plugins.line.me/lineit/share?text=${encodeURIComponent(lineMessage)}`;
  }
  window.open(lineShareUrl, '_blank');
};

const onClickCopyUrl = async () => {
  await navigator.clipboard.writeText(getWebInvitationUrl(publicUrl.value));
  addToastMessage({message: 'URLをコピーしました '});
  return false
};

const onClickPreview = async () => {
  window.open(getWebInvitationUrl(publicUrl.value), '_blank');
  return false
};

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "ゲストリスト",
    link: '/mypage/guest-list',
  },
  {
    title: "ゲストリストを追加"
  }
];

const paramPartsHidden = {
  ShowFooterTop: true,
  FixedFooter: true,
  spBreadcrumbs: true,
  spLogo: true
}

const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('/mypage/webinvitation');
});
</script>

<style lang="scss" scoped>
:deep(.container) {
  textarea{
    min-height: 362px;
    @include sp {
      min-height: 255px;
    }
  }
  .l-column1{
    padding-top: 0;
    max-width: 100%;
  }
  .button--main{
    &.icon-mail{
      color: #FFF;
      background: $color-accent;
      span{
        display: inline-flex;
        align-items: center;
        &::before{
          content: '';
          display: inline-block;
          width: 24px;
          height: 24px;
          margin-right: 2px;
          background-color: #FFF;
          mask-image: url('@/assets/images/icon-mail.svg');
          mask-repeat: no-repeat;
          mask-position: center;
          mask-size: 22px;
          vertical-align: middle;
        }
      }
    }
    &.icon-line{
      color: #FFF;
      background: #06C755;
      span{
        display: inline-flex;
        align-items: center;
        &::before{
          content: '';
          display: inline-block;
          width: 24px;
          height: 24px;
          margin-right: 2px;
          background-image: url('@/assets/images/icon-line.png');
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          vertical-align: middle;
        }
      }
    }
  }
}
@include sp {
  :deep(.container) {
    margin-top: 0;
  }
  :deep(.contents-title) {
    display: none;
  }
  :deep(.breadcrumbs) {
    display: none;
  }
}
.share{
  padding: 22px 16px;
  p{
    color: $color-blacktext2;
    margin-bottom: 24px;
  }
  &_wrap{
    max-width: 640px;
    width: 100%;
    margin: 0 auto;
  }
}

.flex{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.button_wrap{
  max-width: 345px;
  width: 100%;
}
.reset{
  color: $color-grayborder;
  font-size: 14px;
  line-height: 1;
  cursor: pointer;
  &:hover{
    text-decoration: underline;
  }
}
.layout_editor{
  margin-bottom: 20px;
}

.share_button{
  margin-top: 20px;
}
.share_button_wrap1{
  max-width: 525px;
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 38px;
  gap: 0 8px;
  margin: 0 auto 24px;
  &:deep(){
    a{
      padding: 7px 10px;
    }
  }
}
.share_button_wrap2{
  display: flex;
  justify-content: center;
  align-items: center;
}
.link{
  display: inline-flex;
  align-items: center;
  font-size: 12px;
  text-decoration: none;
  color: #49454F;
  margin: 0 15px;
  @include sp {
    margin: 0 12px;
  }
  &:hover{
    text-decoration: underline;
  }
  &.icn-qr{
    &::before{
      content: '';
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-right: 3px;
      background-color: #1C1B1F;
      mask-image: url('@/assets/images/icon-expand.svg');
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: 16px;
      vertical-align: middle;
    }
  }
  &.icn-copy{
    &::before{
      content: '';
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-right: 3px;
      background-color: #1C1B1F;
      mask-image: url('@/assets/images/icon-copy.svg');
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: 18px;
      vertical-align: middle;
    }
  }
  &.icn-external{
    &::before{
      content: '';
      display: inline-block;
      width: 18px;
      height: 18px;
      margin-right: 3px;
      background-color: #1C1B1F;
      mask-image: url('@/assets/images/icon-external.svg');
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: 18px;
      vertical-align: middle;
    }
  }
}
.share .exclaim{
  font-size: 13px;
  line-height: 1.5;
  color: #FF1B1B;
  text-align: center;
  margin-top: 8px;
  margin-bottom: 24px;
  padding-left: 1em;
  text-indent: -1em;
}
</style>