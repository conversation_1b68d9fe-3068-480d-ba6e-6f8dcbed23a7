<template>
  <div class="guest-list is-spbreadcrumbsoff">
    <Main :partsHidden="paramPartsHidden" :breadcrumbs="breadcrumbs" title="ゲストリストを追加">
      <template #main>
        <Loading v-if="isLoading"></Loading>
        <div v-else class="wrapAddGuestList">
          <div v-if="error" class="input-error" style="margin-bottom: 20px;">{{ error }}</div>
          <form class="searchform">
            <InputText title="新規ゲストリスト名" :required="true" size="full" :value="String(input.name)"
              :error="getValidationMessage(v$.name)" @input="input.name = $event.target.value" />
          </form>
          <ButtonMainColor @click="onClickSave()">追加する</ButtonMainColor>
        </div>
      </template>

    </Main>
  </div>
</template>

<script lang="ts" setup>
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { CreateGuestListInput } from '@/composables/generated';
import type { GraphQLValidationErrors } from "@/utils/graphql";

const router = useRouter();

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "マイページ",
    link: '/mypage/',
  },
  {
    title: "ゲストリスト",
    link: '/mypage/guest-list',
  },
  {
    title: "ゲストリストを追加"
  }
];

// 入力項目
const input = ref({
  // TODO: ここは別のAPIから取得する
  member_id: 1,
  name: ''
} as CreateGuestListInput)

// 全体エラー
const error = ref('')

const rules = computed(() => {
  return {
    name: {
      required: helpers.withMessage(validationMessage.required('ゲストリスト名'), required)
    },
  };
});

// サーバサイドエラー
const $externalResults = ref({} as GraphQLValidationErrors)

// バリデーション
const v$ = useVuelidate(rules, input, { $externalResults });

/**
 * 登録
 */
// 登録API
const { create, errors } = useCreateGuestList();

// 更新中のLoading
const isLoading = ref(false);

// 保存ボタンクリック
const onClickSave = async () => {
  // 全体エラーをリセット
  error.value = '';
  // サーバサイドメッセージをリセット
  v$.value.$clearExternalResults();
  // バリデーション実行
  await v$.value.$validate();
  if (v$.value.$error) {
    error.value = APPLICATION_MESSAGES.VALIDATION_ERROR;
    return false;
  }

  isLoading.value = true;
  const isSuccess = await create(input.value);
  isLoading.value = false;
  // エラーの場合
  if (!isSuccess) {
    if (errors.value) $externalResults.value = errors.value;
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    return false;
  }

  // フラッシュメッセージ
  // emits('created', 'ゲストリスト「'+input.value.name+'」を追加しました ')
  router.push({ path: `/mypage/guest-list` })
};


const paramPartsHidden = {
  ShowFooterTop: true,
  FixedFooter: true,
  spBreadcrumbs: true,
  spLogo: true
}

</script>

<style lang="scss" scoped>
.guest-list {
  :deep(.contents) {
    padding-bottom: 100px;
  }

  :deep(.l-column1) {
    height: 100%;
    padding-top: 0;
    padding-bottom: 0;

  }

  .l-column1-inner {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 1050px;
    margin: 0 auto;
  }

  .wrapAddGuestList {
    padding: 24px 16px;

    p {
      margin-bottom: 5px;
      font-size: 12px;
      line-height: 145%;
      letter-spacing: 0.02em;
      color: $color-blackLight;
    }

    .button--main {
      width: 350px;
      max-width: 100%;
      margin-top: 23px;
    }
  }
}

@include sp {
  h1 {
    display: none;
  }
}
</style>