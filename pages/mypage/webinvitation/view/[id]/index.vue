<template>
  <div class="webInvitationView" v-if="form">
    <Loading v-if="isLoading"></Loading>
    <ViewFormWrap v-else :data="form.blocks">
      <div class="blocks">
        <div class="block" v-for="(block, index) in form.blocks" :key="index">
          <component
            :is="onSetComponent(block.id)"
            :data="block.contents"
            :informationData="onFilterBlock('information')"
            :visible="block.visible"
            :isPreview="true"
            :webInvitationData="{
              event_list: events?.eventLists,
              editor_settings: form
            }"
            :isLockSpecifiedPosition="isLockSpecifiedPosition"
            :maxMainVisualImages="maxMainVisualImages"
            :getAspectRatioForPosition="getAspectRatioForPosition"
          ></component>
        </div>
      </div>
    </ViewFormWrap>
  </div>
</template>

<script lang="ts" setup>
import { markRaw } from 'vue'
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import { CreateGuestListInput } from 'composables/generated';
import { GraphQLValidationErrors } from "@/utils/graphql";

const router = useRouter();
const route = useRoute();
const form = ref();
const isLoading = ref(true);

// デザイン設定から画像の縦横比情報を取得（プレビュー用）
const designAspectRatio = ref(null);
const isLockSpecifiedPosition = computed(() => {
  return designAspectRatio.value?.mainVisual?.isLockSpecifiedPosition === true;
});
const maxMainVisualImages = computed(() => {
  if (isLockSpecifiedPosition.value && designAspectRatio.value?.mainVisual?.specifiedImages) {
    return designAspectRatio.value.mainVisual.specifiedImages.length;
  }
  return 3;
});
const getAspectRatioForPosition = (position: number) => {
  if (isLockSpecifiedPosition.value && designAspectRatio.value?.mainVisual?.specifiedImages?.[position]) {
    return designAspectRatio.value.mainVisual.specifiedImages[position];
  }
  return { width: 9, height: 16 };
};

const loadFormData = ref();
onMounted(() => {
  let isPreview = route?.query.prev ? true : false;
  if(isPreview){
    const previewData = sessionStorage.getItem('webInvitationFormData');
    loadFormData.value = JSON.parse(previewData);
  }else{
    // サンプルデータ
    loadFormData.value = {
      blocks: [
        {
          name: 'メインビジュアル',
          id: 'mainVisual',
          contents: {
            selectVisual: 'images', // images or movie
            images: [
              '/images/webinvitation/theme_wa/mainVisual_bg.jpg'
            ],
            movie: '/src/',
            groomName: '新郎の名前',
            brideName: '新婦の名前'
          }
        },
        {
          name: 'カウントダウン',
          id: 'countDown',
          visible: true,
          contents: {}
        },
        {
          name: '挨拶・メッセージ',
          id: 'message',
          visible: true,
          contents: {
            isShowVisual: true,
            selectVisual: 'images',
            images: [
              '/images/webinvitation/theme_wa/gallery_image_1.png',
              '/images/webinvitation/theme_wa/gallery_image_2.png',
              '/images/webinvitation/theme_wa/gallery_image_3.png'
            ],
            movie: '/src/',
            textAlign: 'center',
            message: '謹啓\n皆様におかれましては\nご清祥のこととお慶び申し上げます\nこのたび 私たちは結婚式を\n挙げることになりました\nつきましては 日頃お世話になっている皆様に\nお集まりいただきささやかな披露宴を\n催したいと存じます\nご多用中 誠に恐縮ではございますが\nご来臨の栄を賜りたく\n謹んでご案内申し上げます\n\n謹白'
          }
        },
        {
          name: '新郎・新婦プロフィール',
          id: 'profile',
          visible: true,
          contents: [
            {
              isShowVisual: true,
              selectVisual: 'images', // images or movie
              images: [
                '/images/webinvitation/theme_wa/profile_image_1.png'
              ],
              movie: '/src/',
              name: '岩井 俊平',
              isShowRole: true,
              role: '新郎',
              textAlign: 'center',
              message: 'これからは皆さまや両家の親をお手本に 明るく笑顔あふれる家庭をつくっていきたいと思います\n未熟なふたりではございますが　今後ともご指導ご鞭撻のほど　よろしくお願いします'
            },
            {
              isShowVisual: true,
              selectVisual: 'images', // images or movie
              images: [
                '/images/webinvitation/theme_wa/profile_image_2.png'
              ],
              movie: '/src/',
              name: '福永 このみ',
              isShowRole: true,
              role: '新婦',
              textAlign: 'center',
              message: 'これからは皆さまや両家の親をお手本に 明るく笑顔あふれる家庭をつくっていきたいと思います\n未熟なふたりではございますが　今後ともご指導ご鞭撻のほど　よろしくお願いします'
            },
            {
              isShowVisual: true,
              selectVisual: 'images', // images or movie
              images: [
                '/images/webinvitation/theme_wa/profile_image_3.png'
              ],
              movie: '/src/',
              name: '福永 里香',
              isShowRole: true,
              role: '長女',
              textAlign: 'center',
              message: 'これからは皆さまや両家の親をお手本に 明るく笑顔あふれる家庭をつくっていきたいと思います\n未熟なふたりではございますが　今後ともご指導ご鞭撻のほど　よろしくお願いします'
            }
          ]
        },
        {
          name: '写真ギャラリー',
          id: 'gallery',
          visible: true,
          contents: [
            '/images/webinvitation/theme_wa/gallery_image_1.png',
            '/images/webinvitation/theme_wa/gallery_image_2.png',
            '/images/webinvitation/theme_wa/gallery_image_3.png'
          ]
        },
        {
          name: 'パーティー情報',
          id: 'information',
          visible: true,
          contents: {
            date: '2023/11/22',
            type: 1,
            ceremonyPlans: [
              {
                isShowPlan: true,
                hour: '12',
                minute: '00'
              },
              {
                isShowPlan: true,
                hour: '14',
                minute: '00'
              },
              {
                isShowPlan: true,
                hour: '16',
                minute: '00'
              }
            ],
            ceremonyVenue: '神戸プラザホテル',
            ceremonyZip: '123-4567',
            ceremonyAddress: '兵庫県神戸市中央区元町通１丁目１３−１２',
            isShowCeremonyMaps: true,
            ceremonyTel: '01-1234-5678',
            ceremonyUrl: 'https://www.kobeplaza.com/',
            ceremonyFeeOption: 1,
            ceremonyFeeMale: '',
            ceremonyFeeFemale: '',
            receptionPlans: [
              {
                isShowPlan: true,
                hour: '12',
                minute: '00'
              },
              {
                isShowPlan: true,
                hour: '14',
                minute: '00'
              },
              {
                isShowPlan: true,
                hour: '16',
                minute: '00'
              }
            ],
            receptionVenue: '披露宴会場名',
            receptionZip: '披露宴会場郵便番号',
            receptionAddress: '披露宴会場住所',
            isShowReceptionMaps: true,
            receptionTel: '披露宴会場電話番号',
            receptionUrl: '披露宴会場URL',
            receptionFeeOption: 1,
            receptionFeeMale: '',
            receptionFeeFemale: '',
            otherGroups: [
              {
                plans: [
                  {
                    isShowPlan: true,
                    hour: '0',
                    minute: '00'
                  },
                  {
                    isShowPlan: true,
                    hour: '0',
                    minute: '00'
                  },
                  {
                    isShowPlan: true,
                    hour: '0',
                    minute: '00'
                  }
                ],
                venue: '披露宴会場名',
                zip: '披露宴会場郵便番号',
                address: '披露宴会場住所',
                isShowMaps: true,
                tel: '披露宴会場電話番号',
                url: '披露宴会場URL',
                feeOption: 1,
                feeMale: '',
                feeFemale: '',
              }
            ]
          }
        },
        {
          name: 'フリー項目',
          id: 'freeField',
          visible: true,
          contents: [
            {
              title: 'フリー項目1',
              heading: '挙式ご列席のお願い',
              textAlign: 'center',
              message: '誠に恐れ入りますが<br>挙式にもご列席を賜りたく<br>当日は○時○分までに<br>〇階〇〇（来賓控室・チャペルなど）へ<br>お越しくださいますよう<br>お願い申し上げます',
              isShowVisual: true,
              selectVisual: 'images', // images or movie
              images: [
                '/src/',
                '/src/',
                '/src/'
              ],
              movie: '/src/',
            },
            {
              title: 'フリー項目2',
              heading: 'ドレスコード',
              textAlign: 'left',
              message: 'ドレスコード / スマートカジュアル<br>（参考）<br><a href="#">https://voi.0101.co.jp/voi/content/01/sp/dreni/column/detail004/</a>',
              isShowVisual: true,
              selectVisual: 'images', // images or movie
              images: [
                '/src/',
                '/src/',
                '/src/'
              ],
              movie: '/src/',
            }
          ]
        },
        {
          name: '出欠フォーム',
          id: 'guestAnswer',
          visible: true,
          contents: {
            selectList: [
              {
                title: '新郎新婦ゲスト選択',
                id: '',
                disabled: false,
                required: true,
                visible: true,
              },
              {
                title: 'お名前',
                id: '',
                disabled: false,
                required: true,
                visible: true,
              },
              {
                title: 'お名前（ふりがな）',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: 'お名前（ローマ字）',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: '関係性',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: '間柄',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: 'プロフィール写真',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: '性別',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: 'お誕生日',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: '住所',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: '電話番号',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: 'メールアドレス',
                id: '',
                disabled: false,
                required: true,
                visible: true,
              },
              {
                title: 'アレルギー項目の入力',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: 'お祝い画像・動画',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: 'お祝いメッセージ',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              },
              {
                title: '連名入力',
                id: '',
                disabled: false,
                required: false,
                visible: true,
              }
            ],
            questionnaire: [
              {
                title: 'アンケート項目1',
                heading: '見出し',
                textAlign: 'left',
                message: '',
                method: 'inputText',
                answer: [
                  '',
                  '',
                  ''
                ]
              },
              {
                title: 'アンケート項目2',
                heading: '見出し',
                textAlign: 'left',
                message: '',
                method: 'inputText',
                answer: [
                  '',
                  '',
                  ''
                ]
              }
            ],
            limit: {
              date: '',
              setting: 1,
              textAlign: 'center',
              message: '',
            },
            attendance: {
              isHideAttendance: true,
              isHideSkip: true,
              isAddFields: true,
              fields: []
            }
          }
        }
      ]
    };
  }
});

let formData = sessionStorage.getItem('webInvitationFormData')
const { events, refetch: eventListsRefetch } = useGetEventLists([JSON.parse(formData)]);

const { webInvitationData, refetch } = useGetOneWebInvitation(route.params.id);

watch(() => webInvitationData, (newVal) => {
  if(newVal.value && newVal.value?.m_web_invitation){
    const style = document.createElement('style');
    style.textContent = newVal.value.m_web_invitation.css_code;
    document.head.appendChild(style);
    
    // デザイン設定を取得
    const designSettings = newVal.value?.m_web_invitation?.m_specification_product?.product?.design_settings;
    if (designSettings) {
      try {
        designAspectRatio.value = typeof designSettings === 'string' ? JSON.parse(designSettings) : designSettings;
      } catch (error) {
        console.error('デザイン設定の解析に失敗しました:', error);
        designAspectRatio.value = null;
      }
    }
    
    form.value = loadFormData.value;
    // CSSが適用されるまで待つ
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        setTimeout(() => {
          isLoading.value = false;
        }, 500);
      });
    });
  }
}, {
  deep: true,
  immediate: true
});

const onFilterBlock = (id: String) => {
  let item = form?.value?.blocks.filter((element) => {
    return element.id == id;
  });
  if (!item) {
    return null
  }
  return item[0];
}

const state = reactive({
  mainVisual: markRaw(resolveComponent('ViewFormBlockMainVisual') as Component),
  countDown: markRaw(resolveComponent('ViewFormBlockCountDown') as Component),
  message: markRaw(resolveComponent('ViewFormBlockMessage') as Component),
  profile: markRaw(resolveComponent('ViewFormBlockProfile') as Component),
  gallery: markRaw(resolveComponent('ViewFormBlockGallery') as Component),
  information: markRaw(resolveComponent('ViewFormBlockInformation') as Component),
  gift: markRaw(resolveComponent('ViewFormBlockGift') as Component),
  freeField: markRaw(resolveComponent('ViewFormBlockFreeField') as Component),
  guestAnswer: markRaw(resolveComponent('ViewFormBlockGuestAnswer') as Component)
});

const onSetComponent = (id: String) => {
  return state[id];
}
</script>

<style lang="scss">
@import 'assets/css/webinvitation/common/style.scss';
</style>
