<template>
  <div class="webInvitationView">
    <div v-if="isStatus === 1" class="">
      <input type="text" placeholder="合言葉を入力">
      <button @click="isStatus = 2">招待状を見る</button>
    </div>
    <div v-else-if="isStatus === 2">
      <div class="blocks">
        <div class="block" v-for="(block, index) in blocks" :key="index">
          <component
            :is="block.id"
          ></component>
        </div>
      </div>
      <button @click="isStatus = 3">招待状に回答する</button>
    </div>
    <div v-else-if="isStatus === 3 || isStatus === 4">
      <div class="form" :class="[{'is-input': isStatus === 3}, {'is-confirm': isStatus === 4}]">
        <h2>フォーム</h2>
        <h3 v-if="isStatus === 3">入力フォーム</h3>
        <h3 v-if="isStatus === 4">入力確認</h3>
        <input type="text" placeholder="">
        <div class="btns" v-if="isStatus === 3">
          <button @click="isStatus = 4">確認画面へ</button>
        </div>
        <div class="btns" v-if="isStatus === 4">
          <button @click="isStatus = 5">次へ</button>
        </div>
      </div>
    </div>
    <div v-else-if="isStatus === 5">
      <h3>ご祝儀について</h3>
      <p>ご列席の皆様のご負担や当日の混雑を避けるべく会費の事前受付を可能といたしました</p>
      <p>当日会場にお持ちいただくことも可能ですが受付での会費の受け渡しが不要となりますのでよろしければ事前受付をご利用ください</p>
      <div class="btns">
        <button @click="isStatus = 6">クレジットカードで事前受付</button>
        <button @click="isStatus = 6">当日会場へ持参する</button>
      </div>
    </div>
    <div v-else-if="isStatus === 6">
      <h3>クレジットカードで事前受付</h3>
      <div class="btns">
        <button @click="isStatus = 7">回答する</button>
        <button @click="isStatus = 5">戻る</button>
      </div>
    </div>
    <div v-else-if="isStatus === 7">
      <h2>Thank You!!</h2>
      <p>入力完了しました<br>ありがとうございます</p>
      <p>ご回答ありがとうございました！<br>皆さまにお会いできることを<br>心から楽しみにしております</p>
      <div class="btns">
        <button>閉じる</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { required, helpers } from '@vuelidate/validators';
import { useVuelidate } from '@vuelidate/core';
import type { CreateGuestListInput } from '@/composables/generated';
import type { GraphQLValidationErrors } from "@/utils/graphql";

import ViewFormBlockMainVisual from "~/components/organisms/web-invitation/ViewFormBlockMainVisual.vue";
import ViewFormBlockCountDown from "~/components/organisms/web-invitation/ViewFormBlockCountDown.vue";

const router = useRouter();

// 入力項目
const blocks = ref([
  {
    id: ViewFormBlockMainVisual,
    contents: {
      img: ''
    }
  },
  {
    id: ViewFormBlockCountDown,
    contents: {
      year: '11',
      month: '11',
      date: '1'
    }
  },
])

// 更新中のLoading
const isStatus = ref(1);
</script>

<style lang="scss" scoped>
.guest-list {
  :deep(.contents) {
    padding-bottom: 100px;
  }

  :deep(.l-column1) {
    height: 100%;
    padding-top: 0;
    padding-bottom: 0;

  }

  .l-column1-inner {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 1050px;
    margin: 0 auto;
  }

  .wrapAddGuestList {
    padding: 24px 16px;

    p {
      margin-bottom: 5px;
      font-size: 12px;
      line-height: 145%;
      letter-spacing: 0.02em;
      color: $color-blackLight;
    }

    .button--main {
      width: 350px;
      max-width: 100%;
      margin-top: 23px;
    }
  }
}

@include sp {
  h1 {
    display: none;
  }
}
</style>