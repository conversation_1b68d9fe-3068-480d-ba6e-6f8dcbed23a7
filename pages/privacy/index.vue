<template>
	<Main
  :partsHidden="paramPartsHidden"
  :breadcrumbs="breadcrumbs"
  title="プライバシーポリシー"
  addClass="l-column1-nowrap"
  >
    <template #main>
      <article class="article-questions">
        <div class="article-questions_wrap">
          <div v-html="data.contents[0].rich_text"></div>
        </div>
      </article>
    </template>
  </Main>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const metaTitle = 'プライバシーポリシー｜WEB招待状 Favori（ファヴォリ）';
const metaDescription = 'WEB招待状 Favori（ファヴォリ）のプライバシーポリシーです。';
useHead({
  title: metaTitle,
  meta: [
    { hid: 'description', name: 'description', content: metaDescription },
    { hid: 'og:title', property: 'og:title', content: metaTitle },
    { hid: 'og:description', property: 'og:description', content: metaDescription },
    { hid: 'twitter:title', property: 'twitter:title', content: metaTitle },
    { hid: 'twitter:description', property: 'twitter:description', content: metaDescription },
  ],
})

const router = useRouter();
const route = useRoute();

const breadcrumbs = ref([
  {
    title: 'HOME',
    link: '/',
  },
  {
    title: 'プライバシーポリシー',
  }
] as any);
const paramPartsHidden = {
  spHeaderTitle: true
}

const searchQuery = ref({
  orders: 'publishedAt',
  filters: 'code[equals]exapmle_default_privacy_policy_1'
} as {
  orders: string;
  filters: string;
});

const microCms = new MicroCms();

const isLoading = ref(true);

// 検索結果
const { data , error, refresh} = await microCms.fetch('/content', searchQuery);
console.log(data.value);

onMounted(async () => {
  isLoading.value = false;
});
</script>

<style lang="scss" scoped>
:deep(){
  .nav-sp{
    display: none;
  }
  .l-column1{
    @include sp{
      padding-bottom: 40px;
    }
  }
}
.article-questions{
  color: #333;
  font-size: 13px;
  line-height: 1.5;
  &_wrap{
    max-width: 682px;
    width: 100%;
    padding: 0 16px;
    margin: 0 auto;
    @include pc{
      padding-top: 0;
    }
  }
  &:deep(){
    h2{
      font-size: 16px;
      font-weight: normal;
      line-height: 1;
      padding: 13px 0;
      margin: 0 0 15px;
      border-bottom: 1px solid #DCDCDC;
    }
    h3{
      font-size: 14px;
      font-weight: normal;
      line-height: 1.5;
      margin: 30px 0 15px;
      &::before{
        content: '・';
        @include sp{
          content: '';
        }
      }
    }
    p{
      line-height: 1.5;
      margin-bottom: 15px;
    }
    ol{
      margin: 0 0 15px;
      padding: 0 0 0 20px;
      list-style-type: decimal;
      li{
        list-style-type: decimal;
        & + li{
          margin-top: 1em;
        }
      }
    }
  }
}
</style>
