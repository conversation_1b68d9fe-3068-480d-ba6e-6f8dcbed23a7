<template>
  <div>
    <Loading v-if="isLoading"></Loading>
    <WebInvitationUserTop
      v-else
      :input="input"
      :webInvitationData="detailData"
      :isPreview="true"
    ></WebInvitationUserTop>
    <div class="createDesign" v-if="!isLoading && isShowCreateDesign">
      <div class="createDesign_wrap">
        <a class="createDesign_button" @click.prevent="onClickCreateDesign()">このデザインで作る</a>
      </div>
      <button class="createDesign_close" @click.prevent="onCloseCreateDesign()">
        <img src="@/assets/images/icon-close-b-thin.svg" width="24" height="24" alt="閉じる" />
      </button>
    </div>
    <Transition name="modal-slide">
      <ModalLoginRegister
        v-if="isShowModalRegister() || isShowModalLogin()"
      />
    </Transition>
  </div>
</template>

<style lang="scss" scoped>
.createDesign {
  background-color: rgba(255, 255, 255, 0.3);
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 999999;
  height: 150px;
  pointer-events: none;
  @media (width <= 560px) {
    height: auto;
  }
  &_wrap {
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 30px;
    @media (width <= 560px) {
      align-items: center;
      padding-top: 12px;
    }
  }
  &_button {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 340px;
    width: 100%;
    height: 50px;
    color: #2F587C;
    background-color: #fff;
    border: 1px solid #2F587C;
    padding: 8px;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    text-decoration: none;
    transition: all 0.35s ease-in-out;
    pointer-events: auto;
    @media (width <= 560px) {
      max-width: 245px;
      height: 38px;
      font-size: 12px;
    }
    &:hover {
      box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.15);
    }
    &:active {
      box-shadow: none;
    }
  }
  &_close {
    display: none;
    position: absolute;
    top: 7px;
    right: 7px;
    padding: 0;
    transition: all 0.35s ease-in-out;
    pointer-events: auto;
    @media (width <= 560px) {
      display: block;
    }
    &:active {
      opacity: 0.5;
    }
  }
}
:deep() {
  .modalLogin {
    z-index: 1000000 !important;
  }
}
</style>

<script lang="ts" setup>
useHead({
  meta: [
    { name: 'robots', content: 'noindex' }
  ]
})

const route = useRoute();
const router = useRouter();
const { webInvitationData, refetch } = useGetOneMSpecificationProduct(route?.params.id);
const { loginCheck } = useGetLoginCheck();
const { isShowModalRegister } = useModalRegisterState();
const { showModalLogin, isShowModalLogin } = useModalLoginState();

const input = ref({
  input: {} as CreateGuestNoValidateInput,
  guests: [] as CreateGuestNoValidateInput[],
  guest_event_answers: [] as CreateGuestEventAnswerNoValidateInput[],
  guest_survey_answers: [] as CreateGuestSurveyAnswerInput[],
});

const isLoading = ref(true);
const isShowCreateDesign = ref(true);

// 更新API
const { update, updateErrors } = useUpdateWebInvitation();

/**
 * 「このデザインで作る」右上の閉じるアイコンクリック時の処理
 */
const onCloseCreateDesign = () => {
  isShowCreateDesign.value = false;
};

/**
 * 「このデザインで作る」クリック時の処理
 */
const onClickCreateDesign = async () => {
  const webInvitationId = detailData.value?.id;
  const themeId = webInvitationData.value?.id;

  // ログインチェック
  if (!loginCheck.value?.loginCheck) {
    if (webInvitationData.value?.m_specification_products?.[0]?.product_images?.[0]?.uuid) {
      sessionStorage.setItem('webInvitationThumbnailUUID', webInvitationData.value.m_specification_products[0].product_images[0].uuid);
    }
    if (webInvitationData.value?.name) {
      sessionStorage.setItem('webInvitationThemeName', webInvitationData.value.name);
    }
    // ログインモーダルを表示（ログイン後のリダイレクト先を指定）
    const redirectUrl = `/mypage/webinvitation/create?id=${webInvitationId}&theme=${themeId}`;
    showModalLogin(redirectUrl);
    return false;
  }

  // reselectクエリがある場合の処理
  if (route?.query.reselect) {
    await update({
      input: {
        id: route?.query.reselect,
        m_web_invitation_id: webInvitationId,
        guest_list_id: route?.query.guest
      }
    });

    // セッションストレージに必要な情報を保存
    if (webInvitationData.value?.m_specification_products?.[0]?.product_images?.[0]?.uuid) {
      sessionStorage.setItem('webInvitationThumbnailUUID', webInvitationData.value.m_specification_products[0].product_images[0].uuid);
    }
    if (webInvitationData.value?.name) {
      sessionStorage.setItem('webInvitationThemeName', webInvitationData.value.name);
    }

    // エディターフォームページに遷移
    router.push({
      path: '/mypage/webinvitation/editor/form',
      query: {
        id: route?.query.reselect
      }
    });
    return false;
  }

  // セッションストレージに必要な情報を保存
  if (webInvitationData.value?.m_specification_products?.[0]?.product_images?.[0]?.uuid) {
    sessionStorage.setItem('webInvitationThumbnailUUID', webInvitationData.value.m_specification_products[0].product_images[0].uuid);
  }
  if (webInvitationData.value?.name) {
    sessionStorage.setItem('webInvitationThemeName', webInvitationData.value.name);
  }

  // 作成ページに遷移
  router.push({
    path: '/mypage/webinvitation/create',
    query: {
      id: webInvitationId,
      theme: themeId
    }
  });
  return false;
};

onMounted(async () => {
  await refetch();
  if(!webInvitationData?.value?.id){
    throw createError({
      statusCode: 404,
      message: 'お探しの招待状は見つかりませんでした',
      fatal: true,
    });
  }
  isLoading.value = false;
});

// 画像の取得
const detailData = ref({});
watch(() => webInvitationData, (newVal) => {
  if(newVal && newVal?.value && newVal?.value?.m_specification_products){
    detailData.value = newVal?.value?.m_specification_products?.[0]?.m_web_invitations;
  }
}, {
  deep: true,
  immediate: true
});
</script>
