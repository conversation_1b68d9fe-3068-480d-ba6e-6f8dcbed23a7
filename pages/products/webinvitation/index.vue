<template>
  <div class="webInvitation">
    <Main
      title="デザイン一覧"
      contentSize="lg"
      :partsHidden="paramPartsHidden"
      :breadcrumbs="breadcrumbs"
      :backlink="backlink"
    >
      <template #main>
        <div class="wrap">
          <div class="mb-30">
            <h2 class="cmn-title">デザイン選択</h2>
          </div>
          <div class="themeList">
            <client-only>
              <Loading v-if="loading" :isShowIcon="true" />
            </client-only>
            <div
              v-if="data"
              class="themeList_items"
              data-animation="slideInGroup"
            >
              <div
                class="themeList_item"
                v-for="(item, index) in data"
                :key="index"
              >
                <div class="themeList_item_image">
                  <a
                    class="themeList_item_image_link"
                    href="#"
                    @click.prevent="onClickPreview(item)"
                  >
                    <div>
                      <img class="themeList_item_image_main" :src="!isServer && slideData[index]?.m_specification_products?.[0]?.product_images?.[0]?.src ? slideData[index]?.m_specification_products?.[0]?.product_images?.[0]?.src : '/images/design_placeholder.jpg'" alt="">
                      <span><div class="preview_text">プレビュー</div></span>
                    </div>
                  </a>
                </div>
                <ButtonMainColor
                  baseColor="accent_reversal"
                  size="md"
                  @click="onClickTheme(item, index)"
                >
                  <img src="@/assets/images/icon-edit.svg" width="16" />このデザインで作る
                </ButtonMainColor>
                <p>{{ item?.name }}</p>
                <div class="tags">
                  <a
                    class="tag"
                    v-for="(tag_item, index) in item?.tags"
                    :key="index"
                  >
                    {{ tag_item?.name }}
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div v-if="Math.ceil(paginatorInfo?.total / pageFirst) > 1 && !loading && data.length">
            <WebInvitationPagination
              :current="pageNum"
              :total="paginatorInfo?.total ? Math.ceil(paginatorInfo?.total / pageFirst) : 0"
              @change="onChangePagination"
            ></WebInvitationPagination>
          </div>
        </div>
      </template>
    </Main>
  </div>
</template>

<script lang="ts" setup>
import { extractUuids, setImages } from '@/composables/useGetImageFromUUID';

const route = useRoute();
const router = useRouter();
const isServer = process.server;

const { loginCheck } = useGetLoginCheck();
const { showModalLogin } = useModalLoginState();

/**
 * ページ設定
 */
const metaTitle = 'WEB招待状デザイン一覧｜WEB招待状 Favori（ファヴォリ）';
const metaDescription = 'プロが作る高品質で豊富なデザイン。選べる多彩なスタイルのWEB招待状であなたの特別な日を演出します。紙の招待状や席次表とお揃いのデザインでコーディネートも可能。「完全無料」で簡単・安心。';
useHead({
  title: metaTitle,
  meta: [
    { hid: 'description', name: 'description', content: metaDescription },
    { hid: 'og:title', property: 'og:title', content: metaTitle },
    { hid: 'og:description', property: 'og:description', content: metaDescription },
    { hid: 'twitter:title', property: 'twitter:title', content: metaTitle },
    { hid: 'twitter:description', property: 'twitter:description', content: metaDescription },
  ],
})

// レイアウト設定
const paramPartsHidden = {
  ShowFooterTop: true,
  FixedFooter: true,
  spBreadcrumbs: true,
  spLogo: true,
  spBackTop: false
}

// パンくずリスト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "デザイン一覧"
  }
];

/**
 * 一覧情報の取得
 */
// 更新API
const { update, updateErrors } = useUpdateWebInvitation();

// APIから themeList を読み込み
const pageSort = ref([
  { column: QueryProductWebInvitationsOrderByColumn.DisplayOrder, order: SortOrder.Asc },
  { column: QueryProductWebInvitationsOrderByColumn.CreatedAt, order: SortOrder.Desc }
] as any[]);
const pageFirst = ref(80);
const pageNum = ref(route?.query?.page ? parseInt(route.query.page as string) : 1);
const activeCategory = ref();
if(route?.query?.theme){
  activeCategory.value = route?.query?.theme;
}
const { data, tagGroup, paginatorInfo, refetch, loading } = useGetManyMSpecificationProducts(pageSort, pageFirst, pageNum, activeCategory);

const isReSelect = ref(route?.query.reselect ? true : false);
const backlink = ref('' as string);
onMounted(async () => {
  backlink.value = getBacklink('', '^/mypage/webinvitation');
  await getImagesFromUUIDs(data.value);
});

const activeTheme = ref({
  product: {
    id: null,
    name: ''
  },
  sale_price: 0
});

/**
 * 画像の取得
 */
const slideData = ref(data.value);
const getImagesFromUUIDs = async (obj) => {
  let updateObj = toRaw(obj);
  let uuids = extractUuids(updateObj);
  const { getImages, refetch:imageRefetch, imageLoading } = await useGetManyImages2(uuids);
  watch(() => getImages, (images, prevImages) => {
    if (images.value && !imageLoading) {
      const updatedObjWithImages = setImages(updateObj, images.value);
      slideData.value = updatedObjWithImages;
    }
  }, {
    deep: true,
    immediate: true
  });
}
watch(data, async(newVal) => {
  if(newVal){
    slideData.value = newVal;
    await getImagesFromUUIDs(newVal);
  }
}, {
  deep: true
})

/**
 * ページネーション
 */
const onChangePagination = (value: number) => {
  const query = { ...route.query };

  if (value == 1) {
    delete query.page;
  } else {
    query.page = value.toString();
  }

  router.push({
    query: query
  });
}

// ページネーションの切り替え監視（ブラウザの戻るボタン含む）
watch(
  () => route.query.page,
  (newPage) => {
    pageNum.value = newPage ? Number(newPage) : 1;
    refetch(pageSort, pageFirst, pageNum, activeCategory, { fetchPolicy: 'no-cache' });
  }, {
  deep: true
})

/**
 * 「このデザインで作る」クリック時の処理
 */
const onClickTheme = async(data, index) => {
  activeTheme.value = data;

  if (isServer) { return false; }
  if (!loginCheck.value?.loginCheck) {
    sessionStorage.setItem('webInvitationThumbnailUUID', activeTheme.value?.m_specification_products?.[0]?.product_images?.[0]?.uuid ? activeTheme.value.m_specification_products?.[0]?.product_images?.[0]?.uuid : '');
    sessionStorage.setItem('webInvitationThemeName', activeTheme.value?.name ? activeTheme.value.name : '');
    showModalLogin('/mypage/webinvitation/create?id='+activeTheme.value?.m_specification_products?.[0]?.m_web_invitations?.id+'&theme='+activeTheme.value?.id);
    return false;
  }
  if(isReSelect.value){
    let isSuccess = await update({
      input: {
        id: route?.query.reselect,
        m_web_invitation_id: activeTheme.value?.m_specification_products?.[0]?.m_web_invitations?.id,
        guest_list_id: route?.query.guest
      }
    });

    sessionStorage.setItem('webInvitationThumbnailUUID', activeTheme.value.m_specification_products?.[0]?.product_images?.[0]?.uuid ? activeTheme.value.m_specification_products?.[0]?.product_images?.[0]?.uuid : '');
    sessionStorage.setItem('webInvitationThemeName', activeTheme.value.name ? activeTheme.value.name : '');
    router.push({
      path: '/mypage/webinvitation/editor/form',
      query: {
        id: route?.query.reselect
      }
    });
  }else{
    sessionStorage.setItem('webInvitationThumbnailUUID', activeTheme.value?.m_specification_products?.[0]?.product_images?.[0]?.uuid ? activeTheme.value.m_specification_products?.[0]?.product_images?.[0]?.uuid : '');
    sessionStorage.setItem('webInvitationThemeName', activeTheme.value?.name ? activeTheme.value.name : '');
    router.push({
      path: '/mypage/webinvitation/create',
      query: {
        id: activeTheme.value?.m_specification_products?.[0]?.m_web_invitations?.id,
        theme: activeTheme.value?.id
      }
    });
  }
  return false;
}

/**
 * 「プレビュー」クリック時の処理
 */
const onClickPreview = (data) => {
  activeTheme.value = data;
  const query = {};
  if (route?.query?.reselect) {
    query.reselect = route.query.reselect;
    query.guest = route.query.guest;
  }
  router.push({
    path: `/products/webinvitation/${activeTheme.value?.id}`,
    query: query
  });
}

/**
 * スムーススクロールを無効化
 */
const styleElement = ref<HTMLStyleElement | null>(null);
onMounted(() => {
  styleElement.value = document.createElement('style');
  styleElement.value.innerHTML = `
    html {
      scroll-behavior: auto !important;
    }
  `;
  document.head.appendChild(styleElement.value);
});
onUnmounted(() => {
  if (styleElement.value) {
    document.head.removeChild(styleElement.value);
  }
});
</script>

<style lang="scss" scoped>
@include pc {
  :deep(.l-column1) {
    padding-top: 0;
  }
}
@include sp {
  :deep(.container) {
    margin-top: 0;
  }
  :deep(.contents-title) {
    display: none;
  }
  :deep(.breadcrumbs) {
    display: none;
  }
}
.wrap {
  max-width: 920px;
  width: 100%;
  margin: 0 auto;
  padding: 16px;
}
.cmn-title{
  @include sp {
    margin: 4px 0 0;
  }
}

.unit {
  color: #333;
  font-size: 10px;
}

.separator {
  color: #333;
  font-size: 14px;
}

.bold{
  font-weight: bold;
}
.slide {
  position: relative;

  &_wrap {
    max-width: calc(100% - 148px);
    width: 100%;
    @include sp {
      margin-left: -16px;
      padding: 0 20px;
      max-width: none;
      width: calc(100% + 32px);
    }
  }

  &_nav {
    background: $color-main;
    width: 32px;
    height: 32px;
    border-radius: 16px;
    display: grid;
    place-items: center;
    position: absolute;
    transition: 0.35s ease-in-out;
    top: 0;
    bottom: 0;
    margin: auto;

    &:hover {
      box-shadow: 0 4px 4px rgba(0, 0, 0, 0.12);
    }

    &:active {
      box-shadow: none;
    }

    &::before {
      content: '';
      display: block;
      width: 6px;
      height: 12px;
      background-color: #FFF;
      mask-image: url('@/assets/images/icon-button-next.svg');
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: cover;
    }

    &.is-prev {
      left: 20px;

      &::before {
        transform: rotate(180deg);
      }
    }

    &.is-next {
      right: 20px;
    }
  }
}

.category {
  &_image {
    width: 90px;
    height: 90px;
    box-sizing: border-box;
    border: 1px solid #D8D8D8;
    margin: 0 auto 6px;
    padding: 2px;
    border-radius: 45px;
    position: relative;
    overflow: hidden;
    cursor: pointer;

    &.is-active {
      border-color: #B18A3E;
    }

    img {
      background-color: #ccc;
      width: 100%;
      height: 100%;
      border-radius: 45px;
    }
  }

  &_name {
    font-size: 12px;
    text-align: center;
  }
}

.tags {
  margin-top: 12px;
  @include sp {
    margin-top: 4px;
  }
}

.tag {
  display: inline-block;
  border: 1px solid #D8D8D8;
  border-radius: 20px;
  margin-right: 4px;
  margin-bottom: 4px;
  padding: 2px 6px;
  font-size: 12px;
  @include sp {
    padding: 4px 8px;
    font-size: 10px;
  }
}

.themeList {
  padding: 0;
  @include sp {
    padding: 0 7px;
  }
  &_items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 190px));
    gap: 32px 40px;
    margin-bottom: 80px;

    @include sp {
      gap: 32px 30px;
      grid-template-columns: repeat(auto-fit, calc(50% - 15px));
    }
  }

  &_item {
    &_image {
      width: 100%;
      background: transparent;
      position: relative;
      margin-bottom: 12px;
      padding: 0;
      position: relative;
      @include sp {
        margin-bottom: 14px;
      }
      @include pc {
        &:hover{
          span{
            opacity: 1;
          }
        }
      }
      &_main{
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.15);
        aspect-ratio: 75 / 131;
        object-fit: cover;
        position: relative;
      }
      span{
        display: inline-flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        line-height: 1;
        padding: 7px;
        position: absolute;
        bottom: 8px;
        left: 8px;
        right: 8px;
        background: rgba(244,244,244,0.6);
        border-radius: 4px;
        transition: 0.35s ease-in-out;
        @include sp {
          left: auto;
          padding: 6px;
          overflow: hidden;
          border-radius: 100%;
          .preview_text{
            display: none;
            font-size: 12px;
            line-height: 1;
          }
        }
        @include pc {
          opacity: 0;
          &:hover {
            text-decoration: underline;
          }
        }
        &::before{
          content: '';
          display: inline-block;
          width: 10px;
          height: 10px;
          margin-right: 2px;
          background: url(@/assets/images/icon-search-b.svg) no-repeat center;
          background-size: contain;
          @include sp {
            width: 16px;
            height: 16px;
            margin-right: 0;
          }
        }
      }
      &_link{
        display: block;
        position: relative;
        width: 100%;
        height: 100%;
        color: #333;
        text-align: center;
        text-decoration: none;
        aspect-ratio: 75 / 131;
      }

      .favorite {
        position: absolute;
        right: 6px;
        bottom: -10px;
        width: 34px;
        height: 34px;
        background: $color-mainbackground;
        border-radius: 17px;
        display: grid;
        place-items: center;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        transition: 0.35s ease-in-out;

        &:hover {
          box-shadow: 0 4px 4px rgba(0, 0, 0, 0.12);
        }

        &:active {
          box-shadow: none;
        }

        img {
          margin-top: 2px;
          width: 16px;
        }
      }
    }

    :deep(.button--accent_reversal){
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 6px 10px;
      font-size: 12px;
      user-select: none;
      img{
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }

    p {
      color: #9C9C9C;
      font-size: 16px;
      font-family: 'Encorpada Classic', serif;
      margin-top: 10px;
      margin-bottom: 2px;
      @include sp {
        font-size: 12px;
      }
    }
  }
}
</style>