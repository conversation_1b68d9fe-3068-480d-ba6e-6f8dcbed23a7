<template>
  <div class="page-question-detail">
    <Main
      :breadcrumbs="[
        {
          title: 'HOME',
          link: '/',
        },
        {
          title: 'よくある質問',
          link: '/question',
        },
        {
          title: questionData.question
        }
      ]"
      :title="questionData.question"
      :partsHidden="{
        spH1Title: true,
        spHeaderTitle: true
      }" 
    >
    <template #main>
    <article class="article-questions" v-if="questionData?.id">
      <section class="section-question-detail mb-40">
        <div v-html="questionData.detail"></div>
      </section>
      <div class="contact-box">
        <p>お困りごとが解決しなかった場合は <br class="sp_only">下記よりお問合せください </p>
        <div class="btn-wrap">
          <NuxtLink href="/contact" class="btn btn-primary-outline">お問い合わせフォーム</NuxtLink>
        </div>
      </div>
      <hr class="hr-full">
      <section class="section-search-results">
        <div v-if="questionData?.question_id.length > 0">
          <h2 class="cmn-title">関連する質問</h2>
          <ListFaqDetail
              v-for="(content , index) in questionData?.question_id"
              :key="index"
              :question="content?.question"
              :answer="content?.summary"
              :link="'/question/'+content?.id"
          ></ListFaqDetail>
        </div>
        <div class="btn-wrap">
          <NuxtLink href="/question" class="btn btn-primary-outline"><i class="icn-left material-symbols-outlined">help</i> よくある質問を詳しく見る</NuxtLink>
        </div>
      </section>
    </article> 
    </template>
    </Main>
  </div>
</template>
  
<script lang="ts" setup>
import { ref, watchEffect, computed } from 'vue';
import { useHead, useRouter, useRoute } from '#app';
const route = useRoute();
const microCms = new MicroCms();

// 検索結果
const { data: questionData } = await microCms.fetch('/question/'+route.params.id);


const router = useRouter();

const metaTitle = questionData?.value?.question+'｜よくある質問｜WEB招待状 Favori（ファヴォリ）';
const metaDescription = questionData?.value?.summary.replace(/[\n\r]/g, '');
useHead({
  title: metaTitle,
  meta: [
    { hid: 'description', name: 'description', content: metaDescription },
    { hid: 'og:title', property: 'og:title', content: metaTitle },
    { hid: 'og:description', property: 'og:description', content: metaDescription },
    { hid: 'twitter:title', property: 'twitter:title', content: metaTitle },
    { hid: 'twitter:description', property: 'twitter:description', content: metaDescription },
  ],
})

</script>

<style lang="scss" scoped>
.article-questions {
  padding-top: 60px;
}
</style>