<template>
    <Main
      :breadcrumbs="breadcrumbs"
      :title="title"
      :partsHidden="{
        spH1Title: true,
        spHeaderTitle: false,
      }" 
    >
    <template #main>
      <article class="article-questions">
        <section class="section-search-form">
          <form @submit.prevent="onClickSearch">
            <InputText
              size="full"
              placeholder="例：返信"
              :value="searchKeyword"
              @input="searchKeyword = $event.target.value"
            />
            <button type="submit" class="btn btn-secondary"><i class="icn-left material-icons">search</i> 検索</button>
          </form>
          <div class="search-keywords">
            <p>よく検索されるキーワード：</p>
            <ul class="links">
              <li v-for="(keyword, index) in Keywords?.contents" :key="index">
                <a @click="onClickSearchCategory(keyword.id, true)">
                  {{ "#"+keyword.name }}
                </a>
              </li>
            </ul>
          </div>
          <div v-if="searchCategoryVisible" class="search-category">
            <p>質問カテゴリ</p>
            <ul class="links">
              <li v-for="(category, index) in Categories?.contents" :key="index">
                <a :href="'#' + category.name" class="categories">
                  {{ category.name }}
                </a>
              </li>
            </ul>
          </div>
        </section>
        <hr class="hr-full">
        <Loading v-if="isLoading" class="mt-20"></Loading>
        <section 
            v-else-if="Object.keys(questionsByCategory).length > 0"
            v-for="(contents, contentName) in questionsByCategory"
            :key="contentName"
            class="section-search-results"
          >
          <h2 v-if="!isSearchKeyword" class="cmn-title">
            <span class="title-left" :id="contentName">{{ isHash ? "#" + contentName : contentName }}</span>
            <span class="title-right" @click="scrollToTop">質問を探す ↑</span>
          </h2>
          <ListFaqDetail
              v-for="(content , index) in contents"
              :key="index"
              :question="content.question"
              :answer="content.summary"
              :link="'/question/'+content.id"
          ></ListFaqDetail>
        </section>
        <section v-else class="section-search-results">
          <div class="mt-60">
            <p class="cmn-aligncenter">該当する検索結果がありませんでした </p>
          </div>
        </section>
        
      </article> 
    </template>
  </Main>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const metaTitle = 'よくある質問｜WEB招待状 Favori（ファヴォリ）';
// const metaDescription = '';
useHead({
  title: metaTitle,
  meta: [
    // { hid: 'description', name: 'description', content: metaDescription },
    { hid: 'og:title', property: 'og:title', content: metaTitle },
    // { hid: 'og:description', property: 'og:description', content: metaDescription },
    { hid: 'twitter:title', property: 'twitter:title', content: metaTitle },
    // { hid: 'twitter:description', property: 'twitter:description', content: metaDescription },
  ],
})

const router = useRouter();
const route = useRoute();

const breadcrumbs = ref([
  {
    title: 'HOME',
    link: '/',
  },
  {
    title: 'よくある質問',
  }
] as any);
const isLoading = ref(false);

const title = ref('よくある質問' as string);

const microCms = new MicroCms();
const searchKeyword = ref('');
const categoriesData = ref([]);
const questionsByCategory = ref({});
const isHash = ref(false);
const isSearchKeyword = ref(false);
const searchCategoryVisible = ref(true);

//よく検索されるキーワード
const { data: Keywords , error: errorKeywords, refresh: refreshKeywords } = await microCms.fetch('/category', {
  filters: "type[contains]よく検索されるキーワード",
  orders: "system:default"
});

//質問カテゴリ
const { data: Categories , error: errorCategories, refresh: refreshCategories } = await microCms.fetch('/category', {
  filters: "type[contains]質問カテゴリ",
  orders: "system:default"
});


// カテゴリ別データを取得
const fetchQuestionsByCategory = async () => {

  // 全ての質問を一度に取得
  const { data: questions } = await microCms.fetch('/question', {
    filters: `category_id[exists]`,
    limit: 100,
    orders: "system:default"
  });
  

  // カテゴリと質問をマッピング
  const categoryMap = {};
  Categories?.value?.contents.forEach((category) => {
    categoryMap[category.id] = category.name;
    questionsByCategory.value[category.name] = [];
  });
  
  questions?.value?.contents.forEach((question) => {
    question.category_id.forEach((category) => {
      const categoryName = categoryMap[category.id];
      if (categoryName) {
        questionsByCategory.value[categoryName].push(question);
      }
    });
  });

  // もしカテゴリに質問がない場合は、そのカテゴリを削除
  for (const categoryName in questionsByCategory.value) {
    if (questionsByCategory.value[categoryName].length === 0) {
      delete questionsByCategory.value[categoryName];
    }
  }

};

onMounted(async () => {
  isLoading.value = true;
  await refreshKeywords();
  await refreshCategories();
  if(route.query.q){
    searchKeyword.value = route.query.q;
    setTimeout(async () => {
      await onClickSearch();
    }, 100);
  }else{
    await fetchQuestionsByCategory();
    isLoading.value = false;
  }

  categoriesData.value = [
    ...(Keywords?.value?.contents || []),
    ...(Categories?.value?.contents || [])
  ];
});

// 検索ボタンが押されたときの処理
const onClickSearch = async () => {
  isLoading.value = true;
  isHash.value = false;
  questionsByCategory.value = {};

  if (searchKeyword.value) {
    isSearchKeyword.value = true;
    searchCategoryVisible.value = false;
    router.push({ query: {
      q: searchKeyword.value,
      limit:  100,
      orders: "system:default"
    } });
    const { data: questions } = await microCms.fetch('/question', {
      q: searchKeyword.value,
      limit: 100,
      orders: "system:default"
    });
    if(questions.value.totalCount != 0){
      questionsByCategory.value[searchKeyword.value] = questions.value.contents;
    }
    title.value = '“'+searchKeyword.value+'”の検索結果 ('+questions.value?.totalCount+'件)';
    breadcrumbs.value[1] = { title: 'よくある質問', link: '/question' };
    breadcrumbs.value[2] = { title: '検索' };
  } else {
    isSearchKeyword.value = false;
    searchCategoryVisible.value = true;
    title.value = 'よくある質問';
    breadcrumbs.value = [{
      title: 'HOME',
      link: '/',
    },
    {
      title: 'よくある質問',
    }];
    router.push({ query: {} });
    await fetchQuestionsByCategory();
  }

  isLoading.value = false;
  return false;
};


// タグをクリックしてタグに紐づく質問を取得
const onClickSearchCategory = async (categoryId:string, hash:boolean) => {
  isLoading.value = true;
  isHash.value = hash;
  isSearchKeyword.value = false;
  searchCategoryVisible.value = false;
  searchKeyword.value = "";

  router.push({ query: {
    filters: `category_id[contains]${categoryId}`,
    limit:  100,
    orders: "system:default"
  } });
  const category = categoriesData.value.find(cat => cat.id === categoryId);
  questionsByCategory.value = {};

  const { data: questions } = await microCms.fetch('/question', {
    filters: `category_id[contains]${categoryId}`,
    limit: 100,
    orders: "system:default"
  });
  if(questions.value.totalCount != 0){
    questionsByCategory.value[category.name] = questions.value.contents;
  }
  title.value = 'よくある質問';
    breadcrumbs.value = [{
      title: 'HOME',
      link: '/',
    },
    {
      title: 'よくある質問',
      link: '/question',
    }];
  isLoading.value = false;
};

const scrollToTop = () => {
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

onBeforeRouteUpdate(async (to, from, next) => {
  if (Object.keys(from.query).length > 0 && Object.keys(to.query).length === 0 && to.path === '/question') {
    isLoading.value = true;
    searchKeyword.value = ""
    isHash.value = false;
    isSearchKeyword.value = false;
    searchCategoryVisible.value = true;
    questionsByCategory.value = {};
    title.value = 'よくある質問';
    breadcrumbs.value = [{
      title: 'HOME',
      link: '/',
    },
    {
      title: 'よくある質問',
    }];
    await fetchQuestionsByCategory();
    isLoading.value = false;
  }
  next();
});
</script>

<style lang="scss" scoped>

@include sp {
  :deep(.sp_only) { display: block!important;}
  :deep(.pc_only) { display: block!important;}
  :deep([data-sp-hidden="true"]) { display: block!important;}
  :deep(.article-questions .section-search-form form .btn) {
    width: 96px;
  }
  :deep(.article-questions .section-search-results .cmn-title){
    padding-right: 16px;
  }
  :deep(.header-inner .navi h1[data-v-d406c943]){
    display: none;
  }
}

:deep(.article-questions .section-search-form .search-keywords){
  padding: 19px 0px;
  background-color: white;
}
.search-category {
  border-radius: 4px;
  background: var(--Gray_light, #F4F4F4);
  padding: 19px 24px;
  .links {
    li {
      display: inline-block;
      margin-right: 6px;
      margin-bottom:6px;
      a {
        border: 1px solid var(--Gray, #D9D9D9);
        background: #FFF;
        display: flex;
        padding: 10px 12px;
        font-size: 12px;
        font-weight: 400;
        cursor: pointer;
        transition: 0.35s ease;
      }
    }
  }
}

.section-search-results {
  position: relative;
  padding-bottom: 20px;

  &::after {
    content: '';
    display: block;
    width: 100%;
    height: 8px;
    background-color: #F4F4F4;
    position: absolute;
    bottom: 0;
    left: 0;
  }
  &:last-of-type::after {
    content: none;
  }
}

.cmn-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.title-right {
  font-size: 14px;
  color: #49454F;
  cursor: pointer;
}
.categories {
  text-decoration: none;
  color: black;
}
.categories:hover {
  color: black;
}

</style>