<template>
  <Main
    :partsHidden="paramPartsHidden" 
    >
    <template #main>
      <Loading v-if="isLoading" :fullscreen="true"></Loading>
      <div v-else class="pageRegister">
        <ModalRegisterContentsEmailStep2
          v-if="page == 'emailStep2'"
          :errors="errors"
          :input="inputStep2"
          :isBackHide="true"
          @change="inputStep2 = $event"
          @next="page = 'emailStep3'"
        ></ModalRegisterContentsEmailStep2>
        <ModalRegisterContentsEmailStep3
          v-else-if="page == 'emailStep3'"
          :errors="errors"
          :input="inputStep3"
          @change="inputStep3 = $event"
          @back="page = 'emailStep2'"
          @next="page = 'emailConf'"
        ></ModalRegisterContentsEmailStep3>
        <ModalRegisterContentsEmailConf
          v-else-if="page == 'emailConf'"
          :inputStep1="inputStep1"
          :inputStep2="inputStep2"
          :inputStep3="inputStep3"
          @back="page = 'emailStep3'"
          @next="onClickSave"
        ></ModalRegisterContentsEmailConf>
      </div>
    </template>
</Main>
</template>

<script lang="ts" setup>
import type { CreateMemberInput, MemberRegistQuestionnaireInput, WeddingInfoInput } from '@/composables/generated';
import type { InputStep1 } from '@/components/organisms/ModalRegisterContentsEmailStep1.vue';
import { InputStep1Default } from '@/components/organisms/ModalRegisterContentsEmailStep1.vue';
import type { InputStep2 } from '@/components/organisms/ModalRegisterContentsEmailStep2.vue';
import { InputStep2Default } from '@/components/organisms/ModalRegisterContentsEmailStep2.vue';
import type { InputStep3 } from '@/components/organisms/ModalRegisterContentsEmailStep3.vue';
import { InputStep3Default } from '@/components/organisms/ModalRegisterContentsEmailStep3.vue';

const { isSnsRegister } = useModalRegisterState();

const paramPartsHidden = {
  ShowFooterTop: 'hidden',
  FixedFooter: 'hidden',
  spBreadcrumbs: 'hidden',
}

const router = useRouter();
const route = useRoute();

const page = ref('emailStep2')

// 入力用データ
const inputStep1 = ref(InputStep1Default as InputStep1)
const inputStep2 = ref(InputStep2Default as InputStep2)
const inputStep3 = ref(InputStep3Default as InputStep3)

// 更新用データ
const input = ref({} as CreateMemberInput)
const weddingInfo = ref({} as WeddingInfoInput)
const memberRegistQuestionnaires = ref([] as MemberRegistQuestionnaireInput[])

// 全体エラー
const error = ref('')

// 更新中のLoading
const isLoading = ref(true);

// 更新API
const { create, errors } = useTmpCreateMember();

onMounted(() => {
  let isRedirect = false;
  // SNSログインなのにメールアドレスがなければ
  if (! route.query.email || ! route.query.id) {
    router.push({ path: '/login' })
    isRedirect = true;
  }
  // line でも google でもなければ
  if (route.params.sns !== 'google' && route.params.sns !== 'line') {
    router.push({ path: '/login' })
    isRedirect = true;
  }

  // ログインから来た場合は 未登録のアカウントです 会員登録してください
  if (! isSnsRegister()) {
    router.push({ path: '/login', query: { sns: route.params.sns, error_code: 'IS_NOT_REGISTERED' }})
    isRedirect = true;
  }

  if (! isRedirect) isLoading.value = false;
})


// 保存ボタンクリック
const onClickSave = async() => {
  // ↓ は 本当はAPI側で任意にすべきだが とりあえず空で送信
  input.value.email = route.query.email as string;
  input.value.password = getRandomPassword();

  input.value.last_name = inputStep2.value.last_name;
  input.value.first_name = inputStep2.value.first_name;

  // SNSログイン情報を保存
  input.value.sns_login_id = {};
  input.value.sns_login_id[route.params.sns as string] = route.query.id as string;

  if (! inputStep2.value.wedding_date_is_null) {
    weddingInfo.value.wedding_date = inputStep2.value.wedding_date;
  }
  if (! inputStep2.value.wedding_venue_is_null) {
    weddingInfo.value.wedding_venue = inputStep2.value.wedding_venue;
  }
  weddingInfo.value.guest_count = inputStep2.value.guest_count;

  memberRegistQuestionnaires.value = [];
  const question = 'ファヴォリをどこで初めて知りましたか？';
  for (let i = 0; i < inputStep3.value.answers.length; i++) {
    const answer = inputStep3.value.answers[i];
    memberRegistQuestionnaires.value.push({question: question, answer: answer})
  }
  if (inputStep3.value.answer_text) {
    memberRegistQuestionnaires.value.push({question: question, answer: inputStep3.value.answer_text})
  }

  // 全体エラーをリセット
  error.value = '';
  isLoading.value = true;
  const isSuccess = await create(input.value, memberRegistQuestionnaires.value, weddingInfo.value);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    // stepを判定して戻る
    if ((typeof errors.value?.email !== 'undefined') || 
        (typeof errors.value?.password !== 'undefined')) {
      page.value = 'emailStep1';
    } else if ((typeof errors.value?.last_name !== 'undefined') || 
        (typeof errors.value?.first_name !== 'undefined') ||
        (typeof errors.value?.wedding_date !== 'undefined') ||
        (typeof errors.value?.wedding_venue !== 'undefined') ||
        (typeof errors.value?.guest_count !== 'undefined')) {
      page.value = 'emailStep2';
    } else if (typeof errors.value?.answer !== 'undefined') {
      page.value = 'emailStep3';
    }
    return false;
  }

  // 登録成功 メールアドレス送信済み
  router.push({ path: '/register/send' })
};


</script>


<style lang="scss" scoped>
.pageRegister {
  padding-top: 30px;
}
</style>
<style lang="scss">
.pageRegister {
  .wrap {
    max-width: 420px;
    color: #666;
    margin: 0 auto;
    padding: 0 20px 40px;
  }
  .btn-2col {
    display: flex;
    justify-content: space-between;
    flex-direction: row-reverse;
    > a {
      width: 49%;
    }
  }
  .inputChecks {
    label {
      display: block;
      padding: 0;
      margin: 0;
      margin-bottom: 30px;
    }
  }
}
</style>