<template>
<div class="guest-list is-spbreadcrumbsoff">
  <Main
    :breadcrumbs="breadcrumbs"
    title="新規会員登録"
    >
    <template #main>
      <Loading v-if="isLoading"></Loading>
      <div v-else class="cmn-aligncenter">
        <h2 class="cmn-title size--lg">新規会員登録</h2>
        <p v-if="error" class="input-error mb-50 size--lg">{{ error }}</p>
        <NuxtLink class="cmn-linkback" to="/">トップページへ</NuxtLink>
      </div>
    </template>
  </Main>
</div>
</template>

<script lang="ts" setup>
// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "新規会員登録",
  },
];

const route = useRoute();
const router = useRouter();

// 全体エラー
const error = ref('')

// 更新中のLoading
const isLoading = ref(true);

// 更新API
const { create, errors } = useCreateMember();

onMounted(async () => {
  // 全体エラーをリセット
  error.value = '';
  const isSuccess = await create(route.params.uuid as string);
  isLoading.value = false;
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
  } else {
    // 登録完了
    router.push({ path: '/register/done' })
  }
});

</script>