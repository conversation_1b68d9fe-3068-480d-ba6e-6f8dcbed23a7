<template>
<div class="guest-list is-spbreadcrumbsoff">
  <Main
    :partsHidden="{
        spBreadcrumbs: true,
        ShowFooterTop: true,
        FixedFooter: true,
    }" 
    :breadcrumbs="breadcrumbs" 
    >
    <template #main>
      <div class="cmn-aligncenter" style="padding-top: 20px;">
        <h2 class="cmn-title size--lg">会員登録完了しました</h2>
        <p class="mb-50">会員登録完了メールを送信しましたので<br class="sp_only">ご確認をお願いいたします</p>
        <div style="max-width: 260px;" class="mx-auto">
          <ButtonMainColor baseColor="accent" to="/mypage">マイページへ</ButtonMainColor>
        </div>
      </div>
    </template>
  </Main>
</div>
</template>

<script lang="ts" setup>
// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "新規会員登録",
  },
];
</script>