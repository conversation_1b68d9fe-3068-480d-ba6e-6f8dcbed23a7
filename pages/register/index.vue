<template>
    <Index></Index>
</template>

<script lang="ts" setup>
import Index from '@/pages/index.vue'
const { showModalRegister, setRegisterErrors } = useModalRegisterState();

const route = useRoute();
const router = useRouter();
const page = ref('top')

// エラーがあれば表示
const errors = ref({} as any)
if (route.query.sns && route.query.error_code) {
  errors.value[route.query.sns as string] = SNS_LOGIN_ERRORS?.[route.query.error_code as string];
}
onMounted(() => {
  // エラーを消しておく
  router.push({});
  setRegisterErrors(errors.value);
  showModalRegister();
})
</script>