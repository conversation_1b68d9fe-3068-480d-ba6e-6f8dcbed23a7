<template>
  <div class="guest-list is-spbreadcrumbsoff">
    <Main
      :partsHidden="{
          spBreadcrumbs: true,
          ShowFooterTop: true,
          FixedFooter: true,
      }" 
      :breadcrumbs="breadcrumbs" 
      >
      <template #main>
        <div class="cmn-aligncenter" style="padding-top: 20px;">
          <h2 class="cmn-title size--lg">会員登録URLを送信しました</h2>
          <p class="color-maindark size--xl mb-20">あと少しで完了です</p>
          <p class="mb-50 size--lg">メールをご確認いただき 記載されたURLを<br class="sp_only">クリックして登録を完了してください</p>
          <p class="color-blacklight" style="opacity: 0.6;">※メールが送られてこない場合は <br class="sp_only">迷惑メールに振り分けられているか <br>
          メールアドレスが間違っている可能性がございます<br>
          再度 ご確認をお願いいたします</p>
        </div>
      </template>
    </Main>
  </div>
</template>
  
<script lang="ts" setup>
const { hideModalRegister } = useModalRegisterState();
const { hideModalLogin } = useModalLoginState();

// レイアウト設定
const breadcrumbs = [
  {
    title: "HOME",
    link: '/',
  },
  {
    title: "新規会員登録",
  },
];

onMounted(() => {
  hideModalRegister();
  hideModalLogin();
})
</script>