<template>
<Main
  :partsHidden="{
    spHeaderTitle: true
  }" 
  :breadcrumbs="[
    {
      title: 'HOME',
      link: '/',
    },
    {
      title: 'サイトマップ',
    }
  ]"
  title="サイトマップ"
  >
  <template #main>
    <article class="article-sitemap">
      <div class="sitemap-item">
        <h2 class="cmn-title">商品一覧</h2>
        <p class="item-title"><NuxtLink to="/products/webinvitation">WEB招待状 <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <ul>
          <li><NuxtLink to="/products/webinvitation">WEB招待状</NuxtLink></li>
        </ul>
        <p class="item-title">ペーパーアイテム <i class="icn-right material-symbols-outlined">chevron_right</i></p>
        <ul>
          <li><a href="https://www.favori-cloud.com/invitation_designs" target="_blank">結婚式招待状</a></li>
          <li><a href="https://www.favori-cloud.com/design" target="_blank">席次表・席札・メニュー表</a></li>
          <li><a href="https://www.favori-cloud.com/welcome_board_designs" target="_blank">ウェルカムボード</a></li>
          <li><a href="https://www.favori-cloud.com/marriage_postcard_designs" target="_blank">結婚報告はがき</a></li>
        </ul>
        <p class="item-title">演出アイテム <i class="icn-right material-symbols-outlined">chevron_right</i></p>
        <ul>
          <li><a href="https://www.favori-diy.com/categories/5408452" target="_blank">受付サイン</a></li>
          <li><a href="https://www.favori-diy.com/categories/5408465" target="_blank">イニシャルオブジェ</a></li>
          <li><a href="https://www.favori-diy.com/categories/5408466" target="_blank">リングピロー</a></li>
          <li><a href="https://www.favori-diy.com/categories/5408468" target="_blank">結婚証明書</a></li>
          <li><a href="https://www.favori-diy.com/categories/5408470" target="_blank">花嫁の手紙</a></li>
          <li><a href="https://www.favori-diy.com/categories/3015356" target="_blank">両親贈呈品</a></li>
          <li><a href="https://www.favori-diy.com/categories/5408492" target="_blank">手作りキット</a></li>
        </ul>
        <p class="item-title">引き出物宅配 <i class="icn-right material-symbols-outlined">chevron_right</i></p>
        <ul>
          <li><a href="https://www.favori-cloud.com/gift_designs" target="_blank">受付サイン</a></li>
          <li><a href="https://www.favori-cloud.com/gift_designs" target="_blank">受付サイン</a></li>
        </ul>
        <p class="item-title">プチギフト <i class="icn-right material-symbols-outlined">chevron_right</i></p>
        <ul>
          <li><a href="https://www.favori-cloud.com/petit_gift_designs" target="_blank">ドリップコーヒー</a></li>
        </ul>
        <p class="item-title">結婚報告はがき <i class="icn-right material-symbols-outlined">chevron_right</i></p>
        <ul>
          <li><a href="https://www.favori-cloud.com/marriage_postcard_designs" target="_blank">結婚報告はがき</a></li>
        </ul>
      </div>
      <div class="sitemap-item">
        <h2 class="cmn-title">トップページ</h2>
        <p class="item-title"><NuxtLink to="/">ホーム <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <p class="item-title"><NuxtLink to="/mypage">マイページ <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <h2 class="cmn-title">Favoriについて</h2>
        <!-- <p class="item-title"><a href="https://www.favori-cloud.com/about" target="_blank">Favoriについて <i class="icn-right material-symbols-outlined">chevron_right</i></a></p> -->
        <p class="item-title"><NuxtLink to="/voice">お客様の声 <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <p class="item-title"><NuxtLink to="/sitemap">サイトマップ <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <h2 class="cmn-title">利用ガイド</h2>
        <p class="item-title"><NuxtLink to="/support">サポートトップ <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <p class="item-title"><NuxtLink to="/guide">ご利用ガイド <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <p class="item-title"><NuxtLink to="/question">よくある質問 <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <p class="item-title"><NuxtLink to="/contact">お問い合わせ <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <h2 class="cmn-title">その他</h2>
        <p class="item-title"><NuxtLink to="/tokushoho">特定商取引法 <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <p class="item-title"><NuxtLink to="/privacy">プライバシーポリシー <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
        <p class="item-title"><NuxtLink to="/terms">ご利用規約 <i class="icn-right material-symbols-outlined">chevron_right</i></NuxtLink></p>
      </div>
    </article>
  </template>
</Main>
</template>
      
<script setup lang="ts">
const metaTitle = 'サイトマップ｜WEB招待状 Favori（ファヴォリ）';
const metaDescription = 'WEB招待状 Favori（ファヴォリ）のサイトマップページです。';
useHead({
  title: metaTitle,
  meta: [
    { hid: 'description', name: 'description', content: metaDescription },
    { hid: 'og:title', property: 'og:title', content: metaTitle },
    { hid: 'og:description', property: 'og:description', content: metaDescription },
    { hid: 'twitter:title', property: 'twitter:title', content: metaTitle },
    { hid: 'twitter:description', property: 'twitter:description', content: metaDescription },
  ],
})
</script>
<style lang="scss" scoped>
.article-sitemap {
  padding: 20px 40px;
  display: flex;
  @include sp {
    display: block;
    padding: 20px;
  }
  .sitemap-item {
    width: 50%;
    @include sp {
      width: auto;
      margin-bottom: 45px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .cmn-title {
    margin-top: 45px;
  }
  .cmn-title:first-child {
    margin-top: 0;
  }
  .item-title {
    color: #0F2C4E;
    font-weight: 700;
    font-size: 12px;
    margin: 0;
    margin-bottom: 8px;
    a {
      color: #0F2C4E;
      font-weight: 700;
      font-size: 12px;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
    .icn-right {
      font-size: 1.6em;
      vertical-align: middle;
      margin-top: -2px;
    }
  }
  ul {
    margin-left: 20px;
    margin-bottom: 20px;
    li {
      margin-bottom: 6px;
      a {
        font-size: 12px;
        text-decoration: underline;
        cursor: pointer;
        color: #333333;
        &:hover {
          text-decoration: none;
        }
      }
    }
  }
}
</style>
