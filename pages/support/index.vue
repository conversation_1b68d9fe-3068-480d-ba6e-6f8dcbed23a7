<template>
<Main
  :breadcrumbs="[
    {
      title: 'HOME',
      link: '/',
    },
    {
      title: 'サポート',
    }
  ]"
  title="サポート"
  :partsHidden="{
    spH1Title: true,
    spHeaderTitle: true
  }" 
>
<template #main>
<article class="article-questions" style="padding-top: 0;">
  <section class="section-support mb-40" style="padding-top: 0;">
    <div class="box-news">
      <ul class="list-news">
        <li v-for="(content , index) in visibleNews" :key="index">
          <NuxtLink :to="'/information/'+content.id">
            <span class="date">{{ $dayjs(content.createdAt).format('YYYY/MM/DD') }}</span>          
            <span class="cats">
              <span 
                class="cat" 
                v-for="(category , n) in content.category" :key="n"
                :style="getCategoryStyle(category)"
              >
                  {{ category }}
              </span>
            </span>          
            <span class="title">{{ content.title }}</span>     
            <i class="material-icons icn-right">arrow_forward_ios</i>     
          </NuxtLink>
        </li>
      </ul>
      <div class="cmn-aligncenter mt-22 mb-10 fz-sm" v-if="newsData?.contents.length > itemsToShow">
        <NuxtLink to="#" @click.prevent="showMore" class="link-text">もっと見る <i class="material-icons icn-sm ml-5">arrow_forward_ios</i></NuxtLink>
      </div>
    </div>
  </section>
  <section class="section-search-results">
    <div class="box-support-questions">
      <h3 class="cmn-title color--blue mb-10">よくある質問</h3>
      <p>お客さまからよくお問い合わせいただくご質問と その答えを掲載しております <br>お問い合わせの際には まずこちらをご覧ください </p>
    </div>
    <h2 class="cmn-title">よく見られている質問</h2>
    <ListFaqDetail
        v-for="(content , index) in questionsData?.contents"
        :key="index"
        :question="content.question"
        :answer="content.summary"
        :link="'/question/'+content.id"
    ></ListFaqDetail>
    <div v-if="! questionsData?.totalCount" class="mt-60">
      <p class="cmn-aligncenter">該当する検索結果がありませんでした </p>
    </div>
  </section>
</article> 
</template>
</Main>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const { $dayjs } : any = useNuxtApp();
const microCms = new MicroCms();

const metaTitle = 'サポートトップ｜WEB招待状 Favori（ファヴォリ）';
const metaDescription = 'WEB招待状 Favori（ファヴォリ）のサイトニュースやよくある質問をまとめております。ぜひご覧くださいませ。';
useHead({
  title: metaTitle,
  meta: [
    { hid: 'description', name: 'description', content: metaDescription },
    { hid: 'og:title', property: 'og:title', content: metaTitle },
    { hid: 'og:description', property: 'og:description', content: metaDescription },
    { hid: 'twitter:title', property: 'twitter:title', content: metaTitle },
    { hid: 'twitter:description', property: 'twitter:description', content: metaDescription },
  ],
})

// よく見られているサポート内容
const { data: questionsData} = await microCms.fetch('/question', {
  orders: 'publishedAt',
  filters: 'type[contains]特によくある質問'
});

const { data: newsData} = await microCms.fetch('/notice2', {
  orders: '-publishedAt'
});

const itemsToShow = ref(3);
const visibleNews = computed(() => newsData?.value.contents.slice(0, itemsToShow.value));
const showMore = () => {
  itemsToShow.value = newsData?.value.contents.length;
};
</script>

<style lang="scss" scoped>
.article-questions {
    margin-bottom: -46px;
    @include sp {
        margin-bottom: -56px;
    }
}
:deep(.main-contents .contents .section-inner.l-column1) {
    background: #F4F4F4;
    padding-bottom: 0px;
}
:deep(.section-support .list-news a .title) {
    margin-left: 0;
    display: block;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    max-width: 95%;
    line-height: 1.3;
    margin-top: 8px;
}
:deep(.section-support .link-text) {
    color: inherit;
    font-size: 14px; 
    font-weight: normal; 
    text-decoration: none;
}

.arrow-down {
    transform: rotate(90deg);
}
</style>