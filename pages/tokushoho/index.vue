<template>
	<Main
  :partsHidden="paramPartsHidden"
  :breadcrumbs="breadcrumbs"
  title="特定商取引"
  addClass="l-column1-nowrap"
  >
    <template #main>
      <article class="article-tokushoho">
        <div class="article-tokushoho_wrap">
          <dl>
            <dt>販売業者名</dt>
            <dd>株式会社テトテ</dd>
          </dl>
          <dl>
            <dt>販売業者責任者</dt>
            <dd>佐々木絵理子</dd>
          </dl>
          <dl>
            <dt>所在地</dt>
            <dd>〒164-0002<br>東京都中野区上高田3-39-13 山新新井薬師駅前ビル4階<br>営業時間 11：00～18：00（土日祝休業）</dd>
          </dl>
          <dl>
            <dt>電話番号</dt>
            <dd>03-6274-8695</dd>
          </dl>
          <dl>
            <dt>メールアドレス</dt>
            <dd><a class="cmn-link" href="mailto:<EMAIL>"><EMAIL></a></dd>
          </dl>
          <dl>
            <dt>URL</dt>
            <dd><a class="cmn-link" href="https://favori.wedding/">https://favori.wedding/</a></dd>
          </dl>
          <dl>
            <dt>免責事項について</dt>
            <dd>編集時・お申込時の入力内容にお間違えないようご注意ください。<br>お客様の入力ミスによって生じたトラブルなどは一切の責を負いませんのでご了承下さい。</dd>
          </dl>
        </div>
      </article>
    </template>
  </Main>
</template>

<script setup lang="ts">
const breadcrumbs = ref([
  {
    title: 'HOME',
    link: '/',
  },
  {
    title: '特定商取引',
  }
] as any);
const paramPartsHidden = {
  spHeaderTitle: true
}
</script>

<style lang="scss" scoped>
:deep(){
  .nav-sp{
    display: none;
  }
  .l-column1{
    @include sp{
      padding-bottom: 40px;
    }
  }
  .main-contents .contents .section-inner.l-column1{
    padding-bottom: 50px;
    margin-bottom: 4px;
    min-height: auto;
    @include sp{
      padding-bottom: 20px;
    }
  }
}
.article-tokushoho{
  color: #333;
  font-size: 13px;
  line-height: 1.5;
  &_wrap{
    max-width: 682px;
    width: 100%;
    padding: 0 16px;
    margin: 0 auto;
    @include pc{
      padding-top: 30px;
    }
  }
  dl{
    padding: 20px 8px;
    border-bottom: 1px solid #DCDCDC;
    @include pc{
      display: flex;
      align-items: flex-start;
      justify-content: center;
    }
    dt{
      font-size: 16px;
      font-weight: normal;
      line-height: 1.25;
      letter-spacing: 0.06em;
      padding: 0;
      margin: 0 0 10px;
      @include pc{
        width: 200px;
      }
    }
    dd{
      font-size: 13px;
      line-height: 1.4;
      margin-bottom: 0;
      @include pc{
        width: calc(100% - 200px);
      }
    }
  }
  a{
    text-decoration: underline;
    &:hover{
      text-decoration: none;
    }
  }
}
</style>
