<template>
	<Main
  :partsHidden="paramPartsHidden"
  :breadcrumbs="breadcrumbs"
  title="お客様の声"
  addClass="l-column1-nowrap"
  >
    <template #main>
      <section class="mv">
        <div class="mv_wrap">
          <h2 class="mv_title">
            <span class="mv_title_en">Voice</span>
            <span class="mv_title_ja">お客様の声こそが私たちの財産です</span>
          </h2>
          <p class="mv_text">favoriのWEB招待状を実際にご利用いただいた方々の<br>「声」をご紹介します。</p>
        </div>
      </section>
      <section class="section">
        <div class="section_box">
          <h2>写真も気持ちも伝わるWEB招待状、<br><span>ゲストにも大好評</span>でした！</h2>
          <div class="imageBox">
            <div class="imageBox_image">
              <img src="@/assets/images/voice/section_1_image.jpg" alt="">
            </div>
            <div class="imageBox_text">
              <p>
                「favori」さんのWEB招待状を利用しました。デザインがおしゃれなものばかりで、他のサービスと比べても洗練されていて、とても気に入りました◎テンプレートに沿って入力するだけなので、文章を一から考える必要もなく、住所確認の手間もかからず、とにかく作るのが楽でした！<br>
                特に良かったのは、動画や写真をたくさん載せられるところ！前撮りの写真をたくさん紹介できて、<span>ゲストからも「見ごたえがあって楽しかった」と言ってもらえました。</span>スマホでパッと完結できるところがゲストからも好評でした。迷っている方にはおすすめしたいサービスです！
              </p>
              <p><span class="name">M</span>さま</p>
            </div>
          </div>
          <div class="voiceBox">
            <div class="voiceBox_voice">
              <p>このたびはfavoriのWEB招待状をご利用いただき、誠にありがとうございました。前撮り写真をたっぷりご紹介いただけたとのこと、ご満足いただけて何よりです。ゲストの皆さまにも楽しんでいただけたご様子が伝わってきて、私たちもとても嬉しく拝見しました。今後も、大切な想いをしっかり届けられるサービスを目指してまいります。</p>
            </div>
            <div class="voiceBox_from">
              <div class="voiceBox_from_image">
                <img src="@/assets/images/voice/section_1_icon.png" alt="">
              </div>
              <div class="voiceBox_from_name">
                開発担当スタッフより
              </div>
            </div>
          </div>
        </div>
        <div class="section_box">
          <h2>準備もスムーズで、ゲストからも<br>「<span>おしゃれで可愛い！</span>」の声が</h2>
          <div class="imageBox">
            <div class="imageBox_image">
              <img src="@/assets/images/voice/section_2_image.jpg" alt="">
            </div>
            <div class="imageBox_text">
              <p>
                私たちは親しい人だけを招いたパーティーだったので、招待状は全員WEBで送ることにしました。favoriさんのWEB招待状はテンプレートに沿って入力できるので、言葉選びに悩むこともなく、準備がスムーズに進みました♪<br>
                お気に入りの写真をたくさん載せることができたのも嬉しいポイントでした。「ふたりらしさが伝わってきた」「見やすくて助かった」とゲストからも好評でした！<span>Favori cloudさんで、ゲストリストの連携もできるので、スムーズに席次表などの準備が進められました。</span>WEBに不安がある方もいるかもしれませんが、思った以上に自然に使えて、満足しています。
              </p>
              <p><span class="name">H</span>さま</p>
            </div>
          </div>
          <div class="voiceBox">
            <div class="voiceBox_voice">
              <p>WEB招待状だけでなく、Favori cloudの機能もご活用いただきありがとうございます。ゲストの方からの反応もよかったとのことで、私たちもとても嬉しく思います。おふたりの思いが伝わるようなお手伝いができたこと、スタッフ一同光栄です。これからも、誰でもスムーズに、そして安心してご利用いただけるようなサービスづくりに励んでまいります。</p>
            </div>
            <div class="voiceBox_from">
              <div class="voiceBox_from_image">
                <img src="@/assets/images/voice/section_2_icon.png" alt="">
              </div>
              <div class="voiceBox_from_name">
                サポートスタッフより
              </div>
            </div>
          </div>
        </div>
        <div class="section_box">
          <h2>ゲストとのやり取りも楽しく、<br><span>思い出がひとつ増えました</span></h2>
          <div class="imageBox">
            <div class="imageBox_image">
              <img src="@/assets/images/voice/section_3_image.jpg" alt="">
            </div>
            <div class="imageBox_text">
              <p>
                完全無料で作れて、デザインも豊富。写真をたくさん載せられたり、会場の地図を入れられたりと、欲しい機能がちゃんと揃っていてとても便利でした◎<br>
                特に印象的だったのは、ゲストからメッセージや画像を投稿してもらえる機能。<span>懐かしい写真が届いたり、あたたかいメッセージをもらえたりして、思わず笑ったり感動したり。</span>ゲストからも「こんな招待状は初めてで楽しかった」と言ってもらえました。<br>
                両家ともWEBに前向きで、スマートに準備を進められたのも良かったです。形式にとらわれず、自分たちらしいスタイルでおもてなしができました♪
              </p>
              <p><span class="name">A</span>さま</p>
            </div>
          </div>
          <div class="voiceBox">
            <div class="voiceBox_voice">
              <p>ご家族やゲストの皆さまとの心温まるやり取りを、WEB招待状を通して実現していただけたこと、大変嬉しく思っております。写真やメッセージの機能は、まさに「記録」だけでなく「記憶」として残る瞬間を大切にしてほしい、という思いから作りました。おふたりの大切な一日のお役に立てたこと、心より感謝申し上げます。</p>
            </div>
            <div class="voiceBox_from">
              <div class="voiceBox_from_image">
                <img src="@/assets/images/voice/section_3_icon.png" alt="">
              </div>
              <div class="voiceBox_from_name">
                デザイナーより
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="instagram" v-if="instagramPosts.length">
        <div class="instagram_wrap">
          <h2>Instagram Voice<br><span>インスタグラムでいただいた声</span></h2>
          <ul>
            <li v-for="(post, i) in instagramPosts" :key="i">
              <a :href="post.permalink" target="_blank" class="link-img">
                <img v-if="post?.thumbnail_url" :src="post.thumbnail_url">
                <img v-else :src="post.media_url">
              </a>
            </li>
          </ul>
          <div class="cmn-aligncenter">
            <a href="https://www.instagram.com/favori_wedding/" target="_blank" class="link-text">結婚式に役立つ情報を発信中 <i class="icn-right material-symbols-outlined">chevron_right</i></a>
          </div>
        </div>
      </section>
    </template>
  </Main>
</template>

<script setup lang="ts">

const instagram = new Instagram();
const { posts:instagramPosts } = await instagram.posts(9);


const breadcrumbs = ref([
  {
    title: 'HOME',
    link: '/',
  },
  {
    title: 'お客様の声',
  }
] as any);
const paramPartsHidden = {
  spHeaderTitle: true
}

</script>

<style lang="scss" scoped>
:deep(){
  .l-column1{
    @include sp{
      padding-bottom: 40px;
    }
  }
  .contents-title {
    @include sp{
      display: none;
    }
  }
  .contents {
    color: #333;
    font-size: 13px;
    line-height: 1.5;
    max-width: 100%;
    &_wrap{
      max-width: 100%;
      width: 100%;
      padding: 0;
      margin: 0 auto;
      @include pc{
        padding-top: 0;
      }
    }
    h2{
      font-family: 'Noto Serif JP', sans-serif;
      font-weight: 700;
      font-size: 24px;
      line-height: 1.4;
      letter-spacing: 6%;
      text-align: center;
      margin: 0 0 30px;
      @include sp{
        font-family: 'Noto Sans JP';
        font-weight: 400;
        font-size: 14px;
        margin: 0 0 16px;
        padding: 0 16px;
      }
      span {
        color: #AD871E;
      }
    }
    h3{
      font-family: 'Noto Serif JP', sans-serif;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.5;
      margin: 30px 0 15px;
      &::before{
        content: '・';
        @include sp{
          content: '';
        }
      }
    }
    p{
      line-height: 1.5;
      margin-bottom: 15px;
      @include sp{
        font-size: 14px;
      }
    }
  }
}
.mv {
  background-image: url('@/assets/images/voice/mv_bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px;
  &_wrap {
    color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 450px;
    @include sp{
      min-height: 465px;
    }
  }
  &_title {
    margin: 0 0 10px;
    .mv_title_en {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 66px;
      color: #fff;
      font-family: 'Times';
      font-weight: 400;
      font-size: 34px;
      line-height: 100%;
      letter-spacing: 4%;
      text-align: center;
      margin-bottom: 10px;
    }
    .mv_title_ja {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 66px;
      color: #fff;
      font-family: 'Noto Serif JP';
      font-weight: 700;
      font-size: 24px;
      line-height: 100%;
      letter-spacing: 6%;
      text-align: center;
    }
  }
  &_text {
    font-family: 'Noto Sans JP';
    font-weight: 400;
    font-size: 16px;
    line-height: 1.6;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
  }
}
.section {
  background-image: url('@/assets/images/voice/section_bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px;
  @include sp{
    padding: 24px 12px;
  }
  &_box{
    max-width: 100%;
    width: 100%;
    padding: 40px 20px;
    margin: 0 auto;
    border-radius: 4px;
    background-color: #fff;
    @include sp{
      padding: 16px 0;
    }
    & + .section_box {
      margin-top: 20px;
    }
  }
}
.imageBox {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 24px;
  max-width: 1024px;
  width: 100%;
  margin: 0 auto 20px;
  @include sp{
    flex-direction: column;
    gap: 16px;
    margin: 0 auto;
  }
  &_image {
    width: 100%;
    max-width: 400px;
    @include sp{
      max-width: 100%;
    }
    img {
      width: 100%;
      height: auto;
    }
  }
  &_text {
    max-width: calc(100% - 424px);
    @include sp{
      max-width: 100%;
      padding: 0 16px;
    }
    span {
      color: #AD871E;
      font-weight: bold;
    }
  }
}
.voiceBox {
  max-width: 1024px;
  width: 100%;
  margin: 0 auto;
  @include sp{
    max-width: calc(100% - 32px);
    margin: 0 auto;
  }
  &_voice {
    background-color: #EFF8FF;
    margin-bottom: 12px;
    padding: 16px;
    position: relative;
    @include sp{
      margin: 0 auto 8px;
    }
    &::before {
      content: '';
      display: block;
      position: absolute;
      width: 22px;
      height: 16px;
      left: 190px;
      bottom: -16px;
      background-color: #EFF8FF;
      clip-path: polygon(100% 0%, 0% 100%, 0% 0%);
      @include sp{
        max-width: 100%;
        margin: 0 auto 8px;
        bottom: -24px;
      }
    }
  }
  &_from {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    &_image {
      width: 40px;
      height: 40px;
      img {
        width: 40px;
        height: 40px;
        object-fit: cover;
      }
    }
  }
}
.instagram {
  padding: 52px 20px 20px;
  h2 {
    font-family: 'Noto Serif JP', sans-serif;
    color: #49454F;
    line-height: 1;
    @include sp{
      font-size: 24px;
    }
    span {
      font-size: 12px;
      color: #49454F;
    }
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    max-width: 980px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 20px;
    > * {
      width: 25%;
      padding: 3px;
      text-align: center;
      @include sp {
        width: 33.33%;
      }
    }
  }
  li:nth-child(n + 9) {
    display: none;
    @include sp {
      display: block;
    }
  }
  li a {
    display: block;
    position: relative;
    overflow: hidden;
    &:after {
      content: " ";
      display: block;
      padding-top: 100%;
    }
    img {
      height: 100%;
      width: auto;
      max-width: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      @supports ( object-fit: cover ) {
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        transform: none;
        object-fit: cover;
      }
    }
  }
}
</style>
