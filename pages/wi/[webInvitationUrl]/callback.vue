<template>
  <Loading />
</template>
<script lang="ts" setup>
const router = useRouter();
const route = useRoute();

// 全体エラー
const error = ref('')

// 更新API
const { create, errors } = useCreateWebInvitationGuest();

const { webInvitationData } = useGetOneUrlWebInvitation(route.params.webInvitationUrl as string);

onMounted(async () => {
  // データがおかしい場合
  let data = JSON.parse(sessionStorage.getItem('webInvitationInput') as string);

  // IDが合わない場合
  if (data?.payment?.access_id !== route?.query?.AccessID) {
    sessionStorage.removeItem('webInvitationInput');
    router.push({ path: '/wi/'+webInvitationData.value.public_url});
    return false;
  }
  const payment = {
    access_id: data.payment.access_id,
    access_pass: data.payment.access_pass
  }
  const isSuccess = await create(true, data.input, data.guests, data.free_item_values, data.guest_event_answers, data.guest_survey_answers, payment);
  // エラーの場合
  if (! isSuccess) {
    if (errors.value?.v$?.[0]) error.value = errors.value?.v$?.[0];
    // エラーを渡しておく
    data.error = error.value;
    sessionStorage.setItem('webInvitationInput', JSON.stringify(data));
    router.push({ path: '/wi/'+webInvitationData.value.public_url});
    return false;
  }

  // OKの場合はThankyou
  sessionStorage.removeItem('webInvitationInput');
  router.push({ path: '/wi/'+webInvitationData.value.public_url+'/thankyou'});
});
</script>