<template>
  <Loading v-if="isLoading"></Loading>
  <div v-else-if="mode == 'password'" class="webInvitationView webInvitationViewPassword">
    <div class="webInvitationViewPassword_wrap">
      <div class="webInvitationViewPassword_row">
        <div class="webInvitationViewPassword_input">
          <input class="inputItem" type="text" v-model="passwordText" placeholder="合言葉を入力">
        </div>
      </div>
      <div class="webInvitationViewPassword_label"><span class="webInvitationViewPassword_error">{{ passwordError }}</span></div>
      <button class="btn is-confirm" @click="onClickPassword">招待状を見る</button>
    </div>
  </div>
  <WebInvitationUserTop
    v-else-if="mode == 'input'"
    :webInvitationData="webInvitationData"
    :input="input"
    @change="input = $event"
    @next="mode = 'conf'; isBack = false;"
  ></WebInvitationUserTop>
  <WebInvitationUserConf
    v-else-if="mode == 'conf'"
    :input="input"
    :webInvitationData="webInvitationData"
    @back="mode = 'input'; isBack = true;"
    @next="mode = 'payment'; isBack = false;"
  ></WebInvitationUserConf>
  <WebInvitationUserConf2
    v-else-if="mode == 'payment'"
    :input="input"
    :inputPayment="inputPayment"
    :webInvitationData="webInvitationData"
    :pageError="pageError"
    :isBack="isBack"
    @change="input = $event"
    @changePayment="inputPayment = $event"
    @back="mode = 'conf'; isBack = true;"
    @next="mode = 'check'; isBack = false;"
    @setPageError="pageError = $event"
  ></WebInvitationUserConf2>
  <WebInvitationUserFinal
    v-else-if="mode == 'check'"
    :input="input"
    :inputPayment="inputPayment"
    :webInvitationData="webInvitationData"
    @back="mode = 'payment'; isBack = true;"
    @setPageError="pageError = $event"
  ></WebInvitationUserFinal>
</template>

<script lang="ts" setup>
import bcrypt from 'bcryptjs';
import { useRouteHash } from '@vueuse/router';

useHead({
  title: 'WEB招待状',
  meta: [
    { name: 'robots', content: 'noindex' },
    { hid: 'og:title', name: 'og:title', content: 'WEB招待状' },
    { hid: 'twitter:title', name: 'twitter:title', content: 'WEB招待状' },
    { hid: 'description', name: 'description', content: '　' },
    { hid: 'og:description', name: 'og:description', content: '　' },
    { hid: 'twitter:description', name: 'twitter:description', content: '　' },
    { hid: 'og:image', property: 'og:image', content: 'https://favori.wedding/images/ogp-wi.png' },
  ]
})

const router = useRouter();
const route = useRoute();
// URL : [会員が設定したURL = webInvitationUrl]/information
const { webInvitationData, refetch } = useGetOneUrlWebInvitation(route.params.webInvitationUrl as string);

// 戻ってきた場合、入力内容を維持
const isBack = ref(false);

const mode = ref('input');
const input = ref({
  input: {} as CreateGuestNoValidateInput,
  guests: [] as CreateGuestNoValidateInput[],
  guest_event_answers: [] as CreateGuestEventAnswerNoValidateInput[],
  guest_survey_answers: [] as CreateGuestSurveyAnswerInput[],
});

const inputPayment = ref({
  paymentWay: null,
  card: {}
} as {
  paymentWay: number|null,
  card: any
});

const pageError = ref('')

const isLoading = ref(true);
onMounted(async () => {
  isLoading.value = true;
  await refetch();
  if(!webInvitationData.value?.is_public){
    throw createError({
      statusCode: 404,
      message: 'お探しの招待状は見つかりませんでした',
      fatal: true,
    });
  }
  if(webInvitationData.value?.is_password){
    if(webInvitationData.value){
      const style = document.createElement('style');
      style.textContent = webInvitationData?.value?.m_web_invitation.css_code;
      document.head.appendChild(style);
    }
    mode.value = 'password';
  }
  isLoading.value = false;

  const block = webInvitationData.value?.editor_settings?.blocks.find((block:any) => block.id == 'guestAnswer');
  for (let i = 0; i < block.contents.questionnaire.length; i++) {
    const item = block.contents.questionnaire[i];
    let ui_type = '';
    if (item.method == 'checkbox') {
      ui_type = 'CHECKBOX';
    } else if (item.method == 'radio') {
      ui_type = 'RADIOBUTTON';
    } else if (item.method == 'inputText') {
      ui_type = 'TEXT';
    }
    input.value.guest_survey_answers[i] = {
      ui_type: ui_type,
      question: item.heading,
      answer_content: ''
    }
  }

  // エラーで帰ってきて入力再開の場合
  const webInvitationInput = JSON.parse(sessionStorage.getItem('webInvitationInput') as string);
  if (webInvitationInput?.input) {
    input.value.input = webInvitationInput?.input;
    input.value.guests = webInvitationInput?.guests;
    input.value.free_item_values = webInvitationInput?.free_item_values;
    input.value.guest_event_answers = webInvitationInput?.guest_event_answers;
    input.value.guest_survey_answers = webInvitationInput?.guest_survey_answers;
    // エラー文言を取得
    pageError.value = webInvitationInput?.error;
    sessionStorage.removeItem('webInvitationInput');
    mode.value = 'payment';
    isBack.value = true;
  }
});

const hash = useRouteHash()
// ブラウザバック対策 : 遷移時に #xxx をつける
watch(mode, () => {
  if (mode.value && mode.value != 'input') {
    location.hash = '#'+mode.value;
  } else {
    location.hash = '';
  }
})
// ブラウザバック対策 : #xxx からページを判定する
watch(hash, () => {
  let hashMode = String(hash.value).replace('#', '');
  if (! hash.value) hashMode = '';
  if (hashMode != 'input' && hashMode != 'conf' && hashMode != 'payment' && hashMode != 'check') {
    hashMode = '';
  }
  if (hashMode) {
    mode.value = hashMode;
  } else {
    if(!isPasswordCheck.value && webInvitationData.value?.is_password){
      mode.value = 'password';
    }else{
      mode.value = 'input';
    }
  }
})

const passwordText = ref('');
const isPasswordCheck = ref(false);
const passwordError = ref();
const onClickPassword = () => {
  passwordError.value = null;
  if (passwordText.value == webInvitationData.value?.password) {
    mode.value = 'input';
    isPasswordCheck.value = true;
  } else {
    passwordError.value = '合言葉が違います';
  }
}
</script>

<style lang="scss" scoped>
.webInvitationView.webInvitationViewPassword{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  min-height: 100dvh;
  padding: 0 28px;
  background: #fff;
  .webInvitationViewPassword_wrap{
    max-width: 480px;
    padding: 46px 28px 52px;
    width: 100%;
    background: rgba(255,255,255,0.9);
    .webInvitationViewPassword_row {
      border: none;
      margin: 0;
      padding: 0;
    }
    .webInvitationViewPassword_label {
      margin: 10px 0;
    }
    .webInvitationViewPassword_input {
      width: 100%;
      input {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: block;
        outline: 0;
        width: 100%;
        border: none;
        border-bottom: 1px solid #333;
        border-radius: 0;
        color: #333;
        background: transparent;
        font-size: 12px;
        padding: 12px 14px;
        line-height: 1;
        &::placeholder {
          color: #333;
        }
      }
    }
    .webInvitationViewPassword_error {
      margin: 10px 0;
      color: #f00;
      font-weight: normal;
      font-size: 12px;
    }
  }
  .btn.is-confirm {
    font-size: 13px;
    background: #2f587c;
    margin: 42px auto 0;
    padding: 18px 0;
    cursor: pointer;
    box-shadow: 0 4px 4px rgba(0,0,0,0.12);
    &:active {
      box-shadow: none;
    }
  }
}
</style>