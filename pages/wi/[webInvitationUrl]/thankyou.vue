<template>
<div class="webInvitationThanks">
  <div class="webInvitationThanks_wrap cmn-aligncenter">
    <div class="webInvitationThanks_titleImage">
      <img src="@/assets/images/webInvitation_thankyou_title.png" width="194" alt="Thank you 受付完了">
    </div>
    <h2 class="webInvitationThanks_title">
      <div class="mb-12">ご登録誠にありがとうございます！</div>
      <div>ご登録情報を<br>新郎新婦へお届けしました</div>
    </h2>
    <div class="webInvitationThanks_text mb-12">確認メールをお送りいたしましたので<br>ご確認いただきますようお願いいたします</div>
    <div class="webInvitationThanks_image">
      <img src="@/assets/images/webInvitation_thankyou_image.png" width="240" alt="">
    </div>
  </div>
  <div class="webInvitationThanks_footer cmn-aligncenter">
    <div class="webInvitationThanks_footer_text">ウェディング準備なら</div>
    <img src="@/assets/images/logo.svg" width="62" alt="">
  </div>
</div>
</template>

<style lang="scss" scoped>
.webInvitationThanks {
  position: relative;
  &_wrap{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-family: "Noto Serif JP", serif;
    color: #272727;
    height: calc(100vh - 104px);
    height: calc(100dvh - 104px);
    min-height: 560px;
    @include pc{
      height: auto;
      min-height: auto;
    }
  }
  &_titleImage{
    max-width: 194px;
    margin: 0 auto 36px;
    padding-top: 20px;
    @include pc{
      max-width: 220px;
      margin: 0 auto 40px;
      padding-top: 60px;
    }
  }
  &_title{
    font-weight: normal;
    font-size: 17px;
    line-height: 1.64;
    margin: 0 auto 24px;
    padding: 0;
    @include pc{
      font-size: 20px;
      line-height: 1.5;
      margin: 0 auto 32px;
      br{
        display: none;
      }
    }
  }
  &_text{
    font-size: 14px;
    line-height: 1.46;
    margin: 0 auto 20px;
    @include pc{
      font-size: 16px;
      line-height: 1.6;
      margin: 0 auto 8px;
    }
  }
  &_image{
    max-width: 240px;
    width: 100%;
    margin: 0 auto;
    @include pc{
      max-width: 320px;
      margin: 0 auto 12px;
    }
  }
  &_footer{
    min-height: 104px;
    padding: 20px 0 30px;
    &_text{
      color: #2F587C;
      font-size: 10px;
      line-height: 2.2;
      margin: 0 auto 5px;
    }
    img{
      max-width: 62px;
    }
  }
}
</style>

<script lang="ts" setup>

</script>