import {
  Apollo<PERSON>lient,
  InMemoryCache,
  createHttpLink,
  concat,
  from,
  ApolloLink,
} from '@apollo/client/core'
import { onError } from '@apollo/client/link/error'
import createUploadLink from 'apollo-upload-client/createUploadLink.mjs'
import { provideApolloClient } from '@vue/apollo-composable';

import { DefaultApolloClient, ApolloClients } from '@vue/apollo-composable'
import { setContext } from '@apollo/client/link/context'

const DEFAULT_CLIENT_ID = 'default'

export default defineNuxtPlugin((nuxt) => {
  const { getAccessToken, isSameAccessToken } = useAccessTokenState()
  const { clients } = useRuntimeConfig().public.apollo

  const apolloClients: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: ApolloClient<any>
  } = {}

  // 認証トークンを付与するリンクを作成します
  function createAuthLink(clientId: string, authenticationType = 'Bearer ') {
    const authLink = setContext(async (_, { headers }) => {
      // メモリ上に保持しているトークンとCookieに保存されているトークンを比較
      if (! isSameAccessToken(clientId)) {
        try {
          location.href = '/login';
        } catch (error) {
          
        }
        return {};
      }

      const token = getAccessToken(clientId)
      const authorizationHeader = token
        ? { Authorization: authenticationType ? authenticationType + token : token }
        : {}
      return {
        headers: {
          ...headers,
          ...authorizationHeader,
        },
      }
    })
    return authLink
  }

  const errorLink = onError(({ graphQLErrors, networkError }) => {
    // 認証エラー(無効なaccessTokeの場合など) はログアウト
    if (graphQLErrors?.[0]?.message === 'Unauthenticated.') {
      navigateTo('/logout');
    }

    if (graphQLErrors) {
      try {
        // graphQLErrors.forEach(({ message, locations, path }) =>
        //   console.log(
        //     `[GraphQL error]: Message: ${message}, Location: ${JSON.stringify(
        //       locations
        //     )}, Path: ${path}`
        //   )
        // )
      } catch (e) {
        // console.log('error: print graphql error')
      }
    }

    // if (networkError) console.log(`[Network error]: ${networkError}`)

    //return forward(operation);
  })

  for (const clientId in clients) {
    const options = clients[clientId]

    const httpLink = createUploadLink({
      ...options,
      credentials: 'include',
      fetchOptions: {
        mode: 'cors',
      }
    })

    const authLink = createAuthLink(clientId)
    const link = from([errorLink, concat(authLink, httpLink)])

    const cache = new InMemoryCache()

    const apolloClient = new ApolloClient(
      {
        //@ts-ignore
        link: link,
        cache,
        ssrForceFetchDelay: 100,
        connectToDevTools: clientId == DEFAULT_CLIENT_ID,
      }
    )
    provideApolloClient(apolloClient)
    apolloClients[clientId] = apolloClient
  }

  nuxt.vueApp.provide(DefaultApolloClient, apolloClients.default)
  nuxt.vueApp.provide(ApolloClients, apolloClients)
  // provide $apollo, used directly: $apollo.default
  nuxt.provide('apollo', apolloClients)
})

// @ts-expect-error #app resolved by Nuxt3
declare module '#app' {
  interface NuxtApp {
    $apollo: {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      [key: string]: ApolloClient<any>
    }
  }
}