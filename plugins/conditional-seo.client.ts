export default defineNuxtPlugin(() => {
  const router = useRouter();
  
  // LPページ以外でのみデフォルトSEOを適用
  router.beforeEach((to) => {
    if (to.path.startsWith('/lp/')) {
      // LPページでは共通のSEO設定をクリア（個別設定が優先されるよう）
      if (process.client) {
        // デフォルトのメタタグのうち、LPページで上書きが必要なもののみクリア
        const metaToClear = [
          'meta[hid="description"]',
          'meta[hid="og:title"]', 
          'meta[hid="og:description"]',
          'meta[hid="og:image"]',
          'meta[hid="twitter:title"]',
          'meta[hid="twitter:description"]'
        ];
        
        metaToClear.forEach(selector => {
          const element = document.querySelector(selector);
          if (element) {
            element.remove();
          }
        });
      }
    }
  });
});