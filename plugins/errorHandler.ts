export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.config.errorHandler = (error) => {
    const route = useRoute()
    notifyError(error)

    if (error instanceof AuthnError) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const uniqueName = route.params.uniqueName as string
      if (!!uniqueName) {
        navigateTo(`/${uniqueName}/logout`)
      } else {
        navigateTo('/')
      }
    }
  }
})