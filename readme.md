# Getting Start

[こちら](./docs/GETTING_START.md)

## node -v

    v16.18.0

## npm list vue

    `-- nuxt@3.5.0
      +-- @nuxt/vite-builder@3.5.0
      | +-- @vitejs/plugin-vue-jsx@3.0.1
      | | `-- vue@3.3.2 deduped
      | +-- @vitejs/plugin-vue@4.2.3
      | | `-- vue@3.3.2 deduped
      | `-- vue@3.3.2 deduped
      +-- @unhead/vue@1.1.26
      | `-- vue@3.3.2 deduped
      +-- unplugin-vue-router@0.6.4
      | `-- @vue-macros/common@1.3.1
      |   `-- vue@3.3.2 deduped
      +-- vue-router@4.2.0
      | `-- vue@3.3.2 deduped
      `-- vue@3.3.2
        `-- @vue/server-renderer@3.3.2
          `-- vue@3.3.2 deduped

## 追加インストール

    sass: 1.62.1
    @nuxtjs/style-resources: 1.2.1
    nuxt-sass-resources-loader: 2.0.5

---

## Storybook の起動方法

下記コマンドで Storybook をローカルサーバーで実行する。
URL は「 [http://localhost:6006/](http://localhost:6006/) 」で確認する。

```
npm run storybook
```

ビルドしたい場合は下記コマンドを実行する。
ビルド結果は「/storybook-static」配下に生成される。

```
npm run build-storybook
```
