import ArticleTag from "../../components/atoms/ArticleTag.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ArticleTag>;

const meta: Meta<typeof ArticleTag> = {
  title: "components/atoms/ArticleTag",
  component: ArticleTag,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ArticleTag },
    setup() {
      return { args };
    },
    template: "<ArticleTag v-bind='args'>ArticleTag</ArticleTag>",
  }),
  args: {
    propsArticleTag: ['引き出物', '引き出物宅配']
  },
  argTypes: {
    propsArticleTag: {
      control: {
        type: "array",
      }
    }
  }
};

export const Main: Story = {
  args: {
    propsArticleTag: ['引き出物', '引き出物宅配']
  },
};

export default meta;