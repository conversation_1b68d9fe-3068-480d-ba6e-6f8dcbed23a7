import ButtonMainColor from "../../components/atoms/ButtonMainColor.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ButtonMainColor>;

const meta: Meta<typeof ButtonMainColor> = {
  title: "components/atoms/ButtonMainColor",
  component: ButtonMainColor,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ButtonMainColor },
    setup() {
      return { args };
    },
    template: "<ButtonMainColor v-bind='args'>ButtonMainColor</ButtonMainColor>",
  }),
  args: {
    addClasses: "",
    align: "left",
    size: "lg",
    disabled: false
  },
  argTypes: {
    addClasses: {
      control: {
        type: "string",
      }
    },
    baseColor: {
      control: {
        type: "select",
      },
      options: ["main", "reversal"],
    },
    align: {
      control: {
        type: "select",
      },
      options: ["left", "center", "right"],
    },
    size: {
      control: {
        type: "select",
      },
      options: ["sm", "md", "lg"],
    },
    spsize: {
      control: {
        type: "select",
      },
      options: ["spsm", "spmd", "splg"],
    },
    disabled: {
      control: {
        type: "boolean",
      },
    }
  },
};

export const main: Story = {
  args: {
    classes: "",
    baseColor: 'main',
    align: "left",
    size: "md",
    disabled: false
  },
};

export const reversal: Story = {
  args: {
    classes: "",
    baseColor: 'reversal',
    align: "left",
    size: "md",
    disabled: false
  },
};

export const disabled: Story = {
  args: {
    classes: "",
    baseColor: 'reversal',
    align: "left",
    size: "md",
    disabled: true
  },
};

export default meta;