import InputSearch from "../../components/atoms/InputSearch.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof InputSearch>;

const meta: Meta<typeof InputSearch> = {
  title: "components/atoms/InputSearch",
  component: InputSearch,
  tags: ['autodocs'],
  render: (args) => ({
    components: { InputSearch },
    setup() {
      return { args };
    },
    template: "<InputSearch v-bind='args'>InputSearch</InputSearch>",
  }),
  args: {
    formSettingParams: {
      name: 'name',
      id: 'sample',
      placeholder: 'キーワード検索'
    }
  },
  argTypes: {
    formSettingParams: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    formSettingParams: {
      name: 'name',
      id: 'sample',
      placeholder: 'キーワード検索'
    }
  },
};

export default meta;