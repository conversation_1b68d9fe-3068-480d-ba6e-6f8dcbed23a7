import ProgressGraph from "../../components/atoms/ProgressGraph.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ProgressGraph>;

const meta: Meta<typeof ProgressGraph> = {
  title: "components/atoms/ProgressGraph",
  component: ProgressGraph,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ProgressGraph },
    setup() {
      return { args };
    },
    template: "<ProgressGraph v-bind='args'></ProgressGraph>",
  }),
  args: {
    params: {
    }
  },
  argTypes: {
    params: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    params: {
      current: 2,
      finish: 5,
    }
  }
};

export default meta;