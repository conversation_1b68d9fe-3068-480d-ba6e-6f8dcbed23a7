import Stars from "../../components/atoms/Stars.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof Stars>;

const meta: Meta<typeof Stars> = {
  title: "components/atoms/Stars",
  component: Stars,
  tags: ['autodocs'],
  render: (args) => ({
    components: { Stars },
    setup() {
      return { args };
    },
    template: "<Stars v-bind='args'>Stars</Stars>",
  }),
  args: {
  },
  argTypes: {
    stars: { 
      control: { type: 'range', min: 0, max: 5, step: 0.1 } 
    },
    review: { 
      control: { type: 'number', min: 1, max: 1000, step: 1 }
    },
  }
};

export const Main: Story = {
  args: {
    stars: 3.3,
    review: 17,
  },
};

export default meta;