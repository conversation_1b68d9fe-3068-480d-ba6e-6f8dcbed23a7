import Titleh1 from "../../components/atoms/Titleh1.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof Titleh1>;

const meta: Meta<typeof Titleh1> = {
  title: "components/atoms/Titleh1",
  component: Titleh1,
  tags: ['autodocs'],
  render: (args) => ({
    components: { Titleh1 },
    setup() {
      return { args };
    },
    template: "<Titleh1 v-bind='args'>Titleh1</Titleh1>",
  }),
  args: {
  },
  argTypes: {
  }
};

export const Main: Story = {
  args: {
  },
};

export default meta;