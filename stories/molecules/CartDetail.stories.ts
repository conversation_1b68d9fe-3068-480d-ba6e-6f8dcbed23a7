import CartDetail from "../../components/molecules/CartDetail.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof CartDetail>;

const meta: Meta<typeof CartDetail> = {
  title: "components/molecules/CartDetail",
  component: CartDetail,
  tags: ['autodocs'],
  render: (args) => ({
    components: { CartDetail },
    setup() {
      return { args };
    },
    template: "<CartDetail v-bind='args'>CartDetail</CartDetail>",
  }),
  args: {
    data: [
    ]
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const toedititem: Story = {
  args: {
    data: [
      {
        title: 'Memphis A Bluege[GG]',
        id: 'gift00000000001',
        category: "招待状",
        thumbnail: "/images/sample/thumbnail07.png",
        price: "429",
        priceUnit: "１セット",
        orderd: "",
        option: [
        ],
        addevent: [
          {
            class: "toedititem",
            menu: "作成中のアイテムに戻す",
          },
          {
            class: "tolater",
            menu: "あとで買う",
          },
        ],
      }, 
    ],
  },
};

export const totrash: Story = {
  args: {
    data: [
      {
        title: 'Memphis A Bluege[GG]',
        id: 'gift00000000002',
        category: "招待状",
        thumbnail: "/images/sample/thumbnail07.png",
        price: "429",
        priceUnit: "１セット",
        orderd: "",
        option: [
        ],
        addevent: [
          {
            class: "totrash",
            menu: "削除",
          },
          {
            class: "tolater",
            menu: "あとで買う",
          },
        ],
      }, 
    ],
  },
};

export const withoption: Story = {
  args: {
    data: [
      {
        title: 'Memphis A Bluege[GG]',
        id: 'gift00000000002',
        category: "招待状",
        thumbnail: "/images/sample/thumbnail07.png",
        price: "495",
        priceUnit: "１セット",
        orderd: "20",
        orderdUnit: "セット",
        option: [
          {
            item: "封筒",
            substance: "宛名印刷あり 2枚｜宛名印刷なし 18枚",
          },
          {
            item: "返信はがき",
            substance: "新郎用 10枚｜新婦用 10枚",
          },
          {
            item: "はがき裏面カスタマイズ",
            substance: "希望する",
          },
          {
            item: "付箋",
            substance: "5枚",
          },
          {
            item: "予備封筒",
            substance: "1枚",
          },
        ],
        addevent: [
        ],
      }, 
    ],
  },
};

export default meta;