import ItemCarousel from "../../components/molecules/ItemCarousel.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ItemCarousel>;

const meta: Meta<typeof ItemCarousel> = {
  title: "components/molecules/ItemCarousel",
  component: ItemCarousel,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ItemCarousel },
    setup() {
      return { args };
    },
    template: "<ItemCarousel v-bind='args'>ItemCarousel</ItemCarousel>",
  }),
  args: {
    data: {
    }, 
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    data: {
      thumbnail: [
        "/images/sample/thumbnail06.png",
        "/images/sample/smallthumbnail04.png",
        "/images/sample/smallthumbnail03.png",
        "/images/sample/smallthumbnail02.png",
        "/images/sample/smallthumbnail05.png",
      ],
    },
  },
};

export default meta;