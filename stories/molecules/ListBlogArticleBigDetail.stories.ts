import ListBlogArticleBigDetail from "../../components/molecules/ListBlogArticleBigDetail.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListBlogArticleBigDetail>;

const meta: Meta<typeof ListBlogArticleBigDetail> = {
  title: "components/molecules/ListBlogArticleBigDetail",
  component: ListBlogArticleBigDetail,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListBlogArticleBigDetail },
    setup() {
      return { args };
    },
    template: "<ListBlogArticleBigDetail v-bind='args'></ListBlogArticleBigDetail>",
  }),
  args: {
    data: [
    ],
    assignMenu: {
    }
  },
  argTypes: {
    data: {
      control: {
        type: "array",
      }
    },
    assignMenu: {
      control: {
        type: "object",
      }
    }
  }
};

export const description_true: Story = {
  args: {
    data: [
      {
        title: '最大29％OFF 引き出物節約キャンペーン開催中！数量限定 1000冊のみのご用意です。',
        description: '『引き出物がお得に買える♪』と話題のFavoriの引き出物節約キャンペーン。前回のキャンペーン',
        link: '/article/00000000001',
        thumbnail: "/images/sample/thumbnail08.png",
        date: "2023.1.2",
        category: "引き出物宅配",
        tags: [
          '引き出物',
          "引き出物宅配",
        ],
      },
    ],
    assignMenu: {
      description: true,
      tags: true,
    }
  }
};

export const description_false: Story = {
  args: {
    data: [
      {
        title: '最大29％OFF 引き出物節約キャンペーン開催中！数量限定 1000冊のみのご用意です。',
        description: '『引き出物がお得に買える♪』と話題のFavoriの引き出物節約キャンペーン。前回のキャンペーン',
        link: '/article/00000000001',
        thumbnail: "/images/sample/thumbnail08.png",
        date: "2023.1.2",
        category: "引き出物宅配",
        tags: [
          '引き出物',
          "引き出物宅配",
        ],
      },
    ],
    assignMenu: {
      description: false,
      tags: false,
    }
  }
};

export default meta;