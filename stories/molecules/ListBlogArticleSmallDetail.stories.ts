import ListBlogArticleSmallDetail from "../../components/molecules/ListBlogArticleSmallDetail.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListBlogArticleSmallDetail>;

const meta: Meta<typeof ListBlogArticleSmallDetail> = {
  title: "components/molecules/ListBlogArticleSmallDetail",
  component: ListBlogArticleSmallDetail,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListBlogArticleSmallDetail },
    setup() {
      return { args };
    },
    template: "<ListBlogArticleSmallDetail v-bind='args'></ListBlogArticleSmallDetail>",
  }),
  args: {
    data: [
    ],
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    data: [
      {
        title: '最大29％OFF 引き出物節約キャンペーン開催中！数量限定 1000冊のみのご用意です。',
        description: '『引き出物がお得に買える♪』と話題のFavoriの引き出物節約キャンペーン。前回のキャンペーン',
        link: '/article/00000000001',
        thumbnail: "/images/sample/thumbnail08.png",
        date: "2023.1.2",
        category: "引き出物宅配",
        tags: [
          '引き出物',
          "引き出物宅配",
        ],
      },
    ],
  }
};


export default meta;