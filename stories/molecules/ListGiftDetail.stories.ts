import ListGiftDetail from "../../components/molecules/ListGiftDetail.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListGiftDetail>;

const meta: Meta<typeof ListGiftDetail> = {
  title: "components/molecules/ListGiftDetail",
  component: ListGiftDetail,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListGiftDetail },
    setup() {
      return { args };
    },
    template: "<ListGiftDetail v-bind='args'>ListGiftDetail</ListGiftDetail>",
  }),
  args: {
    class: "",
    data: [
    ]
  },
  argTypes: {
    class: {
      control: {
        type: "inline-radio",
      },
      options: ["listgiftdatail", "listgiftrequest", "listgiftwrapping", "listgiftrequesthistory", "listguestgift", "listsubgift", "listgiftrequestconfirm"],
    },
    data: {
      control: {
        type: "array",
      }
    }
  }
};

export const Main: Story = {
  args: {
    class: "listsubgift",
    data: [
      {
        title: '【パターン D06-3-6】3品セット【Favori 限定】Dolce Duo PRIME CATALOG GIFT ボワール 送料無料',
        category: "",
        number: "SD-1433-03",
        thumbnail: "/images/sample/thumbnail01.png",
        labels: [
        ],
        price: "",
        priceStrike: "",
        priceUnit: "",
        orderd: "",
      },
    ]
  },
};

export default meta;