import ListGiftFixedDetail from "../../components/molecules/ListGiftFixedDetail.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListGiftFixedDetail>;

const meta: Meta<typeof ListGiftFixedDetail> = {
  title: "components/molecules/ListGiftFixedDetail",
  component: ListGiftFixedDetail,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListGiftFixedDetail },
    setup() {
      return { args };
    },
    template: "<ListGiftFixedDetail v-bind='args'>ListGiftFixedDetail</ListGiftFixedDetail>",
  }),
  args: {
    data: [
    ]
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const beforeDelivery: Story = {
  args: {
    data: {
      datail: [
        {
          status: "beforeDelivery",
          name: '大野 将也',
          labels: [
            {
              class: "guest<PERSON>erson",
              title: "新婦側ゲスト",
            },
            {
              class: "delivery",
              title: "引き出物宅配",
            },
          ],
          address: "〒000-0000\r\n京都府京都市北区小山花ノ木町1-2-3",
          telnumber: "090-0000-0000",
          arrival: "",
          invoice: "",
          thumbnails: [
            {
              class: "",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
          ],
        },
      ],
    }, 
  },
};

export const standbyDelivery: Story = {
  args: {
    data: {
      datail: [
        {
          status: "afterDelivery",
          name: '大野 将也',
          labels: [
            {
              class: "standbyDelivery",
              title: "配送準備中",
            },
          ],
          address: "〒000-0000\r\n京都府京都市北区小山花ノ木町1-2-3",
          telnumber: "090-0000-0000",
          arrival: "2023年4月1日に到着予定",
          invoice: "1234-5678-9012",
          thumbnails: [
            {
              class: "",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
          ],
        }, 
      ],
    }, 
  },
};

export const afterDelivery: Story = {
  args: {
    data: {
      datail: [
        {
          status: "afterDelivery",
          name: '大野 将也',
          labels: [
            {
              class: "completionDelivery",
              title: "配送準備完了",
            },
          ],
          address: "〒000-0000\r\n京都府京都市北区小山花ノ木町1-2-3",
          telnumber: "090-0000-0000",
          arrival: "2023年4月1日に到着予定",
          invoice: "1234-5678-9012",
          thumbnails: [
            {
              class: "",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
          ],
        },
      ],
    }, 
  },
};

export default meta;