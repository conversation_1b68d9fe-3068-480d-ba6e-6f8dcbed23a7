import ListHeaderDropMenu from "../../components/molecules/ListHeaderDropMenu.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListHeaderDropMenu>;

const meta: Meta<typeof ListHeaderDropMenu> = {
  title: "components/molecules/ListHeaderDropMenu",
  component: ListHeaderDropMenu,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { ListHeaderDropMenu },
    setup() {
      return { args };
    },
    template: "<ListHeaderDropMenu v-bind='args'>ListHeaderDropMenu</ListHeaderDropMenu>",
  }),
  args: {
    title: 'ペーパーアイテム',
    list: {
    }
  },
  argTypes: {
    title: String,
    list: Array,
  }
};

export const Main: Story = {
  args: {
    title: 'ペーパーアイテム',
    list: [
      {
        menu: "結婚式招待状",
        link: "#01"
      }, 
      {
        menu: "席次表",
        link: "#02"
      }, 
      {
        menu: "席札",
        link: "#03"
      }, 
      {
        menu: "メニュー表",
        link: "#04"
      }, 
      {
        menu: "プログラム（式次第）",
        link: "#05"
      }, 
      {
        menu: "プロフィールブック",
        link: "#06"
      },
      {
        menu: "ゲストカード",
        link: "#07"
      }, 
      {
        menu: "ペーパーアイテム手作り・単品",
        link: "#08"
      }, 
    ],
  },
};

export default meta;