import ListItemsDetail from "../../components/molecules/ListItemsDetail.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListItemsDetail>;

const meta: Meta<typeof ListItemsDetail> = {
  title: "components/molecules/ListItemsDetail",
  component: ListItemsDetail,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListItemsDetail },
    setup() {
      return { args };
    },
    template: "<ListItemsDetail v-bind='args'>ListItemsDetail</ListItemsDetail>",
  }),
  args: {
    data: [
    ]
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    data: [
      {
        title: 'Lucky Gray[GN]',
        id: 'gift00000000001',
        link: '/gift/00000000001',
        thumbnail: "/images/sample/thumbnail06.png",
        price: "297",
        unit: "部",
        stars: 3.3,
        review: 12,
        tags: [
          "ゴールド箔",
          "リボン",
        ],
      }, 
    ],
  },
};

export default meta;