import ListItemsPriceDetail from "../../components/molecules/ListItemsPriceDetail.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListItemsPriceDetail>;

const meta: Meta<typeof ListItemsPriceDetail> = {
  title: "components/molecules/ListItemsPriceDetail",
  component: ListItemsPriceDetail,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListItemsPriceDetail },
    setup() {
      return { args };
    },
    template: "<ListItemsPriceDetail v-bind='args'>ListItemsPriceDetail</ListItemsPriceDetail>",
  }),
  args: {
    data: [
    ]
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const labels_on: Story = {
  args: {
    data: [
      {
        title: '3品セット Dolce Duo PRIME CATAROG GIFT ポワール（¥3,300コース）',
        thumbnail: "/images/sample/thumbnail01.png",
        labels: [
          "数量限定キャンペーン",
        ],
        price: "4510",
        priceStrike: "6600",
      }, 
    ],
  },
};


export const labels_off: Story = {
  args: {
    data: [
      {
        title: '3品セット Dolce Duo PRIME CATAROG GIFT ポワール（¥3,300コース）',
        thumbnail: "/images/sample/thumbnail01.png",
        labels: [
        ],
        price: "4510",
        priceStrike: "6600",
      },
    ],
  },
};

export const priceStrike_off: Story = {
  args: {
    data: [
      {
        title: '3品セット Dolce Duo PRIME CATAROG GIFT ポワール（¥3,300コース）',
        thumbnail: "/images/sample/thumbnail01.png",
        labels: [
        ],
        price: "4510",
      },
    ],
  },
};

export default meta;