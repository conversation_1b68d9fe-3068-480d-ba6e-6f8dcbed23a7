import ListItemsSmallDetail from "../../components/molecules/ListItemsSmallDetail.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListItemsSmallDetail>;

const meta: Meta<typeof ListItemsSmallDetail> = {
  title: "components/molecules/ListItemsSmallDetail",
  component: ListItemsSmallDetail,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListItemsSmallDetail },
    setup() {
      return { args };
    },
    template: "<ListItemsSmallDetail v-bind='args'>ListItemsSmallDetail</ListItemsSmallDetail>",
  }),
  args: {
    data: [
    ]
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    data: [
      {
        title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
        target: 'gift00000000001',
        thumbnail: "/images/sample/thumbnail04.png",
        labels: [
          "数量限定キャンペーン",
        ],
        price: "4510",
        priceStrike: "6600",
      }, 
      {
        title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
        target: 'gift00000000002',
        thumbnail: "/images/sample/thumbnail05.png",
        labels: [
        ],
        price: "4510",
      }, 
      {
        title: 'Dolce duo バウムクーヘン(モダンブラウン)',
        target: 'gift00000000003',
        thumbnail: "/images/sample/thumbnail04.png",
        labels: [
          "数量限定キャンペーン",
        ],
        price: "4510",
        priceStrike: "6600",
      }, 
    ],
  },
};

export default meta;