import ListSnsNavi from "../../components/molecules/ListSnsNavi.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListSnsNavi>;

const meta: Meta<typeof ListSnsNavi> = {
  title: "components/molecules/ListSnsNavi",
  component: ListSnsNavi,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListSnsNavi },
    setup() {
      return { args };
    },
    template: "<ListSnsNavi v-bind='args'>ListSnsNavi</ListSnsNavi>",
  }),
  args: {
  },
  argTypes: {
  }
};

export const Main: Story = {
  args: {
  },
};

export default meta;