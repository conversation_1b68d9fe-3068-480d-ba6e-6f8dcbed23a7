import ListSnsNaviReceive from "../../components/molecules/ListSnsNaviReceive.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListSnsNaviReceive>;

const meta: Meta<typeof ListSnsNaviReceive> = {
  title: "components/molecules/ListSnsNaviReceive",
  component: ListSnsNaviReceive,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListSnsNaviReceive },
    setup() {
      return { args };
    },
    template: "<ListSnsNaviReceive v-bind='args'>ListSnsNaviReceive</ListSnsNaviReceive>",
  }),
  args: {
  },
  argTypes: {
  }
};

export const Main: Story = {
  args: {
  },
};

export default meta;