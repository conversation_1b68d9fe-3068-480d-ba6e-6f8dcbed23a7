import Breadcrumbs from "../../components/organisms/Breadcrumbs.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof Breadcrumbs>;

const meta: Meta<typeof Breadcrumbs> = {
  title: "components/organisms/Breadcrumbs",
  component: Breadcrumbs,
  tags: ['autodocs'],
  render: (args) => ({
    components: { Breadcrumbs },
    setup() {
      return { args };
    },
    template: "<Breadcrumbs v-bind='args'>Breadcrumbs</Breadcrumbs>",
  }),
  args: {
    data: {
    },
  },
  argTypes: {
    data: {
      control: {
        type: "array",
      }
    },
  },
};

export const Main: Story = {
  args: {
    data: {
      parents: [
        {
          title: "HOME",
          link: '/',
        },
        {
          title: "2階層",
          link: '/article/',
        },
      ],
      current: [
        {
          title: "コンポーネント一覧",
        },
      ]
    },
  },
};

export default meta;


