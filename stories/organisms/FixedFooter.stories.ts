import FixedFooter from "../../components/organisms/FixedFooter.vue";

export default {
  title: "components/organisms/FixedFooter",
  component: FixedFooter,
};

const Template = (args: any) => ({
  components: { FixedFooter },
  setup() {
    return {
      args,
    };
  },
  template: `
    <FixedFooter v-bind="args">FixedFooter</FixedFooter>
  `,
});

export const Primary: any = Template.bind({});
Primary.args = {
  type: "FixedFooter",
};