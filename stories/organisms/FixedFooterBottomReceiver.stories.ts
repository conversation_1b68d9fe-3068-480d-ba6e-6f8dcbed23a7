import FixedFooterBottomReceiver from "../../components/organisms/FixedFooterBottomReceiver.vue";

export default {
  title: "components/organisms/FixedFooterBottomReceiver",
  component: FixedFooterBottomReceiver,
};

const Template = (args: any) => ({
  components: { FixedFooterBottomReceiver },
  setup() {
    return {
      args,
    };
  },
  template: `
    <FixedFooterBottomReceiver v-bind="args">FixedFooterBottomReceiver</FixedFooterBottomReceiver>
  `,
});

export const Primary: any = Template.bind({});
Primary.args = {
  type: "FixedFooterBottomReceiver",
};