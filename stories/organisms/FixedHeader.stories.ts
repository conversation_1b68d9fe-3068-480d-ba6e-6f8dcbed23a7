import FixedHeader from "../../components/organisms/FixedHeader.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof FixedHeader>;

const meta: Meta<typeof FixedHeader> = {
  title: "components/organisms/FixedHeader",
  component: FixedHeader,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { FixedHeader },
    setup() {
      return { args };
    },
    template: "<FixedHeader v-bind='args'>FixedHeader</FixedHeader>",
  }),
  args: {
    stateBadge: {
      noticeBadges: 3,
      favoriteBadges: 2,
      cartBadges: 1
    },
    stateLogin: {
    },
    stateRecent: {
    }
  },
  argTypes: {
    stateBadge: {
      control: {
        type: "object",
      }
    },
    stateLogin: {
      control: {
        type: "object",
      }
    },
    stateRecent: {
      control: {
        type: "object",
      }
    },
  },
};

export const Main: Story = {
  args: {
    stateBadge: {
      noticeBadges: 3,
      favoriteBadges: 2,
      cartBadges: 1
    },
    stateLogin: {
      login: true,
      name: '鈴木 このみ',
      date: '2023年4月1日',
    },
    stateRecent: {
      recent: [
        {
          thumbnail: "/images/sample/smallthumbnail01.png",
          link: "#01"
        },
        {
          thumbnail: "/images/sample/smallthumbnail02.png",
          link: "#02"
        },
        {
          thumbnail: "/images/sample/smallthumbnail03.png",
          link: "#03"
        },
        {
          thumbnail: "/images/sample/smallthumbnail04.png",
          link: "#04"
        },
      ],
    },
  },
};

export default meta;


