import FormBasicInformation from "../../components/organisms/FormBasicInformation.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof FormBasicInformation>;

const meta: Meta<typeof FormBasicInformation> = {
  title: "components/organisms/FormBasicInformation",
  component: FormBasicInformation,
  tags: ['autodocs'],
  render: (args) => ({
    components: { FormBasicInformation },
    setup() {
      return { args };
    },
    template: "<FormBasicInformation v-bind='args'>FormBasicInformation</FormBasicInformation>",
  }),
  args: {
    partsHidden: {},
  },
  argTypes: {
    partsHidden: {
      control: {
        type: "array",
      }
    },
  },
};

export const main: Story = {
  args: {
    partsHidden: {
      Name: 'visible',
      Oldkanji: 'visible',
      Romanname: 'visible',
      Kananame: 'visible',
      Honorific: 'visible',
      Gender: 'visible',
      Attendance: 'visible',
      Title: 'visible',
      Group: 'visible',
      Tag: 'visible',
      Allergy: 'visible',
      Birthday: 'visible',
      Deletecompanion: 'visible',
      Addcompanion: 'visible',
    }
  },
};

export default meta;