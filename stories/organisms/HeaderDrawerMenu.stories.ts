import HeaderDrawerMenu from "../../components/organisms/HeaderDrawerMenu.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof HeaderDrawerMenu>;

const meta: Meta<typeof HeaderDrawerMenu> = {
  title: "components/organisms/HeaderDrawerMenu",
  component: HeaderDrawerMenu,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { HeaderDrawerMenu },
    setup() {
      return { args };
    },
    template: "<HeaderDrawerMenu v-bind='args'></HeaderDrawerMenu>",
  }),
  args: {
    data: {
    },
    stateLogin: {
    },
    stateRecent: {
    },
    
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    },
    stateLogin: {
      control: {
        type: "object",
      }
    },
    stateRecent: {
      control: {
        type: "object",
      }
    }
  }
};


const ListHeaderDrawerMenu01 = {
  title: 'For You',
  subtitle: 'おすすめ',
  datail: [
    {
      class: "favorite",
      menu: "お気に入り",
      link: "#01"
    }, 
    {
      class: "history",
      menu: "最近チェックした商品",
    }, 
    {
      class: "diagnosis",
      menu: "無料サンプル請求",
      link: "#01"
    }, 
    {
      class: "download",
      menu: "ダウンロードコンテンツ",
      link: "#01"
    }, 
  ],
}

const ListHeaderDrawerMenu02 = {
  title: 'Category',
  subtitle: '商品カテゴリ',
  datail: [
    {
      class: "category",
      menu: "セットアイテム",
      innermenu: [
        {
          menu: "セットアイテム01",
          link: "#01"
        },
        {
          menu: "セットアイテム02",
          link: "#02"
        },
        {
          menu: "セットアイテム03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ペーパーアイテム",
      innermenu: [
        {
          menu: "ペーパーアイテム01",
          link: "#01"
        },
        {
          menu: "ペーパーアイテム02",
          link: "#02"
        },
        {
          menu: "ペーパーアイテム03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ウェルカムスペース・演出アイテム",
      innermenu: [
        {
          menu: "ウェルカムスペース・演出アイテム01",
          link: "#01"
        },
        {
          menu: "ウェルカムスペース・演出アイテム02",
          link: "#02"
        },
        {
          menu: "ウェルカムスペース・演出アイテム03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "プチギフト",
      innermenu: [
        {
          menu: "プチギフト01",
          link: "#01"
        },
        {
          menu: "プチギフト02",
          link: "#02"
        },
        {
          menu: "プチギフト03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "両親贈呈品・子育て感謝状",
      innermenu: [
        {
          menu: "両親贈呈品・子育て感謝状01",
          link: "#01"
        },
        {
          menu: "両親贈呈品・子育て感謝状02",
          link: "#02"
        },
        {
          menu: "両親贈呈品・子育て感謝状03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "結婚式アルバム",
      innermenu: [
        {
          menu: "結婚式アルバム01",
          link: "#01"
        },
        {
          menu: "結婚式アルバム02",
          link: "#02"
        },
        {
          menu: "結婚式アルバム03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "結婚報告はがき・年賀状",
      innermenu: [
        {
          menu: "結婚報告はがき・年賀状01",
          link: "#01"
        },
        {
          menu: "結婚報告はがき・年賀状02",
          link: "#02"
        },
        {
          menu: "結婚報告はがき・年賀状03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "引き出物宅配",
      innermenu: [
        {
          menu: "引き出物宅配01",
          link: "#01"
        },
        {
          menu: "引き出物宅配02",
          link: "#02"
        },
        {
          menu: "引き出物宅配03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "内祝い・お返しギフト",
      innermenu: [
        {
          menu: "内祝い・お返しギフト01",
          link: "#01"
        },
        {
          menu: "内祝い・お返しギフト02",
          link: "#02"
        },
        {
          menu: "内祝い・お返しギフト03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ギフト・プレゼント・贈答品",
      innermenu: [
        {
          menu: "ギフト・プレゼント・贈答品01",
          link: "#01"
        },
        {
          menu: "ギフト・プレゼント・贈答品02",
          link: "#02"
        },
        {
          menu: "ギフト・プレゼント・贈答品03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "オリジナルグッズ",
      innermenu: [
        {
          menu: "オリジナルグッズ01",
          link: "#01"
        },
        {
          menu: "オリジナルグッズ02",
          link: "#02"
        },
        {
          menu: "オリジナルグッズ03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "結婚式ムービー",
      innermenu: [
        {
          menu: "結婚式ムービー01",
          link: "#01"
        },
        {
          menu: "結婚式ムービー02",
          link: "#02"
        },
        {
          menu: "結婚式ムービー03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ウェディング・ブライダルアクセサリー",
      innermenu: [
        {
          menu: "ウェディング・ブライダルアクセサリー01",
          link: "#01"
        },
        {
          menu: "ウェディング・ブライダルアクセサリー02",
          link: "#02"
        },
        {
          menu: "ウェディング・ブライダルアクセサリー03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "ウェディングドレス",
      innermenu: [
        {
          menu: "ウェディングドレス01",
          link: "#01"
        },
        {
          menu: "ウェディングドレス02",
          link: "#02"
        },
        {
          menu: "ウェディングドレス03",
          link: "#03"
        },
      ],
    }, 
    {
      class: "category",
      menu: "二次会景品",
      innermenu: [
        {
          menu: "二次会景品01",
          link: "#01"
        },
        {
          menu: "二次会景品02",
          link: "#02"
        },
        {
          menu: "二次会景品03",
          link: "#03"
        },
      ],
    }, 
  ],
}

const ListHeaderDrawerMenu03 = {
  title: 'Support',
  subtitle: 'お客様サポート',
  datail: [
    {
      class: "help",
      menu: "サポートトップ",
      link: "#01"
    }, 
    {
      class: "",
      menu: "納期について",
      link: "#02"
    }, 
    {
      class: "",
      menu: "引き出物お届けまでのスケジュール",
      link: "#03"
    }, 
    {
      class: "",
      menu: "ご利用ガイド",
      link: "#04"
    }, 
    {
      class: "",
      menu: "よくある質問",
      link: "/question"
    }, 
    {
      class: "",
      menu: "お問い合わせ",
      link: "#06"
    }, 
  ],
}

const ListHeaderDrawerMenu04 = {
  title: 'About Us',
  subtitle: 'Favoriについて',
  datail: [
    {
      class: "",
      menu: "Favoriについて",
      link: "#01"
    }, 
    {
      class: "",
      menu: "お客様の声",
      link: "#02"
    }, 
    {
      class: "",
      menu: "ブログ&マガジン",
      link: "#03"
    }, 
    {
      class: "",
      menu: "サイトマップ",
      link: "#04"
    }, 
  ],
}

const ListHeaderDrawerMenu = {
  ListHeaderDrawerMenu01, 
  ListHeaderDrawerMenu02,
  ListHeaderDrawerMenu03,
  ListHeaderDrawerMenu04,
}

export const Main: Story = {
  args: {
    data: ListHeaderDrawerMenu,

    stateLogin: {
      login: true,
      name: '鈴木 このみ',
      date: '2023年4月1日',
    },

    stateRecent: {

      recent: [
        {
          thumbnail: "/images/sample/smallthumbnail01.png",
          link: "#01"
        },
        {
          thumbnail: "/images/sample/smallthumbnail02.png",
          link: "#02"
        },
        {
          thumbnail: "/images/sample/smallthumbnail03.png",
          link: "#03"
        },
        {
          thumbnail: "/images/sample/smallthumbnail04.png",
          link: "#04"
        },
      ],
    }

  }
};

export default meta;