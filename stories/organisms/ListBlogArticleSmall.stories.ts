import ListBlogArticleSmall from "../../components/organisms/ListBlogArticleSmall.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListBlogArticleSmall>;

const meta: Meta<typeof ListBlogArticleSmall> = {
  title: "components/organisms/ListBlogArticleSmall",
  component: ListBlogArticleSmall,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListBlogArticleSmall },
    setup() {
      return { args };
    },
    template: "<ListBlogArticleSmall v-bind='args'>ListBlogArticleSmall</ListBlogArticleSmall>",
  }),
  args: {
    data: {
    },
    assignClass: "",
    assignMenu: {
      description: false,
      tags: false,
    },
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    },
    assignClass: {
      control: {
        type: "string",
      }
    },
    assignMenu: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    data: {
      param: [
        {
          datail: [
            {
              title: '最大29％OFF 引き出物節約キャンペーン開催中！数量限定 1000冊のみのご用意です。',
              description: '『引き出物がお得に買える♪』と話題のFavoriの引き出物節約キャンペーン。前回のキャンペーン',
              link: '/article/00000000001',
              thumbnail: "/images/sample/thumbnail08.png",
              date: "2023.1.2",
              category: "引き出物宅配",
              tags: [
                '引き出物',
                "引き出物宅配",
              ],
            }, 
            {
              title: '最大29％OFF 引き出物節約キャンペーン開催中！数量限定 1000冊のみのご用意です。',
              description: '『引き出物がお得に買える♪』と話題のFavoriの引き出物節約キャンペーン。前回のキャンペーン',
              link: '/article/00000000002',
              thumbnail: "/images/sample/thumbnail08.png",
              date: "2023.1.2",
              category: "引き出物宅配",
              tags: [
                '引き出物',
                "引き出物宅配",
              ],
            }, 
            {
              title: '最大29％OFF 引き出物節約キャンペーン開催中！数量限定 1000冊のみのご用意です。',
              description: '『引き出物がお得に買える♪』と話題のFavoriの引き出物節約キャンペーン。前回のキャンペーン',
              link: '/article/00000000003',
              thumbnail: "/images/sample/thumbnail08.png",
              date: "2023.1.2",
              category: "引き出物宅配",
              tags: [
                '引き出物',
                "引き出物宅配",
              ],
            }, 
            {
              title: '最大29％OFF 引き出物節約キャンペーン開催中！数量限定 1000冊のみのご用意です。',
              description: '『引き出物がお得に買える♪』と話題のFavoriの引き出物節約キャンペーン。前回のキャンペーン',
              link: '/article/00000000001',
              thumbnail: "/images/sample/thumbnail08.png",
              date: "2023.1.2",
              category: "引き出物宅配",
              tags: [
                '引き出物',
                "引き出物宅配",
              ],
            }, 
            {
              title: '最大29％OFF 引き出物節約キャンペーン開催中！数量限定 1000冊のみのご用意です。',
              description: '『引き出物がお得に買える♪』と話題のFavoriの引き出物節約キャンペーン。前回のキャンペーン',
              link: '/article/00000000002',
              thumbnail: "/images/sample/thumbnail08.png",
              date: "2023.1.2",
              category: "引き出物宅配",
              tags: [
                '引き出物',
                "引き出物宅配",
              ],
            }, 
            {
              title: '最大29％OFF 引き出物節約キャンペーン開催中！数量限定 1000冊のみのご用意です。',
              description: '『引き出物がお得に買える♪』と話題のFavoriの引き出物節約キャンペーン。前回のキャンペーン',
              link: '/article/00000000003',
              thumbnail: "/images/sample/thumbnail08.png",
              date: "2023.1.2",
              category: "引き出物宅配",
              tags: [
                '引き出物',
                "引き出物宅配",
              ],
            }, 
          ],
        },
      ],
    },
    assignClass: "fullparams",
    assignMenu: {
      description: false,
      tags: false,
    }
  },
};

export default meta;