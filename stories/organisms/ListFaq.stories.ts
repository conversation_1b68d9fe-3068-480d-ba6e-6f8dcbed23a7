import ListFaq from "../../components/organisms/ListFaq.vue";

export default {
  title: "components/organisms/ListFaq",
  component: ListFaq,
  tags: ['organisms'],
  render: (args) => ({
    // Components used in your story `template` are defined in the `components` object
    components: {
      ListFaq,
    },
    // The story's `args` need to be mapped into the template through the `setup()` method
    setup() {
      // Story args can be spread into the returned object
      return {
        ...args,
      };
    },
    // Then, the spread values can be accessed directly in the template
    template: '<ListFaq></ListFaq>',
  }),
  parameters: {
    // More on how to position stories at: https://storybook.js.org/docs/vue/configure/story-layout
    layout: 'fullscreen',
  },
};

export const primary = {
  args: {
    propsHeaderParams: {
      noticeBadges: 3,
      favoriteBadges: 2,
      cartBadges: 1,
    },
  },
};