import ListGiftFixed from "../../components/organisms/ListGiftFixed.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListGiftFixed>;

const meta: Meta<typeof ListGiftFixed> = {
  title: "components/organisms/ListGiftFixed",
  component: ListGiftFixed,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListGiftFixed },
    setup() {
      return { args };
    },
    template: "<ListGiftFixed v-bind='args'>ListGiftFixed</ListGiftFixed>",
  }),
  args: {
    data: {
    }
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    data: {
      datail: [
        {
          status: "beforeDelivery",
          name: '大野 将也',
          honorific: '様',
          labels: [
            {
              class: "guestPerson",
              title: "新婦側ゲスト",
            },
            {
              class: "delivery",
              title: "引き出物宅配",
            },
          ],
          address: "〒000-0000\r\n京都府京都市北区小山花ノ木町1-2-3",
          telnumber: "090-0000-0000",
          arrival: "",
          invoice: "",
          thumbnails: [
            {
              class: "",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
          ],
        }, 
        {
          status: "afterDelivery",
          name: '大野 将也',
          honorific: '様',
          labels: [
            {
              class: "standbyDelivery",
              title: "配送準備中",
            },
          ],
          address: "〒000-0000\r\n京都府京都市北区小山花ノ木町1-2-3",
          telnumber: "090-0000-0000",
          arrival: "2023年4月1日に到着予定",
          invoice: "1234-5678-9012",
          thumbnails: [
            {
              class: "",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
          ],
        }, 
        {
          status: "afterDelivery",
          name: '大野 将也',
          honorific: '様',
          labels: [
            {
              class: "completionDelivery",
              title: "配送準備完了",
            },
          ],
          address: "〒000-0000\r\n京都府京都市北区小山花ノ木町1-2-3",
          telnumber: "090-0000-0000",
          arrival: "2023年4月1日に到着予定",
          invoice: "1234-5678-9012",
          thumbnails: [
            {
              class: "",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
            {
              class: "subGift",
              thumbnail: "/images/sample/thumbnail01.png",
            },
          ],
        }, 
      ],
    }
  },
};

export default meta;