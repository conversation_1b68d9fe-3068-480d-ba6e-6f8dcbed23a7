import ListItemsSmall from "../../components/organisms/ListItemsSmall.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ListItemsSmall>;

const meta: Meta<typeof ListItemsSmall> = {
  title: "components/organisms/ListItemsSmall",
  component: ListItemsSmall,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ListItemsSmall },
    setup() {
      return { args };
    },
    template: "<ListItemsSmall v-bind='args'>ListItemsSmall</ListItemsSmall>",
  }),
  args: {
    data: {
    }
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    data: {
      param: [
        {
          layout: 'twoColumn',
          datail: [
            {
              title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
              target: 'gift00000000001',
              thumbnail: "/images/sample/thumbnail04.png",
              labels: [
                "数量限定キャンペーン",
              ],
              price: "4510",
              priceStrike: "6600",
            }, 
            {
              title: 'ANTQUE マジカルデニッシュ(メイプル×ショコラ)',
              target: 'gift00000000002',
              thumbnail: "/images/sample/thumbnail05.png",
              labels: [
              ],
              price: "4510",
            }, 
            {
              title: 'Dolce duo バウムクーヘン(モダンブラウン)',
              target: 'gift00000000003',
              thumbnail: "/images/sample/thumbnail04.png",
              labels: [
                "数量限定キャンペーン",
              ],
              price: "4510",
              priceStrike: "6600",
            }, 
          ],
        },
        {
          layout: 'threeColumn',
          datail: [
            {
              title: 'ラッピング フルール',
              target: 'gift00000000001',
              thumbnail: "/images/sample/thumbnail04.png",
              labels: [
                "数量限定キャンペーン",
              ],
              price: "4510",
              priceStrike: "6600",
            }, 
            {
              title: 'ラッピング フルール',
              target: 'gift00000000002',
              thumbnail: "/images/sample/thumbnail05.png",
              labels: [
              ],
              price: "4510",
            }, 
            {
              title: 'ラッピング フルール',
              target: 'gift00000000003',
              thumbnail: "/images/sample/thumbnail04.png",
              labels: [
                "数量限定キャンペーン",
              ],
              price: "4510",
              priceStrike: "6600",
            }, 
          ],
        },
      ],
    },
  },
};

export default meta;