import ModalManageCompanion from "../../components/organisms/ModalManageCompanion.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ModalManageCompanion>;

const meta: Meta<typeof ModalManageCompanion> = {
  title: "components/organisms/ModalManageCompanion",
  component: ModalManageCompanion,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ModalManageCompanion },
    setup() {
      return { args };
    },
    template: "<ModalManageCompanion v-bind='args'>ModalManageCompanion</ModalManageCompanion>",
  }),
  args: {
  },
  argTypes: {
    name: String,
    honorific: String,
  }
};


export const Main: Story = {
  args: {
    name: '〇〇',
    honorific: '様',
  },
};

export default meta;