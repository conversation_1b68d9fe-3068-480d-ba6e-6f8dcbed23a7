import ModalManageGroup from "../../components/organisms/ModalManageGroup.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ModalManageGroup>;

const meta: Meta<typeof ModalManageGroup> = {
  title: "components/organisms/ModalManageGroup",
  component: ModalManageGroup,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { ModalManageGroup },
    setup() {
      return { args };
    },
    template: "<ModalManageGroup v-bind='args'>ModalManageGroup</ModalManageGroup>",
  }),
  args: {
  },
  argTypes: {
  }
};


export const Main: Story = {
  args: {
  },
};

export default meta;