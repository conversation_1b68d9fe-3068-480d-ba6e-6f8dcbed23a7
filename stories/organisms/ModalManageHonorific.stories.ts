import ModalManageHonorific from "../../components/organisms/ModalManageHonorific.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ModalManageHonorific>;

const meta: Meta<typeof ModalManageHonorific> = {
  title: "components/organisms/ModalManageHonorific",
  component: ModalManageHonorific,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { ModalManageHonorific },
    setup() {
      return { args };
    },
    template: "<ModalManageHonorific v-bind='args'>ModalManageHonorific</ModalManageHonorific>",
  }),
  args: {
  },
  argTypes: {
  }
};


export const Main: Story = {
  args: {
  },
};

export default meta;