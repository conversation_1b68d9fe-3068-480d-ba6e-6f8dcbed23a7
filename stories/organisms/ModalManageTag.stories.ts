import ModalManageTag from "../../components/organisms/ModalManageTag.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ModalManageTag>;

const meta: Meta<typeof ModalManageTag> = {
  title: "components/organisms/ModalManageTag",
  component: ModalManageTag,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { ModalManageTag },
    setup() {
      return { args };
    },
    template: "<ModalManageTag v-bind='args'>ModalManageTag</ModalManageTag>",
  }),
  args: {
  },
  argTypes: {
  }
};


export const Main: Story = {
  args: {
  },
};

export default meta;