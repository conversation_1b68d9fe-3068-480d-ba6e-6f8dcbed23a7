import ModalManageTitle from "../../components/organisms/ModalManageTitle.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ModalManageTitle>;

const meta: Meta<typeof ModalManageTitle> = {
  title: "components/organisms/ModalManageTitle",
  component: ModalManageTitle,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { ModalManageTitle },
    setup() {
      return { args };
    },
    template: "<ModalManageTitle v-bind='args'>ModalManageTitle</ModalManageTitle>",
  }),
  args: {
  },
  argTypes: {
  }
};


export const Main: Story = {
  args: {
  },
};

export default meta;