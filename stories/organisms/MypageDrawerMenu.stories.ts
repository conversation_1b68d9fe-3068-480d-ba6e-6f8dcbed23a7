import MypageDrawerMenu from "../../components/organisms/MypageDrawerMenu.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof MypageDrawerMenu>;

const meta: Meta<typeof MypageDrawerMenu> = {
  title: "components/organisms/MypageDrawerMenu",
  component: MypageDrawerMenu,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { MypageDrawerMenu },
    setup() {
      return { args };
    },
    template: "<MypageDrawerMenu v-bind='args'>MypageDrawerMenu</MypageDrawerMenu>",
  }),
  args: {
  },
  argTypes: {
    data: {
      stateLogin: {
        type: "object",
      },
      stateBadge: {
        type: "object",
      },
    }
  }
};


export const Main: Story = {
  args: {

    stateLogin: {
      login: true,
      name: '鈴木 このみ',
      date: '2023年4月1日',
    },
    
    stateBadge: {
      noticeBadges: 3,
      favoriteBadges: 2,
      cartBadges: 1,
    }
    
  },
};

export default meta;