import ShowCartDetails from "../../components/organisms/ShowCartDetails.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ShowCartDetails>;

const meta: Meta<typeof ShowCartDetails> = {
  title: "components/organisms/ShowCartDetails",
  component: ShowCartDetails,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ShowCartDetails },
    setup() {
      return { args };
    },
    template: "<ShowCartDetails v-bind='args'>ShowCartDetails</ShowCartDetails>",
  }),
  args: {
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    data: {
      param: [
        {
          datail: [
            {
              title: 'Memphis A Bluege[GG]',
              id: 'gift00000000001',
              category: "招待状",
              thumbnail: "/images/sample/thumbnail07.png",
              price: "429",
              priceUnit: "１セット",
              orderd: "",
              option: [
              ],
              addevent: [
                {
                  class: "toedititem",
                  menu: "作成中のアイテムに戻す",
                },
                {
                  class: "tolater",
                  menu: "あとで買う",
                },
              ],
            }, 
            {
              title: 'Memphis A Bluege[GG]',
              id: 'gift00000000002',
              category: "招待状",
              thumbnail: "/images/sample/thumbnail07.png",
              price: "429",
              priceUnit: "１セット",
              orderd: "",
              option: [
              ],
              addevent: [
                {
                  class: "totrash",
                  menu: "削除",
                },
                {
                  class: "tolater",
                  menu: "あとで買う",
                },
              ],
            }, 
            {
              title: 'Memphis A Bluege[GG]',
              id: 'gift00000000002',
              category: "招待状",
              thumbnail: "/images/sample/thumbnail07.png",
              price: "495",
              priceUnit: "１セット",
              orderd: "20",
              orderdUnit: "セット",
              option: [
                {
                  item: "封筒",
                  substance: "宛名印刷あり 2枚｜宛名印刷なし 18枚",
                },
                {
                  item: "返信はがき",
                  substance: "新郎用 10枚｜新婦用 10枚",
                },
                {
                  item: "はがき裏面カスタマイズ",
                  substance: "希望する",
                },
                {
                  item: "付箋",
                  substance: "5枚",
                },
                {
                  item: "予備封筒",
                  substance: "1枚",
                },
              ],
              addevent: [
              ],
            }, 
          ],
        },
      ],
    }
  },
};

export default meta;