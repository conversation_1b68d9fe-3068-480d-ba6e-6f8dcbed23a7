import ShowDueDate from "../../components/organisms/ShowDueDate.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ShowDueDate>;

const meta: Meta<typeof ShowDueDate> = {
  title: "components/organisms/ShowDueDate",
  component: ShowDueDate,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ShowDueDate },
    setup() {
      return { args };
    },
    template: "<ShowDueDate />",
  }),
  args: {
  },
  argTypes: {
  }
};

export const Main: Story = {
  args: {
  },
};

export default meta;