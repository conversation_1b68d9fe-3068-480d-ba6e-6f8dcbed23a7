import ShowFooterBar<PERSON><PERSON> from "../../components/organisms/ShowFooterBarFrow.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ShowFooterBarFrow>;

const meta: Meta<typeof ShowFooterBarFrow> = {
  title: "components/organisms/ShowFooterBarFrow",
  component: ShowFooterBarFrow,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ShowFooterBarFrow },
    setup() {
      return { args };
    },
    template: "<ShowFooterBarFrow v-bind='args'>ShowFooterBarFrow</ShowFooterBarFrow>",
  }),
  args: {
    data: {
    },
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const message_on: Story = {
  args: {
    data: {
      datail: [
        {
          type: 'message',
          data: [
            {
              strong: '3',
              text: '件 選択されています',
            },
          ],
        },
        {
          type: 'button',
          data: [
            {
              buttonsize: 'harf',
              color: 'glay',
              disabled: false,
              slot: "戻る",
              link: "#01link",
            },
            {
              buttonsize: 'harf',
              color: 'accent',
              disabled: false,
              slot: "送付先リストに追加",
              link: "#0link",
            },
          ],
        },
      ],
    },
  },
};

export const button_harf: Story = {
  args: {
    data: {
      datail: [
        {
          type: 'button',
          data: [
            {
              buttonsize: 'harf',
              color: 'glay',
              disabled: false,
              slot: "キャンセル",
              link: "#01link",
            },
            {
              buttonsize: 'harf',
              color: 'accent',
              disabled: false,
              slot: "送付先リストに追加",
              link: "#0link",
            },
          ],
        },
      ],
    },
  },
};

export const button_full: Story = {
  args: {
    data: {
      datail: [
        {
          type: 'button',
          data: [
            {
              buttonsize: 'full',
              color: 'accent',
              disabled: false,
              slot: "送付先リスト・送り分けを変更する",
              link: "#01link",
            },
            {
              buttonsize: 'harf',
              color: 'glay',
              disabled: false,
              slot: "戻る",
              link: "#01link",
            },
            {
              buttonsize: 'harf',
              color: 'glay',
              disabled: true,
              slot: "お支払い・申し込み",
              link: "#0link",
            },
          ],
        },
      ],
    },
  },
};

export default meta;