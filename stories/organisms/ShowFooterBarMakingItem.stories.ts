import ShowFooterBarMakingItem from "../../components/organisms/ShowFooterBarMakingItem.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ShowFooterBarMakingItem>;

const meta: Meta<typeof ShowFooterBarMakingItem> = {
  title: "components/organisms/ShowFooterBarMakingItem",
  component: ShowFooterBarMakingItem,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ShowFooterBarMakingItem },
    setup() {
      return { args };
    },
    template: "<ShowFooterBarMakingItem v-bind='args'>ShowFooterBarMakingItem</ShowFooterBarMakingItem>",
  }),
  args: {
    data: {
    },
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const Payment_on: Story = {
  args: {
    data: {
      payment: {
        status: "Payment",
        display: [
        ],
        subtotal: [
          {
            article: '小計（税込）',
            amount: 4950,
          },
        ],
        calculation: [
        ],
        discount: [
        ],
        total: [
          {
            article: 'お支払い金額合計（税込）',
            amount: 4950,
          },
        ],
        point: [
          {
            article: '獲得ポイント',
            amount: 49,
          },
        ],
      },
      button: {
        slot: "次へ進む",
        link: "#01link",
      }
    },
  },
};

export const Payment_minimum: Story = {
  args: {
    data: {
      payment: {
        status: "Payment",
        display: [
        ],
        subtotal: [
        ],
        calculation: [
        ],
        discount: [
        ],
        total: [
          {
            article: 'お支払い金額合計（税込）',
            amount: 4950,
          },
        ],
        point: [
          {
            article: '獲得ポイント',
            amount: 49,
          },
        ],
      },
      button: {
        slot: "次へ進む",
        link: "#01link",
      }
    },
  },
};

export const Payment_off: Story = {
  args: {
    data: {
      payment: {
        status: "Payment",
        display: [
        ],
        subtotal: [
        ],
        calculation: [
        ],
        discount: [
        ],
        total: [
        ],
        point: [
        ],
      },
      button: {
        slot: "次へ進む",
        link: "#01link",
      }
    },
  },
};

export default meta;