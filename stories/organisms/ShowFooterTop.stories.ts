import ShowFooterTop from "../../components/organisms/ShowFooterTop.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ShowFooterTop>;

const meta: Meta<typeof ShowFooterTop> = {
  title: "components/organisms/ShowFooterTop",
  component: ShowFooterTop,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ShowFooterTop },
    setup() {
      return { args };
    },
    template: "<ShowFooterTop v-bind='args'>ShowFooterTop</ShowFooterTop>",
  }),
  args: {
    banner: {
    },
    schedule: {
    },
    abouts: {
    },
  },
  argTypes: {
    banner: {
      control: {
        type: "Array",
      }
    },
    schedule: {
      control: {
        type: "Array",
      }
    },
    abouts: {
      control: {
        type: "Array",
      }
    }
  }
};

export const main: Story = {
  args: {
    banner: {
      datail: [
        {
          alt: "バナー1",
          image: "/images/sample/banner01.png",
          link: '/article/00000000001',
          target: "blank",
        },
        {
          alt: "バナー2",
          image: "/images/sample/banner02.png",
          link: '/article/00000000002',
        },
      ],
    },
    schedule: {
      datail: [
        {
          label: '招待状',
          schedule: [
            {
              type: '通常納期',
              shipdate: '8月20日(金)',
            },
            {
              type: '特急納期',
              shipdate: '8月18日(水)',
            },
            {
              type: '超特急納期',
              shipdate: '8月16日(月)',
            },
          ],
        },
        {
          label: '席次表・席札<wbr>メニュー表',
          schedule: [
            {
              type: '通常納期2',
              shipdate: '8月20日(金)',
            },
            {
              type: '特急納期2',
              shipdate: '8月18日(水)',
            },
            {
              type: '超特急納期2',
              shipdate: '8月16日(月)',
            },
          ],
        },
        {
          label: 'ウェルカム<wbr>ボード',
          schedule: [
            {
              type: '通常納期3',
              shipdate: '8月20日(金)',
            },
            {
              type: '特急納期3',
              shipdate: '8月18日(水)',
            },
            {
              type: '超特急納期3',
              shipdate: '8月16日(月)',
            },
          ],
        },
        {
          label: 'プチ<wbr>ギフト',
          schedule: [
            {
              type: '通常納期4',
              shipdate: '8月20日(金)',
            },
            {
              type: '特急納期4',
              shipdate: '8月18日(水)',
            },
            {
              type: '超特急納期4',
              shipdate: '8月16日(月)',
            },
          ],
        },
        {
          label: '結婚報告<wbr>はがき',
          schedule: [
            {
              type: '通常納期5',
              shipdate: '8月20日(金)',
            },
            {
              type: '特急納期5',
              shipdate: '8月18日(水)',
            },
            {
              type: '超特急納期5',
              shipdate: '8月16日(月)',
            },
          ],
        },
      ],
    },
    abouts: {
      datail: [
        {
          alt: "Favoriの充実したサービスについて",
          image: "/images/sample/about.png",
          link: '/article/00000000001',
        },
        {
          alt: "お問い合わせ",
          image: "/images/sample/contact.png",
          link: '/article/00000000002',
        },
      ],
    },
  },
};

export default meta;