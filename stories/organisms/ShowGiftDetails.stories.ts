import ShowGiftDetails from "../../components/organisms/ShowGiftDetails.vue";

export default {
  title: "components/organisms/ShowGiftDetails",
  component: ShowGiftDetails,
};

const Template = (args: any) => ({
  components: { ShowGiftDetails },
  setup() {
    return {
      args,
    };
  },
  template: `
    <ShowGiftDetails v-bind="args">ShowGiftDetails</ShowGiftDetails>
  `,
});

export const Primary: any = Template.bind({});
Primary.args = {
  type: "ShowGiftDetails",
};