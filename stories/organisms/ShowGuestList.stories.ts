import ShowGuestList from "../../components/organisms/ShowGuestList.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ShowGuestList>;

const meta: Meta<typeof ShowGuestList> = {
  title: "components/organisms/ShowGuestList",
  component: ShowGuestList,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ShowGuestList },
    setup() {
      return { args };
    },
    template: "<ShowGuestList v-bind='args'>ShowGuestList</ShowGuestList>",
  }),
  args: {
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    },
    partsHidden: {
      control: {
        type: "object",
      }
    }
  }
};

export const Main: Story = {
  args: {
    data: {
      label: 'メインリスト',
      guests: [
        {
          name: '中谷 隆之介',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '佐伯 美佐',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '日下 勝久',
          icon: '/images/sample/guest_image.png',
          status: '欠席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '佐伯 美佐',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '日下 勝久',
          icon: '/images/sample/guest_image.png',
          status: '欠席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '佐伯 美佐',
          icon: '/images/sample/guest_image.png',
          status: '出席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
        {
          name: '日下 勝久',
          icon: '/images/sample/guest_image.png',
          status: '欠席',
          group: '高校友人',
          tags: [
            '新婦側ゲスト',
            '宅配',
          ],
        },
      ],

    },

    partsHidden: {
      manage: 'hidden',
      statusList: 'hidden',
      status: 'hidden',
      icon: 'hidden',
      group: 'hidden',
    }
  },
};

export default meta;