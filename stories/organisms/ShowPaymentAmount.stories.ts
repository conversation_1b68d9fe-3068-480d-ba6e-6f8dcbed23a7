import ShowPaymentAmount from "../../components/organisms/ShowPaymentAmount.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ShowPaymentAmount>;

const meta: Meta<typeof ShowPaymentAmount> = {
  title: "components/organisms/ShowPaymentAmount",
  component: ShowPaymentAmount,
  tags: ['autodocs'],
  render: (args) => ({
    components: { ShowPaymentAmount },
    setup() {
      return { args };
    },
    template: "<ShowPaymentAmount v-bind='args'></ShowPaymentAmount>",
  }),
  args: {
  },
  argTypes: {
    data: {
      control: {
        type: "object",
      }
    }
  }
};

export const AdditionalPayment: Story = {
  args: {
    data: {
      display: [
        {
          article: '支払い済み金額（税込）',
          amount: 110000,
        },
      ],
      subtotal: [
      ],
      calculation: [
        {
          article: '追加購入商品',
          amount: 110000,
        },
        {
          article: 'コットンバッグ追加',
          amount: 2800,
        },
        {
          article: '離島送料',
          amount: 3300,
        },
        {
          article: 'のしへの旧字・外字の印刷',
          amount: 0,
        },
      ],
      discount: [
        {
          article: 'クーポン値引き',
          amount: -1000,
        },
        {
          article: 'ウォレット利用',
          amount: -30000,
        },
        {
          article: 'ポイント利用',
          amount: -495,
        },
      ],
      total: [
        {
          article: '追加お支払い金額合計（税込）',
          amount: 84605,
        },
      ],
      point: [
        {
          article: '獲得ポイント',
          amount: 846,
        },
      ],
    }
  },
};

export const Payment: Story = {
  args: {
    data: {
      display: [
      ],
      subtotal: [
        {
          article: '小計（税込）',
          amount: 39578,
        },
      ],
      calculation: [
        {
          article: '送料',
          amount: 800,
        },
        {
          article: '特急納期料金',
          amount: 3300,
        },
        {
          article: '超特急納期料金',
          amount: 5500,
        },
      ],
      discount: [
      ],
      total: [
        {
          article: 'お支払い金額合計（税込）',
          amount: 49178,
        },
      ],
      point: [
        {
          article: '獲得ポイント',
          amount: 491,
        },
      ],
    }
  },
};

export default meta;