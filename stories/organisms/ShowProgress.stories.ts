import ShowProgress from "../../components/organisms/ShowProgress.vue";

export default {
  title: "components/organisms/ShowProgress",
  component: ShowProgress,
};

const Template = (args: any) => ({
  components: { ShowProgress },
  setup() {
    return {
      args,
    };
  },
  template: `
    <ShowProgress v-bind="args">ShowProgress</ShowProgress>
  `,
});

export const Primary: any = Template.bind({});
Primary.args = {
  type: "ShowProgress",
};