import complete from "../../../../pages/mypage/gift/complete.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof complete>;

const meta: Meta<typeof complete> = {
  title: "pages/mypage/complete",
  component: complete,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { complete },
    setup() {
      return { args };
    },
    template: "<complete v-bind='args'>complete</complete>",
  }),
  args: {
    thisPageState: {
    },
    benefitsState: {
    }
  },
  argTypes: {
    thisPageState: {
      control: {
        type: "string",
      }
    },
    benefitsState: {
      control: {
        type: "string",
      }
    },
  }
};

export const Main: Story = {
  args: {
    thisPageState: 'emptypayment',
    benefitsState: 'unnecessaryBenefits',
  },
};


export const addPayment: Story = {
  args: {
    thisPageState: 'addPayment',
    benefitsState: 'unnecessaryBenefits',
  },
};


export const rePayment: Story = {
  args: {
    thisPageState: 'repayment',
    benefitsState: 'unnecessaryBenefits',
  },
};

export const necessaryBenefits: Story = {
  args: {
    thisPageState: 'repayment',
    benefitsState: 'necessaryBenefits',
  },
};

export const outBenefits: Story = {
  args: {
    thisPageState: 'repayment',
    benefitsState: 'outBenefits',
  },
};

export const unnecessaryBenefits: Story = {
  args: {
    thisPageState: 'repayment',
    benefitsState: 'unnecessaryBenefits',
  },
};

export default meta;