import confirm from "../../../../pages/mypage/gift/confirm.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof confirm>;

const meta: Meta<typeof confirm> = {
  title: "pages/mypage/confirm",
  component: confirm,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { confirm },
    setup() {
      return { args };
    },
    template: "<confirm v-bind='args'>confirm</confirm>",
  }),
  args: {
    thisPageState: {
    }
  },
  argTypes: {
    thisPageState: {
      control: {
        type: "string",
      }
    }
  }
};

export const Main: Story = {
  args: {
    thisPageState: 'addPayment'
  },
};

export const rePayment: Story = {
  args: {
    thisPageState: 'repayment'
  },
};


export const emptyPayment: Story = {
  args: {
    thisPageState: 'emptypayment'
  },
};

export default meta;