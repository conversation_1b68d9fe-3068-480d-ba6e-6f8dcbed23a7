import guest_list from "../../../../pages/mypage/guest-list/index.vue";
import type { <PERSON>a, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof guest_list>;

const meta: Meta<typeof guest_list> = {
  title: "pages/mypage/guest_list",
  component: guest_list,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { guest_list },
    setup() {
      return { args };
    },
    template: "<guest_list v-bind='args'>guest_list</guest_list>",
  }),
  args: {
    apiGuestList: {
    }
  },
  argTypes: {
    apiGuestList: Array
  }
};

export const Main: Story = {
  args: {
  },
};

export const Sub: Story = {
  args: {
    apiGuestList: {
      datail: [
        {
          label: 'メインリスト',
          guests: [
          ],
    
        },
      ]
    }    
  },
};

export default meta;