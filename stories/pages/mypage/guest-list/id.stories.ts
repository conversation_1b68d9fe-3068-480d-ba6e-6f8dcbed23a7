import guest_list_id from "../../../../pages/mypage/guest-list/[id].vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof guest_list_id>;

const meta: Meta<typeof guest_list_id> = {
  title: "pages/mypage/guest_list_id",
  component: guest_list_id,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { guest_list_id },
    setup() {
      return { args };
    },
    template: "<guest_list_id v-bind='args'>guest_list_id</guest_list_id>",
  }),
  args: {
    apiGuestList: {
    }
  },
  argTypes: {
    apiGuestList: Array
  }
};

export const Main: Story = {
  args: {
  },
};

export const Sub: Story = {
  args: {
    apiGuestList: {
      datail: [
        {
          label: 'メインリスト',
          guests: [
          ],
    
        },
      ]
    }    
  },
};

export default meta;