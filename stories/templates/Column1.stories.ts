import column1 from "../../components/templates/column1.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof column1>;

const meta: Meta<typeof column1> = {
  title: "components/templates/column1",
  component: column1,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { column1 },
    setup() {
      return { args };
    },
    template: "<column1 v-bind='args'>column1</column1>",
  }),
  args: {
  },
  argTypes: {
  }
};

export const Main: Story = {
  args: {
  },
};

export default meta;