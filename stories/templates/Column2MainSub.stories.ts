import Column2MainSub from "../../components/templates/Column2MainSub.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof Column2MainSub>;

const meta: Meta<typeof Column2MainSub> = {
  title: "components/templates/Column2MainSub",
  component: Column2MainSub,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { Column2MainSub },
    setup() {
      return { args };
    },
    template: '<Column2MainSub v-bind="args"><template #title><h1><div class="section-inner"></div></h1></template>  <template #main><div><ul class="itemcart"><li class="items"><div class="thumbnail"><img src="/images/sample/thumbnail07.png" alt="Memphis A Bluege[GG]"></div><div class="datail"><strong class="category">招待状</strong><p class="title">Memphis A Bluege[GG]</p><div class="wrapprice"><strong class="price">¥429</strong><span class="tax">(税込)</span><span class="unit">１セット</span></div><div class="wrapevent"><span class="toedititem btntodo" data-id="gift00000000001" data-class="toedititem">作成中のアイテムに戻す</span><span class="tolater btntodo" data-id="gift00000000001" data-class="tolater">あとで買う</span></div></div></li></ul></div></template>  <template #sub><div class="makingToolbar onpayment"><div class="wrappayment toolbar"><dl><div class="subtotal"><dt>小計（税込）</dt><dd>¥ 4,950</dd></div><div class="totalAmount"><dt>お支払い金額合計（税込）</dt><dd class="total">¥ 4,950</dd></div><div class="point"><dt class="getpoint">獲得ポイント</dt><dd class="getpoint">+49pt</dd></div></dl></div><div class="flow"><a aria-current="page" href="/test2#01link" class="router-link-active router-link-exact-active button--main align--center button--lg button--spmd"><span>次へ進む</span></a></div></div></template></Column2MainSub>',
  }),
  args: {
  },
  argTypes: {
  }
};

export const Main: Story = {
  args: {
  },
};

export default meta;