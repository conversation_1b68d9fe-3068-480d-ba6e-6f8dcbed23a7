import Column2SubMain from "../../components/templates/Column2SubMain.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof Column2SubMain>;

const meta: Meta<typeof Column2SubMain> = {
  title: "components/templates/Column2SubMain",
  component: Column2SubMain,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { Column2SubMain },
    setup() {
      return { args };
    },
    template: '<Column2SubMain v-bind="args"><template #title><div class="titleCategoryWrap"><Titleh1 class="titleCategory">Titleh1</div></template><template #sub><div class="l-column2-sub"><!--[--><!--[--><!--[--><p class="title">カテゴリ<!----></p><ul class=""><!--[--><li class="withicon category"><!--[--><span class="subtitle-innermenu acdtrg is-close">セットアイテム</span><ul class="innermenu acdsbj is-close"><!--[--><li><a aria-current="page" href="/test3#01" class="router-link-active router-link-exact-active">セットアイテム01</a></li><li><a aria-current="page" href="/test3#02" class="router-link-active router-link-exact-active">セットアイテム02</a></li><li><a aria-current="page" href="/test3#03" class="router-link-active router-link-exact-active">セットアイテム03</a></li><!--]--></ul><!--]--></li><li class="withicon category"><!--[--><span class="subtitle-innermenu acdtrg is-close">ペーパーアイテム</span><ul class="innermenu acdsbj is-close"><!--[--><li><a aria-current="page" href="/test3#01" class="router-link-active router-link-exact-active">ペーパーアイテム01</a></li><li><a aria-current="page" href="/test3#02" class="router-link-active router-link-exact-active">ペーパーアイテム02</a></li><li><a aria-current="page" href="/test3#03" class="router-link-active router-link-exact-active">ペーパーアイテム03</a></li><!--]--></ul><!--]--></li></ul><!--]--><!--]--><!--]--></div></template><template #main><img src="/images/sample/Mask_group.png" alt=""></template><template #column2after>column2after</template></Column2SubMain>',
  }),
  args: {
  },
  argTypes: {
  },
};

export const Main: Story = {
  args: {
  },
};

export default meta;