import ModalFull from "../../components/templates/ModalFull.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ModalFull>;

const meta: Meta<typeof ModalFull> = {
  title: "components/templates/ModalFull",
  component: ModalFull,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { ModalFull },
    setup() {
      return { args };
    },
    template: "<ModalFull v-bind='args'>ModalFull</ModalFull>",
  }),
  args: {
  },
  argTypes: {
  }
};

export const Main: Story = {
  args: {
  },
};

export default meta;