import ModalSmall from "../../components/templates/ModalSmall.vue";
import type { Meta, StoryObj } from "@storybook/vue3";

type Story = StoryObj<typeof ModalSmall>;

const meta: Meta<typeof ModalSmall> = {
  title: "components/templates/ModalSmall",
  component: ModalSmall,
  tags: ['fullscreen'],
  render: (args) => ({
    components: { ModalSmall },
    setup() {
      return { args };
    },
    template: "<ModalSmall v-bind='args'>ModalSmall</ModalSmall>",
  }),
  args: {
  },
  argTypes: {
  }
};

export const Main: Story = {
  args: {
  },
};

export default meta;