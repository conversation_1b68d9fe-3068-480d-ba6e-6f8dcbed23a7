export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Date: { input: any; output: any; }
  DateTime: { input: any; output: any; }
  Json: { input: any; output: any; }
  Upload: { input: any; output: any; }
};

/** Account type enum */
export enum AccountTypeEnum {
  /** Current */
  Current = 'Current',
  /** Normal */
  Normal = 'Normal',
  /** Savings */
  Savings = 'Savings'
}

export type Admin = {
  __typename?: 'Admin';
  created_at: Scalars['DateTime']['output'];
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  email: Scalars['String']['output'];
  email_verified_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** 送金 */
  money_transfers?: Maybe<Array<MoneyTransfer>>;
  name: Scalars['String']['output'];
  password: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
};

export type AdminCreateAdminInput = {
  /** メールアドレス */
  email: Scalars['String']['input'];
  /** 管理者名 */
  name: Scalars['String']['input'];
  /** パスワード */
  password: Scalars['String']['input'];
};

export type AdminCreateBasicVariationTemplateInput = {
  /** 色延ばし有無 */
  color_extension?: InputMaybe<Scalars['Boolean']['input']>;
  /** 折り線種類 */
  fold_line_type?: InputMaybe<Scalars['Int']['input']>;
  /** サイズ(高さ) */
  height?: InputMaybe<Scalars['Int']['input']>;
  /** Jsonデータ */
  json_data?: InputMaybe<Scalars['Json']['input']>;
  /** テンプレート名称 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** サイズ(単位) */
  unit?: InputMaybe<Scalars['String']['input']>;
  /** ウォーターマーク有無 */
  watermark?: InputMaybe<Scalars['Boolean']['input']>;
  /** サイズ(幅) */
  width?: InputMaybe<Scalars['Int']['input']>;
};

export type AdminCreateMSpecificationProductInput = {
  /** エディタテンプレート */
  editor_template_id?: InputMaybe<Scalars['ID']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** 品番 */
  item_number?: InputMaybe<Scalars['String']['input']>;
  /** Web招待状マスタ */
  m_web_invitation: AdminCreateMWebInvitationInput;
  /** 商品マスタ */
  product_id?: InputMaybe<Scalars['ID']['input']>;
  /** 規格別商品画像 */
  product_images?: InputMaybe<Array<AdminCreateProductImage>>;
  /** 規格明細1 */
  product_specification_detail_info_id1?: InputMaybe<Scalars['ID']['input']>;
  /** 規格明細2 */
  product_specification_detail_info_id2?: InputMaybe<Scalars['ID']['input']>;
  /** 規格明細3 */
  product_specification_detail_info_id3?: InputMaybe<Scalars['ID']['input']>;
  /** 通常価格 */
  regular_price?: InputMaybe<Scalars['Int']['input']>;
  /** 販売価格 */
  sale_price?: InputMaybe<Scalars['Int']['input']>;
  /** 販売価格利用期間(TO日時) */
  sale_price_end?: InputMaybe<Scalars['DateTime']['input']>;
  /** 販売価格利用期間(FROM日時) */
  sale_price_start?: InputMaybe<Scalars['DateTime']['input']>;
  /** バリエーションテンプレート */
  variation_template_id?: InputMaybe<Scalars['ID']['input']>;
};

export type AdminCreateMWebInvitationInput = {
  /** CSSコード */
  css_code?: InputMaybe<Scalars['String']['input']>;
  /** エディタ設定値JSON */
  editor_settings_json?: InputMaybe<Scalars['Json']['input']>;
  /** 選択ファーストビュー */
  first_view_id?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** 画像アスペクト比設定値JSON */
  image_aspect_settings_json?: InputMaybe<Scalars['Json']['input']>;
  /** メインビジュアル画像利用不可FLG */
  is_main_visual_image_disabled?: InputMaybe<Scalars['Boolean']['input']>;
  /** メインビジュアル画像差し替え可能FLG */
  is_main_visual_image_replaceable?: InputMaybe<Scalars['Boolean']['input']>;
  /** プロフィール画像利用不可FLG */
  is_profile_image_disabled?: InputMaybe<Scalars['Boolean']['input']>;
  /** 規格別商品マスタ */
  m_specification_product_id?: InputMaybe<Scalars['ID']['input']>;
  /** WEB招待状テンプレートマスタ */
  m_web_invitation_template_id?: InputMaybe<Scalars['ID']['input']>;
  /** Web招待状メインビジュアルブロックマスタ */
  m_web_invitation_visual_block_id?: InputMaybe<Scalars['ID']['input']>;
  /** WEB招待状デザイン画像 */
  web_invitation_design_images?: InputMaybe<Array<AdminCreateWebInvitationDesignImageInput>>;
};

export type AdminCreateMasterBlockInput = {
  /** 基本バリエーションテンプレートID */
  basic_variation_template_id?: InputMaybe<Scalars['ID']['input']>;
  /** Jsonデータ */
  json_data?: InputMaybe<Scalars['Json']['input']>;
  /** テンプレート名称 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** 商品種別 */
  product_type?: InputMaybe<ProductTypeEnum>;
};

export type AdminCreateMemberInput = {
  /** 生年月日 */
  birthdate?: InputMaybe<Scalars['Date']['input']>;
  /** メールアドレス */
  email?: InputMaybe<Scalars['String']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** めい */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名(英語) */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓(英語) */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** パスワード */
  password?: InputMaybe<Scalars['String']['input']>;
  /** SNSログインID */
  sns_login_id?: InputMaybe<Scalars['Json']['input']>;
};

export type AdminCreateProductImage = {
  /** ファイル */
  file?: InputMaybe<Scalars['Upload']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** 画像コメント */
  image_comment?: InputMaybe<Scalars['String']['input']>;
  /** 規格別商品マスタ */
  m_specification_product_id?: InputMaybe<Scalars['ID']['input']>;
  /** 画像uuid */
  uuid?: InputMaybe<Scalars['String']['input']>;
};

export type AdminCreateProductTagInput = {
  /** 商品 */
  product_id?: InputMaybe<Scalars['ID']['input']>;
  /** タグ */
  tag_id?: InputMaybe<Scalars['ID']['input']>;
};

export type AdminCreateProductinput = {
  admin_notes?: InputMaybe<Scalars['String']['input']>;
  estimated_delivery_days?: InputMaybe<Scalars['Int']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  is_editor?: InputMaybe<Scalars['Boolean']['input']>;
  is_reservation_period_specified?: InputMaybe<Scalars['Boolean']['input']>;
  is_sales_period_specified?: InputMaybe<Scalars['Boolean']['input']>;
  is_sample_request_allowed?: InputMaybe<Scalars['Boolean']['input']>;
  is_specification?: InputMaybe<Scalars['Boolean']['input']>;
  is_unpublished?: InputMaybe<Scalars['Boolean']['input']>;
  is_use_component?: InputMaybe<Scalars['Boolean']['input']>;
  is_variation_specification?: InputMaybe<Scalars['Boolean']['input']>;
  meta_canonical?: InputMaybe<Scalars['String']['input']>;
  meta_description?: InputMaybe<Scalars['String']['input']>;
  meta_keywords?: InputMaybe<Scalars['String']['input']>;
  meta_title?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  option_product_price_difference?: InputMaybe<Scalars['Int']['input']>;
  product_description?: InputMaybe<Scalars['String']['input']>;
  product_inventory?: InputMaybe<Scalars['Int']['input']>;
  product_type_code?: InputMaybe<Scalars['String']['input']>;
  reservation_period_end?: InputMaybe<Scalars['DateTime']['input']>;
  reservation_period_start?: InputMaybe<Scalars['DateTime']['input']>;
  sales_period_end?: InputMaybe<Scalars['DateTime']['input']>;
  sales_period_start?: InputMaybe<Scalars['DateTime']['input']>;
  shipping_cost?: InputMaybe<Scalars['Int']['input']>;
};

export type AdminCreateVariationTemplateInput = {
  /** 基本バリエーションテンプレートID */
  basic_variation_template_id?: InputMaybe<Scalars['ID']['input']>;
  /** Jsonデータ */
  json_data?: InputMaybe<Scalars['Json']['input']>;
  /** テンプレート名称 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** 商品種別 */
  product_type?: InputMaybe<ProductTypeEnum>;
};

export type AdminCreateWebInvitationDesignImageInput = {
  /** ファイル */
  file?: InputMaybe<Scalars['Upload']['input']>;
  /** 画像ファイル名 */
  file_name?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** WEB招待状マスタ */
  m_web_invitation_id?: InputMaybe<Scalars['ID']['input']>;
};

export type AdminCreateWeddingInfoInput = {
  /** 招待人数 */
  guest_count?: InputMaybe<Scalars['Int']['input']>;
  /** 披露宴日 */
  reception_date?: InputMaybe<Scalars['Date']['input']>;
  /** 挙式日 */
  wedding_date?: InputMaybe<Scalars['Date']['input']>;
  /** 挙式会場名 */
  wedding_venue?: InputMaybe<Scalars['String']['input']>;
};

/** A paginated list of Admin items. */
export type AdminPaginator = {
  __typename?: 'AdminPaginator';
  /** A list of Admin items. */
  data: Array<Admin>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export enum AdminQueryGuestFreeItemValuesOrderByColumn {
  Content = 'CONTENT',
  GuestId = 'GUEST_ID',
  Name = 'NAME'
}

export type AdminQueryGuestFreeItemValuesOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: AdminQueryGuestFreeItemValuesOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

export enum AdminQueryGuestGroupsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  GuestListId = 'GUEST_LIST_ID',
  Name = 'NAME'
}

export type AdminQueryGuestGroupsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: AdminQueryGuestGroupsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

export enum AdminQueryGuestHonorMastersOrderByColumn {
  CreatedAt = 'CREATED_AT',
  Name = 'NAME'
}

export type AdminQueryGuestHonorMastersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: AdminQueryGuestHonorMastersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

export enum AdminQueryGuestListsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  MemberId = 'MEMBER_ID',
  Name = 'NAME'
}

export type AdminQueryGuestListsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: AdminQueryGuestListsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

export enum AdminQueryGuestProfileIconMastersOrderByColumn {
  CreatedAt = 'CREATED_AT',
  PhotoUrl = 'PHOTO_URL'
}

export type AdminQueryGuestProfileIconMastersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: AdminQueryGuestProfileIconMastersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

export enum AdminQueryGuestTagsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  GuestListId = 'GUEST_LIST_ID',
  Tag = 'TAG'
}

export type AdminQueryGuestTagsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: AdminQueryGuestTagsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

export enum AdminQueryGuestTitleMastersOrderByColumn {
  CreatedAt = 'CREATED_AT',
  Name = 'NAME'
}

export type AdminQueryGuestTitleMastersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: AdminQueryGuestTitleMastersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

export enum AdminQueryGuestsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  FirstName = 'FIRST_NAME',
  FirstNameKana = 'FIRST_NAME_KANA',
  FirstNameRomaji = 'FIRST_NAME_ROMAJI',
  GuestListId = 'GUEST_LIST_ID',
  LastName = 'LAST_NAME',
  LastNameKana = 'LAST_NAME_KANA',
  LastNameRomaji = 'LAST_NAME_ROMAJI'
}

export type AdminQueryGuestsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: AdminQueryGuestsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

export enum AdminQueryMoneyTransferOrderByColumn {
  DeadlineDate = 'DEADLINE_DATE',
  TransferAmount = 'TRANSFER_AMOUNT'
}

export type AdminQueryMoneyTransferOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: AdminQueryMoneyTransferOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

export type AdminUpdateAdminInput = {
  /** メールアドレス */
  email: Scalars['String']['input'];
  id: Scalars['ID']['input'];
  /** 管理者名 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** パスワード */
  password?: InputMaybe<Scalars['String']['input']>;
};

export type AdminUpdateBasicVariationTemplateInput = {
  /** 色延ばし有無 */
  color_extension?: InputMaybe<Scalars['Boolean']['input']>;
  /** 折り線種類 */
  fold_line_type?: InputMaybe<Scalars['Int']['input']>;
  /** サイズ(高さ) */
  height?: InputMaybe<Scalars['Int']['input']>;
  /** ID */
  id: Scalars['ID']['input'];
  /** Jsonデータ */
  json_data?: InputMaybe<Scalars['Json']['input']>;
  /** テンプレート名称 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** サイズ(単位) */
  unit?: InputMaybe<Scalars['String']['input']>;
  /** ウォーターマーク有無 */
  watermark?: InputMaybe<Scalars['Boolean']['input']>;
  /** サイズ(幅) */
  width?: InputMaybe<Scalars['Int']['input']>;
};

export type AdminUpdateMSpecificationProductInput = {
  /** エディタテンプレート */
  editor_template_id?: InputMaybe<Scalars['ID']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** 品番 */
  item_number?: InputMaybe<Scalars['String']['input']>;
  /** Web招待状マスタ */
  m_web_invitation: AdminUpdateMWebInvitationInput;
  /** 商品マスタ */
  product_id?: InputMaybe<Scalars['ID']['input']>;
  /** 規格別商品画像 */
  product_images?: InputMaybe<Array<AdminUpdateProductImage>>;
  /** 規格明細1 */
  product_specification_detail_info_id1?: InputMaybe<Scalars['ID']['input']>;
  /** 規格明細2 */
  product_specification_detail_info_id2?: InputMaybe<Scalars['ID']['input']>;
  /** 規格明細3 */
  product_specification_detail_info_id3?: InputMaybe<Scalars['ID']['input']>;
  /** 通常価格 */
  regular_price?: InputMaybe<Scalars['Int']['input']>;
  /** 販売価格 */
  sale_price?: InputMaybe<Scalars['Int']['input']>;
  /** 販売価格利用期間(TO日時) */
  sale_price_end?: InputMaybe<Scalars['DateTime']['input']>;
  /** 販売価格利用期間(FROM日時) */
  sale_price_start?: InputMaybe<Scalars['DateTime']['input']>;
  /** バリエーションテンプレート */
  variation_template_id?: InputMaybe<Scalars['ID']['input']>;
};

export type AdminUpdateMWebInvitationInput = {
  /** CSSコード */
  css_code?: InputMaybe<Scalars['String']['input']>;
  /** エディタ設定値JSON */
  editor_settings_json?: InputMaybe<Scalars['Json']['input']>;
  /** 選択ファーストビュー */
  first_view_id?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** 画像アスペクト比設定値JSON */
  image_aspect_settings_json?: InputMaybe<Scalars['Json']['input']>;
  /** メインビジュアル画像利用不可FLG */
  is_main_visual_image_disabled?: InputMaybe<Scalars['Boolean']['input']>;
  /** メインビジュアル画像差し替え可能FLG */
  is_main_visual_image_replaceable?: InputMaybe<Scalars['Boolean']['input']>;
  /** プロフィール画像利用不可FLG */
  is_profile_image_disabled?: InputMaybe<Scalars['Boolean']['input']>;
  /** 規格別商品マスタ */
  m_specification_product_id?: InputMaybe<Scalars['ID']['input']>;
  /** WEB招待状テンプレートマスタ */
  m_web_invitation_template_id?: InputMaybe<Scalars['ID']['input']>;
  /** Web招待状メインビジュアルブロックマスタ */
  m_web_invitation_visual_block_id?: InputMaybe<Scalars['ID']['input']>;
  /** WEB招待状デザイン画像 */
  web_invitation_design_images?: InputMaybe<Array<AdminUpdateWebInvitationDesignImageInput>>;
};

export type AdminUpdateMasterBlockInput = {
  /** 基本バリエーションテンプレートID */
  basic_variation_template_id?: InputMaybe<Scalars['ID']['input']>;
  /** id */
  id: Scalars['ID']['input'];
  /** Jsonデータ */
  json_data?: InputMaybe<Scalars['Json']['input']>;
  /** テンプレート名称 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** 商品種別 */
  product_type?: InputMaybe<ProductTypeEnum>;
};

export type AdminUpdateMemberInput = {
  /** 生年月日 */
  birthdate?: InputMaybe<Scalars['Date']['input']>;
  /** メールアドレス */
  email?: InputMaybe<Scalars['String']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** めい */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名(英語) */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓(英語) */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** パスワード */
  password?: InputMaybe<Scalars['String']['input']>;
  /** SNSログインID */
  sns_login_id?: InputMaybe<Scalars['Json']['input']>;
};

export type AdminUpdateMoneyTransferInput = {
  /** 口座名義 */
  account_name?: InputMaybe<Scalars['String']['input']>;
  /** 口座番号 */
  account_number?: InputMaybe<Scalars['String']['input']>;
  /** 口座種別 */
  account_type?: InputMaybe<AccountTypeEnum>;
  /** 管理者 */
  admin_id?: InputMaybe<Scalars['ID']['input']>;
  /** 銀行コード */
  bank_code?: InputMaybe<Scalars['String']['input']>;
  /** 銀行名 */
  bank_name?: InputMaybe<Scalars['String']['input']>;
  /** 支店コード */
  branch_code?: InputMaybe<Scalars['String']['input']>;
  /** 支店名 */
  branch_name?: InputMaybe<Scalars['String']['input']>;
  /** 手数料 */
  commission_fee?: InputMaybe<Scalars['Float']['input']>;
  /** 送金完了時間 */
  completion_datetime?: InputMaybe<Scalars['DateTime']['input']>;
  /** 送金期限 */
  deadline_date?: InputMaybe<Scalars['Date']['input']>;
  /** ゲストシステム使用料 */
  guest_system_fee?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['ID']['input'];
  /** エラーフラグ */
  is_error?: InputMaybe<Scalars['Boolean']['input']>;
  /** 会員 */
  member_id?: InputMaybe<Scalars['ID']['input']>;
  /** 会員システム使用料 */
  member_system_fee?: InputMaybe<Scalars['Float']['input']>;
  /** 事前支払金額 */
  prepayment_amount?: InputMaybe<Scalars['Float']['input']>;
  /** 送金ステータス */
  status?: InputMaybe<MoneyTransferStatusEnum>;
  /** システム使用料 */
  system_fee?: InputMaybe<Scalars['Float']['input']>;
  /** 送金金額 */
  transfer_amount?: InputMaybe<Scalars['Float']['input']>;
  /** 送金日 */
  transfer_date?: InputMaybe<Scalars['Date']['input']>;
  /** Web招待状 */
  web_invitation_id?: InputMaybe<Scalars['ID']['input']>;
};

export type AdminUpdateMultiplePaymentDateInput = {
  date: Scalars['Date']['input'];
  ids: Array<Scalars['ID']['input']>;
};

export type AdminUpdateProductImage = {
  /** ファイル */
  file?: InputMaybe<Scalars['Upload']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** 画像コメント */
  image_comment?: InputMaybe<Scalars['String']['input']>;
  /** 規格別商品マスタ */
  m_specification_product_id?: InputMaybe<Scalars['ID']['input']>;
  /** 画像uuid */
  uuid?: InputMaybe<Scalars['String']['input']>;
};

export type AdminUpdateProductinput = {
  admin_notes?: InputMaybe<Scalars['String']['input']>;
  display_order?: InputMaybe<Scalars['String']['input']>;
  estimated_delivery_days?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['ID']['input'];
  is_editor?: InputMaybe<Scalars['Boolean']['input']>;
  is_reservation_period_specified?: InputMaybe<Scalars['Boolean']['input']>;
  is_sales_period_specified?: InputMaybe<Scalars['Boolean']['input']>;
  is_sample_request_allowed?: InputMaybe<Scalars['Boolean']['input']>;
  is_specification?: InputMaybe<Scalars['Boolean']['input']>;
  is_unpublished?: InputMaybe<Scalars['Boolean']['input']>;
  is_use_component?: InputMaybe<Scalars['Boolean']['input']>;
  is_variation_specification?: InputMaybe<Scalars['Boolean']['input']>;
  meta_canonical?: InputMaybe<Scalars['String']['input']>;
  meta_description?: InputMaybe<Scalars['String']['input']>;
  meta_keywords?: InputMaybe<Scalars['String']['input']>;
  meta_title?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  option_product_price_difference?: InputMaybe<Scalars['Int']['input']>;
  product_description?: InputMaybe<Scalars['String']['input']>;
  product_inventory?: InputMaybe<Scalars['Int']['input']>;
  product_type_code?: InputMaybe<Scalars['String']['input']>;
  reservation_period_end?: InputMaybe<Scalars['DateTime']['input']>;
  reservation_period_start?: InputMaybe<Scalars['DateTime']['input']>;
  sales_period_end?: InputMaybe<Scalars['DateTime']['input']>;
  sales_period_start?: InputMaybe<Scalars['DateTime']['input']>;
  shipping_cost?: InputMaybe<Scalars['Int']['input']>;
};

export type AdminUpdateVariationTemplateInput = {
  /** 基本バリエーションテンプレートID */
  basic_variation_template_id?: InputMaybe<Scalars['ID']['input']>;
  /** id */
  id: Scalars['ID']['input'];
  /** Jsonデータ */
  json_data?: InputMaybe<Scalars['Json']['input']>;
  /** テンプレート名称 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** 商品種別 */
  product_type?: InputMaybe<ProductTypeEnum>;
};

export type AdminUpdateWebInvitationDesignImageInput = {
  /** ファイル */
  file?: InputMaybe<Scalars['Upload']['input']>;
  /** 画像ファイル名 */
  file_name?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** WEB招待状マスタ */
  m_web_invitation_id?: InputMaybe<Scalars['ID']['input']>;
};

export type AdminUpdateWeddingInfoInput = {
  /** 招待人数 */
  guest_count?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['ID']['input'];
  /** 披露宴日 */
  reception_date?: InputMaybe<Scalars['Date']['input']>;
  /** 挙式日 */
  wedding_date?: InputMaybe<Scalars['Date']['input']>;
  /** 挙式会場名 */
  wedding_venue?: InputMaybe<Scalars['String']['input']>;
};

/** Attendance enum */
export enum AttendanceEnum {
  /** Absent */
  Absent = 'ABSENT',
  /** Pending */
  Pending = 'PENDING',
  /** Present */
  Present = 'PRESENT'
}

export type BasicVariationTemplate = {
  __typename?: 'BasicVariationTemplate';
  /** 色延ばし有無 */
  color_extension?: Maybe<Scalars['Boolean']['output']>;
  /** 登録日時 */
  created_at?: Maybe<Scalars['DateTime']['output']>;
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 折り線種類 */
  fold_line_type?: Maybe<Scalars['Int']['output']>;
  /** サイズ(高さ) */
  height?: Maybe<Scalars['Int']['output']>;
  /** ID */
  id: Scalars['ID']['output'];
  /** Jsonデータ */
  json_data?: Maybe<Scalars['Json']['output']>;
  /** テンプレート名称 */
  name?: Maybe<Scalars['String']['output']>;
  /** サイズ(単位) */
  unit?: Maybe<Scalars['String']['output']>;
  /** 更新日時 */
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  /** ウォーターマーク有無 */
  watermark?: Maybe<Scalars['Boolean']['output']>;
  /** サイズ(幅) */
  width?: Maybe<Scalars['Int']['output']>;
};

/** A paginated list of BasicVariationTemplate items. */
export type BasicVariationTemplatePaginator = {
  __typename?: 'BasicVariationTemplatePaginator';
  /** A list of BasicVariationTemplate items. */
  data: Array<BasicVariationTemplate>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type BulkCreateGuestInput = {
  /** 丁目・番地 */
  address?: InputMaybe<Scalars['String']['input']>;
  /** アレルギー品目 */
  allergies?: InputMaybe<Scalars['Json']['input']>;
  /** その他アレルギー */
  allergy?: InputMaybe<Scalars['String']['input']>;
  /** 生年月日 */
  birthdate?: InputMaybe<Scalars['Date']['input']>;
  /** 建物名・部屋番号 */
  building?: InputMaybe<Scalars['String']['input']>;
  /** カード決算ID */
  card_settlement_id?: InputMaybe<Scalars['String']['input']>;
  /** 市区町村 */
  city?: InputMaybe<Scalars['String']['input']>;
  /** メールアドレス */
  email?: InputMaybe<Scalars['String']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** めい */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名(ローマ字) */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 性別 */
  gender?: InputMaybe<GenderEnum>;
  /** お気持ち金額 */
  gift_amount?: InputMaybe<Scalars['Int']['input']>;
  guest_event_answers?: InputMaybe<Array<CreateGuestEventAnswerInput>>;
  guest_free_item_values?: InputMaybe<Array<CreateGuestFreeItemValueInput>>;
  /** ゲストグループ */
  guest_group_id?: InputMaybe<Scalars['ID']['input']>;
  /** ゲスト敬称 */
  guest_honor?: InputMaybe<Scalars['String']['input']>;
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  guest_tag_guests?: InputMaybe<Array<CreateGuestTagGuestInput>>;
  /** ゲスト肩書 */
  guest_title?: InputMaybe<Scalars['String']['input']>;
  /** ゲストタイプ */
  guest_type?: InputMaybe<GuestTypeEnum>;
  /** プロフィール画像 */
  image_url?: InputMaybe<Scalars['String']['input']>;
  /** 招待状お届け方法 */
  invitation_delivery?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料負担FLG */
  is_system_fee?: InputMaybe<Scalars['Boolean']['input']>;
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓(ローマ字) */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 会員確認済FLG */
  member_confirm_type?: InputMaybe<MemberConfirmTypeEnum>;
  /** メッセージ */
  message?: InputMaybe<Scalars['String']['input']>;
  /** 連名筆頭者 */
  parent_guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** 会費・ご祝儀支払い方法 */
  payment_method?: InputMaybe<PaymentMethodEnum>;
  /** 電話番号 */
  phone?: InputMaybe<Scalars['String']['input']>;
  /** 郵便番号 */
  postal_code?: InputMaybe<Scalars['String']['input']>;
  /** 都道府県 */
  prefecture?: InputMaybe<Scalars['String']['input']>;
  /** 間柄 */
  relationship?: InputMaybe<Scalars['String']['input']>;
  /** 関係性 */
  relationship_name?: InputMaybe<Scalars['String']['input']>;
  /** 決算金額 */
  settlement_amount?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料 */
  system_fee?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料率 */
  system_fee_rate?: InputMaybe<Scalars['Float']['input']>;
  /** 会費・ご祝儀・お気持ち金額合計金額 */
  total_amount?: InputMaybe<Scalars['Int']['input']>;
  /** Web招待状返信日時 */
  web_invite_reply_datetime?: InputMaybe<Scalars['DateTime']['input']>;
};

export type BulkUpdateGuestInput = {
  /** 丁目・番地 */
  address?: InputMaybe<Scalars['String']['input']>;
  /** アレルギー品目 */
  allergies?: InputMaybe<Scalars['Json']['input']>;
  /** その他アレルギー */
  allergy?: InputMaybe<Scalars['String']['input']>;
  /** 生年月日 */
  birthdate?: InputMaybe<Scalars['Date']['input']>;
  /** 建物名・部屋番号 */
  building?: InputMaybe<Scalars['String']['input']>;
  /** カード決算ID */
  card_settlement_id?: InputMaybe<Scalars['String']['input']>;
  /** 市区町村 */
  city?: InputMaybe<Scalars['String']['input']>;
  /** メールアドレス */
  email?: InputMaybe<Scalars['String']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** めい */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名(ローマ字) */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 性別 */
  gender?: InputMaybe<GenderEnum>;
  /** お気持ち金額 */
  gift_amount?: InputMaybe<Scalars['Int']['input']>;
  guest_event_answers?: InputMaybe<Array<CreateGuestEventAnswerInput>>;
  /** ゲストグループ */
  guest_group_id?: InputMaybe<Scalars['ID']['input']>;
  /** ゲスト敬称 */
  guest_honor?: InputMaybe<Scalars['String']['input']>;
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  guest_tag_guests?: InputMaybe<Array<CreateGuestTagGuestInput>>;
  /** ゲスト肩書 */
  guest_title?: InputMaybe<Scalars['String']['input']>;
  /** ゲストタイプ */
  guest_type?: InputMaybe<GuestTypeEnum>;
  id: Scalars['ID']['input'];
  /** プロフィール画像 */
  image_url?: InputMaybe<Scalars['String']['input']>;
  /** 招待状お届け方法 */
  invitation_delivery?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料負担FLG */
  is_system_fee?: InputMaybe<Scalars['Boolean']['input']>;
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓(ローマ字) */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 会員確認済FLG */
  member_confirm_type?: InputMaybe<MemberConfirmTypeEnum>;
  /** メッセージ */
  message?: InputMaybe<Scalars['String']['input']>;
  /** 連名筆頭者 */
  parent_guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** 会費・ご祝儀支払い方法 */
  payment_method?: InputMaybe<PaymentMethodEnum>;
  /** 電話番号 */
  phone?: InputMaybe<Scalars['String']['input']>;
  /** 郵便番号 */
  postal_code?: InputMaybe<Scalars['String']['input']>;
  /** 都道府県 */
  prefecture?: InputMaybe<Scalars['String']['input']>;
  /** 間柄 */
  relationship?: InputMaybe<Scalars['String']['input']>;
  /** 関係性 */
  relationship_name?: InputMaybe<Scalars['String']['input']>;
  /** 決算金額 */
  settlement_amount?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料 */
  system_fee?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料率 */
  system_fee_rate?: InputMaybe<Scalars['Float']['input']>;
  /** 会費・ご祝儀・お気持ち金額合計金額 */
  total_amount?: InputMaybe<Scalars['Int']['input']>;
  /** Web招待状返信日時 */
  web_invite_reply_datetime?: InputMaybe<Scalars['DateTime']['input']>;
};

export type BulkUpdateGuestTagInput = {
  /** ゲスト(複数) */
  guest_ids?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['String']['input']>;
  /** タグ */
  tags?: InputMaybe<Array<GuestTagInput>>;
};

export type CelebrationFee = {
  __typename?: 'CelebrationFee';
  /** パーティー開催日 */
  event_date?: Maybe<Scalars['String']['output']>;
  /** 会費ご祝儀イベント 配列 */
  events: Array<CelebrationFeeEvent>;
  /** お気持ち金額(合計) */
  gift_total_amount?: Maybe<Scalars['Int']['output']>;
  /** 回答数 */
  guest_event_answers_count?: Maybe<Scalars['Int']['output']>;
  /** Web招待状ID */
  id?: Maybe<Scalars['ID']['output']>;
  /** Web招待状名 */
  name?: Maybe<Scalars['String']['output']>;
  /** 決済金額 */
  payment_amount?: Maybe<Scalars['Int']['output']>;
  /** 振込予定日 */
  scheduled_transfer_date?: Maybe<Scalars['Date']['output']>;
  /** システム利用料 */
  system_amount?: Maybe<Scalars['Int']['output']>;
  /** システム利用料負担金額 */
  system_total_fee?: Maybe<Scalars['Int']['output']>;
  /** 受取金額 */
  total_amount?: Maybe<Scalars['Int']['output']>;
  /** 振込事務手数料 */
  transfer_amount?: Maybe<Scalars['Int']['output']>;
};

export type CelebrationFeeEvent = {
  __typename?: 'CelebrationFeeEvent';
  /** イベント別合計金額 */
  amount_total?: Maybe<Scalars['Int']['output']>;
  /** イベント名 */
  event_name?: Maybe<Scalars['String']['output']>;
  /** お気持ち金額(合計) */
  gift_total_amount?: Maybe<Scalars['Int']['output']>;
  /** 会費ご祝儀ゲスト 配列 */
  guests: Array<CelebrationFeeGuest>;
  /** 支払い済みの人数合計 */
  paid_count_total?: Maybe<Scalars['Int']['output']>;
  /** 当日持参の人数合計 */
  pay_at_venue_count_total?: Maybe<Scalars['Int']['output']>;
  /** 事前支払いの金額合計 */
  prepaid_amount_total?: Maybe<Scalars['Int']['output']>;
  /** 事前支払いの人数合計 */
  prepaid_count_total?: Maybe<Scalars['Int']['output']>;
  /** システム利用料負担金額 */
  system_total_fee?: Maybe<Scalars['Int']['output']>;
};

export type CelebrationFeeGuest = {
  __typename?: 'CelebrationFeeGuest';
  /** 料金 */
  amount?: Maybe<Scalars['Int']['output']>;
  /** 出席 */
  attendance?: Maybe<Scalars['String']['output']>;
  /** 連名者 */
  child_guests?: Maybe<Array<CelebrationFeeGuest>>;
  /** 日付 */
  date?: Maybe<Scalars['Date']['output']>;
  /** 名 */
  first_name?: Maybe<Scalars['String']['output']>;
  /** お気持ち金額 */
  gift_amount?: Maybe<Scalars['Int']['output']>;
  /** 敬称 */
  guest_honor?: Maybe<Scalars['String']['output']>;
  /** ID */
  id?: Maybe<Scalars['ID']['output']>;
  /** 出席フラグ */
  is_attendance?: Maybe<Scalars['Boolean']['output']>;
  /** お気持ちフラグ */
  is_gift_amount?: Maybe<Scalars['Boolean']['output']>;
  /** システム利用料負担フラグ */
  is_system_fee?: Maybe<Scalars['Boolean']['output']>;
  /** 姓 */
  last_name?: Maybe<Scalars['String']['output']>;
  /** 支払いステータス */
  payment_method?: Maybe<PaymentMethodEnum>;
  /** システム利用料金 */
  system_fee?: Maybe<Scalars['Int']['output']>;
};

export type Constant = {
  __typename?: 'Constant';
  /** 間柄リスト */
  relation_ship_list?: Maybe<Array<RelationshipList>>;
};

/** Constant enum */
export enum ConstantEnum {
  /** Constant relationship */
  ConstantRelationship = 'CONSTANT_RELATIONSHIP'
}

export type CreateFamilyProfileInput = {
  /** 生年月日 */
  birth_date?: InputMaybe<Scalars['Date']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** 名（カナ） */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名（romaji） */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓（romaji） */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 並び順 */
  order?: InputMaybe<Scalars['Int']['input']>;
  /** 新郎新婦種別 */
  type?: InputMaybe<FamilyProfileTypeEnum>;
};

export type CreateGuestEventAnswerInput = {
  /** 出欠 */
  attendance?: InputMaybe<Scalars['String']['input']>;
  /** イベント日付 */
  date?: InputMaybe<Scalars['Date']['input']>;
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** ID */
  id?: InputMaybe<Scalars['ID']['input']>;
  /** イベント名 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** 支払金額 */
  payment_amount?: InputMaybe<Scalars['Int']['input']>;
};

export type CreateGuestEventAnswerNoValidateInput = {
  /** 出欠 */
  attendance?: InputMaybe<Scalars['String']['input']>;
  /** イベント日付 */
  date?: InputMaybe<Scalars['Date']['input']>;
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** イベント名 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** 支払金額 */
  payment_amount?: InputMaybe<Scalars['Int']['input']>;
};

export type CreateGuestFreeItemValueInput = {
  /** 内容 */
  content?: InputMaybe<Scalars['String']['input']>;
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** 会員 */
  member_id?: InputMaybe<Scalars['ID']['input']>;
  /** 項目名称 */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type CreateGuestFreeItemValueNoValidateInput = {
  /** 内容 */
  content?: InputMaybe<Scalars['String']['input']>;
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['Int']['input']>;
  /** 会員 */
  member_id?: InputMaybe<Scalars['Int']['input']>;
  /** 項目名称 */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type CreateGuestGroupInput = {
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** ゲストグループ名称 */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type CreateGuestInput = {
  /** 丁目・番地 */
  address?: InputMaybe<Scalars['String']['input']>;
  /** アレルギー品目 */
  allergies?: InputMaybe<Scalars['Json']['input']>;
  /** その他アレルギー */
  allergy?: InputMaybe<Scalars['String']['input']>;
  /** 生年月日 */
  birthdate?: InputMaybe<Scalars['Date']['input']>;
  /** 建物名・部屋番号 */
  building?: InputMaybe<Scalars['String']['input']>;
  /** カード決算ID */
  card_settlement_id?: InputMaybe<Scalars['String']['input']>;
  /** 市区町村 */
  city?: InputMaybe<Scalars['String']['input']>;
  /** メールアドレス */
  email?: InputMaybe<Scalars['String']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** めい */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名(ローマ字) */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 性別 */
  gender?: InputMaybe<GenderEnum>;
  /** お気持ち金額 */
  gift_amount?: InputMaybe<Scalars['Int']['input']>;
  /** ゲストグループ */
  guest_group_id?: InputMaybe<Scalars['ID']['input']>;
  /** ゲスト敬称 */
  guest_honor?: InputMaybe<Scalars['String']['input']>;
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  guest_tag_guests?: InputMaybe<Array<CreateGuestTagGuestInput>>;
  /** ゲスト肩書 */
  guest_title?: InputMaybe<Scalars['String']['input']>;
  /** ゲストタイプ */
  guest_type?: InputMaybe<GuestTypeEnum>;
  /** プロフィール画像 */
  image_url?: InputMaybe<Scalars['String']['input']>;
  /** 招待状お届け方法 */
  invitation_delivery?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料負担FLG */
  is_system_fee?: InputMaybe<Scalars['Boolean']['input']>;
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓(ローマ字) */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 会員確認済FLG */
  member_confirm_type?: InputMaybe<MemberConfirmTypeEnum>;
  /** メッセージ */
  message?: InputMaybe<Scalars['String']['input']>;
  /** 連名筆頭者 */
  parent_guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** 会費・ご祝儀支払い方法 */
  payment_method?: InputMaybe<PaymentMethodEnum>;
  /** 電話番号 */
  phone?: InputMaybe<Scalars['String']['input']>;
  /** 郵便番号 */
  postal_code?: InputMaybe<Scalars['String']['input']>;
  /** 都道府県 */
  prefecture?: InputMaybe<Scalars['String']['input']>;
  /** 間柄 */
  relationship?: InputMaybe<Scalars['String']['input']>;
  /** 関係性 */
  relationship_name?: InputMaybe<Scalars['String']['input']>;
  /** 決算金額 */
  settlement_amount?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料 */
  system_fee?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料率 */
  system_fee_rate?: InputMaybe<Scalars['Float']['input']>;
  /** 会費・ご祝儀・お気持ち金額合計金額 */
  total_amount?: InputMaybe<Scalars['Int']['input']>;
  /** Web招待状返信日時 */
  web_invite_reply_datetime?: InputMaybe<Scalars['DateTime']['input']>;
};

export type CreateGuestListInput = {
  /** ゲストリスト名称 */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type CreateGuestNoValidateInput = {
  /** 丁目・番地 */
  address?: InputMaybe<Scalars['String']['input']>;
  /** アレルギー品目 */
  allergies?: InputMaybe<Scalars['Json']['input']>;
  /** その他アレルギー */
  allergy?: InputMaybe<Scalars['String']['input']>;
  /** 生年月日 */
  birthdate?: InputMaybe<Scalars['Date']['input']>;
  /** 建物名・部屋番号 */
  building?: InputMaybe<Scalars['String']['input']>;
  /** カード決済ID */
  card_settlement_id?: InputMaybe<Scalars['String']['input']>;
  /** 市区町村 */
  city?: InputMaybe<Scalars['String']['input']>;
  /** メールアドレス */
  email?: InputMaybe<Scalars['String']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** めい */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名(ローマ字) */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 性別 */
  gender?: InputMaybe<GenderEnum>;
  /** お気持ち金額 */
  gift_amount?: InputMaybe<Scalars['Int']['input']>;
  /** 連名ゲストイベント別回答 */
  guest_event_answers?: InputMaybe<Array<InputMaybe<CreateGuestEventAnswerNoValidateInput>>>;
  /** ゲストグループ */
  guest_group_id?: InputMaybe<Scalars['ID']['input']>;
  /** ゲスト敬称 */
  guest_honor?: InputMaybe<Scalars['String']['input']>;
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  /** ゲストタグ */
  guest_tag_guests?: InputMaybe<Array<CreateGuestTagGuestInput>>;
  /** ゲスト肩書 */
  guest_title?: InputMaybe<Scalars['String']['input']>;
  /** ゲストタイプ */
  guest_type?: InputMaybe<GuestTypeEnum>;
  /** プロフィール画像uuid */
  image_url?: InputMaybe<Scalars['String']['input']>;
  /** 招待状お届け方法 */
  invitation_delivery?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料負担FLG */
  is_system_fee?: InputMaybe<Scalars['Boolean']['input']>;
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓(ローマ字) */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** Web招待状マスタ */
  m_web_invitation_id?: InputMaybe<Scalars['ID']['input']>;
  /** お祝い画像・動画の種類 */
  media_type?: InputMaybe<MediaTypeEnum>;
  /** お祝い画像・動画uuid */
  media_uuid?: InputMaybe<Scalars['String']['input']>;
  /** 会員確認済FLG */
  member_confirm_type?: InputMaybe<MemberConfirmTypeEnum>;
  /** メッセージ */
  message?: InputMaybe<Scalars['String']['input']>;
  /** 連名筆頭者 */
  parent_guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** 会費・ご祝儀支払い方法 */
  payment_method?: InputMaybe<PaymentMethodEnum>;
  /** 電話番号 */
  phone?: InputMaybe<Scalars['String']['input']>;
  /** 郵便番号 */
  postal_code?: InputMaybe<Scalars['String']['input']>;
  /** 都道府県 */
  prefecture?: InputMaybe<Scalars['String']['input']>;
  /** 間柄 */
  relationship?: InputMaybe<Scalars['String']['input']>;
  /** 関係性 */
  relationship_name?: InputMaybe<Scalars['String']['input']>;
  /** 決算金額 */
  settlement_amount?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料 */
  system_fee?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料率 */
  system_fee_rate?: InputMaybe<Scalars['Float']['input']>;
  /** 会費・ご祝儀・お気持ち金額合計金額 */
  total_amount?: InputMaybe<Scalars['Int']['input']>;
  /** Web招待状 */
  web_invitation_id?: InputMaybe<Scalars['ID']['input']>;
  /** Web招待状返信日時 */
  web_invite_reply_datetime?: InputMaybe<Scalars['DateTime']['input']>;
};

export type CreateGuestSurveyAnswerInput = {
  /** 回答内容 */
  answer_content?: InputMaybe<Scalars['String']['input']>;
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** 質問内容 */
  question?: InputMaybe<Scalars['String']['input']>;
  /** UI種類 */
  ui_type?: InputMaybe<UiTypeEnum>;
};

export type CreateGuestSurveyAnswerNoValidateInput = {
  /** 回答内容 */
  answer_content?: InputMaybe<Scalars['String']['input']>;
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** 質問内容 */
  question?: InputMaybe<Scalars['String']['input']>;
  /** UI種類 */
  ui_type?: InputMaybe<UiTypeEnum>;
};

export type CreateGuestTagGuestInput = {
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** ゲストタグ */
  guest_tag_id?: InputMaybe<Scalars['ID']['input']>;
};

export type CreateGuestTagInput = {
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  /** タグ */
  tag?: InputMaybe<Scalars['String']['input']>;
};

export type CreateMemberBankAccountInput = {
  /** 口座名義 */
  account_name?: InputMaybe<Scalars['String']['input']>;
  /** 口座番号 */
  account_number?: InputMaybe<Scalars['String']['input']>;
  /** 口座種別 */
  account_type?: InputMaybe<AccountTypeEnum>;
  /** 銀行コード */
  bank_code?: InputMaybe<Scalars['String']['input']>;
  /** 銀行名 */
  bank_name?: InputMaybe<Scalars['String']['input']>;
  /** 支店コード */
  branch_code?: InputMaybe<Scalars['String']['input']>;
  /** 支店名 */
  branch_name?: InputMaybe<Scalars['String']['input']>;
  /** 電話番号 */
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type CreateMemberInput = {
  /** 生年月日 */
  birthdate?: InputMaybe<Scalars['Date']['input']>;
  /** メールアドレス */
  email?: InputMaybe<Scalars['String']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** めい */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名(romaji) */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓(romaji) */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** パスワード */
  password?: InputMaybe<Scalars['String']['input']>;
  /** SNSログインID */
  sns_login_id?: InputMaybe<Scalars['Json']['input']>;
};

export type CreateMemberRegistQuestionnaireInput = {
  /** 回答 */
  answer?: InputMaybe<Scalars['String']['input']>;
  /** 質問 */
  question?: InputMaybe<Scalars['String']['input']>;
};

export type CreateWebInvitationInput = {
  block_settings?: InputMaybe<Scalars['Json']['input']>;
  editor_settings?: InputMaybe<Scalars['Json']['input']>;
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  is_password?: InputMaybe<Scalars['Boolean']['input']>;
  is_public?: InputMaybe<Scalars['Boolean']['input']>;
  m_web_invitation_id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
  prepayment_due_date?: InputMaybe<Scalars['Date']['input']>;
  public_url?: InputMaybe<Scalars['String']['input']>;
  reply_deadline_date?: InputMaybe<Scalars['Date']['input']>;
  scheduled_date?: InputMaybe<Scalars['Date']['input']>;
  scheduled_transfer_date?: InputMaybe<Scalars['Date']['input']>;
};

export type CreateWeddingInfoInput = {
  /** 招待人数 */
  guest_count?: InputMaybe<Scalars['Int']['input']>;
  /** 披露宴日 */
  reception_date?: InputMaybe<Scalars['Date']['input']>;
  /** 挙式日 */
  wedding_date?: InputMaybe<Scalars['Date']['input']>;
  /** 挙式会場名 */
  wedding_venue?: InputMaybe<Scalars['String']['input']>;
};

export type EventList = {
  __typename?: 'EventList';
  /** 登録日時 */
  created_at?: Maybe<Scalars['DateTime']['output']>;
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 開催日 */
  event_date?: Maybe<Scalars['Date']['output']>;
  /** イベント名 */
  event_name?: Maybe<Scalars['String']['output']>;
  /** 時間(Json) */
  event_time?: Maybe<Scalars['Json']['output']>;
  /** 会費・ご祝儀(JSON) */
  fees?: Maybe<Scalars['Json']['output']>;
  /** ゲストイベント別回答 */
  guest_event_answers: Array<GuestEventAnswer>;
  /** ゲストリスト */
  guest_list: GuestList;
  id: Scalars['ID']['output'];
  /** 更新日時 */
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  /** 会場住所 */
  venue_address?: Maybe<Scalars['String']['output']>;
  /** 会場名 */
  venue_name: Scalars['String']['output'];
  /** 会場名(よみがな) */
  venue_name_kana?: Maybe<Scalars['String']['output']>;
  /** 会場郵便番号 */
  venue_postal_code?: Maybe<Scalars['String']['output']>;
  /** 会場TEL */
  venue_tel?: Maybe<Scalars['String']['output']>;
  /** 会場URL */
  venue_url?: Maybe<Scalars['String']['output']>;
};

/** 時候の句のJSONテキスト */
export type ExampleContentSeason = {
  __typename?: 'ExampleContentSeason';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type ExampleSentence = {
  __typename?: 'ExampleSentence';
  /** 作成日時 */
  created_at: Scalars['DateTime']['output'];
  /** 表示順 */
  display_order?: Maybe<Scalars['String']['output']>;
  /** 文例テキスト */
  example_text?: Maybe<Scalars['String']['output']>;
  /** 文例集種別1 */
  example_type1?: Maybe<ExampleType1Enum>;
  /** 文例集種別2 */
  example_type2?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  /** タイトル */
  title?: Maybe<Scalars['String']['output']>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type ExampleSentenceGroup = {
  __typename?: 'ExampleSentenceGroup';
  /** 時候の句のJSONテキスト */
  example_content_season?: Maybe<Array<Maybe<ExampleContentSeason>>>;
  /** 文例集 */
  example_sentences: Array<ExampleSentence>;
};

/** Example type1 enum */
export enum ExampleType1Enum {
  /** Editor bride profile */
  EditorBrideProfile = 'EDITOR_BRIDE_PROFILE',
  /** Editor family profile */
  EditorFamilyProfile = 'EDITOR_FAMILY_PROFILE',
  /** Editor greeting */
  EditorGreeting = 'EDITOR_GREETING',
  /** Editor groom profile */
  EditorGroomProfile = 'EDITOR_GROOM_PROFILE',
  /** Message free */
  MessageFree = 'MESSAGE_FREE',
  /** Message to guests */
  MessageToGuests = 'MESSAGE_TO_GUESTS'
}

export type FamilyProfile = {
  __typename?: 'FamilyProfile';
  /** 生年月日 */
  birth_date?: Maybe<Scalars['Date']['output']>;
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 名 */
  first_name?: Maybe<Scalars['String']['output']>;
  /** 名 */
  first_name_kana?: Maybe<Scalars['String']['output']>;
  /** 名 */
  first_name_romaji?: Maybe<Scalars['String']['output']>;
  /** id */
  id?: Maybe<Scalars['ID']['output']>;
  /** 姓 */
  last_name?: Maybe<Scalars['String']['output']>;
  /** 姓 */
  last_name_kana?: Maybe<Scalars['String']['output']>;
  /** 姓 */
  last_name_romaji?: Maybe<Scalars['String']['output']>;
  /** 会員 */
  member: Member;
  /** 並び順 */
  order?: Maybe<Scalars['Int']['output']>;
  /** 新郎新婦種別 */
  type?: Maybe<FamilyProfileTypeEnum>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** Family profile type enum */
export enum FamilyProfileTypeEnum {
  /** Bride */
  Bride = 'BRIDE',
  /** Female */
  Female = 'FEMALE',
  /** Groom */
  Groom = 'GROOM',
  /** Male */
  Male = 'MALE',
  /** Other */
  Other = 'OTHER'
}

/** File type */
export enum FileType {
  /** File type etc master */
  FileTypeEtcMaster = 'FILE_TYPE_ETC_MASTER',
  /** File type guest celebration image */
  FileTypeGuestCelebrationImage = 'FILE_TYPE_GUEST_CELEBRATION_IMAGE',
  /** File type guest upload file */
  FileTypeGuestUploadFile = 'FILE_TYPE_GUEST_UPLOAD_FILE',
  /** File type material */
  FileTypeMaterial = 'FILE_TYPE_MATERIAL',
  /** File type member etc */
  FileTypeMemberEtc = 'FILE_TYPE_MEMBER_ETC',
  /** File type member material */
  FileTypeMemberMaterial = 'FILE_TYPE_MEMBER_MATERIAL',
  /** File type product master */
  FileTypeProductMaster = 'FILE_TYPE_PRODUCT_MASTER',
  /** File type web invitation material */
  FileTypeWebInvitationMaterial = 'FILE_TYPE_WEB_INVITATION_MATERIAL'
}

/** Gender enum */
export enum GenderEnum {
  /** Etc */
  Etc = 'ETC',
  /** Female */
  Female = 'FEMALE',
  /** Male */
  Male = 'MALE'
}

export type GiftArrangementHistory = {
  __typename?: 'GiftArrangementHistory';
  /** 手配日時 */
  arrangement_datetime: Scalars['DateTime']['output'];
  /** 作成日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** お届け希望日 */
  desired_delivery_date: Scalars['Date']['output'];
  /** 引き出物Box情報履歴 */
  gift_box_info_history?: Maybe<GiftBoxInfoHistory>;
  /** 引き出物カードバッグ手配履歴 */
  gift_card_bag_arrangement_history?: Maybe<GiftCardBagArrangementHistory>;
  /** 引き出物送付先情報履歴 */
  gift_delivery_info_histories: Array<GiftDeliveryInfoHistory>;
  /** 引き出物のし情報履歴 */
  gift_wrap_info_history?: Maybe<GiftWrapInfoHistory>;
  id: Scalars['ID']['output'];
  /** 申込済みフラグ */
  is_apply_complete: Scalars['Boolean']['output'];
  /** 会員 */
  member: Member;
  /** 作業メモ */
  memo?: Maybe<Scalars['String']['output']>;
  /** 手配会社への伝達事項 */
  note?: Maybe<Scalars['String']['output']>;
  /** 発注期日 */
  order_deadline?: Maybe<Scalars['Date']['output']>;
  /** 決済履歴 */
  payment_histories: Array<PaymentHistory>;
  /** 送り主丁目・番地 */
  sender_address1?: Maybe<Scalars['String']['output']>;
  /** 送り主建物名・部屋番号 */
  sender_address2?: Maybe<Scalars['String']['output']>;
  /** 送り主市区町村 */
  sender_city?: Maybe<Scalars['String']['output']>;
  /** 送り主氏名 */
  sender_name?: Maybe<Scalars['String']['output']>;
  /** 送り主電話番号 */
  sender_phone_number?: Maybe<Scalars['String']['output']>;
  /** 送り主郵便番号 */
  sender_postal_code?: Maybe<Scalars['String']['output']>;
  /** 送り主都道府県 */
  sender_prefecture?: Maybe<Scalars['String']['output']>;
  /** 手配日時 */
  shipping_company_id: Scalars['ID']['output'];
  /** 出荷日 */
  shipping_date?: Maybe<Scalars['Date']['output']>;
  /** 出荷ステータス */
  shipping_status: Scalars['Int']['output'];
  /** 伝票件名 */
  slip_title?: Maybe<Scalars['String']['output']>;
  /** 担当者 */
  staff?: Maybe<Staff>;
  /** ステータス */
  status: Scalars['Int']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftArrangementHistoryPaymentHistory = {
  __typename?: 'GiftArrangementHistoryPaymentHistory';
  /** 作成日時 */
  created_at: Scalars['DateTime']['output'];
  /** 決済履歴 */
  gift_arrangement_history: GiftArrangementHistory;
  id: Scalars['ID']['output'];
  /** 引き出物手配履歴 */
  payment_history: PaymentHistory;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftAssignment = {
  __typename?: 'GiftAssignment';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 手配前引き出物割り当てメインギフト履歴 */
  gift_shipping_info: GiftShippingInfo;
  id: Scalars['ID']['output'];
  /** 購入済FLG */
  is_purchase: Scalars['Boolean']['output'];
  /** 決済履歴明細(購入済みFLG=TRUE) */
  payment_history_detail?: Maybe<PaymentHistoryDetail>;
  /** 商品マスタ(購入済み=FALSEの時に使用) */
  product?: Maybe<Product>;
  /** 引き出物割り当てサブギフト */
  sub_gift_assignments: Array<SubGiftAssignment>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftAssignmentHistory = {
  __typename?: 'GiftAssignmentHistory';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 引き出物送付先情報履歴 */
  gift_delivery_info_history: GiftDeliveryInfoHistory;
  id: Scalars['ID']['output'];
  /** 決済履歴明細 */
  payment_history_detail: PaymentHistoryDetail;
  /** 引き出物割り当てサブギフト履歴 */
  sub_gift_assignment_histories: Array<SubGiftAssignmentHistory>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftBoxInfoHistory = {
  __typename?: 'GiftBoxInfoHistory';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 引き出物手配履歴 */
  gift_arrangement_history: GiftArrangementHistory;
  /** 引き出物オプション商品 */
  gift_option_product: GiftOptionProduct;
  /** 引き出物オプション説明 */
  gift_option_product_description?: Maybe<Scalars['String']['output']>;
  /** 引き出物商品画像 */
  gift_option_product_image: Scalars['String']['output'];
  /** 引き出物オプション商品名 */
  gift_option_product_name: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftCardBagArrangementHistory = {
  __typename?: 'GiftCardBagArrangementHistory';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** お届け希望日 */
  delivery_date: Scalars['Date']['output'];
  /** お届け希望時間 */
  delivery_time?: Maybe<Scalars['String']['output']>;
  /** 引き出物手配履歴 */
  gift_arrangement_history: GiftArrangementHistory;
  /** 引き出物カードバッグ手配履歴明細 */
  gift_card_bag_arrangement_history_details: Array<GiftCardBagArrangementHistoryDetail>;
  id: Scalars['ID']['output'];
  /** 出荷日 */
  shipping_date: Scalars['Date']['output'];
  /** 出荷期日 */
  shipping_deadline: Scalars['Date']['output'];
  /** 出荷元 */
  shipping_origin: Scalars['String']['output'];
  /** 出荷ステータス */
  shipping_status: Scalars['Int']['output'];
  /** ステータス */
  status: Scalars['Int']['output'];
  /** 入稿予定日 */
  submission_schedule: Scalars['Date']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftCardBagArrangementHistoryDetail = {
  __typename?: 'GiftCardBagArrangementHistoryDetail';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 引き出物カードバッグ手配履歴 */
  gift_card_bag_arrangement_history: GiftCardBagArrangementHistory;
  id: Scalars['ID']['output'];
  /** 商品 */
  product: Product;
  /** 数量 */
  quantity: Scalars['Int']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftDeliveryInfoHistory = {
  __typename?: 'GiftDeliveryInfoHistory';
  /** 丁目・番地 */
  address: Scalars['String']['output'];
  /** 建物名・部屋番号 */
  building?: Maybe<Scalars['String']['output']>;
  /** 市区町村 */
  city: Scalars['String']['output'];
  /** 作成日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 引き出物送付先情報履歴 */
  gift_arrangement_history: GiftArrangementHistory;
  /** 引き出物割り当てメインギフト履歴 */
  gift_assignment_history: GiftAssignmentHistory;
  /** ゲスト */
  guest: Guest;
  /** ゲストタグ */
  guest_tag: GuestTag;
  id: Scalars['ID']['output'];
  /** 電話番号 */
  phone: Scalars['String']['output'];
  /** 郵便番号 */
  postal_code: Scalars['String']['output'];
  /** 都道府県 */
  prefecture: Scalars['String']['output'];
  /** 宛名 */
  recipient_name: Scalars['String']['output'];
  /** 配送伝票番号 */
  shipping_number?: Maybe<Scalars['String']['output']>;
  /** 配送状況 */
  shipping_status?: Maybe<Scalars['Int']['output']>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftOptionProduct = {
  __typename?: 'GiftOptionProduct';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 説明 */
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** 画像 */
  image: Scalars['String']['output'];
  /** 引き出物オプション商品スキーマ定義 */
  name: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftShippingInfo = {
  __typename?: 'GiftShippingInfo';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 手配前引き出物割り当てメインギフト */
  gift_assignment: GiftAssignment;
  /** ゲスト */
  guest: Guest;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftWrapInfoHistory = {
  __typename?: 'GiftWrapInfoHistory';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 引き出物手配履歴 */
  gift_arrangement_history: GiftArrangementHistory;
  id: Scalars['ID']['output'];
  /** 旧字・外字指定詳細 */
  kanji_alphabet_details?: Maybe<Scalars['String']['output']>;
  /** のし記載1 */
  noshi_content_1?: Maybe<Scalars['String']['output']>;
  /** のし記載2 */
  noshi_content_2?: Maybe<Scalars['String']['output']>;
  /** のしPDFURL */
  noshi_pdf_url?: Maybe<Scalars['String']['output']>;
  /** のし行数 */
  noshi_rows: Scalars['Int']['output'];
  /** 入稿用のしPDFURL */
  submission_noshi_pdf_url?: Maybe<Scalars['String']['output']>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GiftWrapping = {
  __typename?: 'GiftWrapping';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 引き出物オプション商品 */
  gift_option_product?: Maybe<GiftOptionProduct>;
  id: Scalars['ID']['output'];
  /** 購入済みFLG */
  is_purchase: Scalars['Boolean']['output'];
  /** 会員 */
  member: Member;
  /** 決済履歴明細(購入済みFLG=true) */
  payment_history_detail?: Maybe<PaymentHistoryDetail>;
  /** 商品(購入済みFLG=false) */
  product?: Maybe<Product>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type Guest = {
  __typename?: 'Guest';
  /** 丁目・番地 */
  address?: Maybe<Scalars['String']['output']>;
  /** アレルギー品目 */
  allergies?: Maybe<Scalars['Json']['output']>;
  /** その他アレルギー */
  allergy?: Maybe<Scalars['String']['output']>;
  /** 生年月日 */
  birthdate?: Maybe<Scalars['Date']['output']>;
  /** 建物名・部屋番号 */
  building?: Maybe<Scalars['String']['output']>;
  /** カード決算ID */
  card_settlement_id?: Maybe<Scalars['String']['output']>;
  /** 配下の連名ゲスト */
  children_guests: Array<Guest>;
  /** 市区町村 */
  city?: Maybe<Scalars['String']['output']>;
  /** 作成日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** メールアドレス */
  email?: Maybe<Scalars['String']['output']>;
  /** 名 */
  first_name: Scalars['String']['output'];
  /** めい */
  first_name_kana?: Maybe<Scalars['String']['output']>;
  /** 名(ローマ字) */
  first_name_romaji?: Maybe<Scalars['String']['output']>;
  /** 性別 */
  gender?: Maybe<GenderEnum>;
  /** お気持ち金額 */
  gift_amount?: Maybe<Scalars['Int']['output']>;
  /** ゲストイベント別回答 */
  guest_event_answers: Array<GuestEventAnswer>;
  /** ゲスト出席 */
  guest_event_attendance?: Maybe<Array<GuestEventAttendance>>;
  /** ゲストフリー項目値 */
  guest_free_item_values: Array<GuestFreeItemValue>;
  /** ゲストグループ */
  guest_group?: Maybe<GuestGroup>;
  /** ゲスト敬称 */
  guest_honor?: Maybe<Scalars['String']['output']>;
  /** ゲストリスト */
  guest_list: GuestList;
  /** ゲストアンケート別回答 */
  guest_survey_answers: Array<GuestSurveyAnswer>;
  /** ゲストタグ */
  guest_tags: Array<GuestTag>;
  /** ゲスト肩書 */
  guest_title?: Maybe<Scalars['String']['output']>;
  /** ゲストタイプ */
  guest_type?: Maybe<GuestTypeEnum>;
  id: Scalars['ID']['output'];
  /** プロフィール画像 */
  image_url?: Maybe<Scalars['String']['output']>;
  /** 招待状お届け方法 */
  invitation_delivery?: Maybe<Scalars['Int']['output']>;
  /** システム利用料負担FLG */
  is_system_fee?: Maybe<Scalars['Boolean']['output']>;
  /** 姓 */
  last_name: Scalars['String']['output'];
  /** せい */
  last_name_kana?: Maybe<Scalars['String']['output']>;
  /** 姓(ローマ字) */
  last_name_romaji?: Maybe<Scalars['String']['output']>;
  /** WEB招待状マスタ */
  m_web_invitation?: Maybe<MWebInvitation>;
  /** お祝い画像・動画の種類 */
  media_type?: Maybe<MediaTypeEnum>;
  /** お祝い画像・動画UUID */
  media_uuid?: Maybe<Scalars['String']['output']>;
  /** 会員 */
  member: Member;
  /** 会員確認済FLG */
  member_confirm_type?: Maybe<MemberConfirmTypeEnum>;
  /** メッセージ */
  message?: Maybe<Scalars['String']['output']>;
  /** 連名筆頭者 */
  parent_guest?: Maybe<Guest>;
  /** 会費・ご祝儀支払い方法 */
  payment_method?: Maybe<PaymentMethodEnum>;
  /** 電話番号 */
  phone?: Maybe<Scalars['String']['output']>;
  /** 郵便番号 */
  postal_code?: Maybe<Scalars['String']['output']>;
  /** 都道府県 */
  prefecture?: Maybe<Scalars['String']['output']>;
  /** 間柄 */
  relationship?: Maybe<Scalars['String']['output']>;
  /** 関係性 */
  relationship_name?: Maybe<Scalars['String']['output']>;
  /** 決算金額 */
  settlement_amount?: Maybe<Scalars['Int']['output']>;
  /** システム利用料 */
  system_fee?: Maybe<Scalars['Int']['output']>;
  /** システム利用料率 */
  system_fee_rate?: Maybe<Scalars['Float']['output']>;
  /** 会費・ご祝儀・お気持ち金額合計金額 */
  total_amount?: Maybe<Scalars['Int']['output']>;
  /** 最終更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** ユーザー作成WEB招待状 */
  web_invitation?: Maybe<WebInvitation>;
  /** Web招待状返信日時 */
  web_invite_reply_datetime?: Maybe<Scalars['DateTime']['output']>;
};

export type GuestEvent = {
  __typename?: 'GuestEvent';
  /** 丁目・番地 */
  address?: Maybe<Scalars['String']['output']>;
  /** アレルギー品目 */
  allergies?: Maybe<Scalars['Json']['output']>;
  /** その他アレルギー */
  allergy?: Maybe<Scalars['String']['output']>;
  /** 生年月日 */
  birthdate?: Maybe<Scalars['Date']['output']>;
  /** 建物名・部屋番号 */
  building?: Maybe<Scalars['String']['output']>;
  /** カード決算ID */
  card_settlement_id?: Maybe<Scalars['String']['output']>;
  /** 市区町村 */
  city?: Maybe<Scalars['String']['output']>;
  /** 作成日時 */
  created_at?: Maybe<Scalars['DateTime']['output']>;
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** メールアドレス */
  email?: Maybe<Scalars['String']['output']>;
  /** 名 */
  first_name?: Maybe<Scalars['String']['output']>;
  /** めい */
  first_name_kana?: Maybe<Scalars['String']['output']>;
  /** 名(ローマ字) */
  first_name_romaji?: Maybe<Scalars['String']['output']>;
  /** 性別 */
  gender?: Maybe<GenderEnum>;
  /** お気持ち金額 */
  gift_amount?: Maybe<Scalars['Int']['output']>;
  /** ゲスト出席 */
  guest_event_attendances: Array<GuestEventAttendance>;
  /** ゲストタグ */
  guest_event_tags?: Maybe<Array<GuestEventTag>>;
  /** ゲストグループID */
  guest_group_id?: Maybe<Scalars['String']['output']>;
  /** ゲストグループ名 */
  guest_group_name?: Maybe<Scalars['String']['output']>;
  /** ゲスト敬称 */
  guest_honor?: Maybe<Scalars['String']['output']>;
  /** ゲストリストID */
  guest_list_id?: Maybe<Scalars['String']['output']>;
  /** ゲスト肩書 */
  guest_title?: Maybe<Scalars['String']['output']>;
  /** ゲストタイプ */
  guest_type?: Maybe<GuestTypeEnum>;
  id: Scalars['ID']['output'];
  /** プロフィール画像 */
  image_url?: Maybe<Scalars['String']['output']>;
  /** 招待状お届け方法 */
  invitation_delivery?: Maybe<Scalars['Int']['output']>;
  /** システム利用料負担FLG */
  is_system_fee?: Maybe<Scalars['Boolean']['output']>;
  /** 姓 */
  last_name?: Maybe<Scalars['String']['output']>;
  /** せい */
  last_name_kana?: Maybe<Scalars['String']['output']>;
  /** 姓(ローマ字) */
  last_name_romaji?: Maybe<Scalars['String']['output']>;
  /** 招待状マスタID */
  m_web_invitation_id?: Maybe<Scalars['String']['output']>;
  /** お祝い画像・動画の種類 */
  media_type?: Maybe<MediaTypeEnum>;
  /** お祝い画像・動画UUID */
  media_uuid?: Maybe<Scalars['String']['output']>;
  /** 会員確認済FLG */
  member_confirm_type?: Maybe<MemberConfirmTypeEnum>;
  /** 会員ID */
  member_id?: Maybe<Scalars['String']['output']>;
  /** メッセージ */
  message?: Maybe<Scalars['String']['output']>;
  /** 筆頭者ID */
  parent_guest_id?: Maybe<Scalars['String']['output']>;
  /** 会費・ご祝儀支払い方法 */
  payment_method?: Maybe<PaymentMethodEnum>;
  /** 電話番号 */
  phone?: Maybe<Scalars['String']['output']>;
  /** 郵便番号 */
  postal_code?: Maybe<Scalars['String']['output']>;
  /** 都道府県 */
  prefecture?: Maybe<Scalars['String']['output']>;
  /** 間柄 */
  relationship?: Maybe<Scalars['String']['output']>;
  /** 決算金額 */
  settlement_amount?: Maybe<Scalars['Int']['output']>;
  /** システム利用料 */
  system_fee?: Maybe<Scalars['Int']['output']>;
  /** システム利用料率 */
  system_fee_rate?: Maybe<Scalars['Float']['output']>;
  /** 会費・ご祝儀・お気持ち金額合計金額 */
  total_amount?: Maybe<Scalars['Int']['output']>;
  /** 最終更新日時 */
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  /** 招待状ID */
  web_invitation_id?: Maybe<Scalars['String']['output']>;
  /** Web招待状返信日時 */
  web_invite_reply_datetime?: Maybe<Scalars['DateTime']['output']>;
};

export type GuestEventAnswer = {
  __typename?: 'GuestEventAnswer';
  /** 出欠 */
  attendance?: Maybe<Scalars['String']['output']>;
  /** 作成日時 */
  created_at: Scalars['DateTime']['output'];
  /** イベント日付 */
  date?: Maybe<Scalars['Date']['output']>;
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** ゲスト */
  guest?: Maybe<Guest>;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** イベント名 */
  name?: Maybe<Scalars['String']['output']>;
  /** 支払金額 */
  payment_amount?: Maybe<Scalars['Int']['output']>;
  /** 最終更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GuestEventAttendance = {
  __typename?: 'GuestEventAttendance';
  attendance?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type GuestEventAttendanceQueryInput = {
  attendance?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type GuestEventTag = {
  __typename?: 'GuestEventTag';
  id?: Maybe<Scalars['String']['output']>;
  tag?: Maybe<Scalars['String']['output']>;
};

export type GuestFreeItemValue = {
  __typename?: 'GuestFreeItemValue';
  /** 内容 */
  content: Scalars['String']['output'];
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** ゲスト */
  guest?: Maybe<Guest>;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** 項目名称 */
  name: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** A paginated list of GuestFreeItemValue items. */
export type GuestFreeItemValuePaginator = {
  __typename?: 'GuestFreeItemValuePaginator';
  /** A list of GuestFreeItemValue items. */
  data: Array<GuestFreeItemValue>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type GuestGroup = {
  __typename?: 'GuestGroup';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** ゲストリスト */
  guest_list: GuestList;
  /** ゲスト */
  guests: Array<Guest>;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** ゲストグループ名称 */
  name: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** A paginated list of GuestGroup items. */
export type GuestGroupPaginator = {
  __typename?: 'GuestGroupPaginator';
  /** A list of GuestGroup items. */
  data: Array<GuestGroup>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type GuestHonorMaster = {
  __typename?: 'GuestHonorMaster';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** 敬称名称 */
  name: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** A paginated list of GuestHonorMaster items. */
export type GuestHonorMasterPaginator = {
  __typename?: 'GuestHonorMasterPaginator';
  /** A list of GuestHonorMaster items. */
  data: Array<GuestHonorMaster>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type GuestList = {
  __typename?: 'GuestList';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** パーティ情報配列(アクセサ) */
  event_list?: Maybe<Scalars['Json']['output']>;
  /** ゲストグループ */
  guest_groups: Array<GuestGroup>;
  /** ゲストタグ */
  guest_tags: Array<GuestTag>;
  /** ゲスト */
  guests: Array<Guest>;
  id: Scalars['ID']['output'];
  /** デフォルトゲストリストフラグ */
  is_default?: Maybe<Scalars['Boolean']['output']>;
  /** 最新のゲスト更新日(アクセサ) */
  latest_guest_updated_at?: Maybe<Scalars['DateTime']['output']>;
  /** 会員 */
  member: Member;
  /** ゲストリスト名称 */
  name: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** ゲストタグ */
  web_invitations: Array<WebInvitation>;
};

/** A paginated list of GuestList items. */
export type GuestListPaginator = {
  __typename?: 'GuestListPaginator';
  /** A list of GuestList items. */
  data: Array<GuestList>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

/** A paginated list of Guest items. */
export type GuestPaginator = {
  __typename?: 'GuestPaginator';
  /** A list of Guest items. */
  data: Array<Guest>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type GuestPayments = {
  __typename?: 'GuestPayments';
  /** 口座名義 */
  account_name?: Maybe<Scalars['String']['output']>;
  /** 口座番号 */
  account_number?: Maybe<Scalars['String']['output']>;
  /** 口座種別 */
  account_type?: Maybe<AccountTypeEnum>;
  /** 新会員番号 */
  alternate_member_number?: Maybe<Scalars['String']['output']>;
  /** 銀行コード */
  bank_code?: Maybe<Scalars['String']['output']>;
  /** 銀行名 */
  bank_name?: Maybe<Scalars['String']['output']>;
  /** 支店コード */
  branch_code?: Maybe<Scalars['String']['output']>;
  /** 支店名 */
  branch_name?: Maybe<Scalars['String']['output']>;
  /** 手数料 */
  commission_fee?: Maybe<Scalars['Int']['output']>;
  /** 送金完了日時 */
  completion_datetime?: Maybe<Scalars['DateTime']['output']>;
  /** 送金日 */
  date?: Maybe<Scalars['Date']['output']>;
  /** 送金期限 */
  deadline_date?: Maybe<Scalars['Date']['output']>;
  /** メールアドレス */
  email?: Maybe<Scalars['String']['output']>;
  /** ゲストシステム利用料 */
  guest_system_fee?: Maybe<Scalars['Int']['output']>;
  /** Web招待状ID */
  id: Scalars['ID']['output'];
  /** 会員ID */
  member_id?: Maybe<Scalars['ID']['output']>;
  /** 会員名 */
  member_name?: Maybe<Scalars['String']['output']>;
  /** 会員番号 */
  member_number?: Maybe<Scalars['String']['output']>;
  /** 送金予定額 */
  member_settlement_amount?: Maybe<Scalars['Int']['output']>;
  /** 会員システム利用料 */
  member_system_fee?: Maybe<Scalars['Int']['output']>;
  /** web招待状名 */
  name?: Maybe<Scalars['String']['output']>;
  /** 電話番号 */
  phone?: Maybe<Scalars['String']['output']>;
  /** 事前支払い締め日 */
  prepayment_due_date?: Maybe<Scalars['Date']['output']>;
  /** 送金ステータス */
  status?: Maybe<MoneyTransferGuestPaymentStatusEnum>;
  /** システム利用料 */
  system_fee?: Maybe<Scalars['Int']['output']>;
  /** 会費・ご祝儀 */
  total_amount?: Maybe<Scalars['Int']['output']>;
};

/** A paginated list of GuestPayments items. */
export type GuestPaymentsPaginator = {
  __typename?: 'GuestPaymentsPaginator';
  /** A list of GuestPayments items. */
  data: Array<GuestPayments>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type GuestProfileIconMaster = {
  __typename?: 'GuestProfileIconMaster';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** 写真URL */
  photo_url: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** A paginated list of GuestProfileIconMaster items. */
export type GuestProfileIconMasterPaginator = {
  __typename?: 'GuestProfileIconMasterPaginator';
  /** A list of GuestProfileIconMaster items. */
  data: Array<GuestProfileIconMaster>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type GuestSurveyAnswer = {
  __typename?: 'GuestSurveyAnswer';
  /** 回答内容 */
  answer_content?: Maybe<Scalars['String']['output']>;
  /** 作成日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** ゲスト */
  guest?: Maybe<Guest>;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** 質問内容 */
  question?: Maybe<Scalars['String']['output']>;
  /** UI種類 */
  ui_type?: Maybe<UiTypeEnum>;
  /** 最終更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GuestTag = {
  __typename?: 'GuestTag';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** ゲストリスト */
  guest_list: GuestList;
  /** ゲスト */
  guests: Array<Guest>;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** タグ */
  tag: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type GuestTagGuest = {
  __typename?: 'GuestTagGuest';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** ゲスト */
  guest: Guest;
  /** ゲストタグ */
  guest_tag: GuestTag;
  id: Scalars['ID']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** A paginated list of GuestTagGuest items. */
export type GuestTagGuestPaginator = {
  __typename?: 'GuestTagGuestPaginator';
  /** A list of GuestTagGuest items. */
  data: Array<GuestTagGuest>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type GuestTagInput = {
  dirty?: InputMaybe<Scalars['Boolean']['input']>;
  selected?: InputMaybe<Scalars['Boolean']['input']>;
  tag?: InputMaybe<Scalars['String']['input']>;
  tag_id?: InputMaybe<Scalars['String']['input']>;
};

/** A paginated list of GuestTag items. */
export type GuestTagPaginator = {
  __typename?: 'GuestTagPaginator';
  /** A list of GuestTag items. */
  data: Array<GuestTag>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type GuestTagResponse = {
  __typename?: 'GuestTagResponse';
  /** ゲストID複数 */
  guest_ids?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  /** ゲストリストID */
  guest_list_id?: Maybe<Scalars['String']['output']>;
  /** タグ一覧 */
  tags?: Maybe<Array<TagResponse>>;
};

export type GuestTitleMaster = {
  __typename?: 'GuestTitleMaster';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** 肩書名称 */
  name: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** A paginated list of GuestTitleMaster items. */
export type GuestTitleMasterPaginator = {
  __typename?: 'GuestTitleMasterPaginator';
  /** A list of GuestTitleMaster items. */
  data: Array<GuestTitleMaster>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

/** Guest type enum */
export enum GuestTypeEnum {
  /** Bride */
  Bride = 'BRIDE',
  /** Etc */
  Etc = 'ETC',
  /** Groom */
  Groom = 'GROOM'
}

export type Image = {
  __typename?: 'Image';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** ファイル種類 */
  extension_type?: Maybe<Scalars['String']['output']>;
  /** ファイル種別 */
  file_type?: Maybe<FileType>;
  /** ユーザーファイル名 */
  name?: Maybe<Scalars['String']['output']>;
  /** オーナーID */
  owner_id?: Maybe<Scalars['String']['output']>;
  /** オーナー種類 */
  owner_type?: Maybe<OwnerType>;
  /** 画像・動画種別 */
  type?: Maybe<ImageTypeEnum>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  uuid: Scalars['ID']['output'];
};

export type ImageResponse = {
  __typename?: 'ImageResponse';
  /** 画像パス(有効期限つき) */
  presigned_url?: Maybe<Scalars['String']['output']>;
  /** 動画(有効期限つき) */
  presigned_url_main?: Maybe<Scalars['String']['output']>;
  /** サイズ指定 */
  size?: Maybe<Scalars['String']['output']>;
  /** 画像取得結果 */
  status?: Maybe<Scalars['Boolean']['output']>;
  /** ファイル種別 */
  type?: Maybe<ImageTypeEnum>;
  /** uuid */
  uuid?: Maybe<Scalars['String']['output']>;
};

/** Image type enum */
export enum ImageTypeEnum {
  /** Image */
  Image = 'IMAGE',
  /** Video */
  Video = 'VIDEO'
}

export type ImageUploadResponse = {
  __typename?: 'ImageUploadResponse';
  /** ファイル拡張子 */
  file_extension?: Maybe<Scalars['String']['output']>;
  /** オリジナル(元サイズ)画像 */
  presigned_url?: Maybe<Scalars['String']['output']>;
  /** Lサイズ画像 */
  presigned_url_l?: Maybe<Scalars['String']['output']>;
  /** Mサイズ画像 */
  presigned_url_m?: Maybe<Scalars['String']['output']>;
  /** 動画 */
  presigned_url_main?: Maybe<Scalars['String']['output']>;
  /** Sサイズ画像 */
  presigned_url_s?: Maybe<Scalars['String']['output']>;
  /** ファイル種別 */
  type?: Maybe<ImageTypeEnum>;
  /** uuid */
  uuid?: Maybe<Scalars['String']['output']>;
};

/** Invitation delivery enum */
export enum InvitationDeliveryEnum {
  /** Unset */
  Unset = 'UNSET',
  /** Web invitation */
  WebInvitation = 'WEB_INVITATION'
}

export type MSpecificationProduct = {
  __typename?: 'MSpecificationProduct';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** エディタテンプレート */
  editor_template_id?: Maybe<Scalars['ID']['output']>;
  id: Scalars['ID']['output'];
  /** 品番 */
  item_number?: Maybe<Scalars['String']['output']>;
  /** Web招待状マスタ */
  m_web_invitations?: Maybe<MWebInvitation>;
  /** 商品マスタ(販売期間検索なし) */
  no_period_product: Product;
  /** 商品マスタ(販売期間検索あり) */
  product?: Maybe<Product>;
  /** 規格別商品画像 */
  product_images: Array<ProductImage>;
  /** 規格明細1 */
  product_specification_detail_info_id1?: Maybe<Scalars['ID']['output']>;
  /** 規格明細2 */
  product_specification_detail_info_id2?: Maybe<Scalars['ID']['output']>;
  /** 規格明細3 */
  product_specification_detail_info_id3?: Maybe<Scalars['ID']['output']>;
  /** 通常価格 */
  regular_price?: Maybe<Scalars['Int']['output']>;
  /** 販売価格 */
  sale_price?: Maybe<Scalars['Int']['output']>;
  /** 販売価格利用期間(TO日時) */
  sale_price_end?: Maybe<Scalars['DateTime']['output']>;
  /** 販売価格利用期間(FROM日時) */
  sale_price_start?: Maybe<Scalars['DateTime']['output']>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** バリエーションテンプレート */
  variation_template_id?: Maybe<Scalars['ID']['output']>;
};

/** A paginated list of MSpecificationProduct items. */
export type MSpecificationProductPaginator = {
  __typename?: 'MSpecificationProductPaginator';
  /** A list of MSpecificationProduct items. */
  data: Array<MSpecificationProduct>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type MWebInvitation = {
  __typename?: 'MWebInvitation';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** CSSコード(置換) */
  css_code?: Maybe<Scalars['String']['output']>;
  /** CSSコード(置換　管理画面) */
  css_code_replace?: Maybe<Scalars['String']['output']>;
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** エディタ設定値JSON */
  editor_settings_json?: Maybe<Scalars['Json']['output']>;
  /** 画像アスペクト比設定値(Jsonエンコード) */
  encode_image_aspect_settings_json?: Maybe<Scalars['String']['output']>;
  /** 選択ファーストビュー */
  first_view_id?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  /** 画像アスペクト比設定値 */
  image_aspect_settings_json?: Maybe<Scalars['Json']['output']>;
  /** メインビジュアル画像利用不可FLG */
  is_main_visual_image_disabled: Scalars['Boolean']['output'];
  /** メインビジュアル画像差し替え可能FLG */
  is_main_visual_image_replaceable: Scalars['Boolean']['output'];
  /** プロフィール画像利用不可FLG */
  is_profile_image_disabled: Scalars['Boolean']['output'];
  /** 規格別商品マスタ */
  m_specification_product: MSpecificationProduct;
  /** WEB招待状テンプレートマスタ */
  m_web_invitation_template: MWebInvitationTemplate;
  /** Web招待状メインビジュアルブロックマスタ */
  m_web_invitation_visual_block: MWebInvitationVisualBlock;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** WEB招待状デザイン画像 */
  web_invitation_design_images: Array<WebInvitationDesignImage>;
};

export type MWebInvitationTemplate = {
  __typename?: 'MWebInvitationTemplate';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** テンプレート説明 */
  description: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  /** テンプレートイメージ画像 */
  image?: Maybe<Scalars['String']['output']>;
  /** WEB招待状マスタ */
  m_web_invitations?: Maybe<Array<MWebInvitation>>;
  /** テンプレート名 */
  name: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** WEB招待状共通テンプレートブロック */
  web_invitation_template_blocks?: Maybe<Array<WebInvitationTemplateBlock>>;
};

export type MWebInvitationVisualBlock = {
  __typename?: 'MWebInvitationVisualBlock';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** メインビジュアルコンポーネント画像ファイル名 */
  image_file_name: Scalars['String']['output'];
  /** メインビジュアルコンポーネント論理名 */
  logical_name: Scalars['String']['output'];
  /** メインビジュアルコンポーネント物理名 */
  physical_name: Scalars['String']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type MasterBlock = {
  __typename?: 'MasterBlock';
  /** 登録日時 */
  created_at?: Maybe<Scalars['DateTime']['output']>;
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** ID */
  id: Scalars['ID']['output'];
  /** Jsonデータ */
  json_data?: Maybe<Scalars['Json']['output']>;
  /** ブロック名称 */
  name?: Maybe<Scalars['String']['output']>;
  /** 商品種別 */
  product_type?: Maybe<ProductTypeEnum>;
  /** 更新日時 */
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  /** バリエーションテンプレート */
  variation_templates: Array<VariationTemplate>;
};

/** Media type enum */
export enum MediaTypeEnum {
  /** Media type image */
  MediaTypeImage = 'MEDIA_TYPE_IMAGE',
  /** Media type movie */
  MediaTypeMovie = 'MEDIA_TYPE_MOVIE'
}

export type Member = {
  __typename?: 'Member';
  /** 新会員番号 */
  alternate_member_number?: Maybe<Scalars['String']['output']>;
  /** 生年月日 */
  birthdate?: Maybe<Scalars['Date']['output']>;
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** メールアドレス */
  email: Scalars['String']['output'];
  email_verified_at?: Maybe<Scalars['DateTime']['output']>;
  /** 家族プロフィール */
  family_profiles?: Maybe<Array<FamilyProfile>>;
  /** 名 */
  first_name: Scalars['String']['output'];
  /** めい */
  first_name_kana?: Maybe<Scalars['String']['output']>;
  /** 名(romaji) */
  first_name_romaji?: Maybe<Scalars['String']['output']>;
  gift_arrangement_histories: Array<GiftArrangementHistory>;
  gift_shipping_infos: Array<GiftShippingInfo>;
  gift_wrappings: Array<GiftWrapping>;
  /** ゲストフリー項目値 */
  guest_free_item_values?: Maybe<Array<GuestFreeItemValue>>;
  /** ゲストリスト */
  guest_lists?: Maybe<Array<GuestList>>;
  /** ゲストタグ */
  guest_tags?: Maybe<Array<GuestTag>>;
  /** ゲスト */
  guests?: Maybe<Array<Guest>>;
  id: Scalars['ID']['output'];
  /** 口座登録アラートフラグ */
  isAccountAlertRegist?: Maybe<Scalars['Boolean']['output']>;
  /** 本会員FLG */
  is_regist?: Maybe<Scalars['Boolean']['output']>;
  /** SNSログイン時にパスワードでのログインも利用するか */
  is_use_password?: Maybe<Scalars['Boolean']['output']>;
  /** 姓 */
  last_name: Scalars['String']['output'];
  /** せい */
  last_name_kana?: Maybe<Scalars['String']['output']>;
  /** 姓(romaji) */
  last_name_romaji?: Maybe<Scalars['String']['output']>;
  /** 会員銀行口座 */
  member_bank_account?: Maybe<MemberBankAccount>;
  /** 会員登録時アンケート情報 */
  member_regist_questionnaires?: Maybe<Array<MemberRegistQuestionnaire>>;
  /** 送金 */
  money_transfers?: Maybe<Array<MoneyTransfer>>;
  /** 会員番号 */
  number?: Maybe<Scalars['String']['output']>;
  /** パスワード */
  password?: Maybe<Scalars['String']['output']>;
  /** SNSログインID */
  sns_login_id?: Maybe<Scalars['Json']['output']>;
  /** 仮会員UUID */
  tmp_uuid?: Maybe<Scalars['String']['output']>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** 結婚式情報 */
  wedding_info?: Maybe<WeddingInfo>;
};

export type MemberBankAccount = {
  __typename?: 'MemberBankAccount';
  /** 口座名義 */
  account_name?: Maybe<Scalars['String']['output']>;
  /** 口座番号 */
  account_number?: Maybe<Scalars['String']['output']>;
  /** 口座種別 */
  account_type?: Maybe<AccountTypeEnum>;
  /** 銀行コード */
  bank_code?: Maybe<Scalars['String']['output']>;
  /** 銀行名 */
  bank_name?: Maybe<Scalars['String']['output']>;
  /** 支店コード */
  branch_code?: Maybe<Scalars['String']['output']>;
  /** 支店名 */
  branch_name?: Maybe<Scalars['String']['output']>;
  /** 作成日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** 電話番号 */
  phone?: Maybe<Scalars['String']['output']>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** Member confirm type enum */
export enum MemberConfirmTypeEnum {
  /** New */
  New = 'New',
  /** Normal */
  Normal = 'Normal',
  /** Unread */
  Unread = 'Unread'
}

/** A paginated list of Member items. */
export type MemberPaginator = {
  __typename?: 'MemberPaginator';
  /** A list of Member items. */
  data: Array<Member>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

/** Member questionnaire type */
export enum MemberQuestionnaireType {
  /** Blog */
  Blog = 'BLOG',
  /** Instagram */
  Instagram = 'INSTAGRAM',
  /** Other */
  Other = 'OTHER',
  /** Referral */
  Referral = 'REFERRAL',
  /** Website */
  Website = 'WEBSITE',
  /** Web advertisement */
  WebAdvertisement = 'WEB_ADVERTISEMENT'
}

export type MemberRegistQuestionnaire = {
  __typename?: 'MemberRegistQuestionnaire';
  /** 回答 */
  answer?: Maybe<Scalars['String']['output']>;
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** 質問 */
  question: Scalars['Int']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type MoneyTransfer = {
  __typename?: 'MoneyTransfer';
  /** ゲストシステム使用料 */
  accessor_guest_system_fee?: Maybe<Scalars['Float']['output']>;
  /** 会員システム使用料 */
  accessor_member_system_fee?: Maybe<Scalars['Float']['output']>;
  /** システム使用料 */
  accessor_system_fee?: Maybe<Scalars['Float']['output']>;
  /** 口座名義 */
  account_name?: Maybe<Scalars['String']['output']>;
  /** 口座番号 */
  account_number?: Maybe<Scalars['String']['output']>;
  /** 口座種別 */
  account_type?: Maybe<AccountTypeEnum>;
  /** 管理人 */
  admin?: Maybe<Admin>;
  /** 銀行コード */
  bank_code?: Maybe<Scalars['String']['output']>;
  /** 銀行名 */
  bank_name?: Maybe<Scalars['String']['output']>;
  /** 支店コード */
  branch_code?: Maybe<Scalars['String']['output']>;
  /** 支店名 */
  branch_name?: Maybe<Scalars['String']['output']>;
  /** 手数料 */
  commission_fee?: Maybe<Scalars['Float']['output']>;
  /** 送金完了日時 */
  completion_datetime?: Maybe<Scalars['DateTime']['output']>;
  created_at?: Maybe<Scalars['DateTime']['output']>;
  /** 送金期限 */
  deadline_date?: Maybe<Scalars['Date']['output']>;
  /** ゲストシステム使用料 */
  guest_system_fee?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  /** 送金エラー */
  is_error?: Maybe<Scalars['Boolean']['output']>;
  /** 会員 */
  member: Member;
  /** 会員システム使用料 */
  member_system_fee?: Maybe<Scalars['Float']['output']>;
  /** 事前支払金額 */
  prepayment_amount?: Maybe<Scalars['Float']['output']>;
  /** 送金ステータス */
  status?: Maybe<MoneyTransferStatusEnum>;
  /** システム使用料 */
  system_fee?: Maybe<Scalars['Float']['output']>;
  /** 送金金額 */
  transfer_amount?: Maybe<Scalars['Float']['output']>;
  /** 送金日 */
  transfer_date?: Maybe<Scalars['Date']['output']>;
  updated_at?: Maybe<Scalars['DateTime']['output']>;
  /** Web招待状 */
  web_invitation: WebInvitation;
};

export type MoneyTransferCsvUpload = {
  __typename?: 'MoneyTransferCsvUpload';
  /** 失敗回数 */
  error_count?: Maybe<Scalars['Int']['output']>;
  /** 成功回数 */
  success_count?: Maybe<Scalars['Int']['output']>;
};

/** Money transfer error status enum */
export enum MoneyTransferErrorStatusEnum {
  /** All */
  All = 'ALL',
  /** Error */
  Error = 'ERROR',
  /** Not error */
  NotError = 'NOT_ERROR'
}

/** Money transfer guest payment status enum */
export enum MoneyTransferGuestPaymentStatusEnum {
  /** Completed */
  Completed = 'COMPLETED',
  /** Not transferred */
  NotTransferred = 'NOT_TRANSFERRED',
  /** Reservation */
  Reservation = 'RESERVATION',
  /** Unconfirmed */
  Unconfirmed = 'UNCONFIRMED'
}

/** A paginated list of MoneyTransfer items. */
export type MoneyTransferPaginator = {
  __typename?: 'MoneyTransferPaginator';
  /** A list of MoneyTransfer items. */
  data: Array<MoneyTransfer>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

/** Money transfer status enum */
export enum MoneyTransferStatusEnum {
  /** All */
  All = 'ALL',
  /** Completed */
  Completed = 'COMPLETED',
  /** Not transferred */
  NotTransferred = 'NOT_TRANSFERRED',
  /** Reservation */
  Reservation = 'RESERVATION'
}

export type Mutation = {
  __typename?: 'Mutation';
  /** ユーザー作成Web招待状データ 登録 */
  CreateWebInvitation?: Maybe<WebInvitation>;
  /** ユーザー作成Web招待状データ 変更 */
  UpdateWebInvitation?: Maybe<WebInvitation>;
  /** 商品マスタ（Web招待状）コピー */
  adminCopyProductWebInvitation: Product;
  /** 管理者情報登録 */
  adminCreateAdmin: Admin;
  /** 基本バリエーションテンプレート 登録 */
  adminCreateBasicVariationTemplate?: Maybe<BasicVariationTemplate>;
  /** 会員情報登録 */
  adminCreateMember: Member;
  /** 商品マスタ（Web招待状）登録 */
  adminCreateProductWebInvitation: Product;
  /** バリエーションテンプレート情報新規作成 */
  adminCreateVariationTemplate: VariationTemplate;
  /** 送金情報 入出金明細CSVインポート */
  adminCsvMoneyTransferImport: MoneyTransferCsvUpload;
  /** 管理者情報削除 */
  adminDeleteAdmin: Admin;
  /** 基本バリエーションテンプレート 削除 */
  adminDeleteBasicVariationTemplate?: Maybe<BasicVariationTemplate>;
  /** 会員情報削除 */
  adminDeleteMember: Member;
  /** 商品マスタ（Web招待状）削除 */
  adminDeleteProductWebInvitation: Product;
  /** バリエーションテンプレート情報 削除 */
  adminDeleteVariationTemplate?: Maybe<VariationTemplate>;
  /** 会員なりすましログイン */
  adminLoginMember?: Maybe<Scalars['String']['output']>;
  /** 管理者情報更新 */
  adminUpdateAdmin: Admin;
  /** 基本バリエーションテンプレート 更新 */
  adminUpdateBasicVariationTemplate: BasicVariationTemplate;
  /** 会員情報更新 */
  adminUpdateMember: Member;
  /** 送金更新 */
  adminUpdateMoneyTransfer: MoneyTransfer;
  /** 複数データの送金日を更新 */
  adminUpdateMultiplePaymentDate: Scalars['Boolean']['output'];
  /** 商品マスタ（Web招待状）登録 */
  adminUpdateProductWebInvitation: Product;
  /** バリエーションテンプレート情報変更 */
  adminUpdateVariationTemplate: VariationTemplate;
  /** ファイルアップロード(バイナリー) */
  binaryUploadImage: ImageResponse;
  /** ファイルアップロード(バイナリー) version2 */
  binaryUploadImage2: ImageUploadResponse;
  /** ゲスト情報一括登録 */
  bulkCreateGuest: Scalars['Boolean']['output'];
  /** ゲスト情報複数一括更新 */
  bulkUpdateGuest?: Maybe<Scalars['Boolean']['output']>;
  /** タグ一括編集用 保存API */
  bulkUpdateGuestTag: Scalars['Boolean']['output'];
  /** メールアドレス再設定URLのチェック */
  checkChangeEmailUrl: Scalars['Boolean']['output'];
  /** パスワード再設定URLのチェック */
  checkResetPasswordUrl: Scalars['Boolean']['output'];
  contactMail: Scalars['Boolean']['output'];
  /** ゲスト情報コピー */
  copyGuest?: Maybe<Array<Guest>>;
  /** ゲストリストコピー */
  copyGuestList: GuestList;
  /** コピー ユーザー作成Web招待状データ */
  copyWebInvitation?: Maybe<WebInvitation>;
  /** ゲスト情報新規作成 */
  createGuest: Guest;
  /** ゲストフリー項目値作成 */
  createGuestFreeItemValue: GuestFreeItemValue;
  /** ゲストグループ登録 */
  createGuestGroup: GuestGroup;
  /** ゲストリスト作成 */
  createGuestList: GuestList;
  /** ゲストタグ作成 */
  createGuestTag: GuestTag;
  /** タグとゲストの紐付け作成 */
  createGuestTagGuest: GuestTagGuest;
  /** 会員情報本登録 */
  createMember: Scalars['String']['output'];
  /** 会員銀行口座 登録 */
  createMemberBankAccount: MemberBankAccount;
  /** ゲスト自動登録(WEB招待状回答) */
  createWebInvitationGuest?: Maybe<Guest>;
  /** WEB招待状回答時の決済 */
  createWebInvitationGuestPayment?: Maybe<PaymentResult>;
  /** ゲスト自動登録(情報収集ツール) */
  createWebToolGuest: Guest;
  /** ゲスト情報削除 */
  deleteGuest: Scalars['Boolean']['output'];
  /** ゲストフリー項目値削除 */
  deleteGuestFreeItemValue?: Maybe<GuestFreeItemValue>;
  /** ゲストグループ削除 */
  deleteGuestGroup?: Maybe<GuestGroup>;
  /** ゲストリスト削除 */
  deleteGuestList: Scalars['Boolean']['output'];
  /** ゲストタグ削除 */
  deleteGuestTag?: Maybe<GuestTag>;
  /** タグとゲストの紐付け削除 */
  deleteGuestTagGuest?: Maybe<GuestTagGuest>;
  /** 会員削除(退会) */
  deleteMember?: Maybe<Member>;
  /** ユーザー作成Web招待状データ 削除 */
  deleteWebInvitation?: Maybe<WebInvitation>;
  /** ゲストファイルアップロード(バイナリー) */
  guestBinaryUploadImage: ImageResponse;
  /** ゲストファイルアップロード(バイナリー) version2 */
  guestBinaryUploadImage2: ImageUploadResponse;
  /** ゲストファイルアップロード(base64) */
  guestUploadImage?: Maybe<ImageResponse>;
  /** 管理画面ログイン */
  loginAdmin?: Maybe<Scalars['String']['output']>;
  /** 会員ログイン */
  loginMember?: Maybe<Scalars['String']['output']>;
  /** 管理画面ログアウト */
  logoutAdmin?: Maybe<Scalars['Boolean']['output']>;
  /** 会員ログアウト */
  logoutMember?: Maybe<Scalars['Boolean']['output']>;
  /** パスワード再設定 */
  resetPassword: Scalars['Boolean']['output'];
  /** メールアドレス再設定URL送信 */
  sendChangeEmailUrl: Scalars['Boolean']['output'];
  /** パスワードリセットURL送信 */
  sendResetPasswordUrl: Scalars['Boolean']['output'];
  /** 会員情報仮登録 */
  tmpCreateMember: Scalars['Boolean']['output'];
  /** ゲスト情報変更 */
  updateGuest: Guest;
  /** ゲストフリー項目値更新 */
  updateGuestFreeItemValue: GuestFreeItemValue;
  /** ゲストグループ変更 */
  updateGuestGroup: GuestGroup;
  /** ゲストリスト更新 */
  updateGuestList: GuestList;
  /** ゲストタグ更新 */
  updateGuestTag: GuestTag;
  /** タグとゲストの紐付け更新 */
  updateGuestTagGuest: GuestTagGuest;
  /** 画像アップロード拡張子更新 */
  updateImageExtension: Scalars['Boolean']['output'];
  /** 画像非表示更新 */
  updateImageHidden: Scalars['Boolean']['output'];
  /** 会員更新 */
  updateMember?: Maybe<Member>;
  /** 会員銀行口座 更新 */
  updateMemberBankAccount: MemberBankAccount;
  /** パスワード変更 */
  updateMemberPassword: Scalars['Boolean']['output'];
  /** ファイルアップロード(base64) */
  uploadImage: ImageResponse;
  /** 家族プロフィール 登録・更新 */
  upsertFamilyProfile?: Maybe<Array<FamilyProfile>>;
};


export type MutationCreateWebInvitationArgs = {
  input: CreateWebInvitationInput;
};


export type MutationUpdateWebInvitationArgs = {
  input: UpdateWebInvitationInput;
  is_synced?: InputMaybe<Scalars['Boolean']['input']>;
};


export type MutationAdminCopyProductWebInvitationArgs = {
  id: Scalars['ID']['input'];
};


export type MutationAdminCreateAdminArgs = {
  admin: AdminCreateAdminInput;
};


export type MutationAdminCreateBasicVariationTemplateArgs = {
  input: AdminCreateBasicVariationTemplateInput;
};


export type MutationAdminCreateMemberArgs = {
  member: AdminCreateMemberInput;
  wedding_info?: InputMaybe<AdminCreateWeddingInfoInput>;
};


export type MutationAdminCreateProductWebInvitationArgs = {
  m_specification_products?: InputMaybe<Array<AdminCreateMSpecificationProductInput>>;
  product: AdminCreateProductinput;
  product_tags?: InputMaybe<Array<AdminCreateProductTagInput>>;
};


export type MutationAdminCreateVariationTemplateArgs = {
  input: AdminCreateVariationTemplateInput;
  master_block_ids?: InputMaybe<Array<Scalars['ID']['input']>>;
};


export type MutationAdminCsvMoneyTransferImportArgs = {
  file: Scalars['Upload']['input'];
};


export type MutationAdminDeleteAdminArgs = {
  id: Scalars['ID']['input'];
};


export type MutationAdminDeleteBasicVariationTemplateArgs = {
  id: Scalars['ID']['input'];
};


export type MutationAdminDeleteMemberArgs = {
  id: Scalars['ID']['input'];
};


export type MutationAdminDeleteProductWebInvitationArgs = {
  id: Scalars['ID']['input'];
};


export type MutationAdminDeleteVariationTemplateArgs = {
  id: Scalars['ID']['input'];
};


export type MutationAdminLoginMemberArgs = {
  id: Scalars['ID']['input'];
};


export type MutationAdminUpdateAdminArgs = {
  admin: AdminUpdateAdminInput;
};


export type MutationAdminUpdateBasicVariationTemplateArgs = {
  input: AdminUpdateBasicVariationTemplateInput;
};


export type MutationAdminUpdateMemberArgs = {
  member: AdminUpdateMemberInput;
  wedding_info?: InputMaybe<AdminUpdateWeddingInfoInput>;
};


export type MutationAdminUpdateMoneyTransferArgs = {
  money_transfer: AdminUpdateMoneyTransferInput;
};


export type MutationAdminUpdateMultiplePaymentDateArgs = {
  date?: InputMaybe<Scalars['Date']['input']>;
  ids?: InputMaybe<Array<Scalars['ID']['input']>>;
};


export type MutationAdminUpdateProductWebInvitationArgs = {
  m_specification_products?: InputMaybe<Array<AdminUpdateMSpecificationProductInput>>;
  product: AdminUpdateProductinput;
  product_tags?: InputMaybe<Array<AdminCreateProductTagInput>>;
};


export type MutationAdminUpdateVariationTemplateArgs = {
  input: AdminUpdateVariationTemplateInput;
  master_block_ids?: InputMaybe<Array<Scalars['ID']['input']>>;
};


export type MutationBinaryUploadImageArgs = {
  file_data: Scalars['Upload']['input'];
  file_type: FileType;
};


export type MutationBinaryUploadImage2Args = {
  file_data: Scalars['Upload']['input'];
  file_type: FileType;
};


export type MutationBulkCreateGuestArgs = {
  input: Array<BulkCreateGuestInput>;
};


export type MutationBulkUpdateGuestArgs = {
  input?: InputMaybe<Array<BulkUpdateGuestInput>>;
};


export type MutationBulkUpdateGuestTagArgs = {
  input: BulkUpdateGuestTagInput;
};


export type MutationCheckChangeEmailUrlArgs = {
  token: Scalars['String']['input'];
};


export type MutationCheckResetPasswordUrlArgs = {
  token: Scalars['String']['input'];
};


export type MutationContactMailArgs = {
  content: Scalars['String']['input'];
  email: Scalars['String']['input'];
  first_name: Scalars['String']['input'];
  first_name_kana: Scalars['String']['input'];
  last_name: Scalars['String']['input'];
  last_name_kana: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCopyGuestArgs = {
  guest_ids: Array<Scalars['ID']['input']>;
  guest_list_id: Scalars['ID']['input'];
};


export type MutationCopyGuestListArgs = {
  id: Scalars['ID']['input'];
  name: Scalars['String']['input'];
};


export type MutationCopyWebInvitationArgs = {
  id: Scalars['String']['input'];
};


export type MutationCreateGuestArgs = {
  free_item_values?: InputMaybe<Array<CreateGuestFreeItemValueInput>>;
  guest_event_answers?: InputMaybe<Array<CreateGuestEventAnswerInput>>;
  guest_survey_answers?: InputMaybe<Array<CreateGuestSurveyAnswerInput>>;
  guests?: InputMaybe<Array<CreateGuestInput>>;
  input: CreateGuestInput;
};


export type MutationCreateGuestFreeItemValueArgs = {
  input: CreateGuestFreeItemValueInput;
};


export type MutationCreateGuestGroupArgs = {
  input: CreateGuestGroupInput;
};


export type MutationCreateGuestListArgs = {
  input: CreateGuestListInput;
};


export type MutationCreateGuestTagArgs = {
  input: CreateGuestTagInput;
};


export type MutationCreateGuestTagGuestArgs = {
  input: CreateGuestTagGuestInput;
};


export type MutationCreateMemberArgs = {
  uuid: Scalars['String']['input'];
};


export type MutationCreateMemberBankAccountArgs = {
  input: CreateMemberBankAccountInput;
};


export type MutationCreateWebInvitationGuestArgs = {
  free_item_values?: InputMaybe<Array<CreateGuestFreeItemValueNoValidateInput>>;
  guest_event_answers?: InputMaybe<Array<CreateGuestEventAnswerNoValidateInput>>;
  guest_survey_answers?: InputMaybe<Array<CreateGuestSurveyAnswerInput>>;
  guests: Array<CreateGuestNoValidateInput>;
  input: CreateGuestNoValidateInput;
  is_save: Scalars['Boolean']['input'];
  payment?: InputMaybe<PaymentInput>;
};


export type MutationCreateWebInvitationGuestPaymentArgs = {
  guests: Array<CreateGuestNoValidateInput>;
  input: CreateGuestNoValidateInput;
  payment?: InputMaybe<PaymentInput>;
};


export type MutationCreateWebToolGuestArgs = {
  guests: Array<CreateGuestInput>;
  input: CreateGuestInput;
};


export type MutationDeleteGuestArgs = {
  id: Array<Scalars['ID']['input']>;
  is_delete_all: Scalars['Boolean']['input'];
};


export type MutationDeleteGuestFreeItemValueArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteGuestGroupArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteGuestListArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteGuestTagArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteGuestTagGuestArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteWebInvitationArgs = {
  id: Scalars['ID']['input'];
};


export type MutationGuestBinaryUploadImageArgs = {
  dir_name: Scalars['String']['input'];
  file_data: Scalars['Upload']['input'];
};


export type MutationGuestBinaryUploadImage2Args = {
  dir_name: Scalars['String']['input'];
  file_data: Scalars['Upload']['input'];
};


export type MutationGuestUploadImageArgs = {
  dir_name: Scalars['String']['input'];
  file_data: Scalars['String']['input'];
};


export type MutationLoginAdminArgs = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationLoginMemberArgs = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationResetPasswordArgs = {
  password: Scalars['String']['input'];
  token: Scalars['String']['input'];
};


export type MutationSendChangeEmailUrlArgs = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationSendResetPasswordUrlArgs = {
  email: Scalars['String']['input'];
};


export type MutationTmpCreateMemberArgs = {
  input: CreateMemberInput;
  member_regist_questionnaire?: InputMaybe<Array<CreateMemberRegistQuestionnaireInput>>;
  wedding_info?: InputMaybe<CreateWeddingInfoInput>;
};


export type MutationUpdateGuestArgs = {
  free_item_values?: InputMaybe<Array<CreateGuestFreeItemValueInput>>;
  guest_event_answers?: InputMaybe<Array<CreateGuestEventAnswerInput>>;
  guest_survey_answers?: InputMaybe<Array<CreateGuestSurveyAnswerInput>>;
  guests?: InputMaybe<Array<UpdateGuestInput>>;
  input: UpdateGuestInput;
};


export type MutationUpdateGuestFreeItemValueArgs = {
  input: UpdateGuestFreeItemValueInput;
};


export type MutationUpdateGuestGroupArgs = {
  input: UpdateGuestGroupInput;
};


export type MutationUpdateGuestListArgs = {
  input: UpdateGuestListInput;
};


export type MutationUpdateGuestTagArgs = {
  input: UpdateGuestTagInput;
};


export type MutationUpdateGuestTagGuestArgs = {
  input: UpdateGuestTagGuestInput;
};


export type MutationUpdateImageExtensionArgs = {
  name?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateImageHiddenArgs = {
  uuid: Scalars['String']['input'];
};


export type MutationUpdateMemberArgs = {
  input?: InputMaybe<UpdateMemberInput>;
  wedding_info?: InputMaybe<UpdateWeddingInfoInput>;
};


export type MutationUpdateMemberBankAccountArgs = {
  input: UpdateMemberBankAccountInput;
};


export type MutationUpdateMemberPasswordArgs = {
  new_password: Scalars['String']['input'];
  password: Scalars['String']['input'];
};


export type MutationUploadImageArgs = {
  file_data: Scalars['String']['input'];
  file_type: FileType;
};


export type MutationUpsertFamilyProfileArgs = {
  input: Array<CreateFamilyProfileInput>;
  is_save: Scalars['Boolean']['input'];
};

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT'
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM'
}

/** Owner type */
export enum OwnerType {
  /** Owner type admin */
  OwnerTypeAdmin = 'OWNER_TYPE_ADMIN',
  /** Owner type list */
  OwnerTypeList = 'OWNER_TYPE_LIST',
  /** Owner type member */
  OwnerTypeMember = 'OWNER_TYPE_MEMBER'
}

/** Information about pagination using a fully featured paginator. */
export type PaginatorInfo = {
  __typename?: 'PaginatorInfo';
  /** Number of items in the current page. */
  count: Scalars['Int']['output'];
  /** Index of the current page. */
  currentPage: Scalars['Int']['output'];
  /** Index of the first item in the current page. */
  firstItem?: Maybe<Scalars['Int']['output']>;
  /** Are there more pages after this one? */
  hasMorePages: Scalars['Boolean']['output'];
  /** Index of the last item in the current page. */
  lastItem?: Maybe<Scalars['Int']['output']>;
  /** Index of the last available page. */
  lastPage: Scalars['Int']['output'];
  /** Number of items per page. */
  perPage: Scalars['Int']['output'];
  /** Number of total available items. */
  total: Scalars['Int']['output'];
};

export type PaymentHistory = {
  __typename?: 'PaymentHistory';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 引き出物手配り歴 */
  gift_arrangement_history: Array<GiftArrangementHistory>;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** 注文履歴 */
  order_history_id?: Maybe<Scalars['Int']['output']>;
  /** 決済履歴明細 */
  payment_history_details: Array<PaymentHistoryDetail>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type PaymentHistoryDetail = {
  __typename?: 'PaymentHistoryDetail';
  /** キャンセル日時 */
  cancel_date?: Maybe<Scalars['DateTime']['output']>;
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** キャンセルFLG */
  is_cancel: Scalars['Boolean']['output'];
  /** 決済履歴 */
  payment_history: PaymentHistory;
  /** 商品 */
  product: Product;
  /** 商品名 */
  product_name: Scalars['String']['output'];
  /** 数量 */
  quantity: Scalars['Int']['output'];
  /** 単価 */
  unit_price: Scalars['Int']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type PaymentInput = {
  /** 取引ID */
  access_id?: InputMaybe<Scalars['String']['input']>;
  /** 取引パスワード */
  access_pass?: InputMaybe<Scalars['String']['input']>;
  /** callback_url */
  callback_url?: InputMaybe<Scalars['String']['input']>;
  /** カード決済Token */
  card_token?: InputMaybe<Scalars['String']['input']>;
  /** order_id */
  order_id?: InputMaybe<Scalars['String']['input']>;
};

/** Payment method enum */
export enum PaymentMethodEnum {
  /** Advance payment */
  AdvancePayment = 'ADVANCE_PAYMENT',
  /** Bring on the day */
  BringOnTheDay = 'BRING_ON_THE_DAY',
  /** Paid */
  Paid = 'PAID'
}

export type PaymentResult = {
  __typename?: 'PaymentResult';
  access_id?: Maybe<Scalars['String']['output']>;
  access_pass?: Maybe<Scalars['String']['output']>;
  order_id?: Maybe<Scalars['String']['output']>;
  redirect_url?: Maybe<Scalars['String']['output']>;
};

export type Product = {
  __typename?: 'Product';
  admin_notes?: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  display_order?: Maybe<Scalars['String']['output']>;
  estimated_delivery_days?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  is_editor?: Maybe<Scalars['Boolean']['output']>;
  is_reservation_period_specified?: Maybe<Scalars['Boolean']['output']>;
  is_sales_period_specified?: Maybe<Scalars['Boolean']['output']>;
  is_sample_request_allowed?: Maybe<Scalars['Boolean']['output']>;
  is_specification?: Maybe<Scalars['Boolean']['output']>;
  is_unpublished?: Maybe<Scalars['Boolean']['output']>;
  is_use_component?: Maybe<Scalars['Boolean']['output']>;
  is_variation_specification?: Maybe<Scalars['Boolean']['output']>;
  m_specification_products?: Maybe<Array<MSpecificationProduct>>;
  meta_canonical?: Maybe<Scalars['String']['output']>;
  meta_description?: Maybe<Scalars['String']['output']>;
  meta_keywords?: Maybe<Scalars['String']['output']>;
  meta_title?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  option_product_price_difference?: Maybe<Scalars['Int']['output']>;
  product_description?: Maybe<Scalars['String']['output']>;
  product_inventory?: Maybe<Scalars['Int']['output']>;
  product_type_code?: Maybe<Scalars['String']['output']>;
  reservation_period_end?: Maybe<Scalars['DateTime']['output']>;
  reservation_period_start?: Maybe<Scalars['DateTime']['output']>;
  sales_period_end?: Maybe<Scalars['DateTime']['output']>;
  sales_period_start?: Maybe<Scalars['DateTime']['output']>;
  shipping_cost?: Maybe<Scalars['Int']['output']>;
  tags: Array<Tag>;
  updated_at: Scalars['DateTime']['output'];
};

export type ProductImage = {
  __typename?: 'ProductImage';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** 画像 */
  image: Image;
  /** 画像コメント */
  image_comment?: Maybe<Scalars['String']['output']>;
  /** 規格別商品マスタ */
  m_specification_product: MSpecificationProduct;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** uuid */
  uuid: Scalars['String']['output'];
};

/** A paginated list of Product items. */
export type ProductPaginator = {
  __typename?: 'ProductPaginator';
  /** A list of Product items. */
  data: Array<Product>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type ProductTag = {
  __typename?: 'ProductTag';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** 商品マスタ */
  product?: Maybe<Product>;
  /** 並び順 */
  sort_order?: Maybe<Scalars['Int']['output']>;
  /** タグマスタ */
  tag?: Maybe<Tag>;
  /** タグ利用分類 */
  tag_type?: Maybe<Scalars['Int']['output']>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** Product type enum */
export enum ProductTypeEnum {
  /** Download product */
  DownloadProduct = 'DOWNLOAD_PRODUCT',
  /** Gift delivery arrangement */
  GiftDeliveryArrangement = 'GIFT_DELIVERY_ARRANGEMENT',
  /** Invitation */
  Invitation = 'INVITATION',
  /** Marriage announcement card */
  MarriageAnnouncementCard = 'MARRIAGE_ANNOUNCEMENT_CARD',
  /** Normal product */
  NormalProduct = 'NORMAL_PRODUCT',
  /** Petit gift */
  PetitGift = 'PETIT_GIFT',
  /** Seating chart */
  SeatingChart = 'SEATING_CHART',
  /** Set product */
  SetProduct = 'SET_PRODUCT',
  /** Web invitation */
  WebInvitation = 'WEB_INVITATION',
  /** Welcome board */
  WelcomeBoard = 'WELCOME_BOARD'
}

export type Query = {
  __typename?: 'Query';
  /** 会員情報詳細取得 */
  adminAdmin?: Maybe<Admin>;
  /** 管理者情報一覧取得 */
  adminAdmins: AdminPaginator;
  /** ゲスト詳細情報取得 */
  adminGuest?: Maybe<Guest>;
  /** ゲストフリー項目値検索詳細取得 */
  adminGuestFreeItemValue?: Maybe<GuestFreeItemValue>;
  /** ゲストフリー項目値一覧取得 */
  adminGuestFreeItemValues: GuestFreeItemValuePaginator;
  /** ゲストグループ検索詳細取得 */
  adminGuestGroup?: Maybe<GuestGroup>;
  /** ゲストグループ一覧取得 */
  adminGuestGroups: GuestGroupPaginator;
  /** ゲスト敬称初期値マスタ検索詳細取得 */
  adminGuestHonorMaster?: Maybe<GuestHonorMaster>;
  /** ゲスト敬称初期値マスタ一覧取得 */
  adminGuestHonorMasters: GuestHonorMasterPaginator;
  /** ゲストリスト検索詳細取得 */
  adminGuestList?: Maybe<GuestList>;
  /** ゲストリスト一覧取得 */
  adminGuestLists: GuestListPaginator;
  /** 送金情報詳細取得 */
  adminGuestPayment?: Maybe<GuestPayments>;
  /** 送金情報一覧取得 */
  adminGuestPayments: GuestPaymentsPaginator;
  /** ゲストプロフィールアイコンマスタ検索詳細取得 */
  adminGuestProfileIconMaster?: Maybe<GuestProfileIconMaster>;
  /** ゲストプロフィールアイコンマスタ一覧取得 */
  adminGuestProfileIconMasters: GuestProfileIconMasterPaginator;
  /** ゲストタグ取得詳細取得 */
  adminGuestTag?: Maybe<GuestTag>;
  /** ゲストタグゲスト検索詳細取得 */
  adminGuestTagGuest?: Maybe<GuestTagGuest>;
  /** ゲストタグゲスト一覧取得 */
  adminGuestTagGuests: GuestTagGuestPaginator;
  /** ゲストタグ一覧取得 */
  adminGuestTags: GuestTagPaginator;
  /** ゲスト肩書リスト初期値マスタ検索詳細取得 */
  adminGuestTitleMaster?: Maybe<GuestTitleMaster>;
  /** ゲスト肩書リスト初期値マスタ一覧取得 */
  adminGuestTitleMasters: GuestTitleMasterPaginator;
  /** ゲスト一覧取得 */
  adminGuests: GuestPaginator;
  /** WEB招待状共通テンプレートマスタ詳細取得 */
  adminMWebInvitationTemplate?: Maybe<MWebInvitationTemplate>;
  /** Web招待状共通テンプレートマスター一覧取得 */
  adminMWebInvitationTemplates: Array<MWebInvitationTemplate>;
  /** WEB招待状メインビジュアルブロックマスタ詳細 取得 */
  adminMWebInvitationVisualBlock?: Maybe<MWebInvitationVisualBlock>;
  /** WEB招待状メインビジュアルブロックマスタ一覧取得 */
  adminMWebInvitationVisualBlocks: Array<MWebInvitationVisualBlock>;
  /** 会員情報詳細取得 */
  adminMember?: Maybe<Member>;
  /** 会員情報一覧取得 */
  adminMembers: MemberPaginator;
  /** 送金情報詳細取得 */
  adminMoneyTransfer?: Maybe<MoneyTransfer>;
  /** 送金情報一覧取得 */
  adminMoneyTransfers: MoneyTransferPaginator;
  /** 商品マスタ（Web招待状）詳細取得 */
  adminProductWebInvitation?: Maybe<Product>;
  /** 商品マスタ（Web招待状）一覧取得 */
  adminProductWebInvitations: WebInvitationProductPaginator;
  adminTags: Array<Tag>;
  /** 送金情報合計金額 */
  adminTotalGuestPaymentAmount: Scalars['Int']['output'];
  /** 送金情報合計金額 */
  adminTotalMoneyTransferAmount?: Maybe<Scalars['Int']['output']>;
  /** 基本バリエーションテンプレート詳細取得 */
  basicVariationTemplate?: Maybe<BasicVariationTemplate>;
  /** 基本バリエーションテンプレート一覧取得 */
  basicVariationTemplates: BasicVariationTemplatePaginator;
  /** 会費ご祝儀詳細 */
  celebrationFeeDetail: Array<CelebrationFee>;
  /** 定数取得 */
  constants?: Maybe<Constant>;
  /** ゲスト新規登録者数取得 */
  countNewGuest: Scalars['Int']['output'];
  /** web招待状データ登録前のイベント パーティ情報を作成して取得するAPI */
  eventLists?: Maybe<Scalars['Json']['output']>;
  /** 文例集 */
  exampleSentences?: Maybe<ExampleSentenceGroup>;
  /** ゲスト詳細情報取得 */
  guest?: Maybe<Guest>;
  /** ゲストフリー項目値ID検索 */
  guestFreeItemValue?: Maybe<GuestFreeItemValue>;
  /** ゲストフリー項目値取得 */
  guestFreeItemValues: Array<GuestFreeItemValue>;
  /** ゲストグループID検索 */
  guestGroup?: Maybe<GuestGroup>;
  /** ゲストグループ取得 */
  guestGroups: Array<GuestGroup>;
  /** ゲスト敬称リスト初期値マスタID検索 */
  guestHonorMaster?: Maybe<GuestHonorMaster>;
  /** ゲスト敬称初期値マスタ一覧取得 */
  guestHonorMasters: Array<GuestHonorMaster>;
  /** ゲストリストID検索 */
  guestList?: Maybe<GuestList>;
  /** ゲストリスト一覧取得 */
  guestLists: Array<GuestList>;
  /** ゲストプロフィールプリセットアイコンマスタID検索 */
  guestProfileIconMaster?: Maybe<GuestProfileIconMaster>;
  /** ゲストプロフィールアイコンマスタ一覧取得 */
  guestProfileIconMasters: Array<GuestProfileIconMaster>;
  /** ゲストタグID検索 */
  guestTag?: Maybe<GuestTag>;
  /** タグとゲストの紐付けID検索 */
  guestTagGuest?: Maybe<GuestTagGuest>;
  /** タグとゲストの紐付け全レコード取得 */
  guestTagGuests: Array<GuestTagGuest>;
  /** ゲストタグ取得 */
  guestTags: Array<GuestTag>;
  /** ゲスト肩書リスト初期値マスタID検索 */
  guestTitleMaster?: Maybe<GuestTitleMaster>;
  /** ゲスト肩書リスト初期値マスタ一覧取得 */
  guestTitleMasters: Array<GuestTitleMaster>;
  /** ゲスト一覧取得 */
  guests: Array<GuestEvent>;
  /** 会員送金ステータス情報存在チェック */
  hasMoneyTransfer: Scalars['Boolean']['output'];
  /** 未払いの招待状が存在するかチェック */
  hasUnpaidInvitations?: Maybe<Scalars['Boolean']['output']>;
  /** 共通ファイル取得 */
  images: Array<ImageResponse>;
  /** 共通ファイル取得 version2 */
  images2: Array<ImageResponse>;
  /** ログインチェック */
  loginCheck: Scalars['Boolean']['output'];
  /** 商品マスタ（Web招待状）詳細取得 旧 */
  mSpecificationProduct: MSpecificationProduct;
  /** 商品マスタ（Web招待状）一覧取得 旧 */
  mSpecificationProducts: MSpecificationProductPaginator;
  /** WEB招待状共通テンプレートマスタ詳細取得 */
  mWebInvitationTemplate: MWebInvitationTemplate;
  /** ファイルタイプに基づくファイル一覧を取得(デフォルトはおすすめ素材) */
  materialListImages: Array<ImageResponse>;
  meAdmin?: Maybe<Admin>;
  /** 会員アップロード素材一覧取得 */
  memberMaterialListImages: Array<ImageResponse>;
  /** ログイン者情報取得 */
  memberMe?: Maybe<Member>;
  /** 商品マスタ（Web招待状）詳細取得 新 */
  productWebInvitation?: Maybe<Product>;
  /** 商品マスタ（Web招待状）一覧取得 新 */
  productWebInvitations: ProductPaginator;
  /** システム設定の最初の一件を取得 */
  system?: Maybe<System>;
  tagGroup: TagGroup;
  /** タグ一括編集用 タグ一覧取得 */
  updateGuestTags?: Maybe<GuestTagResponse>;
  /** ゲストユーザー作成web招待データ詳細 */
  urlWebInvitation?: Maybe<WebInvitation>;
  /** バリエーションテンプレート詳細取得 */
  variationTemplate?: Maybe<VariationTemplate>;
  /** バリエーションテンプレート 一覧取得 */
  variationTemplates: VariationTemplatePaginator;
  /** ユーザー作成Web招待状データ詳細 */
  webInvitation?: Maybe<WebInvitation>;
  /** ユーザー作成Web招待状データ 一覧取得 */
  webInvitations: Array<WebInvitation>;
};


export type QueryAdminAdminArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminAdminsArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  first?: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminGuestArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestFreeItemValueArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestFreeItemValuesArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryAdminGuestFreeItemValuesOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminGuestGroupArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestGroupsArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryAdminGuestGroupsOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminGuestHonorMasterArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestHonorMastersArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryAdminGuestHonorMastersOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminGuestListArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestListsArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryAdminGuestListsOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminGuestPaymentArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestPaymentsArgs = {
  account?: InputMaybe<RegistrationEnum>;
  dead_line_from_date?: InputMaybe<Scalars['Date']['input']>;
  dead_line_to_date?: InputMaybe<Scalars['Date']['input']>;
  first?: Scalars['Int']['input'];
  from_date?: InputMaybe<Scalars['Date']['input']>;
  member_name?: InputMaybe<Scalars['String']['input']>;
  member_number?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<Array<MoneyTransferGuestPaymentStatusEnum>>;
  to_date?: InputMaybe<Scalars['Date']['input']>;
};


export type QueryAdminGuestProfileIconMasterArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestProfileIconMastersArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryAdminGuestProfileIconMastersOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminGuestTagArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestTagGuestArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestTagGuestsArgs = {
  first?: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminGuestTagsArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryAdminGuestTagsOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminGuestTitleMasterArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminGuestTitleMastersArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryAdminGuestTitleMastersOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminGuestsArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryAdminGuestsOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminMWebInvitationTemplateArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminMWebInvitationVisualBlockArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminMemberArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminMembersArgs = {
  email?: InputMaybe<Scalars['String']['input']>;
  first?: Scalars['Int']['input'];
  is_active?: InputMaybe<Scalars['Int']['input']>;
  is_regist?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  number?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryAdminMoneyTransferArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminMoneyTransfersArgs = {
  account?: InputMaybe<RegistrationEnum>;
  error_status?: InputMaybe<MoneyTransferErrorStatusEnum>;
  first?: Scalars['Int']['input'];
  from?: InputMaybe<Scalars['Date']['input']>;
  member_name?: InputMaybe<Scalars['String']['input']>;
  member_number?: InputMaybe<Scalars['String']['input']>;
  orderBy?: InputMaybe<Array<QueryAdminMoneyTransfersOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
  status?: InputMaybe<MoneyTransferStatusEnum>;
  to?: InputMaybe<Scalars['Date']['input']>;
  transfer_from?: InputMaybe<Scalars['Date']['input']>;
  transfer_to?: InputMaybe<Scalars['Date']['input']>;
};


export type QueryAdminProductWebInvitationArgs = {
  id: Scalars['ID']['input'];
};


export type QueryAdminProductWebInvitationsArgs = {
  first?: Scalars['Int']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  sales_period_end?: InputMaybe<Scalars['DateTime']['input']>;
  sales_period_start?: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryAdminTotalGuestPaymentAmountArgs = {
  account?: InputMaybe<RegistrationEnum>;
  dead_line_from_date?: InputMaybe<Scalars['Date']['input']>;
  dead_line_to_date?: InputMaybe<Scalars['Date']['input']>;
  from_date?: InputMaybe<Scalars['Date']['input']>;
  member_name?: InputMaybe<Scalars['String']['input']>;
  member_number?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Array<MoneyTransferGuestPaymentStatusEnum>>;
  to_date?: InputMaybe<Scalars['Date']['input']>;
};


export type QueryAdminTotalMoneyTransferAmountArgs = {
  account?: InputMaybe<RegistrationEnum>;
  error_status?: InputMaybe<MoneyTransferErrorStatusEnum>;
  from?: InputMaybe<Scalars['Date']['input']>;
  member_name?: InputMaybe<Scalars['String']['input']>;
  member_number?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<MoneyTransferStatusEnum>;
  to?: InputMaybe<Scalars['Date']['input']>;
  transfer_from?: InputMaybe<Scalars['Date']['input']>;
  transfer_to?: InputMaybe<Scalars['Date']['input']>;
};


export type QueryBasicVariationTemplateArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
};


export type QueryBasicVariationTemplatesArgs = {
  first?: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryConstantsArgs = {
  constant: Array<ConstantEnum>;
};


export type QueryCountNewGuestArgs = {
  member_confirm_type: MemberConfirmTypeEnum;
};


export type QueryEventListsArgs = {
  event_list: Scalars['Json']['input'];
};


export type QueryExampleSentencesArgs = {
  example_type1?: InputMaybe<ExampleType1Enum>;
  example_type2?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGuestArgs = {
  id: Scalars['ID']['input'];
  is_update_member_confirm_type: Scalars['Boolean']['input'];
};


export type QueryGuestFreeItemValueArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGuestFreeItemValuesArgs = {
  guest_id: Scalars['ID']['input'];
};


export type QueryGuestGroupArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGuestGroupsArgs = {
  guest_list_id: Scalars['ID']['input'];
};


export type QueryGuestHonorMasterArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGuestHonorMastersArgs = {
  orderBy?: InputMaybe<Array<QueryGuestHonorMastersOrderByOrderByClause>>;
};


export type QueryGuestListArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGuestListsArgs = {
  orderBy?: InputMaybe<Array<QueryGuestListsOrderByOrderByClause>>;
};


export type QueryGuestProfileIconMasterArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGuestProfileIconMastersArgs = {
  orderBy?: InputMaybe<Array<QueryGuestProfileIconMastersOrderByOrderByClause>>;
};


export type QueryGuestTagArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGuestTagGuestArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGuestTagsArgs = {
  guest_list_id: Scalars['ID']['input'];
};


export type QueryGuestTitleMasterArgs = {
  id: Scalars['ID']['input'];
};


export type QueryGuestTitleMastersArgs = {
  orderBy?: InputMaybe<Array<QueryGuestTitleMastersOrderByOrderByClause>>;
};


export type QueryGuestsArgs = {
  guest_event_attendances?: InputMaybe<Array<GuestEventAttendanceQueryInput>>;
  guest_group_ids?: InputMaybe<Array<Scalars['String']['input']>>;
  guest_list_id: Scalars['String']['input'];
  guest_name?: InputMaybe<Scalars['String']['input']>;
  guest_tag_ids?: InputMaybe<Array<Scalars['String']['input']>>;
  guest_type?: InputMaybe<GuestTypeEnum>;
  is_update_member_confirm_type: Scalars['Boolean']['input'];
  orderBy?: InputMaybe<Array<QueryGuestsOrderByRelationOrderByClause>>;
  web_invitation_id?: InputMaybe<Scalars['String']['input']>;
};


export type QueryHasMoneyTransferArgs = {
  status: MoneyTransferStatusEnum;
};


export type QueryImagesArgs = {
  uuids: Array<Scalars['String']['input']>;
};


export type QueryImages2Args = {
  uuids: Array<Scalars['String']['input']>;
};


export type QueryMSpecificationProductArgs = {
  id: Scalars['ID']['input'];
};


export type QueryMSpecificationProductsArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryMSpecificationProductsOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
  product_tag_ids?: InputMaybe<Array<Scalars['ID']['input']>>;
};


export type QueryMWebInvitationTemplateArgs = {
  id: Scalars['ID']['input'];
};


export type QueryMaterialListImagesArgs = {
  file_type?: InputMaybe<FileType>;
};


export type QueryProductWebInvitationArgs = {
  id: Scalars['ID']['input'];
};


export type QueryProductWebInvitationsArgs = {
  first?: Scalars['Int']['input'];
  orderBy?: InputMaybe<Array<QueryProductWebInvitationsOrderByOrderByClause>>;
  page?: InputMaybe<Scalars['Int']['input']>;
  product_tag_ids?: InputMaybe<Array<Scalars['ID']['input']>>;
};


export type QueryTagGroupArgs = {
  id: Scalars['ID']['input'];
};


export type QueryUpdateGuestTagsArgs = {
  guest_ids: Array<InputMaybe<Scalars['ID']['input']>>;
  guest_list_id: Scalars['ID']['input'];
};


export type QueryUrlWebInvitationArgs = {
  public_url: Scalars['String']['input'];
};


export type QueryVariationTemplateArgs = {
  id?: InputMaybe<Scalars['ID']['input']>;
};


export type QueryVariationTemplatesArgs = {
  first?: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  product_type?: InputMaybe<ProductTypeEnum>;
};


export type QueryWebInvitationArgs = {
  id: Scalars['ID']['input'];
};


export type QueryWebInvitationsArgs = {
  orderBy?: InputMaybe<Array<QueryWebInvitationsOrderByOrderByClause>>;
};

/** Allowed column names for Query.adminGuestFreeItemValues.orderBy. */
export enum QueryAdminGuestFreeItemValuesOrderByColumn {
  Content = 'CONTENT',
  GuestId = 'GUEST_ID',
  Name = 'NAME'
}

/** Order by clause for Query.adminGuestFreeItemValues.orderBy. */
export type QueryAdminGuestFreeItemValuesOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryAdminGuestFreeItemValuesOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.adminGuestGroups.orderBy. */
export enum QueryAdminGuestGroupsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  GuestListId = 'GUEST_LIST_ID',
  Name = 'NAME'
}

/** Order by clause for Query.adminGuestGroups.orderBy. */
export type QueryAdminGuestGroupsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryAdminGuestGroupsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.adminGuestHonorMasters.orderBy. */
export enum QueryAdminGuestHonorMastersOrderByColumn {
  CreatedAt = 'CREATED_AT',
  Name = 'NAME'
}

/** Order by clause for Query.adminGuestHonorMasters.orderBy. */
export type QueryAdminGuestHonorMastersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryAdminGuestHonorMastersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.adminGuestLists.orderBy. */
export enum QueryAdminGuestListsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  MemberId = 'MEMBER_ID',
  Name = 'NAME'
}

/** Order by clause for Query.adminGuestLists.orderBy. */
export type QueryAdminGuestListsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryAdminGuestListsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.adminGuestProfileIconMasters.orderBy. */
export enum QueryAdminGuestProfileIconMastersOrderByColumn {
  CreatedAt = 'CREATED_AT',
  PhotoUrl = 'PHOTO_URL'
}

/** Order by clause for Query.adminGuestProfileIconMasters.orderBy. */
export type QueryAdminGuestProfileIconMastersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryAdminGuestProfileIconMastersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.adminGuestTags.orderBy. */
export enum QueryAdminGuestTagsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  GuestListId = 'GUEST_LIST_ID',
  Tag = 'TAG'
}

/** Order by clause for Query.adminGuestTags.orderBy. */
export type QueryAdminGuestTagsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryAdminGuestTagsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.adminGuestTitleMasters.orderBy. */
export enum QueryAdminGuestTitleMastersOrderByColumn {
  CreatedAt = 'CREATED_AT',
  Name = 'NAME'
}

/** Order by clause for Query.adminGuestTitleMasters.orderBy. */
export type QueryAdminGuestTitleMastersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryAdminGuestTitleMastersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.adminGuests.orderBy. */
export enum QueryAdminGuestsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  FirstName = 'FIRST_NAME',
  FirstNameKana = 'FIRST_NAME_KANA',
  FirstNameRomaji = 'FIRST_NAME_ROMAJI',
  GuestListId = 'GUEST_LIST_ID',
  LastName = 'LAST_NAME',
  LastNameKana = 'LAST_NAME_KANA',
  LastNameRomaji = 'LAST_NAME_ROMAJI'
}

/** Order by clause for Query.adminGuests.orderBy. */
export type QueryAdminGuestsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryAdminGuestsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.adminMoneyTransfers.orderBy. */
export enum QueryAdminMoneyTransfersOrderByColumn {
  DeadlineDate = 'DEADLINE_DATE',
  TransferAmount = 'TRANSFER_AMOUNT'
}

/** Order by clause for Query.adminMoneyTransfers.orderBy. */
export type QueryAdminMoneyTransfersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryAdminMoneyTransfersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.guestHonorMasters.orderBy. */
export enum QueryGuestHonorMastersOrderByColumn {
  CreatedAt = 'CREATED_AT',
  Name = 'NAME'
}

/** Order by clause for Query.guestHonorMasters.orderBy. */
export type QueryGuestHonorMastersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryGuestHonorMastersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.guestLists.orderBy. */
export enum QueryGuestListsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  IsDefault = 'IS_DEFAULT',
  Name = 'NAME'
}

/** Order by clause for Query.guestLists.orderBy. */
export type QueryGuestListsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryGuestListsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.guestProfileIconMasters.orderBy. */
export enum QueryGuestProfileIconMastersOrderByColumn {
  CreatedAt = 'CREATED_AT',
  PhotoUrl = 'PHOTO_URL'
}

/** Order by clause for Query.guestProfileIconMasters.orderBy. */
export type QueryGuestProfileIconMastersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryGuestProfileIconMastersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.guestTitleMasters.orderBy. */
export enum QueryGuestTitleMastersOrderByColumn {
  CreatedAt = 'CREATED_AT',
  Name = 'NAME'
}

/** Order by clause for Query.guestTitleMasters.orderBy. */
export type QueryGuestTitleMastersOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryGuestTitleMastersOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.guests.orderBy. */
export enum QueryGuestsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  Name = 'NAME'
}

/** Aggregate specification for Query.guests.orderBy.guest_group. */
export type QueryGuestsOrderByGuest_Group = {
  /** The aggregate function to apply to the column. */
  aggregate: OrderByRelationWithColumnAggregateFunction;
  /** Name of the column to use. */
  column?: InputMaybe<QueryGuestsOrderByGuest_GroupColumn>;
};

/** Allowed column names for Query.guests.orderBy. */
export enum QueryGuestsOrderByGuest_GroupColumn {
  Name = 'NAME'
}

/** Order by clause for Query.guests.orderBy. */
export type QueryGuestsOrderByRelationOrderByClause = {
  /** The column that is used for ordering. */
  column?: InputMaybe<QueryGuestsOrderByColumn>;
  /** Aggregate specification. */
  guest_group?: InputMaybe<QueryGuestsOrderByGuest_Group>;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.mSpecificationProducts.orderBy. */
export enum QueryMSpecificationProductsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  UpdatedAt = 'UPDATED_AT'
}

/** Order by clause for Query.mSpecificationProducts.orderBy. */
export type QueryMSpecificationProductsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryMSpecificationProductsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.productWebInvitations.orderBy. */
export enum QueryProductWebInvitationsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  DisplayOrder = 'DISPLAY_ORDER',
  UpdatedAt = 'UPDATED_AT'
}

/** Order by clause for Query.productWebInvitations.orderBy. */
export type QueryProductWebInvitationsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryProductWebInvitationsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Allowed column names for Query.webInvitations.orderBy. */
export enum QueryWebInvitationsOrderByColumn {
  CreatedAt = 'CREATED_AT',
  UpdatedAt = 'UPDATED_AT'
}

/** Order by clause for Query.webInvitations.orderBy. */
export type QueryWebInvitationsOrderByOrderByClause = {
  /** The column that is used for ordering. */
  column: QueryWebInvitationsOrderByColumn;
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Registration enum */
export enum RegistrationEnum {
  /** All */
  All = 'All',
  /** Registered */
  Registered = 'Registered',
  /** Unregistered */
  Unregistered = 'Unregistered'
}

/** Relationship enum */
export enum RelationshipEnum {
  /** Relation acquaintance */
  RelationAcquaintance = 'RELATION_ACQUAINTANCE',
  /** Relation brother in law code */
  RelationBrotherInLawCode = 'RELATION_BROTHER_IN_LAW_CODE',
  /** Relation childhood friend */
  RelationChildhoodFriend = 'RELATION_CHILDHOOD_FRIEND',
  /** Relation colleague */
  RelationColleague = 'RELATION_COLLEAGUE',
  /** Relation cousin elder code */
  RelationCousinElderCode = 'RELATION_COUSIN_ELDER_CODE',
  /** Relation cousin elder sister code */
  RelationCousinElderSisterCode = 'RELATION_COUSIN_ELDER_SISTER_CODE',
  /** Relation cousin nephew code */
  RelationCousinNephewCode = 'RELATION_COUSIN_NEPHEW_CODE',
  /** Relation cousin niece code */
  RelationCousinNieceCode = 'RELATION_COUSIN_NIECE_CODE',
  /** Relation cousin younger code */
  RelationCousinYoungerCode = 'RELATION_COUSIN_YOUNGER_CODE',
  /** Relation cousin younger sister code */
  RelationCousinYoungerSisterCode = 'RELATION_COUSIN_YOUNGER_SISTER_CODE',
  /** Relation daughter code */
  RelationDaughterCode = 'RELATION_DAUGHTER_CODE',
  /** Relation elder brother code */
  RelationElderBrotherCode = 'RELATION_ELDER_BROTHER_CODE',
  /** Relation elder sister code */
  RelationElderSisterCode = 'RELATION_ELDER_SISTER_CODE',
  /** Relation father code */
  RelationFatherCode = 'RELATION_FATHER_CODE',
  /** Relation former colleague */
  RelationFormerColleague = 'RELATION_FORMER_COLLEAGUE',
  /** Relation friend */
  RelationFriend = 'RELATION_FRIEND',
  /** Relation grandfather code */
  RelationGrandfatherCode = 'RELATION_GRANDFATHER_CODE',
  /** Relation grandmother code */
  RelationGrandmotherCode = 'RELATION_GRANDMOTHER_CODE',
  /** Relation grand aunt code */
  RelationGrandAuntCode = 'RELATION_GRAND_AUNT_CODE',
  /** Relation grand aunt younger code */
  RelationGrandAuntYoungerCode = 'RELATION_GRAND_AUNT_YOUNGER_CODE',
  /** Relation grand uncle code */
  RelationGrandUncleCode = 'RELATION_GRAND_UNCLE_CODE',
  /** Relation grand uncle younger code */
  RelationGrandUncleYoungerCode = 'RELATION_GRAND_UNCLE_YOUNGER_CODE',
  /** Relation high school friend */
  RelationHighSchoolFriend = 'RELATION_HIGH_SCHOOL_FRIEND',
  /** Relation high school junior */
  RelationHighSchoolJunior = 'RELATION_HIGH_SCHOOL_JUNIOR',
  /** Relation high school senior */
  RelationHighSchoolSenior = 'RELATION_HIGH_SCHOOL_SENIOR',
  /** Relation junior colleague */
  RelationJuniorColleague = 'RELATION_JUNIOR_COLLEAGUE',
  /** Relation list */
  RelationList = 'RELATION_LIST',
  /** Relation maternal aunt code */
  RelationMaternalAuntCode = 'RELATION_MATERNAL_AUNT_CODE',
  /** Relation maternal uncle code */
  RelationMaternalUncleCode = 'RELATION_MATERNAL_UNCLE_CODE',
  /** Relation middle school friend */
  RelationMiddleSchoolFriend = 'RELATION_MIDDLE_SCHOOL_FRIEND',
  /** Relation middle school junior */
  RelationMiddleSchoolJunior = 'RELATION_MIDDLE_SCHOOL_JUNIOR',
  /** Relation middle school senior */
  RelationMiddleSchoolSenior = 'RELATION_MIDDLE_SCHOOL_SENIOR',
  /** Relation mother code */
  RelationMotherCode = 'RELATION_MOTHER_CODE',
  /** Relation nephew code */
  RelationNephewCode = 'RELATION_NEPHEW_CODE',
  /** Relation niece code */
  RelationNieceCode = 'RELATION_NIECE_CODE',
  /** Relation other */
  RelationOther = 'RELATION_OTHER',
  /** Relation paternal aunt code */
  RelationPaternalAuntCode = 'RELATION_PATERNAL_AUNT_CODE',
  /** Relation paternal uncle code */
  RelationPaternalUncleCode = 'RELATION_PATERNAL_UNCLE_CODE',
  /** Relation president */
  RelationPresident = 'RELATION_PRESIDENT',
  /** Relation relative code */
  RelationRelativeCode = 'RELATION_RELATIVE_CODE',
  /** Relation senior colleague */
  RelationSeniorColleague = 'RELATION_SENIOR_COLLEAGUE',
  /** Relation sister in law code */
  RelationSisterInLawCode = 'RELATION_SISTER_IN_LAW_CODE',
  /** Relation son code */
  RelationSonCode = 'RELATION_SON_CODE',
  /** Relation supervisor */
  RelationSupervisor = 'RELATION_SUPERVISOR',
  /** Relation teacher */
  RelationTeacher = 'RELATION_TEACHER',
  /** Relation university friend */
  RelationUniversityFriend = 'RELATION_UNIVERSITY_FRIEND',
  /** Relation university junior */
  RelationUniversityJunior = 'RELATION_UNIVERSITY_JUNIOR',
  /** Relation university senior */
  RelationUniversitySenior = 'RELATION_UNIVERSITY_SENIOR',
  /** Relation vocational friend */
  RelationVocationalFriend = 'RELATION_VOCATIONAL_FRIEND',
  /** Relation younger brother code */
  RelationYoungerBrotherCode = 'RELATION_YOUNGER_BROTHER_CODE',
  /** Relation younger brother in law code */
  RelationYoungerBrotherInLawCode = 'RELATION_YOUNGER_BROTHER_IN_LAW_CODE',
  /** Relation younger sister code */
  RelationYoungerSisterCode = 'RELATION_YOUNGER_SISTER_CODE',
  /** Relation younger sister in law code */
  RelationYoungerSisterInLawCode = 'RELATION_YOUNGER_SISTER_IN_LAW_CODE'
}

export type RelationshipList = {
  __typename?: 'RelationshipList';
  /** カテゴリー */
  category?: Maybe<Scalars['String']['output']>;
  /** カテゴリー別リスト */
  options?: Maybe<Array<RelationshipOption>>;
};

/** Relationship name enum */
export enum RelationshipNameEnum {
  /** Relationship name company */
  RelationshipNameCompany = 'RELATIONSHIP_NAME_COMPANY',
  /** Relationship name family */
  RelationshipNameFamily = 'RELATIONSHIP_NAME_FAMILY',
  /** Relationship name friend */
  RelationshipNameFriend = 'RELATIONSHIP_NAME_FRIEND',
  /** Relationship name list */
  RelationshipNameList = 'RELATIONSHIP_NAME_LIST'
}

export type RelationshipOption = {
  __typename?: 'RelationshipOption';
  /** 値 */
  id?: Maybe<Scalars['Int']['output']>;
  /** 名称 */
  name?: Maybe<Scalars['String']['output']>;
};

/** Sort order */
export enum SortOrder {
  /** Asc */
  Asc = 'ASC',
  /** Desc */
  Desc = 'DESC'
}

export type Staff = {
  __typename?: 'Staff';
  /** 登録日時 */
  created_at?: Maybe<Scalars['DateTime']['output']>;
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** 商品名 */
  name: Scalars['String']['output'];
  /** 更新日時 */
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type SubGiftAssignment = {
  __typename?: 'SubGiftAssignment';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  gift_assignment: GiftAssignment;
  gift_option_product: GiftOptionProduct;
  id: Scalars['ID']['output'];
  sub_gift_number: Scalars['Int']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type SubGiftAssignmentHistory = {
  __typename?: 'SubGiftAssignmentHistory';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 引き出物割り当てメインギフト履歴 */
  gift_assignment_history: GiftAssignmentHistory;
  /** 引き出物オプション商品 */
  gift_option_product: GiftOptionProduct;
  /** 引き出物オプション商品説明 */
  gift_option_product_description?: Maybe<Scalars['String']['output']>;
  /** 引き出物オプション商品画像 */
  gift_option_product_image: Scalars['String']['output'];
  /** 引き出物オプション商品名 */
  gift_option_product_name: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  /** サブギフト番号 */
  sub_gift_number: Scalars['Int']['output'];
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

/** システム設定 */
export type System = {
  __typename?: 'System';
  /** 作成日時 */
  created_at?: Maybe<Scalars['DateTime']['output']>;
  /** システム設定ID */
  id: Scalars['ID']['output'];
  /** 事前支払い一時停止開始日時 */
  prepayment_pause_from_at?: Maybe<Scalars['DateTime']['output']>;
  /** 事前支払い一時停止終了日時 */
  prepayment_pause_to_at?: Maybe<Scalars['DateTime']['output']>;
  /** 更新日時 */
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type Tag = {
  __typename?: 'Tag';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** タグアイコン画像URL */
  image_url?: Maybe<Scalars['String']['output']>;
  /** タグアイコン画像表示FLG */
  is_display?: Maybe<Scalars['Boolean']['output']>;
  /** タグ名 */
  name: Scalars['String']['output'];
  /** 商品タグ */
  products: Array<Product>;
  /** タググループ */
  tag_group: TagGroup;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type TagGroup = {
  __typename?: 'TagGroup';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  /** グループ名称 */
  name: Scalars['String']['output'];
  tags?: Maybe<Array<Tag>>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type TagResponse = {
  __typename?: 'TagResponse';
  /** dirtyフラグ */
  dirty?: Maybe<Scalars['Boolean']['output']>;
  /** 選択状態 */
  selected?: Maybe<Scalars['Boolean']['output']>;
  /** タグ名 */
  tag?: Maybe<Scalars['String']['output']>;
  /** タグID */
  tag_id?: Maybe<Scalars['String']['output']>;
};

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT'
}

/** Ui type enum */
export enum UiTypeEnum {
  /** Checkbox */
  Checkbox = 'CHECKBOX',
  /** Radiobutton */
  Radiobutton = 'RADIOBUTTON',
  /** Text */
  Text = 'TEXT'
}

export type UpdateGuestEventAnswerInput = {
  /** 出欠 */
  attendance?: InputMaybe<Scalars['String']['input']>;
  /** イベント日付 */
  date?: InputMaybe<Scalars['Date']['input']>;
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
  /** イベント名 */
  name?: InputMaybe<Scalars['String']['input']>;
  /** 支払金額 */
  payment_amount?: InputMaybe<Scalars['Int']['input']>;
};

export type UpdateGuestFreeItemValueInput = {
  /** 内容 */
  content?: InputMaybe<Scalars['String']['input']>;
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['ID']['input'];
  /** 会員 */
  member_id?: InputMaybe<Scalars['Int']['input']>;
  /** 項目名称 */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateGuestGroupInput = {
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
  /** ゲストグループ名称 */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateGuestInput = {
  /** 丁目・番地 */
  address?: InputMaybe<Scalars['String']['input']>;
  /** アレルギー品目 */
  allergies?: InputMaybe<Scalars['Json']['input']>;
  /** その他アレルギー */
  allergy?: InputMaybe<Scalars['String']['input']>;
  /** 生年月日 */
  birthdate?: InputMaybe<Scalars['Date']['input']>;
  /** 建物名・部屋番号 */
  building?: InputMaybe<Scalars['String']['input']>;
  /** カード決算ID */
  card_settlement_id?: InputMaybe<Scalars['String']['input']>;
  /** 市区町村 */
  city?: InputMaybe<Scalars['String']['input']>;
  /** メールアドレス */
  email?: InputMaybe<Scalars['String']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** めい */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名(ローマ字) */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 性別 */
  gender?: InputMaybe<GenderEnum>;
  /** お気持ち金額 */
  gift_amount?: InputMaybe<Scalars['Int']['input']>;
  /** ゲストグループ */
  guest_group_id?: InputMaybe<Scalars['ID']['input']>;
  /** ゲスト敬称 */
  guest_honor?: InputMaybe<Scalars['String']['input']>;
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  guest_tag_guests?: InputMaybe<Array<UpdateGuestTagGuestInput>>;
  /** ゲスト肩書 */
  guest_title?: InputMaybe<Scalars['String']['input']>;
  /** ゲストタイプ */
  guest_type?: InputMaybe<GuestTypeEnum>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** プロフィール画像 */
  image_url?: InputMaybe<Scalars['String']['input']>;
  /** 招待状お届け方法 */
  invitation_delivery?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料負担FLG */
  is_system_fee?: InputMaybe<Scalars['Boolean']['input']>;
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓(ローマ字) */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** 会員確認済FLG */
  member_confirm_type?: InputMaybe<MemberConfirmTypeEnum>;
  /** メッセージ */
  message?: InputMaybe<Scalars['String']['input']>;
  /** 連名筆頭者 */
  parent_guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** 会費・ご祝儀支払い方法 */
  payment_method?: InputMaybe<PaymentMethodEnum>;
  /** 電話番号 */
  phone?: InputMaybe<Scalars['String']['input']>;
  /** 郵便番号 */
  postal_code?: InputMaybe<Scalars['String']['input']>;
  /** 都道府県 */
  prefecture?: InputMaybe<Scalars['String']['input']>;
  /** 間柄 */
  relationship?: InputMaybe<Scalars['String']['input']>;
  /** 関係性 */
  relationship_name?: InputMaybe<Scalars['String']['input']>;
  /** 決算金額 */
  settlement_amount?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料 */
  system_fee?: InputMaybe<Scalars['Int']['input']>;
  /** システム利用料率 */
  system_fee_rate?: InputMaybe<Scalars['Float']['input']>;
  /** 会費・ご祝儀・お気持ち金額合計金額 */
  total_amount?: InputMaybe<Scalars['Int']['input']>;
  /** Web招待状返信日時 */
  web_invite_reply_datetime?: InputMaybe<Scalars['DateTime']['input']>;
};

export type UpdateGuestListInput = {
  id: Scalars['ID']['input'];
  /** ゲストリスト名称 */
  name?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateGuestSurveyAnswerInput = {
  /** 回答内容 */
  answer_content?: InputMaybe<Scalars['String']['input']>;
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
  /** 質問内容 */
  question?: InputMaybe<Scalars['String']['input']>;
  /** UI種類 */
  ui_type?: InputMaybe<UiTypeEnum>;
};

export type UpdateGuestTagGuestInput = {
  /** ゲスト */
  guest_id?: InputMaybe<Scalars['ID']['input']>;
  /** ゲストタグ */
  guest_tag_id?: InputMaybe<Scalars['ID']['input']>;
};

export type UpdateGuestTagInput = {
  /** ゲストリスト */
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
  /** タグ */
  tag?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateMemberBankAccountInput = {
  /** 口座名義 */
  account_name?: InputMaybe<Scalars['String']['input']>;
  /** 口座番号 */
  account_number?: InputMaybe<Scalars['String']['input']>;
  /** 口座種別 */
  account_type?: InputMaybe<AccountTypeEnum>;
  /** 銀行コード */
  bank_code?: InputMaybe<Scalars['String']['input']>;
  /** 銀行名 */
  bank_name?: InputMaybe<Scalars['String']['input']>;
  /** 支店コード */
  branch_code?: InputMaybe<Scalars['String']['input']>;
  /** 支店名 */
  branch_name?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  /** 電話番号 */
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateMemberInput = {
  /** 生年月日 */
  birthdate?: InputMaybe<Scalars['Date']['input']>;
  /** メールアドレス */
  email?: InputMaybe<Scalars['String']['input']>;
  /** 名 */
  first_name?: InputMaybe<Scalars['String']['input']>;
  /** めい */
  first_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 名(romaji) */
  first_name_romaji?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['ID']['input']>;
  /** 姓 */
  last_name?: InputMaybe<Scalars['String']['input']>;
  /** せい */
  last_name_kana?: InputMaybe<Scalars['String']['input']>;
  /** 姓(romaji) */
  last_name_romaji?: InputMaybe<Scalars['String']['input']>;
  /** パスワード */
  password?: InputMaybe<Scalars['String']['input']>;
  /** SNSログインID */
  sns_login_id?: InputMaybe<Scalars['Json']['input']>;
};

export type UpdateWebInvitationInput = {
  block_settings?: InputMaybe<Scalars['Json']['input']>;
  editor_settings?: InputMaybe<Scalars['Json']['input']>;
  guest_list_id?: InputMaybe<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
  is_password?: InputMaybe<Scalars['Boolean']['input']>;
  is_public?: InputMaybe<Scalars['Boolean']['input']>;
  m_web_invitation_id?: InputMaybe<Scalars['ID']['input']>;
  member_id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
  prepayment_due_date?: InputMaybe<Scalars['Date']['input']>;
  public_url?: InputMaybe<Scalars['String']['input']>;
  reply_deadline_date?: InputMaybe<Scalars['Date']['input']>;
  scheduled_date?: InputMaybe<Scalars['Date']['input']>;
  scheduled_transfer_date?: InputMaybe<Scalars['Date']['input']>;
};

export type UpdateWeddingInfoInput = {
  /** 招待人数 */
  guest_count?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['ID']['input'];
  /** 披露宴日 */
  reception_date?: InputMaybe<Scalars['Date']['input']>;
  /** 挙式日 */
  wedding_date?: InputMaybe<Scalars['Date']['input']>;
  /** 挙式会場名 */
  wedding_venue?: InputMaybe<Scalars['String']['input']>;
};

/** Account of a person who utilizes this application. */
export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at: Scalars['DateTime']['output'];
  /** Unique email address. */
  email: Scalars['String']['output'];
  /** When the email was verified. */
  email_verified_at?: Maybe<Scalars['DateTime']['output']>;
  /** Unique primary key. */
  id: Scalars['ID']['output'];
  /** Non-unique name. */
  name: Scalars['String']['output'];
  /** When the account was last updated. */
  updated_at: Scalars['DateTime']['output'];
};

export type VariationTemplate = {
  __typename?: 'VariationTemplate';
  /** 基本バリエーションテンプレート */
  basic_variation_template?: Maybe<BasicVariationTemplate>;
  /** 登録日時 */
  created_at?: Maybe<Scalars['DateTime']['output']>;
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** ID */
  id: Scalars['ID']['output'];
  /** Jsonデータ */
  json_data?: Maybe<Scalars['Json']['output']>;
  /** マスターブロック */
  master_blocks: Array<MasterBlock>;
  /** テンプレート名称 */
  name?: Maybe<Scalars['String']['output']>;
  /** 商品種別 */
  product_type?: Maybe<ProductTypeEnum>;
  /** 更新日時 */
  updated_at?: Maybe<Scalars['DateTime']['output']>;
};

export type VariationTemplateMasterBlock = {
  __typename?: 'VariationTemplateMasterBlock';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  id: Scalars['ID']['output'];
  /** マスターブロック */
  master_block: MasterBlock;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** バリエーションテンプレート */
  variation_template: VariationTemplate;
};

/** A paginated list of VariationTemplate items. */
export type VariationTemplatePaginator = {
  __typename?: 'VariationTemplatePaginator';
  /** A list of VariationTemplate items. */
  data: Array<VariationTemplate>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type WebInvitation = {
  __typename?: 'WebInvitation';
  /** ブロック設定値 */
  block_settings?: Maybe<Scalars['Json']['output']>;
  /** 作成日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** エディタ設定値 */
  editor_settings?: Maybe<Scalars['Json']['output']>;
  /** パーティ情報配列(アクセサ) */
  event_list?: Maybe<Scalars['Json']['output']>;
  /** ゲストリスト */
  guest_list: GuestList;
  /** ゲスト */
  guests: Array<Guest>;
  /** 事前支払いゲストの存在チェック */
  has_pre_paid_guest?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  /** パスワード利用フラグ */
  is_password?: Maybe<Scalars['Boolean']['output']>;
  /** 公開済みフラグ */
  is_public?: Maybe<Scalars['Boolean']['output']>;
  /** Web招待状マスタ */
  m_web_invitation: MWebInvitation;
  /** 会員 */
  member: Member;
  /** 招待状名 */
  name?: Maybe<Scalars['String']['output']>;
  /** パスワード */
  password?: Maybe<Scalars['String']['output']>;
  /** 事前支払い締め日 */
  prepayment_due_date?: Maybe<Scalars['Date']['output']>;
  /** 公開URL */
  public_url?: Maybe<Scalars['String']['output']>;
  /** 回答締切日 */
  reply_deadline_date?: Maybe<Scalars['Date']['output']>;
  /** 開催日 */
  scheduled_date?: Maybe<Scalars['Date']['output']>;
  /** 送金予定日 */
  scheduled_transfer_date?: Maybe<Scalars['Date']['output']>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type WebInvitationDesignImage = {
  __typename?: 'WebInvitationDesignImage';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 画像ファイル名 */
  file_name?: Maybe<Scalars['String']['output']>;
  /** ファイルのパス */
  file_path?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  /** WEB招待状マスタ */
  m_web_invitation: MWebInvitation;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
};

export type WebInvitationProduct = {
  __typename?: 'WebInvitationProduct';
  display_order?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  images?: Maybe<Array<WebInvitationProductImage>>;
  name?: Maybe<Scalars['String']['output']>;
};

export type WebInvitationProductImage = {
  __typename?: 'WebInvitationProductImage';
  presigned_url?: Maybe<Scalars['String']['output']>;
};

/** A paginated list of WebInvitationProduct items. */
export type WebInvitationProductPaginator = {
  __typename?: 'WebInvitationProductPaginator';
  /** A list of WebInvitationProduct items. */
  data: Array<WebInvitationProduct>;
  /** Pagination information about the list of items. */
  paginatorInfo: PaginatorInfo;
};

export type WebInvitationTemplateBlock = {
  __typename?: 'WebInvitationTemplateBlock';
  /** ブロック名称 */
  block_name: Scalars['String']['output'];
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 表示順 */
  display_order: Scalars['Int']['output'];
  id: Scalars['ID']['output'];
  /** WEB招待状テンプレートマスタ */
  m_web_invitation_template: MWebInvitationTemplate;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** ブロックVueコンポーネント名 */
  vue_component_name: Scalars['String']['output'];
};

export type WeddingInfo = {
  __typename?: 'WeddingInfo';
  /** 登録日時 */
  created_at: Scalars['DateTime']['output'];
  /** 削除日時 */
  deleted_at?: Maybe<Scalars['DateTime']['output']>;
  /** 招待人数 */
  guest_count?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  /** 会員 */
  member: Member;
  /** 送金 */
  money_transfers?: Maybe<Array<MoneyTransfer>>;
  /** 披露宴日 */
  reception_date?: Maybe<Scalars['Date']['output']>;
  /** 更新日時 */
  updated_at: Scalars['DateTime']['output'];
  /** 挙式日 */
  wedding_date?: Maybe<Scalars['Date']['output']>;
  /** 挙式会場名 */
  wedding_venue?: Maybe<Scalars['String']['output']>;
};
