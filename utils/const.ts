// エラーメッセージ
export const APPLICATION_MESSAGES = {
  'VALIDATION_ERROR': '入力内容を確認してください',
  'API_DEFAULT_ERROR': '通信に失敗しました しばらくしてから再度お試しください',
  'API_AUTH_ERROR' : '認証に失敗しました',
}

// 性別
export const GENDER_MASTER: {
  [key: string]: string;
} = {
  'ETC': 'その他',
  'MALE': '男性',
  'FEMALE': '女性'
}

// 性別
export const GUEST_TYPE_MASTER: {
  [key: string]: string;
} = {
  'GROOM': '新郎ゲスト',
  'BRIDE': '新婦ゲスト'
}

// 性別
export const PAYMENT_METHOD_MASTER: {
  [key: string]: string;
} = {
  'ADVANCE_PAYMENT': '事前支払い',
  'BRING_ON_THE_DAY': '持参する',
  'PAID': '支払い済み'
}



// 出欠
export const GUEST_ATTENDANCE_MASTER: {
  [key: string]: string;
} = {
  'PRESENT': '出席',
  'ABSENT': '欠席',
  'PENDING': '保留',
}

// 未読・既読管理
export const MEMBER_CONFIRM_TYPE_MASTER: {
  [key: string]: string;
} = {
  'New': '新着',
  'Unread': '未読',
  'Normal': '通常',
}

// 招待状お渡し方法
export const GUEST_INVITATION_DELIVERY_MASTER: {
  [key: number]: string;
} = {
  1: 'WEB',
  2: '郵送',
  31: '手渡し'
}

// 都道府県
export const PREFECTURE_MASTER = ['北海道', '青森県', '岩手県', '宮城県', '秋田県', '山形県', '福島県', '茨城県', '栃木県', '群馬県', '埼玉県', '千葉県', '東京都', '神奈川県', '新潟県', '富山県', '石川県', '福井県', '山梨県', '長野県', '岐阜県', '静岡県', '愛知県', '三重県', '滋賀県', '京都府', '大阪府', '兵庫県', '奈良県', '和歌山県', '鳥取県', '島根県', '岡山県', '広島県', '山口県', '徳島県', '香川県', '愛媛県', '高知県', '福岡県', '佐賀県', '長崎県', '熊本県', '大分県', '宮崎県', '鹿児島県', '沖縄県']


export const GUEST_COUNT_OPTIONS = [
  {value: '', label:'未定'},
  {value: '9', label:'〜10人'},
  {value: '10', label:'10人〜'},
  {value: '20', label:'20人〜'},
  {value: '30', label:'30人〜'},
  {value: '40', label:'40人〜'},
  {value: '50', label:'50人〜'},
  {value: '60', label:'60人〜'},
  {value: '70', label:'70人〜'},
  {value: '80', label:'80人〜'},
  {value: '90', label:'90人〜'},
  {value: '100', label:'100人〜'}
];

export const MEMBER_REGIST_QUESTIONNAIRE_WAYS = [
  '友人・家族からの紹介',
  'Webサイト',
  'Web広告',
  'Instagram',
  'ブログ',
  'その他'
];

export const RELATIONSHIP_NAME_MASTER = {
  'RELATIONSHIP_NAME_FAMILY': '親族',
  'RELATIONSHIP_NAME_FRIEND': '友人',
  'RELATIONSHIP_NAME_SCHOOL': '学校',
  'RELATIONSHIP_NAME_COMPANY': '会社',
  'RELATIONSHIP_NAME_OTHER': 'その他',
};

export const RELATIONSHIP_MASTER = {
  'RELATION_FATHER': '父',
  'RELATION_MOTHER': '母',
  'RELATION_BROTHER': '兄',
  'RELATION_SISTER': '弟',
  'RELATION_YOUNGER_BROTHER': '姉',
  'RELATION_YOUNGER_SISTER': '妹',
  'RELATION_BROTHER_IN_LAW': '義姉',
  'RELATION_SISTER_IN_LAW': '義兄',
  'RELATION_YOUNGER_BROTHER_IN_LAW': '義妹',
  'RELATION_YOUNGER_SISTER_IN_LAW': '義弟',
  'RELATION_NEPHEW': '甥',
  'RELATION_NIECE': '姪',
  'RELATION_GRANDFATHER': '祖父',
  'RELATION_GRANDMOTHER': '祖母',
  'RELATION_GREAT_UNCLE_PATERNAL': '大伯父(祖父母の兄)',
  'RELATION_GREAT_AUNT_PATERNAL': '大伯母(祖父母の姉)',
  'RELATION_GREAT_UNCLE_MATERNAL': '大叔父(祖父母の弟)',
  'RELATION_GREAT_AUNT_MATERNAL': '大叔母(祖父母の妹)',
  // 'x': '大叔父(祖父母の弟)',
  // 'x': '大叔母(祖父母の妹)',
  'RELATION_UNCLE_PATERNAL': '伯父(父母の兄)',
  'RELATION_AUNT_PATERNAL': '伯母(父母の姉)',
  'RELATION_UNCLE_MATERNAL': '叔父(父母の弟)',
  'RELATION_AUNT_MATERNAL': '叔母(父母の妹)',
  'RELATION_ELDER_COUSIN': '従兄(年上のいとこ)',
  'RELATION_ELDER_COUSIN_FEMALE': '従姉(年上のいとこ)',
  'RELATION_YOUNGER_COUSIN': '従弟(年下のいとこ)',
  'RELATION_YOUNGER_COUSIN_FEMALE': '従妹(年下のいとこ)',
  'RELATION_GRANDNEPHEW': '従甥',
  'RELATION_GRANDNIECE': '従姪',
  'RELATION_RELATIVE': '親戚',
  'RELATION_SPOUSE': '配偶者',
  'RELATION_SON': '息子',
  'RELATION_DAUGHTER': '娘',
  'RELATION_CHILDHOOD_FRIEND': '幼なじみ',
  'RELATION_FRIEND': '友人',
  'RELATION_ACQUAINTANCE': '知人',
  'RELATION_UNIVERSITY_SENIOR': '大学先輩',
  'RELATION_HIGH_SCHOOL_SENIOR': '高校先輩',
  'RELATION_MIDDLE_SCHOOL_SENIOR': '中学先輩',
  'RELATION_UNIVERSITY_CLASSMATE': '大学同級生',
  'RELATION_HIGH_SCHOOL_CLASSMATE': '高校同級生',
  'RELATION_MIDDLE_SCHOOL_CLASSMATE': '中学同級生',
  'RELATION_UNIVERSITY_JUNIOR': '大学後輩',
  'RELATION_HIGH_SCHOOL_JUNIOR': '高校後輩',
  'RELATION_MIDDLE_SCHOOL_JUNIOR': '中学後輩',
  'RELATION_MENTOR': '恩師',
  'RELATION_PRESIDENT': '社長',
  'RELATION_SUPERVISOR': '上司',
  'RELATION_SENIOR_COLLEAGUE': '先輩',
  'RELATION_COLLEAGUE': '同僚',
  'RELATION_FORMER_COLLEAGUE': '元同僚',
  'RELATION_JUNIOR_COLLEAGUE': '後輩',
  'RELATION_OTHER': 'その他',
};

export const SNS_LOGIN_ERRORS: any = {
  'NO_EMAIL': "メールアドレスが未設定のアカウントです \nメールアドレスでログインしてください",
  'IS_REGISTERED': "すでに会員登録済みのアカウントです ログインしてください",
  'IS_NOT_REGISTERED': "仮登録の状態です。\n送信された会員登録URLをクリックして登録を完了してください",
  'IS_NOT_REGISTER_COMPLETE': "送信された会員登録URLをクリックして登録を完了してください",
  'IS_REGISTERED_EMAIL': "メールアドレスで会員登録されたアカウントです \nメールアドレスでログインしてください",
  'IS_REGISTERED_LINE': "LINEで会員登録されたアカウントです \nLINEでログインしてください",
  'IS_REGISTERED_GOOGLE': "Googleで会員登録されたアカウントです \nGoogleでログインしてください",
};

export const REDIRECT_PATH_COOKIE_KEY = 'redirectUrl';

// 例文モーダルのカテゴリ
export const EXAMPLE_SENTENCE_TYPE: {
  title: string
  type: string
}[] = [
  {
    title: '友人',
    type: 'FRIEND'
  },
  {
    title: '親族',
    type: 'FAMILY'
  },
  {
    title: '上司',
    type: 'BOSS'
  }
];

export const SYSTEM_FEE_RATE = .031;
