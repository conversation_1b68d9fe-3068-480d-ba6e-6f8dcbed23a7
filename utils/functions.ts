import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import sanitizeHtml from 'sanitize-html';
dayjs.extend(utc);
dayjs.extend(timezone);

// ランダムな文字列を生成
export const getRandomString = (num:number) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < num; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

// ランダムな文字列を生成
export const getRandomPassword = () => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let result = '';
  const charactersLength = characters.length;
  for (let i = 0; i < 16; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  result += '#X';
  return result;
}

// ファイルを base64 に変換して返す
export const convertBase64 = async (file: any) => {
  const reader = new FileReader();
  reader.readAsDataURL(file);

  // 画像ファイルであるかどうかをチェック
  if (!file.type.startsWith('image/')) {
    return false;
  }

  let res = '';
  await new Promise(function (resolve) {
    // 変換が終わったら実行される
    reader.onload = () => {
      const image: any = new Image();
      image.src = reader.result;
      image.onload = function () {
        res = String(reader.result);
        resolve(res);
      };
    };
  });
  return res;
};

// ファイルを base64 に変換して返す（動画用）
export const convertBase64Video = async (file:any) => {
  const reader = new FileReader()
  reader.readAsDataURL(file)

  // 動画ファイルかどうかをチェック
  if (!file.type.startsWith('video/')) {
    return false;
  }

  let res = '';
  await new Promise(function (resolve, reject) {
    // 変換が終わったら実行される
    reader.onload = (fileEvent) => {
      // 動画要素を作成してファイルを検証
      const video = document.createElement('video');
      video.src = fileEvent?.target?.result as string;

      video.onloadedmetadata = () => {
        // 動画が正常に読み込まれた場合
        res = fileEvent?.target?.result as string;
        resolve(res);
      };

      video.onerror = () => {
        // 動画ファイルが破損または読み込めない場合
        reject(new Error('対応していない動画形式です'));
      };
    };

    reader.onerror = () => {
      reject(new Error('ファイルの読み込みに失敗しました'));
    };
  });
  return res;
}

// URLからアップロード形式に変換
export const convertUrlToFile = async (url:string) => {
  const urlObject = new URL('https://'+location.host+url);
  const fileName = urlObject.pathname.split('/').pop() as string;
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error('Failed to fetch the image');
    
    const blob = await response.blob();
    return new File([blob], fileName, { type: blob.type });
  } catch (error) {
    // console.error('Error converting URL to File:', error);
  }
  return null;
}

// ファイルを base64 に変換して返す
export const getFileName = (url:string) => {
  const result = url.match(".+/(.+?)([\?#;].*)?$")?.[1];
  if (! result) return '';
  return result;
}

// free_item_values.0.name 的なStringを free_item_values[0].name に変える
export const setNestedValue = <T>(obj: T, key: string, value: any) => {
  const keys = key.split('.');
  keys.reduce((acc: any, currentKey, index) => {
    if (index === keys.length - 1) {
      acc[currentKey] = value;
    } else {
      if (typeof acc[currentKey] === 'undefined') {
        acc[currentKey] = {};
      }
    }
    return acc[currentKey];
  }, obj);
};

export const setNumberFormat = (int:any, str = '-') => {
  if (int === '' || typeof int === 'undefined') int = null;
  if (isNaN(int)) int = null;
  if (int === null) {
    return str;
  } else {
    return int.toLocaleString();
  }
};

export const nl2br = (str = '') => {
  if (!str) str = '';
  str = str.replace(/\r\n/g, "<br />");
  str = str.replace(/(\n|\r)/g, "<br />");
  str = str.replace('\\n', "<br />");
  return str;
};

// <br>タグ以外をサニタイズする関数
export const sanitizeContent = (htmlContent: string) => {
  if(!htmlContent) { return ''; }
  const data = htmlContent.replace(/</g, '&lt;').replace(/>/g, '&gt;');
  return sanitizeHtml(data, {
    allowedTags: [],
    allowedAttributes: {}
  });
}

export const downloadByApi = async (uri = '', post = {}, filename = '') => {
  const { getAccessToken } = useAccessTokenState();
  const config = useRuntimeConfig();
  const backEndpoint = config.public.apiEndpoint.replace('/graphql', '/api');
  try {
    const response = await fetch(backEndpoint + uri, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer `+ getAccessToken(),
      },
      body: JSON.stringify(post),
    });
    if (!response.ok) {
      throw new Error('サーバーからのレスポンスが正常ではありません');
    }
    const blob = await response.blob(); // レスポンスを Blob として取得

    // Blob からダウンロード用の URL を生成
    const url = window.URL.createObjectURL(blob);

    // ダウンロード用のリンクを作成し、クリックイベントを発火
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename); // ダウンロードファイル名を設定
    document.body.appendChild(link);
    link.click();

    // 後処理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url); // 生成した URL を破棄
  } catch (error) {
    // console.error('CSV ダウンロードエラー:', error);
  }
};

export const csvToArray = (text = '') => {
  const lineList = text.split("\n");
  const resultObj = lineList
    .filter((_, index) => index !== 0)
    .map((line) => {
      return line.split("\t");
  });
  return resultObj;
}

export const getEventsByWebInvitation = (webInvitation:any = {}) => {
  const { $dayjs } : any = useNuxtApp();

  // APIからイベントの一覧を取得
  let event_list = [];
  if (webInvitation?.event_list) {
    event_list = webInvitation?.event_list;
  }else{
    event_list = ['挙式', '披露宴']
  }

  // 招待状の設定から金額などを取得
  let events = [];
  let block;
  if(webInvitation?.editor_settings){
    block = webInvitation?.editor_settings?.blocks.find((block:any) => block.id == 'information');
  }else if(webInvitation?.editor_settings_json){
    block = webInvitation?.editor_settings_json?.blocks.find((block:any) => block.id == 'information');
  }
  // console.log(block?.contents?.date);
  const date = dayjs(block?.contents?.date).tz('Asia/Tokyo').format('YYYY-MM-DD');
  // 全パターンを作成しバックエンド側の処理で判断する
  // 挙式・披露宴のイベント
  const eventNames = ['挙式・披露宴', '挙式', '披露宴'];
  for (let i = 0; i < eventNames.length; i++) {
    const eventName = eventNames[i];
    let event = block?.contents?.events.find((event:any) => ! event?.eventName);
    if (! event?.feeOption) event = block?.contents?.events.find((event:any) => event?.eventName == eventName);
    events.push({
      name: eventName,
      date: date,
      feeOption: event?.feeOption,
      feeAmount: event?.feeAmount
    })
  }
  for (let i = 0; i < block?.contents?.events.length; i++) {
    const event = block?.contents?.events[i];
    if (! event?.eventName) continue;
    if (events.findIndex(item => item.name == event?.eventName) !== -1) continue;

    // feeOption が gift_system:ご祝儀 / membership_fee_common:会費共通 / membership_fee_separate:会費別
    events.push({
      name: event?.eventName, 
      date: date,
      feeOption: event?.feeOption,
      feeAmount: event?.feeAmount
    });
  }

  // イベントの一覧に金額を格納
  let results = [];
  for (let i = 0; i < event_list.length; i++) {
    const eventName = event_list[i];
    const event = events.find(item => item.name == eventName);
    if (event) {
      results.push(event)
    } else {
      results.push({
        name: eventName,
        date: date,
        feeOption: 'gift_system',
        feeAmount: null
      })
    }
  }
  // console.log(events);
  // console.log(results);
  return results;
}

export const removeQueryParam = (url:string, param:string) => {
  // paramをエンコード
  var encodedParam = encodeURIComponent(param);
  // URLからparamの部分を削除
  var regex = new RegExp(`[?&]${encodedParam}=[^&]*`, 'g');
  // "?" で始まる場合、その後に "&" があれば "?" に置換
  var cleanUrl = url.replace(regex, '');
  cleanUrl = cleanUrl.replace(/&/, '?');
  // 末尾の "?" または "&" を削除
  cleanUrl = cleanUrl.replace(/[?&]$/, '');
  return cleanUrl;
}


// Cookieに保存する
import { useCookies } from '@vueuse/integrations/useCookies'
export const mySetCookie = (key = '', str = '') => {
  if (typeof window === 'undefined') {
    // SSRの場合
    const event = useRequestEvent();
    if (event?.node?.res) {
      if (str) {
        if (typeof str !== 'string') str = JSON.stringify(str);
        event.node.res.setHeader('Set-Cookie', key+'='+encodeURIComponent(str)+'; Path=/;');
      } else {
        event.node.res.setHeader('Set-Cookie', key+'=null; Path=/; max-age=0;');
      }
    }
  } else {
    // ブラウザ上の処理の場合
    const cookies = useCookies([key]);
    if (str) {
      cookies.set(key, str, {path: '/'})
    } else {
      cookies.set(key, null, {path: '/', maxAge: 0})
    }
  }
}

// Cookieから取得する
export const myGetCookie = (key = '') => {
  if (typeof window === 'undefined') {
    // SSRの場合
    const headers = useRequestHeaders(['cookie'])
    if (headers?.cookie) {
      const cookies = headers.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === key) {
          const data = decodeURIComponent(value);
          if (data) return JSON.parse(data);
          return data;
        }
      }
    }
  } else {
    // ブラウザ上の処理の場合
    const cookies = useCookies([key])
    return cookies.get(key);
  }
  return '';
}

// 郵便番号の形式に変換
export const formatZip = (str = '') => {
  if (/^([0-9]{3})([0-9]{4})$/.test(str)) return str.replace(/^([0-9]{3})([0-9]{4})$/, '$1-$2');
  return str;
}


// ログイン必須のURLか判定
export const checkMustLoginUrl = (url = '') => {
  return /^\/mypage/.test(url);
}



// 戻るリンクを取得
// backlink に指定でデフォルト
// pattern に条件があれば条件
// pattern は、ナビゲーションとか共通エリアから飛べちゃうページで変なページへの遷移を抑制するのにつかう
// onMounted時に使う
export const getBacklink = (backlink = '', pattern = '') => {
  const route = useRoute();
  const url = route.fullPath;
  const urlHistory = getUrlHistory();
  // console.log(urlHistory[1]);
  // const index = urlHistory.findIndex((item:string) => item === url);
  // 戻るリンクは1件前を見る
  const index = 0;
  const hisotry = urlHistory?.[index+1];
  if (hisotry) {
    // 戻るがTOPページなら、戻るリンクは不要
    if (hisotry == '/') return backlink;

    // 条件に合うか判断
    if (pattern) {
      // console.log(new RegExp(pattern).test(hisotry));
      if (new RegExp(pattern).test(hisotry)) return hisotry;
    } else {
      // 戻るがあれば戻る
      return hisotry;
    }
  }

  // デフォルト値を出す
  return backlink;
}
// 閲覧履歴を取得
export const getUrlHistory = () => {
  let urlHistory = JSON.parse(sessionStorage.getItem('urlHistory') as string);
  if (! urlHistory) urlHistory = [];
  return urlHistory;
}
// 閲覧履歴を保存
export const saveUrlHistory = () => {
  const route = useRoute();
  const url = route.fullPath;
  let urlHistory = getUrlHistory();
  const index = urlHistory.findIndex((item:string) => item === url);
  
  if (index === 0) {
    // 同じURLなら、リロードなので履歴を消す
    clearUrlHistory();
    return false;
  } else if (index === 1) {
    // 1ページ前に戻ったら、0番目を履歴を削除
    urlHistory.splice(0, 1);
  } else {
      // 0件目と一緒じゃなければ保存
    // 最初似追加
    urlHistory.unshift(url);
    // 100件まで保存
    for (var i = urlHistory.length - 1; i >= 100; i--) {
      urlHistory.splice(i, 1);
    }
  }
  sessionStorage.setItem('urlHistory', JSON.stringify(urlHistory));
}
// 履歴を上書き
export const overwriteUrlHistory = () => {
  const route = useRoute();
  const url = route.fullPath;
  let urlHistory = getUrlHistory();
  urlHistory[0] = url;
  sessionStorage.setItem('urlHistory', JSON.stringify(urlHistory));
}
// 閲覧履歴を削除
export const clearUrlHistory = () => {
  sessionStorage.removeItem('urlHistory');
}

export const getCreateWebInvitationGuestAmounts = (input:any = {}) => {
  let result = {
    // 筆頭者
    guest: {
      amount: 0,
      systemFee: 0,
    },
    // 連名
    guests: [],
    // 合計値
    sum: {
      amount: 0,
      systemFee: 0
    }
  } as any;
  // 連名
  for (let i = 0; i < input.guests.length; i++) {
    result.guests[i] = {
      amount: 0,
      systemFee: 0
    }
  }

  for (let i = 0; i < input.guest_event_answers.length; i++) {
    const guest_event_answer = input.guest_event_answers[i];
    if (guest_event_answer.attendance !== GUEST_ATTENDANCE_MASTER.PRESENT) continue;

    if (guest_event_answer?.payment_amount) {
      result.guest.amount += Number(guest_event_answer?.payment_amount);
    }

    if (! guest_event_answer?.payment_amounts) continue;
    for (let n = 0; n < guest_event_answer?.payment_amounts.length; n++) {
      if (guest_event_answer?.isNotGuestsPayment) continue;
      const payment_amount = guest_event_answer?.payment_amounts[n];
      if (payment_amount) {
        result.guests[n].amount += Number(payment_amount)
      }
    }
  }
  if (input.input?.gift_amount) result.guest.amount += Number(input.input.gift_amount);

  if (input.input?.is_system_fee) {
    result.guest.systemFee = Math.floor(result.guest.amount * SYSTEM_FEE_RATE);
    for (let i = 0; i < result.guests.length; i++) {
      result.guests[i].systemFee = Math.floor(result.guests[i].amount * SYSTEM_FEE_RATE);
    }
  }

  result.sum.amount = result.guest.amount;
  result.sum.systemFee = result.guest.systemFee;
  for (let i = 0; i < result.guests.length; i++) {
    result.sum.amount += result.guests[i].amount ;
    result.sum.systemFee += result.guests[i].systemFee ;
  }
  return result;
}

// Web招待状入力フォームでAPIへの送信形式に変換する
export const getCreateWebInvitationGuestData = (input:any = {}, webInvitationData:any = {}, materialImages:any = []) => {
  const { $dayjs } = useNuxtApp() as any;

  // 決済情報
  const guestAmounts = getCreateWebInvitationGuestAmounts(input);

  let guest = JSON.parse(JSON.stringify(input.input));
  guest.guest_list_id = webInvitationData?.guest_list?.id;
  guest.web_invitation_id = webInvitationData?.id;
  delete guest.image;
  if (input.input.image?.uuid) guest.image_url = input.input.image?.uuid;
  guest.is_system_fee = false;
  if (input.input.is_system_fee) guest.is_system_fee = true;
  delete guest.media;
  delete guest.media_image;
  delete guest.is_media_upload;
  if (input.input.media?.uuid) {
    if (input.input.media?.type == 'video') {
      guest.media_type = 'MEDIA_TYPE_MOVIE';
    } else {
      guest.media_type = 'MEDIA_TYPE_IMAGE';
    }
    guest.media_uuid = input.input.media?.uuid;
  } else {
    if (input.input.is_media_upload == 1) {
      if (input.input.media?.uuid) {
        if (input.input.media?.type == 'video') {
          guest.media_type = 'MEDIA_TYPE_MOVIE';
        } else {
          guest.media_type = 'MEDIA_TYPE_IMAGE';
        }
        guest.media_uuid = input.input.media?.uuid;
      }
    } else {
      let media = materialImages.find(item => item.uuid == input.input.media_image) as any;
      if (media) {
        input.input.media = media;
        guest.media_type = 'MEDIA_TYPE_IMAGE';
        guest.media_uuid = media.uuid;
      }
    }  
  }
  guest.web_invite_reply_datetime = dayjs(new Date()).tz('Asia/Tokyo').format('YYYY-MM-DD HH:mm:ss');
  guest.invitation_delivery = 1;

  if (guest?.card_token) delete guest.card_token;

  if (input.input?.payment_method == 'ADVANCE_PAYMENT') {
    guest.system_fee = guestAmounts.guest.systemFee;
    guest.system_fee_rate = SYSTEM_FEE_RATE;
    guest.total_amount = guestAmounts.guest.amount;
    guest.settlement_amount = guestAmounts.guest.amount + guestAmounts.guest.systemFee;
  }

  let guest_event_answers = [];
  for (let n = 0; n < input.guest_event_answers.length; n++) {
    let guest_event_answer = JSON.parse(JSON.stringify(input.guest_event_answers[n]));
    if (typeof guest_event_answer?.isNotGuestsPayment !== 'undefined') delete guest_event_answer.isNotGuestsPayment;
    if (typeof guest_event_answer?.payment_amounts !== 'undefined') delete guest_event_answer.payment_amounts;
    guest_event_answers.push(guest_event_answer);
  }

  let guests = JSON.parse(JSON.stringify(input.guests));
  for (let i = 0; i < guests.length; i++) {
    delete guests[i].image;
    if (input.guests[i].image?.uuid) guests[i].image_url = input.guests[i].image?.uuid;
    guests[i].guest_list_id = guest.guest_list_id;
    guests[i].guest_type = guest.guest_type;
    guests[i].postal_code = guest.postal_code;
    guests[i].prefecture = guest.prefecture;
    guests[i].city = guest.city;
    guests[i].address = guest.address;
    guests[i].building = guest.building;
    guests[i].phone = guest.phone;
    guests[i].web_invitation_id = guest.web_invitation_id;
    guests[i].web_invite_reply_datetime = guest.web_invite_reply_datetime;
    guests[i].invitation_delivery = guest.invitation_delivery;

    let guest_event_answers = [];
    for (let n = 0; n < input.guest_event_answers.length; n++) {
      let guest_event_answer = JSON.parse(JSON.stringify(input.guest_event_answers[n]));
      if (typeof guest_event_answer?.isNotGuestsPayment !== 'undefined') delete guest_event_answer.isNotGuestsPayment;
      if (typeof guest_event_answer?.payment_amounts !== 'undefined') delete guest_event_answer.payment_amounts;
      guest_event_answer.payment_amount = null;
      if (input.guest_event_answers[n]?.payment_amounts?.[i] && ! input.guest_event_answers[n]?.isNotGuestsPayment) guest_event_answer.payment_amount = input.guest_event_answers[n]?.payment_amounts?.[i];
      guest_event_answers.push(guest_event_answer);
    }
    guests[i].guest_event_answers = guest_event_answers;
    guests[i].payment_method = input.input?.payment_method;
    if (input.input?.payment_method == 'ADVANCE_PAYMENT') {
      guests[i].system_fee = guestAmounts.guests[i].systemFee;
      guests[i].system_fee_rate = SYSTEM_FEE_RATE;
      guests[i].total_amount = guestAmounts.guests[i].amount;
      guests[i].settlement_amount = guestAmounts.guests[i].amount + guestAmounts.guests[i].systemFee;
    }
    guests[i].is_system_fee = false;
    if (input.input.is_system_fee) guests[i].is_system_fee = true;  
  }
  return {
    input: guest, 
    guests: guests, 
    free_item_values: input.free_item_values, 
    guest_event_answers: guest_event_answers, 
    guest_survey_answers: input.guest_survey_answers
  };
}

//お知らせ カテゴリ別ラベルの色を修正
export const getCategoryStyle = (category: string) => {
  switch (category) {
    case '新商品':
      return { 
        color: '#2F587C', 
        border: '1px solid #2F587C',
        width: '90px',
        'text-align': 'center',
        'font-weight': 700,
      };
    case 'ご案内':
      return { 
        color: '#E65C7A', 
        border: '1px solid #E65C7A',
        width: '90px',
        'text-align': 'center',
        'font-weight': 700,
      };
    case 'キャンペーン':
      return { 
        color: '#AD871E', 
        border: '1px solid #AD871E',
        width: '90px',
        'text-align': 'center',
        'font-weight': 700,
      };
  }
};

// LINEブラウザ対策 (キャッシュ問題や表示の差異など）
// 後から変更が容易なように共通関数化
export const getWebInvitationUrl = (publicUrl = '') => {
  return publicUrl + '?openExternalBrowser=1';
}