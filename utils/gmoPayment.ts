// PGマルチペイメントサービス トークン決済v2
// https://static.mul-pay.jp/doc/card-token/#tag/intro
export class gmoPaymentClass {
  errorCodes: any;

  constructor() {
    this.errorCodes = {
      '000': 'MPクレカトークン取得正常終了',
      '100': 'カード番号を確認してください',
      '101': 'カード番号を確認してください',
      '102': 'カード番号を確認してください',
      '110': '有効期限を確認してください',
      '111': '有効期限を確認してください',
      '112': '有効期限を確認してください',
      '113': '有効期限を確認してください',
      '121': 'セキュリティコードを確認してください',
      '122': 'セキュリティコード桁数エラー',
      '131': 'カード名義人を確認してください',
      '132': 'カード名義人を確認してください',

      '141': 'MPクレカトークン発行数フォーマットエラー(数字以外を含む)',
      '142': 'MPクレカトークン発行数フォーマットエラー(1-10の範囲外)',
      '150': 'カード情報を暗号化した情報を確認してください',
      '160': 'ショップIDを確認してください',
      '161': 'ショップIDフォーマットエラー(14 桁以上)',
      '180': 'ショップIDまたは公開鍵ハッシュ値がマスターに存在しない',
      '190': 'カード情報(Encrypted)が復号できない',
      '191': 'カード情報(Encrypted)復号化後フォーマットエラー',
      '200': 'callbackを確認してください',
      '201': 'callbackフォーマットエラー(半角英数字、アンダースコア、ピリオド以外を含む)',
      '701': 'MPクレカトークン用パラメーター(cardObject)が存在しない',
      '901': '当サービス内部のシステムエラー',
      '902': '処理が混み合っている'
    };
  }
  // JSをロード
  async load() {
    if (typeof Multipayment === 'undefined') {
      const { mptoken_url } = useRuntimeConfig().public.gmo
      const script = document.createElement('script')
      script.setAttribute('src', mptoken_url)
      script.setAttribute('id', 'gmoPayment')
      document.head.appendChild(script);
      await this.waitLoad();
    }
    await this.init();
  }

  async waitLoad() {
    return new Promise((resolve) => {
      const timer = setInterval(async function(){
        if (typeof Multipayment !== 'undefined') {
          clearInterval(timer);
          resolve(true);
        }
      }, 500);
    });
  }

  async init() {
    const { shop_id } = useRuntimeConfig().public.gmo
    // リロード時
    Multipayment.init(shop_id);
  }

  getError(resultCode = '') {
    if (this.errorCodes?.[resultCode]) return this.errorCodes?.[resultCode];
    return '入力内容を確認してください。';
  }
}
export const gmoPayment = new gmoPaymentClass()