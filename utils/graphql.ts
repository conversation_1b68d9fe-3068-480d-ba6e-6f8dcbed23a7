
export type GraphQLValidationErrors = {
  [key: string]: string[] | any
}

/**
 * GraphQLのエラーからエラーメッセージを抽出する
 * @param error GraphQLエラー
 * @returns { [key: string]: string[] }
 */
export const parseGraphQLErrors = (error: any) => {
  // テスト用
  // return {last_name: ['string'],first_name: []};
  const errorMessages = {} as GraphQLValidationErrors
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  unref(error)?.graphQLErrors?.map((error: any) => {
    const validation = error.extensions?.validation
    if (typeof validation === 'undefined') return true;
    Object.keys(validation).forEach((key: string) => {
      const message = validation[key]
      let messages = [] as string[];
      if (Array.isArray(message)) {
        messages = message as string[];
      } else if (typeof message === 'string') {
        messages = [message]
      } else {
        throw new Error('Invalid error message type')
      }

      setNestedValue(errorMessages, key, messages);
    })
  })

  // MEMO
  // error.message の返却例
  // - メールアドレスもしくは、パスワードが違います
  // - Variable "$input" got invalid value {"id":"40","guest_list_id":1,"image_url":null,"parent_guest_id":null,"last_name":"1","first_name":"2","last_name_romaji":"5","first_name_romaji":"3","last_name_kana":"5","first_name_kana":"4","guest_honor":"\u30b2\u30b9\u30c8\u656c\u79f01","gender":"FEMALE","attendance":"ABSENT","guest_title":"\u30b2\u30b9\u30c8\u80a9\u66f81","relationship":null,"allergy":"1","birthdate":"1989-01-01","postal_code":"1","prefecture":"\u9752\u68ee\u770c","city":"2","address":"3","building":"4","phone":"5","email":null,"message":"3","invitation_delivery":0,"guest_group_id":null,"guest_tag_guests":[]}; Field "attendance" is not defined by type "UpdateGuestInput".
  // - Internal server error 
  // - "Validation failed for the field [createGuestList]."
  // 全体メッセージをセット
  if (error?.message.indexOf('Validation failed ') === -1 && error?.message.indexOf(' got invalid value ') === -1 && error?.message !== 'Internal server error') {
    // エラーメッセージがあれば
    errorMessages.v$ = [error.message];
  } else if (JSON.stringify(errorMessages) != '{}') {
    // バリデーションメッセージを取得できたら
    errorMessages.v$ = [APPLICATION_MESSAGES.VALIDATION_ERROR];
  } else {
    // 取れなかったら全体エラー
    errorMessages.v$ = [APPLICATION_MESSAGES.API_DEFAULT_ERROR];
  }
  return errorMessages
}