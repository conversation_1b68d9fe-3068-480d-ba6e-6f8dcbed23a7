
export class Instagram {
  private access_token: string;
  private account_id: string;

  public constructor() {
    const config = useRuntimeConfig();
    this.access_token = config.public.instagram.access_token;
    this.account_id = config.public.instagram.account_id;
  }

  public async posts(limit: number) {
    const url = 'https://graph.facebook.com/v19.0/'+this.account_id+'/media?fields=caption,media_url,media_type,thumbnail_url,permalink,timestamp,username&limit='+limit+'&access_token='+this.access_token;

    const { data, error, refresh } = await useFetch(url, {
      lazy: true,
      server: false
    }) as any;
    const posts = computed(() => data.value?.data as any[] || []);
    return {
      posts,
      error,
      refresh,
    }
  }
}
