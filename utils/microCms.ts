
export type MicroCmsQuestion = {
  id: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  revisedAt: string;
  question?: string;
  summary?: string;
  detail?: string;
  image?: string[];
  category?: string[];
  type?: string[];
  question_id?: string[];
}

export type MicroCmsKeyword = {
  id: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  revisedAt: string;
  keyword?: string;
  order?: string;
}

export type MicroCmsLandingPage = {
  id: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  revisedAt: string;
  
  // 基本情報
  title?: string;
  view?: string[]; // 表示箇所（"専用ページ（/lp/コンテンツID）"を含む）
  public?: string[]; // 環境設定（本番サーバー、ステージングサーバー、開発サーバー）
  
  // コンテンツ（実際のAPI構造）
  contents?: Array<{
    fieldId: string;
    body?: string; // リッチエディタコンテンツ
    body_html?: string; // HTMLコンテンツ（優先度高）
    custom_css?: string; // カスタムCSS
    custom_js?: string; // カスタムJS
    // ショートコード用データ
    couponCodeNumber?: string;
    couponCodeTextColor?: string;
    couponCodeBtnBgColor?: string;
    couponCodeBtnColor?: string;
    copyBoxText?: string;
    [key: string]: any;
  }>;
  
  // SEO設定（将来対応用）
  seo_title?: string;
  seo_description?: string;
  seo_ogp_image?: {
    url: string;
    height: number;
    width: number;
  };
  
  // その他設定
  description?: string;
  ogp_image?: {
    url: string;
    height: number;
    width: number;
  };
  
  [key: string]: any; // その他の動的なフィールド
}

export class MicroCms {
  private uri: string;
  private apiKey: string;

  public constructor() {
    const config = useRuntimeConfig();
    this.uri = config.public.microCms.clients.default.uri;
    this.apiKey = config.public.microCms.clients.default.apiKey;
  }

  public async fetch(
    path: string,
    params: any = {},
    ssr: boolean = true
  ) {

    let option = {
      headers: {
        'X-API-KEY': this.apiKey 
      },
      params: params
    } as any;
    if (! ssr) {
      option.lazy =  true;
      option.server =  false;
    }
    return await useFetch(this.uri + path, option);
  }

  public async fetchWithPaging(
    path: string,
    params: any = {},
    limit: number,
    offset: number,
    ssr: boolean = true
  ) {
    params.limit = limit;
    params.offset = offset;

    return await this.fetch(path, params, ssr);
  }
}
