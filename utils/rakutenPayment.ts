// 楽天ペイボルトAPI
// https://docs.payment.global.rakuten.com/ja/guides/payvault/
export class rakutenPaymentClass {
  isMasked: boolean;
  error: string;
  result: any;

  constructor() {
    this.isMasked = false;
    this.error = '';
    this.result = {};
  }
  
  // 楽天ペイボルトAPIをロード
  async load() {
    if (typeof payvault === 'undefined') {
      const { payvault_url } = useRuntimeConfig().public.rakuten
      const script = document.createElement('script')
      script.setAttribute('src', payvault_url)
      script.setAttribute('id', 'rakutenPayment')
      document.head.appendChild(script);
      await this.waitLoad();
    }
    await this.init();
  }

  async waitLoad() {
    return new Promise((resolve) => {
      const timer = setInterval(async function(){
        if (typeof payvault !== 'undefined') {
          clearInterval(timer);
          resolve(true);
        }
      }, 500);
    });
  }

  async init() {
    // リロード時
    if (typeof payvault.destroy === 'function') {
      while (true) {
        try {
          payvault.destroy();
        } catch (error) {
          // console.log(error);
          break;
        }
      }
    }

    // スタイルを適用
    payvault.setup(myElements);

    const _ = this;
    payvault.card.addEventListener('cardNumber', 'input', function(event:any) {
      _.error = '';
      if (! event.valid) {
        _.error = '正しいカード番号を入力してください';
      }
    });
  }

  async createToken() {
    const { service_id } = useRuntimeConfig().public.rakuten
    if (typeof payvault.card.createToken === 'undefined') {
      await this.load();
    }
    this.result = {};
    const response = await payvault.card.createToken({ serviceId: service_id });  
    // console.log(response);
    if (response.resultType === 'success') {
      this.result = response.maskedCardDetails;
      // console.log('正しく処理されました JS Console タブ内のレスポンスデータを確認してください ');
      this.error = '';
    } else {
      // console.log('処理エラー 詳細については JS Console タブ内の情報を見てください ');
      // this.error = '処理エラー';
      // this.error = response.errorCode;
      // this.error = response.errorMessage;
      this.error = '入力内容を確認してください';
    }
  }

  async toggleMask() {
    const currentCardLength = await payvault.card.getLength('cardNumber');
    if (currentCardLength < 1) return;
    this.isMasked = !this.isMasked;
    payvault.card.maskCardNumber(this.isMasked);
    // maskToggle.classList.remove('eye-ban-l');
    // maskToggle.classList.remove('eye-l');
    // maskToggle.classList.add(isMasked ? 'eye-l' : 'eye-ban-l');
  }
}


// Styles
const inputElementStyles = {
  base: {
    'color': '#333333',
    'border': '1px solid #d9d9d9',
    'border-radius': '4px'
  },
  valid: {
    'color': '#333333',
    'border': '1px solid #d9d9d9',
    'border-radius': '4px'
  },
  invalid: {
    'color': '#e65c7a',
    'border': '1px solid #e65c7a',
    'border-radius': '4px'
  },
  focus: {
    'color': '#333333',
    'border': '1px solid #d9d9d9',
    'border-radius': '4px'
  },
};

// Element Options
const myElements = {
  cardNumber: {
    mount: '#my-card-mount', // DOM Element | JQuery Element | id
    styles: inputElementStyles,
    placeholder: '1111222233334444'
  },
  cvv: {
    mount: '#my-cvv-mount',
    styles: inputElementStyles,
    placeholder: '123'
  },
  expirationMonth: {
    mount: '#my-expiration-month-mount',
    styles: inputElementStyles,
    placeholder: '10'
  },
  expirationYear: {
    mount: '#my-expiration-year-mount',
    styles: inputElementStyles,
    placeholder: '2027'
  }
};

export const rakutenPayment = new rakutenPaymentClass()