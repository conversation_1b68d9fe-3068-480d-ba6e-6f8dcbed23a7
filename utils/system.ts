// 事前支払いの一時停止期間の判定
export const isPrepaymentPausePeriod = (system, date) => {
  if (!date) { return false }
  const createdDate = new Date(date);
  const now = new Date();
  const from = system?.prepayment_pause_from_at ? new Date(system.prepayment_pause_from_at) : null;
  const to = system?.prepayment_pause_to_at ? new Date(system.prepayment_pause_to_at) : null;

  if (from && to) {
    return createdDate >= from && createdDate <= to && now <= to;
  } else if (from) {
    return createdDate >= from;
  } else {
    return false
  }
}
