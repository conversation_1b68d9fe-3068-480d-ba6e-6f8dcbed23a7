// バリデーションメッセージ
class validationMessageClass {
  required(value:string) {
    return `${value}は必須項目です`;
  }
  email(value:string) {
    return `${value}の形式が正しくありません`;
  }
  minLength(value:string, length:number) {
    return `${value}は${length}文字以上で入力してください`;
  }
  maxLength(value:string, length:number) {
    return `${value}は${length}文字以下で入力してください`;
  }
  password(value:string) {
    return `${value}は英小文字、英大文字、数字をそれぞれ1文字以上を入力してください`;
  }
  date(value:string) {
    return `${value}は正しい日付を入力してください`;
  }
  eventDate() {
    return `期日には過去日付、または開催日以降の日付は設定できません`;
  }
  numeric(value:string) {
    return `${value}は半角数字で入力してください`;
  }
  publicUrl(value:string) {
    return `${value}は半角英字(小文字)・半角数字とハイフン(-) アンダースコア (_) ピリオド (.)のみで入力してください`;
  }
  format(value:string) {
    return `${value}の形式が正しくありません`;
  }
  accountName(value:string) {
    return `${value}は全角カナ・全角数値・全角大文字アルファベット・（）・ーのみで入力してください`;
  }
  furiKana(value:string) {
    return `${value}は全角かなで入力してください。`;
  }
  fullWidthKatakana(value:string) {
    return `${value}はカナで入力してください。`;
  }
}

export const validationMessage = new validationMessageClass()

// フォームに適した値に変換
export function getValidationMessage(data:any) {
  if (data?.$errors?.[0]?.$message) return String(data.$errors[0].$message);
  return '';
}

// 独自バリデーション
export const validationPassword = (value:any) => {
  return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z0-9!#@\-+$&\.]+$/.test(value);
}
export const validationDate = (value:any) => {
  if (! value) return true;
  // 変な文字列
  if (! /^[0-9]{4}.[0-9]{1,2}.[0-9]{1,2}$/.test(value)) return false;

  // 有効じゃない日時
  let datetime = JSON.parse(JSON.stringify(value));
  if (typeof datetime === 'string') {
    datetime = datetime.replace(/([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})/, '$1/$2/$3');
  }
  let date = new Date(datetime);
  if (date.toString() === "Invalid Date") return false;

  // 2022-02-31 などなら無効にする
  var month = date.getMonth() + 1;
  if (month != parseInt(value.replace(/^[0-9]{4}.([0-9]{1,2}).[0-9]{1,2}$/, '$1'))) {
    return false;
  }

  return true
}
export const validationPostalCode = (value:any) => {
  if (! value) return true;
  if (! /^[0-9]{3}-[0-9]{4}$/.test(value) && ! /^[0-9]{3}[0-9]{4}$/.test(value)) return false;
  return true
}
// export const validationPhone = (value:any) => {
//   if (! value) return true;
//   if (! /^[0-9\-\+]+$/.test(value)) return false;
//   return true
// }
export const validationPublicUrl = (value:any) => {
  return /^[A-Za-z0-9\-_\.]+$/.test(value);
}

// なんでもOK : サーバサイドからのバリデーションを取得する際に、なにかしらの指定をしないと表示されないので、追加用
export const noValidation = (value:any) => {
  return true;
}

type ValidationRequiredIfFn = (value: any) => boolean;
export const validationRequiredIf = (isCondition: boolean): ValidationRequiredIfFn => {
  return (value: any): boolean => {
    if(!isCondition){
      return true
    }
    if(value){
      return true
    }
    return false
  }
}

type ValidatorFn = (value: any) => boolean;
export const validationEventDate = (comparisonValue: any): ValidatorFn => {
  return (value: any): boolean => {
    if (!value || !comparisonValue) return true;
    let date = new Date(value);
    let comparisonDate = new Date(comparisonValue);
    let now = new Date();

    // 日付のみを比較するために、時間をリセット
    date.setHours(0, 0, 0, 0);
    comparisonDate.setHours(0, 0, 0, 0);
    now.setHours(0, 0, 0, 0);

    // 過去の日付でないこと、かつ開催日以前であることを確認
    if (date < now || date > comparisonDate) return false;

    return true;
  }
}